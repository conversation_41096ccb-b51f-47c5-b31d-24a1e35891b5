{"version": 3, "sources": ["src/app/pages/register/register.page.ts", "src/app/pages/register/register.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { IonicModule, Platform, AlertController } from '@ionic/angular';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { Router } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { environment } from '../../../environments/environment';\r\nimport { FcmService } from '../../services/fcm.service';\r\n\r\n@Component({\r\n  standalone: true,\r\n  imports: [IonicModule, FormsModule],\r\n  selector: 'app-register',\r\n  templateUrl: './register.page.html',\r\n  styleUrls: ['./register.page.scss'],\r\n})\r\nexport class RegisterPage implements OnInit {\r\n  // ... rest of your class code ...\r\n  user = {\r\n    full_name: '',\r\n    email: '',\r\n    password: '',\r\n    confirmPassword: ''\r\n  };\r\n\r\n  fcmToken: string = '';\r\n  private fcmTokenReady = false;\r\n\r\n  constructor(\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private http: HttpClient,\r\n    private platform: Platform,\r\n    private fcmService: FcmService,\r\n    private alertController: AlertController\r\n  ) {}\r\n\r\n  async ngOnInit() {\r\n    console.log('🔥 Register page initializing...');\r\n    // Initialize FCM and get token\r\n    await this.initializeFCM();\r\n  }\r\n\r\n  /**\r\n   * Initialize FCM service and get token\r\n   */\r\n  async initializeFCM() {\r\n    try {\r\n      console.log('🔥 Initializing FCM for registration...');\r\n\r\n      // Initialize FCM service first\r\n      await this.fcmService.initPush();\r\n\r\n      // Get FCM token\r\n      await this.getFCMToken();\r\n\r\n      console.log('✅ FCM initialization complete, token ready:', !!this.fcmToken);\r\n      this.fcmTokenReady = true;\r\n    } catch (error) {\r\n      console.error('❌ FCM initialization failed:', error);\r\n      // Continue without FCM - app should still work\r\n      this.fcmTokenReady = false;\r\n    }\r\n  }\r\n\r\n  async getFCMToken() {\r\n    try {\r\n      // For browser testing, create a mock token\r\n      if (!this.platform.is('cordova') && !this.platform.is('capacitor')) {\r\n        console.log('Running in browser, using mock FCM token');\r\n        this.fcmToken = 'browser-mock-token-' + Math.random().toString(36).substring(2, 15);\r\n        console.log('Mock FCM Token:', this.fcmToken);\r\n        return;\r\n      }\r\n\r\n      // For real devices, use the FCM service\r\n      console.log('Getting FCM token from service...');\r\n      this.fcmToken = await this.fcmService.getToken();\r\n      console.log('✅ FCM Token obtained:', this.fcmToken.substring(0, 20) + '...');\r\n\r\n    } catch (error) {\r\n      console.error('❌ Error getting FCM token from service:', error);\r\n      // Continue without token - app should still work\r\n      this.fcmToken = '';\r\n    }\r\n  }\r\n\r\n  async onRegister() {\r\n    if (this.user.password !== this.user.confirmPassword) {\r\n      await this.presentAlert('Registration Failed', 'Passwords do not match!');\r\n      return;\r\n    }\r\n\r\n    this.authService.register({\r\n      full_name: this.user.full_name,\r\n      email: this.user.email,\r\n      password: this.user.password,\r\n      password_confirmation: this.user.confirmPassword\r\n    }).subscribe({\r\n      next: async res => {\r\n        console.log('Registration successful:', res);\r\n\r\n        // Try to register the FCM token immediately after registration\r\n        if (this.fcmToken) {\r\n          console.log('Registering FCM token after registration:', this.fcmToken);\r\n\r\n          // Include Firebase project ID in the request\r\n          const payload = {\r\n            token: this.fcmToken,\r\n            device_type: this.platform.is('ios') ? 'ios' : 'android',\r\n            project_id: environment.firebase.projectId\r\n            // Note: We don't have user_id yet since we're not logged in\r\n          };\r\n\r\n          console.log('Token registration payload:', payload);\r\n\r\n          // Use the FCM service to register the token\r\n          this.fcmService.registerTokenWithBackend(this.fcmToken);\r\n        } else {\r\n          console.warn('No FCM token available to register after registration');\r\n        }\r\n\r\n        await this.presentAlert('Registration Successful', 'Your account has been created successfully. Please log in.');\r\n        this.router.navigate(['/login']);\r\n      },\r\n      error: async err => {\r\n        console.error('Registration error:', err);\r\n        await this.presentAlert('Registration Failed', 'Registration failed: ' + (err.error?.message || 'Unknown error'));\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Helper method to register a token with multiple endpoints\r\n   * @param payload The token payload to send\r\n   */\r\n  async registerTokenWithEndpoints(payload: any) {\r\n    const endpoints = [\r\n      `${environment.apiUrl}/device-token`,\r\n      'http://localhost:8000/api/device-token',\r\n      'https://7af9-43-226-6-217.ngrok-free.app/api/device-token'\r\n    ];\r\n\r\n    for (const endpoint of endpoints) {\r\n      try {\r\n        const response = await this.http.post(endpoint, payload).toPromise();\r\n        console.log(`FCM token registered with ${endpoint}:`, response);\r\n        // Store the token in localStorage for potential recovery\r\n        localStorage.setItem('fcm_token', this.fcmToken);\r\n        // Successfully registered, no need to try other endpoints\r\n        break;\r\n      } catch (error) {\r\n        console.error(`Error registering token with ${endpoint}:`, error);\r\n        // Continue to the next endpoint\r\n      }\r\n    }\r\n  }\r\n\r\n  async presentAlert(header: string, message: string) {\r\n    const alert = await this.alertController.create({\r\n      header: header,\r\n      message: message,\r\n      buttons: ['OK']\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  goToLogin() {\r\n    this.router.navigate(['/login']);\r\n  }\r\n}", "\r\n\r\n<ion-content class=\"ion-padding register-bg\">\r\n  <div class=\"register-wrapper\">\r\n    <img src=\"assets/ALERTO.png\" alt=\"App Logo\" class=\"register-logo\" />\r\n    <h1 class=\"register-title\">Sign Up Here!</h1>\r\n    <form (ngSubmit)=\"onRegister()\" class=\"register-form\">\r\n      <ion-item>\r\n        <ion-label position=\"floating\">Full Name:</ion-label>\r\n        <ion-input type=\"text\" [(ngModel)]=\"user.full_name\" name=\"full_name\" required></ion-input>\r\n      </ion-item>\r\n\r\n      <ion-item>\r\n        <ion-label position=\"floating\">Email:</ion-label>\r\n        <ion-input type=\"email\" [(ngModel)]=\"user.email\" name=\"email\" required></ion-input>\r\n      </ion-item>\r\n\r\n      <ion-item>\r\n        <ion-label position=\"floating\">Password:</ion-label>\r\n        <ion-input type=\"password\" [(ngModel)]=\"user.password\" name=\"password\" required></ion-input>\r\n      </ion-item>\r\n\r\n      <ion-item>\r\n        <ion-label position=\"floating\">Confirm Password:</ion-label>\r\n        <ion-input type=\"password\" [(ngModel)]=\"user.confirmPassword\" name=\"confirmPassword\" required></ion-input>\r\n      </ion-item>\r\n<br>  <br>\r\n      <ion-button expand=\"block\" type=\"submit\" class=\"register-btn\">Register</ion-button>\r\n    </form>\r\n\r\n    <div class=\"ion-text-center ion-margin-top\">\r\n      <ion-text>Already have an account? </ion-text>\r\n      <a (click)=\"goToLogin()\"><strong><u>Log In</u></strong></a>\r\n    </div>\r\n\r\n    \r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBM,IAAO,eAAP,MAAO,cAAY;EAYvB,YACU,aACA,QACA,MACA,UACA,YACA,iBAAgC;AALhC,SAAA,cAAA;AACA,SAAA,SAAA;AACA,SAAA,OAAA;AACA,SAAA,WAAA;AACA,SAAA,aAAA;AACA,SAAA,kBAAA;AAhBV,SAAA,OAAO;MACL,WAAW;MACX,OAAO;MACP,UAAU;MACV,iBAAiB;;AAGnB,SAAA,WAAmB;AACX,SAAA,gBAAgB;EASrB;EAEG,WAAQ;;AACZ,cAAQ,IAAI,yCAAkC;AAE9C,YAAM,KAAK,cAAa;IAC1B;;;;;EAKM,gBAAa;;AACjB,UAAI;AACF,gBAAQ,IAAI,gDAAyC;AAGrD,cAAM,KAAK,WAAW,SAAQ;AAG9B,cAAM,KAAK,YAAW;AAEtB,gBAAQ,IAAI,oDAA+C,CAAC,CAAC,KAAK,QAAQ;AAC1E,aAAK,gBAAgB;MACvB,SAAS,OAAO;AACd,gBAAQ,MAAM,qCAAgC,KAAK;AAEnD,aAAK,gBAAgB;MACvB;IACF;;EAEM,cAAW;;AACf,UAAI;AAEF,YAAI,CAAC,KAAK,SAAS,GAAG,SAAS,KAAK,CAAC,KAAK,SAAS,GAAG,WAAW,GAAG;AAClE,kBAAQ,IAAI,0CAA0C;AACtD,eAAK,WAAW,wBAAwB,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAClF,kBAAQ,IAAI,mBAAmB,KAAK,QAAQ;AAC5C;QACF;AAGA,gBAAQ,IAAI,mCAAmC;AAC/C,aAAK,WAAW,MAAM,KAAK,WAAW,SAAQ;AAC9C,gBAAQ,IAAI,8BAAyB,KAAK,SAAS,UAAU,GAAG,EAAE,IAAI,KAAK;MAE7E,SAAS,OAAO;AACd,gBAAQ,MAAM,gDAA2C,KAAK;AAE9D,aAAK,WAAW;MAClB;IACF;;EAEM,aAAU;;AACd,UAAI,KAAK,KAAK,aAAa,KAAK,KAAK,iBAAiB;AACpD,cAAM,KAAK,aAAa,uBAAuB,yBAAyB;AACxE;MACF;AAEA,WAAK,YAAY,SAAS;QACxB,WAAW,KAAK,KAAK;QACrB,OAAO,KAAK,KAAK;QACjB,UAAU,KAAK,KAAK;QACpB,uBAAuB,KAAK,KAAK;OAClC,EAAE,UAAU;QACX,MAAM,CAAM,QAAM;AAChB,kBAAQ,IAAI,4BAA4B,GAAG;AAG3C,cAAI,KAAK,UAAU;AACjB,oBAAQ,IAAI,6CAA6C,KAAK,QAAQ;AAGtE,kBAAM,UAAU;cACd,OAAO,KAAK;cACZ,aAAa,KAAK,SAAS,GAAG,KAAK,IAAI,QAAQ;cAC/C,YAAY,YAAY,SAAS;;;AAInC,oBAAQ,IAAI,+BAA+B,OAAO;AAGlD,iBAAK,WAAW,yBAAyB,KAAK,QAAQ;UACxD,OAAO;AACL,oBAAQ,KAAK,uDAAuD;UACtE;AAEA,gBAAM,KAAK,aAAa,2BAA2B,4DAA4D;AAC/G,eAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;QACjC;QACA,OAAO,CAAM,QAAM;AACjB,kBAAQ,MAAM,uBAAuB,GAAG;AACxC,gBAAM,KAAK,aAAa,uBAAuB,2BAA2B,IAAI,OAAO,WAAW,gBAAgB;QAClH;OACD;IACH;;;;;;EAMM,2BAA2B,SAAY;;AAC3C,YAAM,YAAY;QAChB,GAAG,YAAY,MAAM;QACrB;QACA;;AAGF,iBAAW,YAAY,WAAW;AAChC,YAAI;AACF,gBAAM,WAAW,MAAM,KAAK,KAAK,KAAK,UAAU,OAAO,EAAE,UAAS;AAClE,kBAAQ,IAAI,6BAA6B,QAAQ,KAAK,QAAQ;AAE9D,uBAAa,QAAQ,aAAa,KAAK,QAAQ;AAE/C;QACF,SAAS,OAAO;AACd,kBAAQ,MAAM,gCAAgC,QAAQ,KAAK,KAAK;QAElE;MACF;IACF;;EAEM,aAAa,QAAgB,SAAe;;AAChD,YAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;QAC9C;QACA;QACA,SAAS,CAAC,IAAI;OACf;AAED,YAAM,MAAM,QAAO;IACrB;;EAEA,YAAS;AACP,SAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;EACjC;;;uCA1JW,eAAY,4BAAA,WAAA,GAAA,4BAAA,MAAA,GAAA,4BAAA,UAAA,GAAA,4BAAA,QAAA,GAAA,4BAAA,UAAA,GAAA,4BAAA,eAAA,CAAA;IAAA;EAAA;;yEAAZ,eAAY,WAAA,CAAA,CAAA,cAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,eAAA,aAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,OAAA,qBAAA,OAAA,YAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,UAAA,GAAA,CAAA,YAAA,UAAA,GAAA,CAAA,QAAA,QAAA,QAAA,aAAA,YAAA,IAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,SAAA,QAAA,SAAA,YAAA,IAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,YAAA,QAAA,YAAA,YAAA,IAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,YAAA,QAAA,mBAAA,YAAA,IAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,UAAA,SAAA,QAAA,UAAA,GAAA,cAAA,GAAA,CAAA,GAAA,mBAAA,gBAAA,GAAA,CAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,sBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACdzB,QAAA,yBAAA,GAAA,eAAA,CAAA,EAA6C,GAAA,OAAA,CAAA;AAEzC,QAAA,oBAAA,GAAA,OAAA,CAAA;AACA,QAAA,yBAAA,GAAA,MAAA,CAAA;AAA2B,QAAA,iBAAA,GAAA,eAAA;AAAa,QAAA,uBAAA;AACxC,QAAA,yBAAA,GAAA,QAAA,CAAA;AAAM,QAAA,qBAAA,YAAA,SAAA,iDAAA;AAAA,iBAAY,IAAA,WAAA;QAAY,CAAA;AAC5B,QAAA,yBAAA,GAAA,UAAA,EAAU,GAAA,aAAA,CAAA;AACuB,QAAA,iBAAA,GAAA,YAAA;AAAU,QAAA,uBAAA;AACzC,QAAA,yBAAA,GAAA,aAAA,CAAA;AAAuB,QAAA,2BAAA,iBAAA,SAAA,yDAAA,QAAA;AAAA,UAAA,6BAAA,IAAA,KAAA,WAAA,MAAA,MAAA,IAAA,KAAA,YAAA;AAAA,iBAAA;QAAA,CAAA;AAAuD,QAAA,uBAAA,EAAY;AAG5F,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACuB,QAAA,iBAAA,IAAA,QAAA;AAAM,QAAA,uBAAA;AACrC,QAAA,yBAAA,IAAA,aAAA,CAAA;AAAwB,QAAA,2BAAA,iBAAA,SAAA,0DAAA,QAAA;AAAA,UAAA,6BAAA,IAAA,KAAA,OAAA,MAAA,MAAA,IAAA,KAAA,QAAA;AAAA,iBAAA;QAAA,CAAA;AAA+C,QAAA,uBAAA,EAAY;AAGrF,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACuB,QAAA,iBAAA,IAAA,WAAA;AAAS,QAAA,uBAAA;AACxC,QAAA,yBAAA,IAAA,aAAA,CAAA;AAA2B,QAAA,2BAAA,iBAAA,SAAA,0DAAA,QAAA;AAAA,UAAA,6BAAA,IAAA,KAAA,UAAA,MAAA,MAAA,IAAA,KAAA,WAAA;AAAA,iBAAA;QAAA,CAAA;AAAqD,QAAA,uBAAA,EAAY;AAG9F,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACuB,QAAA,iBAAA,IAAA,mBAAA;AAAiB,QAAA,uBAAA;AAChD,QAAA,yBAAA,IAAA,aAAA,CAAA;AAA2B,QAAA,2BAAA,iBAAA,SAAA,0DAAA,QAAA;AAAA,UAAA,6BAAA,IAAA,KAAA,iBAAA,MAAA,MAAA,IAAA,KAAA,kBAAA;AAAA,iBAAA;QAAA,CAAA;AAAmE,QAAA,uBAAA,EAAY;AAElH,QAAA,oBAAA,IAAA,IAAA,EAAI,IAAA,IAAA;AACE,QAAA,yBAAA,IAAA,cAAA,EAAA;AAA8D,QAAA,iBAAA,IAAA,UAAA;AAAQ,QAAA,uBAAA,EAAa;AAGrF,QAAA,yBAAA,IAAA,OAAA,EAAA,EAA4C,IAAA,UAAA;AAChC,QAAA,iBAAA,IAAA,2BAAA;AAAyB,QAAA,uBAAA;AACnC,QAAA,yBAAA,IAAA,KAAA,EAAA;AAAG,QAAA,qBAAA,SAAA,SAAA,4CAAA;AAAA,iBAAS,IAAA,UAAA;QAAW,CAAA;AAAE,QAAA,yBAAA,IAAA,QAAA,EAAQ,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,QAAA;AAAM,QAAA,uBAAA,EAAI,EAAS,EAAI,EACvD,EAGF;;;AA3BuB,QAAA,oBAAA,CAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,KAAA,SAAA;AAKC,QAAA,oBAAA,CAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,KAAA,KAAA;AAKG,QAAA,oBAAA,CAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,KAAA,QAAA;AAKA,QAAA,oBAAA,CAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,KAAA,eAAA;;sBDbvB,aAAW,WAAA,YAAA,UAAA,SAAA,UAAA,SAAA,4BAAE,aAAW,oBAAA,iBAAA,sBAAA,mBAAA,SAAA,MAAA,GAAA,QAAA,CAAA,gmDAAA,EAAA,CAAA;EAAA;;;sEAKvB,cAAY,CAAA;UAPxB;yBACa,MAAI,SACP,CAAC,aAAa,WAAW,GAAC,UACzB,gBAAc,UAAA,g+CAAA,QAAA,CAAA,+yCAAA,EAAA,CAAA;;;;6EAIb,cAAY,EAAA,WAAA,gBAAA,UAAA,2CAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
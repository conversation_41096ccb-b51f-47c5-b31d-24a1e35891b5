import{c as P}from"./chunk-LOLLZ3RS.js";import{a as k,d as F}from"./chunk-7LH2AG5T.js";import{b as c}from"./chunk-MCRJI3T3.js";import{a as x,b as y}from"./chunk-4EI7TLDT.js";import{b as D,f as R,g as T,h as L,k as w}from"./chunk-UYQ7EZNZ.js";import{a as g,e as C,f as I}from"./chunk-BAKMWPBW.js";import{a as A}from"./chunk-OBXDPQ3V.js";import{g as u}from"./chunk-2R6CW7ES.js";var h='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',N=(e,n)=>{let t=e.querySelector(h);q(t,n??e)},_=(e,n)=>{let t=Array.from(e.querySelectorAll(h)),o=t.length>0?t[t.length-1]:null;q(o,n??e)},q=(e,n)=>{let t=e,o=e?.shadowRoot;if(o&&(t=o.querySelector(h)||e),t){let i=t.closest("ion-radio-group");i?i.setFocus():w(t)}else n.focus()},E=0,Y=0,O=new WeakMap,m=e=>({create(t){return $(e,t)},dismiss(t,o,i){return J(document,t,o,e,i)},getTop(){return u(this,null,function*(){return v(document,e)})}}),pe=m("ion-alert"),ge=m("ion-action-sheet"),ve=m("ion-loading"),he=m("ion-modal");var Oe=m("ion-popover"),be=m("ion-toast"),we=e=>{typeof document<"u"&&Q(document);let n=E++;e.overlayIndex=n},ye=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++Y}`),e.id),$=(e,n)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let t=document.createElement(e);return t.classList.add("overlay-hidden"),Object.assign(t,Object.assign(Object.assign({},n),{hasController:!0})),j(document).appendChild(t),new Promise(o=>D(t,o))}):Promise.resolve(),H=e=>e.classList.contains("overlay-hidden"),B=(e,n)=>{let t=e,o=e?.shadowRoot;o&&(t=o.querySelector(h)||e),t?w(t):n.focus()},z=(e,n)=>{let t=v(n,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),o=e.target;if(!t||!o||t.classList.contains(se))return;let i=()=>{if(t===o)t.lastFocus=void 0;else if(o.tagName==="ION-TOAST")B(t.lastFocus,t);else{let a=L(t);if(!a.contains(o))return;let s=a.querySelector(".ion-overlay-wrapper");if(!s)return;if(s.contains(o)||o===a.querySelector("ion-backdrop"))t.lastFocus=o;else{let d=t.lastFocus;N(s,t),d===n.activeElement&&_(s,t),t.lastFocus=n.activeElement}}},r=()=>{if(t.contains(o))t.lastFocus=o;else if(o.tagName==="ION-TOAST")B(t.lastFocus,t);else{let a=t.lastFocus;N(t),a===n.activeElement&&_(t),t.lastFocus=n.activeElement}};t.shadowRoot?r():i()},Q=e=>{E===0&&(E=1,e.addEventListener("focus",n=>{z(n,e)},!0),e.addEventListener("ionBackButton",n=>{let t=v(e);t?.backdropDismiss&&n.detail.register(F,()=>{t.dismiss(void 0,S)})}),k()||e.addEventListener("keydown",n=>{if(n.key==="Escape"){let t=v(e);t?.backdropDismiss&&t.dismiss(void 0,S)}}))},J=(e,n,t,o,i)=>{let r=v(e,o,i);return r?r.dismiss(n,t):Promise.reject("overlay does not exist")},X=(e,n)=>(n===void 0&&(n="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(n)).filter(t=>t.overlayIndex>0)),b=(e,n)=>X(e,n).filter(t=>!H(t)),v=(e,n,t)=>{let o=b(e,n);return t===void 0?o[o.length-1]:o.find(i=>i.id===t)},V=(e=!1)=>{let t=j(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");t&&(e?t.setAttribute("aria-hidden","true"):t.removeAttribute("aria-hidden"))},Ae=(e,n,t,o,i)=>u(null,null,function*(){var r,a;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(V(!0),document.body.classList.add(A)),oe(e.el),U(e.el),e.presented=!0,e.willPresent.emit(),(r=e.willPresentShorthand)===null||r===void 0||r.emit();let s=y(e),d=e.enterAnimation?e.enterAnimation:g.get(n,s==="ios"?t:o);(yield G(e,d,e.el,i))&&(e.didPresent.emit(),(a=e.didPresentShorthand)===null||a===void 0||a.emit()),e.el.tagName!=="ION-TOAST"&&Z(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),Z=e=>u(null,null,function*(){let n=document.activeElement;if(!n)return;let t=n?.shadowRoot;t&&(n=t.querySelector(h)||n),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&n.focus()}),Ee=(e,n,t,o,i,r,a)=>u(null,null,function*(){var s,d;if(!e.presented)return!1;let f=(c!==void 0?b(c):[]).filter(p=>p.tagName!=="ION-TOAST");f.length===1&&f[0].id===e.el.id&&(V(!1),document.body.classList.remove(A)),e.presented=!1;try{U(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:n,role:t}),(s=e.willDismissShorthand)===null||s===void 0||s.emit({data:n,role:t});let p=y(e),W=e.leaveAnimation?e.leaveAnimation:g.get(o,p==="ios"?i:r);t!==ne&&(yield G(e,W,e.el,a)),e.didDismiss.emit({data:n,role:t}),(d=e.didDismissShorthand)===null||d===void 0||d.emit({data:n,role:t}),(O.get(e)||[]).forEach(K=>K.destroy()),O.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(p){I(`[${e.el.tagName.toLowerCase()}] - `,p)}return e.el.remove(),ie(),!0}),j=e=>e.querySelector("ion-app")||e.body,G=(e,n,t,o)=>u(null,null,function*(){t.classList.remove("overlay-hidden");let i=e.el,r=n(i,o);(!e.animated||!g.getBoolean("animated",!0))&&r.duration(0),e.keyboardClose&&r.beforeAddWrite(()=>{let s=t.ownerDocument.activeElement;s?.matches("input,ion-input, ion-textarea")&&s.blur()});let a=O.get(e)||[];return O.set(e,[...a,r]),yield r.play(),!0}),Se=(e,n)=>{let t,o=new Promise(i=>t=i);return ee(e,n,i=>{t(i.detail)}),o},ee=(e,n,t)=>{let o=i=>{T(e,n,o),t(i)};R(e,n,o)},Ce=e=>e==="cancel"||e===S,te=e=>e(),Ie=(e,n)=>{if(typeof e=="function")return g.get("_zoneGate",te)(()=>{try{return e(n)}catch(o){throw o}})},S="backdrop",ne="gesture",De=39,Re=e=>{let n=!1,t,o=P(),i=(s=!1)=>{if(t&&!s)return{delegate:t,inline:n};let{el:d,hasController:l,delegate:f}=e;return n=d.parentNode!==null&&!l,t=n?f||o:f,{inline:n,delegate:t}};return{attachViewToDom:s=>u(null,null,function*(){let{delegate:d}=i(!0);if(d)return yield d.attachViewToDom(e.el,s);let{hasController:l}=e;if(l&&s!==void 0)throw new Error("framework delegate is missing");return null}),removeViewFromDom:()=>{let{delegate:s}=i();s&&e.el!==void 0&&s.removeViewFromDom(e.el.parentElement,e.el)}}},Te=()=>{let e,n=()=>{e&&(e(),e=void 0)};return{addClickListener:(o,i)=>{n();let r=i!==void 0?document.getElementById(i):null;if(!r){C(`[${o.tagName.toLowerCase()}] - A trigger element with the ID "${i}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,o);return}e=((s,d)=>{let l=()=>{d.present()};return s.addEventListener("click",l),()=>{s.removeEventListener("click",l)}})(r,o)},removeClickListener:n}},U=e=>{c!==void 0&&x("android")&&e.setAttribute("aria-hidden","true")},oe=e=>{var n;if(c===void 0)return;let t=b(c);for(let o=t.length-1;o>=0;o--){let i=t[o],r=(n=t[o+1])!==null&&n!==void 0?n:e;(r.hasAttribute("aria-hidden")||r.tagName!=="ION-TOAST")&&i.setAttribute("aria-hidden","true")}},ie=()=>{if(c===void 0)return;let e=b(c);for(let n=e.length-1;n>=0;n--){let t=e[n];if(t.removeAttribute("aria-hidden"),t.tagName!=="ION-TOAST")break}},se="ion-disable-focus-trap";export{N as a,_ as b,pe as c,ge as d,ve as e,he as f,Oe as g,be as h,we as i,ye as j,v as k,Ae as l,Ee as m,Se as n,Ce as o,Ie as p,S as q,ne as r,De as s,Re as t,Te as u,se as v};

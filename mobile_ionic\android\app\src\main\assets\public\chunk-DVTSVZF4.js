import{a as G,b as W,c as Y}from"./chunk-C4VSGXVP.js";import{a as V}from"./chunk-JEH6AHBE.js";import{a as B}from"./chunk-FULEFYAM.js";import"./chunk-X4DCX5TK.js";import{$a as U,Bb as z,C as v,Cb as Q,E as p,F as f,G as C,I as A,L as w,Ma as R,N as x,Na as q,Oa as I,Pa as _,Va as $,W as L,_ as O,_a as S,ba as T,da as N,g as b,p as h,tb as D,ub as H,x as M,yb as K,z as P,zb as F}from"./chunk-PBKSAHK2.js";import"./chunk-MBKQLJTW.js";import"./chunk-F3654E4N.js";import"./chunk-FHR3DP7J.js";import"./chunk-A4FGPDGZ.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-AUB5HKS7.js";import"./chunk-RS5W3JWO.js";import"./chunk-LOLLZ3RS.js";import"./chunk-XZOVPSKP.js";import"./chunk-7LH2AG5T.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-SPZFNIGG.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-KY4M3ZA2.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-4EI7TLDT.js";import"./chunk-FED6QSGK.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{a as y,b as E,f as j,g as c}from"./chunk-2R6CW7ES.js";var i=j(Y());var ut=(()=>{class m{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.loadingCtrl=h(F),this.toastCtrl=h(z),this.alertCtrl=h(K),this.http=h(O),this.router=h(N),this.route=h(T),this.mapboxRouting=h(W),this.offlineStorage=h(V)}ngOnInit(){console.log("\u{1F7E0} EARTHQUAKE MAP: Component initialized..."),this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null,console.log("\u{1F7E0} EARTHQUAKE MAP: New center to highlight:",this.newCenterId))})}ngAfterViewInit(){return c(this,null,function*(){console.log("\u{1F7E0} EARTHQUAKE MAP: View initialized, loading map..."),setTimeout(()=>c(this,null,function*(){yield this.loadEarthquakeMap()}),100)})}loadEarthquakeMap(){return c(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading earthquake evacuation centers...",spinner:"crescent"});yield t.present();try{let o=yield G.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),n=o.coords.latitude,e=o.coords.longitude;this.userLocation={lat:n,lng:e},console.log(`\u{1F7E0} EARTHQUAKE MAP: User location [${n}, ${e}]`),this.initializeMap(n,e),yield this.loadEarthquakeCenters(n,e),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F7E0} Showing ${this.evacuationCenters.length} earthquake evacuation centers`,duration:3e3,color:"warning",position:"top"})).present()}catch(o){yield t.dismiss(),console.error("\u{1F7E0} EARTHQUAKE MAP: Error loading map",o),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadEarthquakeMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,o){if(console.log(`\u{1F7E0} EARTHQUAKE MAP: Initializing map at [${t}, ${o}]`),!document.getElementById("earthquake-map"))throw console.error("\u{1F7E0} EARTHQUAKE MAP: Container #earthquake-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=i.map("earthquake-map").setView([t,o],13),i.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=i.marker([t,o],{icon:i.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadEarthquakeCenters(t,o){return c(this,null,function*(){try{console.log("\u{1F7E0} EARTHQUAKE MAP: Fetching earthquake centers...");let n=[];if(this.offlineStorage.isOfflineMode()||!navigator.onLine){if(console.log("\u{1F504} Loading earthquake centers from offline storage"),n=yield this.offlineStorage.getEvacuationCenters(),console.log("\u{1F4F1} OFFLINE DATA:",n),n.length===0){console.warn("\u26A0\uFE0F No cached evacuation centers found"),yield(yield this.alertCtrl.create({header:"No Offline Data",message:"No offline evacuation data available. Please sync data when online.",buttons:["OK"]})).present();return}}else try{n=yield b(this.http.get(`${B.apiUrl}/evacuation-centers`)),console.log("\u{1F7E0} EARTHQUAKE MAP: Total centers received from API:",n?.length||0)}catch(e){if(console.error("\u274C API failed, falling back to offline data:",e),n=yield this.offlineStorage.getEvacuationCenters(),n.length===0){yield(yield this.alertCtrl.create({header:"Connection Error",message:"Cannot connect to server and no offline data available. Please check your connection or sync data when online.",buttons:["OK"]})).present();return}}if(this.evacuationCenters=n.filter(e=>e.disaster_type==="Earthquake"),console.log(`\u{1F7E0} EARTHQUAKE MAP: Filtered to ${this.evacuationCenters.length} earthquake centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Earthquake Centers",message:"No earthquake evacuation centers found in the data.",buttons:["OK"]})).present();return}yield this.addMarkersAndRoutes(t,o)}catch(n){console.error("\u{1F7E0} EARTHQUAKE MAP: Error loading centers",n);try{console.log("\u{1F504} Last resort: trying offline storage...");let a=yield this.offlineStorage.getEvacuationCenters();if(this.evacuationCenters=a.filter(s=>s.disaster_type==="Earthquake"),this.evacuationCenters.length>0){console.log(`\u{1F7E0} Loaded ${this.evacuationCenters.length} earthquake centers from offline storage`),yield this.addMarkersAndRoutes(t,o);return}}catch(a){console.error("\u274C Offline storage also failed:",a)}yield(yield this.toastCtrl.create({message:"Error loading earthquake centers. Please check your connection or sync offline data.",duration:4e3,color:"danger"})).present()}})}addMarkersAndRoutes(t,o){return c(this,null,function*(){let n=this.offlineStorage.isOfflineMode()||!navigator.onLine;if(this.evacuationCenters.forEach(e=>{let a=Number(e.latitude),s=Number(e.longitude);if(!isNaN(a)&&!isNaN(s)){let r=i.marker([a,s],{icon:i.icon({iconUrl:"assets/forEarthquake.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),l=this.calculateDistance(t,o,a,s);r.on("click",()=>{n?this.showOfflineMarkerInfo(e,l):this.showTransportationOptions(e)});let u=this.newCenterId&&e.id.toString()===this.newCenterId,g=n?"<p><em>\u{1F4F1} Offline Mode - Limited functionality</em></p>":"<p><em>Click marker for route options</em></p>";r.bindPopup(`
          <div class="evacuation-popup">
            <h3>\u{1F7E0} ${e.name} ${u?"\u2B50 NEW!":""}</h3>
            <p><strong>Type:</strong> Earthquake Center</p>
            <p><strong>Distance:</strong> ${(l/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${e.capacity||"N/A"}</p>
            ${g}
            ${u?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
          </div>
        `),u&&(r.openPopup(),this.map.setView([a,s],15),this.toastCtrl.create({message:`\u{1F195} New earthquake evacuation center: ${e.name}`,duration:5e3,color:"warning",position:"top"}).then(d=>d.present())),r.addTo(this.map),console.log(`\u{1F7E0} Added earthquake marker: ${e.name}`)}}),n?console.log("\u{1F7E0} Offline mode: Showing markers only (no routing)"):(console.log("\u{1F7E0} Online mode: Auto-routing to 2 nearest earthquake centers..."),yield this.routeToTwoNearestCenters()),this.evacuationCenters.length>0){let e=i.latLngBounds([]);e.extend([t,o]),this.evacuationCenters.forEach(a=>{e.extend([Number(a.latitude),Number(a.longitude)])}),this.map.fitBounds(e,{padding:[50,50]})}})}calculateDistance(t,o,n,e){let s=t*Math.PI/180,r=n*Math.PI/180,l=(n-t)*Math.PI/180,u=(e-o)*Math.PI/180,g=Math.sin(l/2)*Math.sin(l/2)+Math.cos(s)*Math.cos(r)*Math.sin(u/2)*Math.sin(u/2);return 6371e3*(2*Math.atan2(Math.sqrt(g),Math.sqrt(1-g)))}routeToTwoNearestCenters(){return c(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F7E0} EARTHQUAKE MAP: No user location or evacuation centers available");return}try{console.log("\u{1F7E0} EARTHQUAKE MAP: Finding 2 nearest earthquake centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0)return;this.clearRoutes(),yield this.calculateRoutes(t)}catch(t){console.error("\u{1F7E0} EARTHQUAKE MAP: Error calculating routes",t)}})}getTwoNearestCenters(t,o){return this.evacuationCenters.map(e=>E(y({},e),{distance:this.calculateDistance(t,o,Number(e.latitude),Number(e.longitude))})).sort((e,a)=>e.distance-a.distance).slice(0,2)}calculateRoutes(t){return c(this,null,function*(){if(this.userLocation){this.routeLayer=i.layerGroup().addTo(this.map);for(let o=0;o<t.length;o++){let n=t[o],e=Number(n.latitude),a=Number(n.longitude);if(!isNaN(e)&&!isNaN(a))try{let s=this.mapboxRouting.convertTravelModeToProfile("walking"),r=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,a,e,s,{geometries:"geojson",overview:"simplified",steps:!1});if(r&&r.routes&&r.routes.length>0){let l=r.routes[0];i.polyline(l.geometry.coordinates.map(d=>[d[1],d[0]]),{color:"#ff9500",weight:4,opacity:.8,dashArray:o===0?void 0:"10, 10"}).addTo(this.routeLayer),console.log(`\u{1F7E0} Route ${o+1}: ${(l.distance/1e3).toFixed(2)}km, ${(l.duration/60).toFixed(0)}min`)}}catch(s){console.error(`\u{1F7E0} Error calculating route to center ${o+1}:`,s)}}}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers.forEach(t=>{this.map.removeLayer(t)}),this.nearestMarkers=[]}showOfflineMarkerInfo(t,o){return c(this,null,function*(){yield(yield this.alertCtrl.create({header:`\u{1F4F1} ${t.name}`,message:`
        <div style="text-align: left;">
          <p><strong>Type:</strong> Earthquake Center</p>
          <p><strong>Distance:</strong> ${(o/1e3).toFixed(2)} km</p>
          <p><strong>Address:</strong> ${t.address||"N/A"}</p>
          <p><strong>Capacity:</strong> ${t.capacity||"N/A"}</p>
          <p><strong>Status:</strong> ${t.status||"N/A"}</p>
          <br>
          <p><em>\u{1F4F1} Offline Mode: Routing not available. Use external navigation apps for directions.</em></p>
        </div>
      `,buttons:[{text:"Open in Maps",handler:()=>{this.openInExternalMaps(t)}},{text:"Close",role:"cancel"}]})).present()})}openInExternalMaps(t){return c(this,null,function*(){let o=Number(t.latitude),n=Number(t.longitude),e=`https://www.google.com/maps/dir/?api=1&destination=${o},${n}&travelmode=walking`;try{window.open(e,"_system")}catch(a){console.error("Error opening external maps:",a),yield(yield this.toastCtrl.create({message:"Could not open external maps app",duration:3e3,color:"danger"})).present()}})}showTransportationOptions(t){return c(this,null,function*(){if(this.offlineStorage.isOfflineMode()||!navigator.onLine){let e=this.calculateDistance(this.userLocation?.lat||0,this.userLocation?.lng||0,Number(t.latitude),Number(t.longitude));yield this.showOfflineMarkerInfo(t,e);return}yield(yield this.alertCtrl.create({header:`Route to ${t.name}`,message:"Choose your transportation mode:",buttons:[{text:"\u{1F6B6}\u200D\u2642\uFE0F Walk",handler:()=>{this.routeToCenter(t,"walking")}},{text:"\u{1F6B4}\u200D\u2642\uFE0F Cycle",handler:()=>{this.routeToCenter(t,"cycling")}},{text:"\u{1F697} Drive",handler:()=>{this.routeToCenter(t,"driving")}},{text:"Cancel",role:"cancel"}]})).present()})}routeToCenter(t,o){return c(this,null,function*(){if(!this.userLocation)return;if(this.offlineStorage.isOfflineMode()||!navigator.onLine){console.log("\u{1F7E0} Offline mode: Cannot calculate routes"),yield(yield this.toastCtrl.create({message:"\u{1F4F1} Offline mode: Routing not available. Use external navigation apps.",duration:4e3,color:"warning"})).present(),yield this.openInExternalMaps(t);return}try{this.clearRoutes();let e=Number(t.latitude),a=Number(t.longitude);if(!isNaN(e)&&!isNaN(a)){let s=this.mapboxRouting.convertTravelModeToProfile(o),r=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,a,e,s,{geometries:"geojson",overview:"full",steps:!1});if(r&&r.routes&&r.routes.length>0){let l=r.routes[0],u="#ff9500";this.routeLayer=i.layerGroup().addTo(this.map);let g=i.polyline(l.geometry.coordinates.map(k=>[k[1],k[0]]),{color:u,weight:5,opacity:.8});g.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`\u{1F7E0} Route: ${(l.distance/1e3).toFixed(2)}km, ${(l.duration/60).toFixed(0)}min via ${o}`,duration:4e3,color:"warning"})).present(),this.map.fitBounds(g.getBounds(),{padding:[50,50]})}}}catch(e){console.error("\u{1F7E0} Error routing to center:",e),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}goBack(){this.router.navigate(["/tabs/home"])}ionViewWillLeave(){this.clearRoutes(),this.map&&this.map.remove()}static{this.\u0275fac=function(o){return new(o||m)}}static{this.\u0275cmp=P({type:m,selectors:[["app-earthquake-map"]],decls:18,vars:3,consts:[[3,"translucent"],["color","warning"],["slot","start"],[3,"click"],["name","chevron-back-outline"],[3,"fullscreen"],["id","earthquake-map",2,"height","100%","width","100%"],[1,"floating-info"],[1,"info-row"],["name","warning","color","warning"],[1,"info-text"]],template:function(o,n){o&1&&(p(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),A("click",function(){return n.goBack()}),C(4,"ion-icon",4),f()(),p(5,"ion-title"),w(6,"\u{1F7E0} Earthquake Evacuation Centers"),f()()(),p(7,"ion-content",5),C(8,"div",6),p(9,"div",7)(10,"ion-card")(11,"ion-card-content")(12,"div",8),C(13,"ion-icon",9),p(14,"span"),w(15),f()(),p(16,"div",10),w(17," Showing evacuation centers specifically for earthquake disasters "),f()()()()()),o&2&&(v("translucent",!0),M(7),v("fullscreen",!0),M(8),x("Earthquake Centers: ",n.evacuationCenters.length,""))},dependencies:[Q,R,q,I,_,$,S,U,D,H,L],styles:["#earthquake-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:250px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-warning);margin-bottom:4px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);line-height:1.3}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-warning);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-warning);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}"]})}}return m})();export{ut as EarthquakeMapPage};

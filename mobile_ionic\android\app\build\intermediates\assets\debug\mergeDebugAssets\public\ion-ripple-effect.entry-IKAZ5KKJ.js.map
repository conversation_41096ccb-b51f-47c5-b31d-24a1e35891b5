{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-ripple-effect.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as readTask, w as writeTask, h, f as getElement, e as Host } from './index-527b9e34.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport './index-cfd9c1f2.js';\nconst rippleEffectCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:strict;pointer-events:none}:host(.unbounded){contain:layout size style}.ripple-effect{border-radius:50%;position:absolute;background-color:currentColor;color:inherit;contain:strict;opacity:0;-webkit-animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;will-change:transform, opacity;pointer-events:none}.fade-out{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1));-webkit-animation:150ms fadeOutAnimation forwards;animation:150ms fadeOutAnimation forwards}@-webkit-keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@-webkit-keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@-webkit-keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}@keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}\";\nconst IonRippleEffectStyle0 = rippleEffectCss;\nconst RippleEffect = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.type = 'bounded';\n  }\n  /**\n   * Adds the ripple effect to the parent element.\n   *\n   * @param x The horizontal coordinate of where the ripple should start.\n   * @param y The vertical coordinate of where the ripple should start.\n   */\n  async addRipple(x, y) {\n    return new Promise(resolve => {\n      readTask(() => {\n        const rect = this.el.getBoundingClientRect();\n        const width = rect.width;\n        const height = rect.height;\n        const hypotenuse = Math.sqrt(width * width + height * height);\n        const maxDim = Math.max(height, width);\n        const maxRadius = this.unbounded ? maxDim : hypotenuse + PADDING;\n        const initialSize = Math.floor(maxDim * INITIAL_ORIGIN_SCALE);\n        const finalScale = maxRadius / initialSize;\n        let posX = x - rect.left;\n        let posY = y - rect.top;\n        if (this.unbounded) {\n          posX = width * 0.5;\n          posY = height * 0.5;\n        }\n        const styleX = posX - initialSize * 0.5;\n        const styleY = posY - initialSize * 0.5;\n        const moveX = width * 0.5 - posX;\n        const moveY = height * 0.5 - posY;\n        writeTask(() => {\n          const div = document.createElement('div');\n          div.classList.add('ripple-effect');\n          const style = div.style;\n          style.top = styleY + 'px';\n          style.left = styleX + 'px';\n          style.width = style.height = initialSize + 'px';\n          style.setProperty('--final-scale', `${finalScale}`);\n          style.setProperty('--translate-end', `${moveX}px, ${moveY}px`);\n          const container = this.el.shadowRoot || this.el;\n          container.appendChild(div);\n          setTimeout(() => {\n            resolve(() => {\n              removeRipple(div);\n            });\n          }, 225 + 100);\n        });\n      });\n    });\n  }\n  get unbounded() {\n    return this.type === 'unbounded';\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '40c7f73e7f5f67e29f83e1236a61c6e1c9943c42',\n      role: \"presentation\",\n      class: {\n        [mode]: true,\n        unbounded: this.unbounded\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst removeRipple = ripple => {\n  ripple.classList.add('fade-out');\n  setTimeout(() => {\n    ripple.remove();\n  }, 200);\n};\nconst PADDING = 10;\nconst INITIAL_ORIGIN_SCALE = 0.5;\nRippleEffect.style = IonRippleEffectStyle0;\nexport { RippleEffect as ion_ripple_effect };"], "mappings": ";;;;;;;;;;;;;;;;;AAMA,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,eAAe,MAAM;AAAA,EACzB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,UAAU,GAAG,GAAG;AAAA;AACpB,aAAO,IAAI,QAAQ,aAAW;AAC5B,iBAAS,MAAM;AACb,gBAAM,OAAO,KAAK,GAAG,sBAAsB;AAC3C,gBAAM,QAAQ,KAAK;AACnB,gBAAM,SAAS,KAAK;AACpB,gBAAM,aAAa,KAAK,KAAK,QAAQ,QAAQ,SAAS,MAAM;AAC5D,gBAAM,SAAS,KAAK,IAAI,QAAQ,KAAK;AACrC,gBAAM,YAAY,KAAK,YAAY,SAAS,aAAa;AACzD,gBAAM,cAAc,KAAK,MAAM,SAAS,oBAAoB;AAC5D,gBAAM,aAAa,YAAY;AAC/B,cAAI,OAAO,IAAI,KAAK;AACpB,cAAI,OAAO,IAAI,KAAK;AACpB,cAAI,KAAK,WAAW;AAClB,mBAAO,QAAQ;AACf,mBAAO,SAAS;AAAA,UAClB;AACA,gBAAM,SAAS,OAAO,cAAc;AACpC,gBAAM,SAAS,OAAO,cAAc;AACpC,gBAAM,QAAQ,QAAQ,MAAM;AAC5B,gBAAM,QAAQ,SAAS,MAAM;AAC7B,oBAAU,MAAM;AACd,kBAAM,MAAM,SAAS,cAAc,KAAK;AACxC,gBAAI,UAAU,IAAI,eAAe;AACjC,kBAAM,QAAQ,IAAI;AAClB,kBAAM,MAAM,SAAS;AACrB,kBAAM,OAAO,SAAS;AACtB,kBAAM,QAAQ,MAAM,SAAS,cAAc;AAC3C,kBAAM,YAAY,iBAAiB,GAAG,UAAU,EAAE;AAClD,kBAAM,YAAY,mBAAmB,GAAG,KAAK,OAAO,KAAK,IAAI;AAC7D,kBAAM,YAAY,KAAK,GAAG,cAAc,KAAK;AAC7C,sBAAU,YAAY,GAAG;AACzB,uBAAW,MAAM;AACf,sBAAQ,MAAM;AACZ,6BAAa,GAAG;AAAA,cAClB,CAAC;AAAA,YACH,GAAG,MAAM,GAAG;AAAA,UACd,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,QACL,CAAC,IAAI,GAAG;AAAA,QACR,WAAW,KAAK;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,IAAM,eAAe,YAAU;AAC7B,SAAO,UAAU,IAAI,UAAU;AAC/B,aAAW,MAAM;AACf,WAAO,OAAO;AAAA,EAChB,GAAG,GAAG;AACR;AACA,IAAM,UAAU;AAChB,IAAM,uBAAuB;AAC7B,aAAa,QAAQ;", "names": [], "x_google_ignoreList": [0]}
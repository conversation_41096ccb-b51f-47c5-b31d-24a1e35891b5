import {
  environment
} from "./chunk-I7MI46CM.js";
import {
  BooleanValueAccessorDirective,
  CommonModule,
  Component,
  FormsModule,
  HttpClient,
  Injectable,
  IonButton,
  IonButtons,
  IonCheckbox,
  IonContent,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonModal2 as IonModal,
  IonSelect,
  IonSelectOption,
  IonText,
  IonTitle,
  IonToolbar,
  IonicModule,
  MaxLengthValidator,
  NgControlStatus,
  NgControlStatusGroup,
  NgForm,
  NgIf,
  NgModel,
  NumericValueAccessorDirective,
  RequiredValidator,
  Router,
  SelectValueAccessorDirective,
  TextValueAccessorDirective,
  setClassMetadata,
  ɵNgNoValidate,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import "./chunk-UL2P3LPA.js";

// src/app/services/mobile-user.service.ts
var MobileUserService = class _MobileUserService {
  constructor(http) {
    this.http = http;
    this.apiUrl = `${environment.apiUrl}/mobile-users`;
  }
  saveUserData(data) {
    return this.http.post(this.apiUrl, data);
  }
  createUser(userData) {
    return this.http.post(this.apiUrl, userData);
  }
  static {
    this.\u0275fac = function MobileUserService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MobileUserService)(\u0275\u0275inject(HttpClient));
    };
  }
  static {
    this.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _MobileUserService, factory: _MobileUserService.\u0275fac, providedIn: "root" });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MobileUserService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

// src/app/pages/data/data.page.ts
function DataPage_ion_text_36_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-text", 25)(1, "p");
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.errorMessage);
  }
}
function DataPage_ng_template_47_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-title");
    \u0275\u0275text(3, "Terms and Conditions");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "ion-buttons", 26)(5, "ion-button", 27);
    \u0275\u0275listener("click", function DataPage_ng_template_47_Template_ion_button_click_5_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.closeTermsModal());
    });
    \u0275\u0275element(6, "ion-icon", 28);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(7, "ion-content", 1)(8, "div", 29)(9, "h2");
    \u0275\u0275text(10, "Alerto - Emergency Evacuation App");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "h3");
    \u0275\u0275text(12, "Terms and Conditions of Use");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "p")(14, "strong");
    \u0275\u0275text(15, "Last Updated:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "h4");
    \u0275\u0275text(18, "1. Acceptance of Terms");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "p");
    \u0275\u0275text(20, "By using the Alerto emergency evacuation application, you agree to be bound by these Terms and Conditions. If you do not agree to these terms, please do not use this application.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "h4");
    \u0275\u0275text(22, "2. Purpose of the Application");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(23, "p");
    \u0275\u0275text(24, "Alerto is designed to provide emergency evacuation information and guidance during natural disasters including earthquakes, typhoons, and flash floods in the Philippines. The app provides location-based evacuation center recommendations and emergency notifications.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "h4");
    \u0275\u0275text(26, "3. User Responsibilities");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(27, "p");
    \u0275\u0275text(28, "\u2022 Provide accurate personal information including contact details");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(29, "p");
    \u0275\u0275text(30, "\u2022 Keep your mobile number updated for emergency notifications");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(31, "p");
    \u0275\u0275text(32, "\u2022 Use the app responsibly during emergency situations");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(33, "p");
    \u0275\u0275text(34, "\u2022 Follow official emergency protocols and local authority instructions");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(35, "h4");
    \u0275\u0275text(36, "4. Data Collection and Privacy");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(37, "p");
    \u0275\u0275text(38, "We collect and store your personal information including name, mobile number, age, gender, and address to provide personalized emergency services. Your location data may be accessed to provide relevant evacuation center recommendations.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(39, "h4");
    \u0275\u0275text(40, "5. Emergency Notifications");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(41, "p");
    \u0275\u0275text(42, "By using this app, you consent to receive emergency push notifications on your device. These notifications are critical for your safety during disaster events.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(43, "h4");
    \u0275\u0275text(44, "6. Limitation of Liability");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(45, "p");
    \u0275\u0275text(46, "While we strive to provide accurate and timely information, Alerto and its developers are not liable for any damages or losses resulting from the use of this application. Always follow official emergency protocols and local authority guidance.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(47, "h4");
    \u0275\u0275text(48, "7. Service Availability");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(49, "p");
    \u0275\u0275text(50, "We cannot guarantee uninterrupted service availability, especially during extreme weather conditions or network outages that may occur during disasters.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(51, "h4");
    \u0275\u0275text(52, "8. Updates and Modifications");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(53, "p");
    \u0275\u0275text(54, "These terms may be updated periodically. Continued use of the application constitutes acceptance of any modifications.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(55, "h4");
    \u0275\u0275text(56, "9. Contact Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(57, "p");
    \u0275\u0275text(58, "For questions about these terms or the application, please contact our support team through the app's feedback feature.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(59, "p")(60, "strong");
    \u0275\u0275text(61, 'By clicking "I accept" below, you acknowledge that you have read, understood, and agree to be bound by these Terms and Conditions.');
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(62, "div", 30)(63, "ion-button", 31);
    \u0275\u0275listener("click", function DataPage_ng_template_47_Template_ion_button_click_63_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.closeTermsModal());
    });
    \u0275\u0275text(64, " Close ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(65, "ion-button", 32);
    \u0275\u0275listener("click", function DataPage_ng_template_47_Template_ion_button_click_65_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.acceptTerms());
    });
    \u0275\u0275text(66, " I Accept ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(16);
    \u0275\u0275textInterpolate1(" ", ctx_r1.getCurrentDate(), "");
  }
}
var DataPage = class _DataPage {
  constructor(router, mobileUserService) {
    this.router = router;
    this.mobileUserService = mobileUserService;
    this.userData = {
      full_name: "",
      mobile_number: "",
      age: "",
      gender: "",
      address: ""
    };
    this.acceptedTerms = false;
    this.showError = false;
    this.errorMessage = "";
    this.isTermsModalOpen = false;
  }
  /**
   * Open Terms and Conditions modal
   */
  openTermsModal() {
    this.isTermsModalOpen = true;
  }
  /**
   * Close Terms and Conditions modal
   */
  closeTermsModal() {
    this.isTermsModalOpen = false;
  }
  /**
   * Accept terms from modal and close it
   */
  acceptTerms() {
    this.acceptedTerms = true;
    this.closeTermsModal();
  }
  /**
   * Get current date for terms display
   */
  getCurrentDate() {
    return (/* @__PURE__ */ new Date()).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  }
  /**
   * Handle mobile number input and format validation
   * Philippines format: +63 + 10 digits (total 11 digits)
   */
  onMobileNumberInput(event) {
    let value = event.target.value;
    value = value.replace(/\D/g, "");
    if (value.length > 10) {
      value = value.substring(0, 10);
    }
    if (value.length > 0 && !value.startsWith("9")) {
      if (value.length === 1 && value !== "9") {
        value = "9" + value;
      }
    }
    this.userData.mobile_number = value;
    if (this.showError && this.errorMessage.includes("Mobile number")) {
      this.showError = false;
      this.errorMessage = "";
    }
  }
  /**
   * Get the complete mobile number with country code
   * Returns format: +63-************
   */
  getCompletePhoneNumber() {
    if (!this.userData.mobile_number)
      return "";
    return `+63${this.userData.mobile_number}`;
  }
  /**
   * Validate Philippine mobile number format
   * Must be exactly 10 digits starting with 9 (after +63)
   * Total: +63 + 10 digits = 11 digits
   */
  isValidPhilippineMobile() {
    const mobileNumber = this.userData.mobile_number;
    return /^9[0-9]{9}$/.test(mobileNumber) && mobileNumber.length === 10;
  }
  onSave() {
    console.log(this.userData);
    const requiredFields = ["full_name", "mobile_number", "age", "gender", "address"];
    for (const field of requiredFields) {
      const value = this.userData[field];
      if (value === void 0 || value === null || value.toString().trim() === "") {
        this.showError = true;
        this.errorMessage = "All fields are required.";
        return;
      }
    }
    if (!this.isValidPhilippineMobile()) {
      this.showError = true;
      this.errorMessage = "Mobile number must be exactly 10 digits starting with 9 (e.g., +63-************).";
      return;
    }
    if (isNaN(Number(this.userData.age)) || Number(this.userData.age) <= 0) {
      this.showError = true;
      this.errorMessage = "Age must be a positive number.";
      return;
    }
    if (!this.acceptedTerms) {
      this.showError = true;
      this.errorMessage = "You must accept the Terms and Conditions.";
      return;
    }
    this.mobileUserService.createUser({
      full_name: this.userData.full_name,
      mobile_number: this.userData.mobile_number,
      age: Number(this.userData.age),
      gender: this.userData.gender,
      address: this.userData.address
    }).subscribe({
      next: () => {
        const userData = {
          full_name: this.userData.full_name,
          mobile_number: this.userData.mobile_number,
          age: this.userData.age,
          gender: this.userData.gender,
          address: this.userData.address
        };
        localStorage.setItem("userData", JSON.stringify(userData));
        localStorage.setItem("onboardingComplete", "true");
        this.router.navigate(["/tabs/home"]);
      },
      error: (err) => {
        this.showError = true;
        this.errorMessage = err.error?.message || "Failed to save user.";
      }
    });
  }
  static {
    this.\u0275fac = function DataPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _DataPage)(\u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(MobileUserService));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DataPage, selectors: [["app-data"]], decls: 48, vars: 8, consts: [["termsModal", ""], [1, "ion-padding"], [1, "profile-container"], [1, "header-logo"], [1, "home-title"], ["src", "assets/ALERTO.png", "alt", "App Logo", 1, "home-logo"], [3, "ngSubmit"], ["position", "floating", 2, "font-size", "15px"], ["type", "text", "name", "full_name", "required", "", 2, "font-size", "20px", "padding-bottom", "10px", 3, "ngModelChange", "ngModel"], [2, "display", "flex", "align-items", "center", "width", "100%"], [2, "font-size", "20px", "font-weight", "bold", "margin-right", "8px", "color", "#333", "min-width", "40px"], ["type", "tel", "name", "mobile_number", "placeholder", "************", "maxlength", "10", "required", "", 2, "font-size", "20px", "padding-bottom", "10px", "flex", "1", 3, "ngModelChange", "ionInput", "ngModel"], ["type", "number", "name", "age", "required", "", 2, "font-size", "20px", "padding-bottom", "10px", 3, "ngModelChange", "ngModel"], ["position", "floating"], ["name", "gender", "required", "", 3, "ngModelChange", "ngModel"], ["value", "Male"], ["value", "Female"], ["value", "Other"], ["type", "text", "name", "address", "required", "", 2, "font-size", "20px", "padding-bottom", "10px", 3, "ngModelChange", "ngModel"], ["color", "danger", "class", "error-message", 4, "ngIf"], [1, "terms-checkbox"], ["slot", "start", "name", "acceptedTerms", "required", "", 3, "ngModelChange", "ngModel"], [2, "color", "#3880ff", "text-decoration", "underline", "cursor", "pointer", 3, "click"], ["expand", "block", "type", "submit", 1, "ion-margin-top"], [3, "willDismiss", "isOpen"], ["color", "danger", 1, "error-message"], ["slot", "end"], [3, "click"], ["name", "close"], [1, "terms-content"], [1, "modal-buttons"], ["expand", "block", "fill", "outline", 3, "click"], ["expand", "block", 3, "click"]], template: function DataPage_Template(rf, ctx) {
      if (rf & 1) {
        const _r1 = \u0275\u0275getCurrentView();
        \u0275\u0275elementStart(0, "ion-content", 1)(1, "div", 2)(2, "div", 3)(3, "div", 4);
        \u0275\u0275element(4, "img", 5);
        \u0275\u0275text(5, " Hi, Welcome to Safe Area!");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(6, "form", 6);
        \u0275\u0275listener("ngSubmit", function DataPage_Template_form_ngSubmit_6_listener() {
          \u0275\u0275restoreView(_r1);
          return \u0275\u0275resetView(ctx.onSave());
        });
        \u0275\u0275elementStart(7, "ion-item")(8, "ion-label", 7);
        \u0275\u0275text(9, "Full Name:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(10, "ion-input", 8);
        \u0275\u0275twoWayListener("ngModelChange", function DataPage_Template_ion_input_ngModelChange_10_listener($event) {
          \u0275\u0275restoreView(_r1);
          \u0275\u0275twoWayBindingSet(ctx.userData.full_name, $event) || (ctx.userData.full_name = $event);
          return \u0275\u0275resetView($event);
        });
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(11, "ion-item")(12, "ion-label", 7);
        \u0275\u0275text(13, "Mobile Number:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(14, "div", 9)(15, "span", 10);
        \u0275\u0275text(16, "+63");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(17, "ion-input", 11);
        \u0275\u0275twoWayListener("ngModelChange", function DataPage_Template_ion_input_ngModelChange_17_listener($event) {
          \u0275\u0275restoreView(_r1);
          \u0275\u0275twoWayBindingSet(ctx.userData.mobile_number, $event) || (ctx.userData.mobile_number = $event);
          return \u0275\u0275resetView($event);
        });
        \u0275\u0275listener("ionInput", function DataPage_Template_ion_input_ionInput_17_listener($event) {
          \u0275\u0275restoreView(_r1);
          return \u0275\u0275resetView(ctx.onMobileNumberInput($event));
        });
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(18, "ion-item")(19, "ion-label", 7);
        \u0275\u0275text(20, "Age:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(21, "ion-input", 12);
        \u0275\u0275twoWayListener("ngModelChange", function DataPage_Template_ion_input_ngModelChange_21_listener($event) {
          \u0275\u0275restoreView(_r1);
          \u0275\u0275twoWayBindingSet(ctx.userData.age, $event) || (ctx.userData.age = $event);
          return \u0275\u0275resetView($event);
        });
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(22, "ion-item")(23, "ion-label", 13);
        \u0275\u0275text(24, "Gender ");
        \u0275\u0275elementStart(25, "ion-select", 14);
        \u0275\u0275twoWayListener("ngModelChange", function DataPage_Template_ion_select_ngModelChange_25_listener($event) {
          \u0275\u0275restoreView(_r1);
          \u0275\u0275twoWayBindingSet(ctx.userData.gender, $event) || (ctx.userData.gender = $event);
          return \u0275\u0275resetView($event);
        });
        \u0275\u0275elementStart(26, "ion-select-option", 15);
        \u0275\u0275text(27, "Male");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(28, "ion-select-option", 16);
        \u0275\u0275text(29, "Female");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(30, "ion-select-option", 17);
        \u0275\u0275text(31, "Other");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(32, "ion-item")(33, "ion-label", 7);
        \u0275\u0275text(34, "Address:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(35, "ion-input", 18);
        \u0275\u0275twoWayListener("ngModelChange", function DataPage_Template_ion_input_ngModelChange_35_listener($event) {
          \u0275\u0275restoreView(_r1);
          \u0275\u0275twoWayBindingSet(ctx.userData.address, $event) || (ctx.userData.address = $event);
          return \u0275\u0275resetView($event);
        });
        \u0275\u0275elementEnd()();
        \u0275\u0275template(36, DataPage_ion_text_36_Template, 3, 1, "ion-text", 19);
        \u0275\u0275elementStart(37, "div", 20)(38, "ion-checkbox", 21);
        \u0275\u0275twoWayListener("ngModelChange", function DataPage_Template_ion_checkbox_ngModelChange_38_listener($event) {
          \u0275\u0275restoreView(_r1);
          \u0275\u0275twoWayBindingSet(ctx.acceptedTerms, $event) || (ctx.acceptedTerms = $event);
          return \u0275\u0275resetView($event);
        });
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(39, "label");
        \u0275\u0275text(40, "I accept the ");
        \u0275\u0275elementStart(41, "a", 22);
        \u0275\u0275listener("click", function DataPage_Template_a_click_41_listener() {
          \u0275\u0275restoreView(_r1);
          return \u0275\u0275resetView(ctx.openTermsModal());
        });
        \u0275\u0275text(42, "Terms and Conditions");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(43, "ion-button", 23);
        \u0275\u0275text(44, " Save ");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(45, "ion-modal", 24, 0);
        \u0275\u0275listener("willDismiss", function DataPage_Template_ion_modal_willDismiss_45_listener() {
          \u0275\u0275restoreView(_r1);
          return \u0275\u0275resetView(ctx.closeTermsModal());
        });
        \u0275\u0275template(47, DataPage_ng_template_47_Template, 67, 1, "ng-template");
        \u0275\u0275elementEnd();
      }
      if (rf & 2) {
        \u0275\u0275advance(10);
        \u0275\u0275twoWayProperty("ngModel", ctx.userData.full_name);
        \u0275\u0275advance(7);
        \u0275\u0275twoWayProperty("ngModel", ctx.userData.mobile_number);
        \u0275\u0275advance(4);
        \u0275\u0275twoWayProperty("ngModel", ctx.userData.age);
        \u0275\u0275advance(4);
        \u0275\u0275twoWayProperty("ngModel", ctx.userData.gender);
        \u0275\u0275advance(10);
        \u0275\u0275twoWayProperty("ngModel", ctx.userData.address);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.showError);
        \u0275\u0275advance(2);
        \u0275\u0275twoWayProperty("ngModel", ctx.acceptedTerms);
        \u0275\u0275advance(7);
        \u0275\u0275property("isOpen", ctx.isTermsModalOpen);
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonCheckbox, IonContent, IonHeader, IonIcon, IonInput, IonItem, IonLabel, IonSelect, IonSelectOption, IonText, IonTitle, IonToolbar, IonModal, BooleanValueAccessorDirective, NumericValueAccessorDirective, SelectValueAccessorDirective, TextValueAccessorDirective, CommonModule, NgIf, FormsModule, \u0275NgNoValidate, NgControlStatus, NgControlStatusGroup, RequiredValidator, MaxLengthValidator, NgModel, NgForm], styles: ["\n\n.profile-container[_ngcontent-%COMP%] {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  max-width: 420px;\n  margin: 0 auto;\n  padding: 32px 20px;\n}\nform[_ngcontent-%COMP%] {\n  width: 100%;\n}\nion-item[_ngcontent-%COMP%] {\n  --background: #f9f9f9;\n  --border-radius: 25px;\n  --padding-start: 10px;\n  --padding-end: 10px;\n  --inner-padding-top: 5px;\n  --inner-padding-bottom: 5px;\n  margin-bottom: 10px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  height: 65px;\n}\nion-label[_ngcontent-%COMP%] {\n  font-size: 5px;\n  color: #333;\n}\nion-input[_ngcontent-%COMP%], \nion-select[_ngcontent-%COMP%] {\n  font-size: 15px;\n}\n.terms-checkbox[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  margin-top: 12px;\n  font-size: 14px;\n  color: #444;\n  flex-wrap: wrap;\n  line-height: 1.4;\n}\n.terms-checkbox[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%] {\n  margin-right: 8px;\n}\n.terms-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  color: #03b2dd;\n  text-align: center;\n  margin-bottom: 10px;\n  font-size: 1.5rem;\n}\n.terms-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: #333;\n  text-align: center;\n  margin-bottom: 20px;\n  font-size: 1.2rem;\n}\n.terms-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  color: #03b2dd;\n  margin-top: 20px;\n  margin-bottom: 10px;\n  font-size: 1.1rem;\n  font-weight: 600;\n}\n.terms-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #444;\n  line-height: 1.6;\n  margin-bottom: 12px;\n  text-align: justify;\n}\n.terms-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\n  font-weight: 600;\n  color: #333;\n  text-align: center;\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f0f9ff;\n  border-radius: 8px;\n  border-left: 4px solid #03b2dd;\n}\n.modal-buttons[_ngcontent-%COMP%] {\n  margin-top: 30px;\n  padding: 20px 0;\n}\n.modal-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  margin-bottom: 10px;\n  --border-radius: 25px;\n  height: 45px;\n  font-weight: 600;\n}\n.modal-buttons[_ngcontent-%COMP%]   ion-button[fill=outline][_ngcontent-%COMP%] {\n  --color: #666;\n  --border-color: #666;\n}\n.terms-checkbox[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  color: #1565c0;\n  text-decoration: underline;\n  font-weight: 500;\n  margin-left: 4px;\n}\nion-button[_ngcontent-%COMP%] {\n  margin-top: 24px;\n  --background: #1565c0;\n  --border-radius: 10px;\n  --box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);\n}\n.error-message[_ngcontent-%COMP%] {\n  text-align: center;\n  margin: 10px 0;\n  color: #e53935;\n}\n.error-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 14px;\n}\n.header-logo[_ngcontent-%COMP%] {\n  text-align: center;\n}\n.header-logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 80px;\n  height: auto;\n}\n.header-logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 18px;\n  color: #222;\n  font-weight: 600;\n  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);\n}\n.home-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 15px;\n  font-weight: 700;\n  letter-spacing: 1px;\n  text-shadow: 1px 2px 4px #ccc;\n  padding-top: 59px;\n}\n/*# sourceMappingURL=data.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DataPage, [{
    type: Component,
    args: [{ selector: "app-data", standalone: true, imports: [IonicModule, CommonModule, FormsModule], template: `\r
<ion-content class="ion-padding">\r
  <div class="profile-container">\r
\r
    <div class="header-logo">\r
      <div class="home-title"><img src="assets/ALERTO.png" alt="App Logo" class="home-logo"/> Hi, Welcome to Safe Area!</div>\r
    </div>\r
    <form (ngSubmit)="onSave()">\r
      <ion-item>\r
        <ion-label position="floating" style="font-size: 15px;">Full Name:</ion-label>\r
        <ion-input type="text" [(ngModel)]="userData.full_name" name="full_name" style="font-size: 20px; padding-bottom: 10px;" required></ion-input>\r
      </ion-item>\r
\r
      <ion-item>\r
        <ion-label position="floating" style="font-size: 15px;">Mobile Number:</ion-label>\r
        <div style="display: flex; align-items: center; width: 100%;">\r
          <span style="font-size: 20px; font-weight: bold; margin-right: 8px; color: #333; min-width: 40px;">+63</span>\r
          <ion-input\r
            type="tel"\r
            [(ngModel)]="userData.mobile_number"\r
            name="mobile_number"\r
            style="font-size: 20px; padding-bottom: 10px; flex: 1;"\r
            placeholder="************"\r
            maxlength="10"\r
            (ionInput)="onMobileNumberInput($event)"\r
            required>\r
          </ion-input>\r
        </div>\r
\r
      </ion-item>\r
\r
      <ion-item>\r
        <ion-label position="floating"  style="font-size: 15px;">Age:</ion-label>\r
        <ion-input type="number" [(ngModel)]="userData.age" name="age" style="font-size: 20px; padding-bottom: 10px; "required></ion-input>\r
      </ion-item>\r
\r
      <ion-item>\r
        <ion-label position="floating" >Gender <ion-select [(ngModel)]="userData.gender" name="gender" required>\r
          <ion-select-option value="Male">Male</ion-select-option>\r
          <ion-select-option value="Female">Female</ion-select-option>\r
          <ion-select-option value="Other">Other</ion-select-option>\r
        </ion-select></ion-label>\r
\r
      </ion-item>\r
\r
      <ion-item>\r
        <ion-label position="floating" style="font-size: 15px;">Address:</ion-label>\r
        <ion-input type="text" [(ngModel)]="userData.address" name="address" style="font-size: 20px; padding-bottom: 10px;" required></ion-input>\r
      </ion-item>\r
\r
      <ion-text color="danger" *ngIf="showError" class="error-message">\r
        <p>{{ errorMessage }}</p>\r
      </ion-text>\r
\r
      <div class="terms-checkbox">\r
        <ion-checkbox slot="start" [(ngModel)]="acceptedTerms" name="acceptedTerms" required></ion-checkbox>\r
        <label>I accept the <a (click)="openTermsModal()" style="color: #3880ff; text-decoration: underline; cursor: pointer;">Terms and Conditions</a></label>\r
      </div>\r
\r
      <ion-button expand="block" type="submit" class="ion-margin-top">\r
        Save\r
      </ion-button>\r
    </form>\r
  </div>\r
</ion-content>\r
\r
<!-- Terms and Conditions Modal -->\r
<ion-modal #termsModal [isOpen]="isTermsModalOpen" (willDismiss)="closeTermsModal()">\r
  <ng-template>\r
    <ion-header>\r
      <ion-toolbar>\r
        <ion-title>Terms and Conditions</ion-title>\r
        <ion-buttons slot="end">\r
          <ion-button (click)="closeTermsModal()">\r
            <ion-icon name="close"></ion-icon>\r
          </ion-button>\r
        </ion-buttons>\r
      </ion-toolbar>\r
    </ion-header>\r
\r
    <ion-content class="ion-padding">\r
      <div class="terms-content">\r
        <h2>Alerto - Emergency Evacuation App</h2>\r
        <h3>Terms and Conditions of Use</h3>\r
\r
        <p><strong>Last Updated:</strong> {{ getCurrentDate() }}</p>\r
\r
        <h4>1. Acceptance of Terms</h4>\r
        <p>By using the Alerto emergency evacuation application, you agree to be bound by these Terms and Conditions. If you do not agree to these terms, please do not use this application.</p>\r
\r
        <h4>2. Purpose of the Application</h4>\r
        <p>Alerto is designed to provide emergency evacuation information and guidance during natural disasters including earthquakes, typhoons, and flash floods in the Philippines. The app provides location-based evacuation center recommendations and emergency notifications.</p>\r
\r
        <h4>3. User Responsibilities</h4>\r
        <p>\u2022 Provide accurate personal information including contact details</p>\r
        <p>\u2022 Keep your mobile number updated for emergency notifications</p>\r
        <p>\u2022 Use the app responsibly during emergency situations</p>\r
        <p>\u2022 Follow official emergency protocols and local authority instructions</p>\r
\r
        <h4>4. Data Collection and Privacy</h4>\r
        <p>We collect and store your personal information including name, mobile number, age, gender, and address to provide personalized emergency services. Your location data may be accessed to provide relevant evacuation center recommendations.</p>\r
\r
        <h4>5. Emergency Notifications</h4>\r
        <p>By using this app, you consent to receive emergency push notifications on your device. These notifications are critical for your safety during disaster events.</p>\r
\r
        <h4>6. Limitation of Liability</h4>\r
        <p>While we strive to provide accurate and timely information, Alerto and its developers are not liable for any damages or losses resulting from the use of this application. Always follow official emergency protocols and local authority guidance.</p>\r
\r
        <h4>7. Service Availability</h4>\r
        <p>We cannot guarantee uninterrupted service availability, especially during extreme weather conditions or network outages that may occur during disasters.</p>\r
\r
        <h4>8. Updates and Modifications</h4>\r
        <p>These terms may be updated periodically. Continued use of the application constitutes acceptance of any modifications.</p>\r
\r
        <h4>9. Contact Information</h4>\r
        <p>For questions about these terms or the application, please contact our support team through the app's feedback feature.</p>\r
\r
        <p><strong>By clicking "I accept" below, you acknowledge that you have read, understood, and agree to be bound by these Terms and Conditions.</strong></p>\r
      </div>\r
\r
      <div class="modal-buttons">\r
        <ion-button expand="block" fill="outline" (click)="closeTermsModal()">\r
          Close\r
        </ion-button>\r
        <ion-button expand="block" (click)="acceptTerms()">\r
          I Accept\r
        </ion-button>\r
      </div>\r
    </ion-content>\r
  </ng-template>\r
</ion-modal>\r
`, styles: ["/* src/app/pages/data/data.page.scss */\n.profile-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  max-width: 420px;\n  margin: 0 auto;\n  padding: 32px 20px;\n}\nform {\n  width: 100%;\n}\nion-item {\n  --background: #f9f9f9;\n  --border-radius: 25px;\n  --padding-start: 10px;\n  --padding-end: 10px;\n  --inner-padding-top: 5px;\n  --inner-padding-bottom: 5px;\n  margin-bottom: 10px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  height: 65px;\n}\nion-label {\n  font-size: 5px;\n  color: #333;\n}\nion-input,\nion-select {\n  font-size: 15px;\n}\n.terms-checkbox {\n  display: flex;\n  align-items: center;\n  margin-top: 12px;\n  font-size: 14px;\n  color: #444;\n  flex-wrap: wrap;\n  line-height: 1.4;\n}\n.terms-checkbox ion-checkbox {\n  margin-right: 8px;\n}\n.terms-content h2 {\n  color: #03b2dd;\n  text-align: center;\n  margin-bottom: 10px;\n  font-size: 1.5rem;\n}\n.terms-content h3 {\n  color: #333;\n  text-align: center;\n  margin-bottom: 20px;\n  font-size: 1.2rem;\n}\n.terms-content h4 {\n  color: #03b2dd;\n  margin-top: 20px;\n  margin-bottom: 10px;\n  font-size: 1.1rem;\n  font-weight: 600;\n}\n.terms-content p {\n  color: #444;\n  line-height: 1.6;\n  margin-bottom: 12px;\n  text-align: justify;\n}\n.terms-content p:last-child {\n  font-weight: 600;\n  color: #333;\n  text-align: center;\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f0f9ff;\n  border-radius: 8px;\n  border-left: 4px solid #03b2dd;\n}\n.modal-buttons {\n  margin-top: 30px;\n  padding: 20px 0;\n}\n.modal-buttons ion-button {\n  margin-bottom: 10px;\n  --border-radius: 25px;\n  height: 45px;\n  font-weight: 600;\n}\n.modal-buttons ion-button[fill=outline] {\n  --color: #666;\n  --border-color: #666;\n}\n.terms-checkbox a {\n  color: #1565c0;\n  text-decoration: underline;\n  font-weight: 500;\n  margin-left: 4px;\n}\nion-button {\n  margin-top: 24px;\n  --background: #1565c0;\n  --border-radius: 10px;\n  --box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);\n}\n.error-message {\n  text-align: center;\n  margin: 10px 0;\n  color: #e53935;\n}\n.error-message p {\n  margin: 0;\n  font-size: 14px;\n}\n.header-logo {\n  text-align: center;\n}\n.header-logo img {\n  width: 80px;\n  height: auto;\n}\n.header-logo h2 {\n  font-size: 18px;\n  color: #222;\n  font-weight: 600;\n  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);\n}\n.home-title {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 15px;\n  font-weight: 700;\n  letter-spacing: 1px;\n  text-shadow: 1px 2px 4px #ccc;\n  padding-top: 59px;\n}\n/*# sourceMappingURL=data.page.css.map */\n"] }]
  }], () => [{ type: Router }, { type: MobileUserService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DataPage, { className: "DataPage", filePath: "src/app/pages/data/data.page.ts", lineNumber: 15 });
})();
export {
  DataPage
};
//# sourceMappingURL=data.page-XIQOZXSV.js.map

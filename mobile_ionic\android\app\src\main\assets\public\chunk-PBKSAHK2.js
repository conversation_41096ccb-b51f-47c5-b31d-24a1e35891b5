import{b as Lc}from"./chunk-SV2ZKNWA.js";import{c as Nc,e as Oc,f as kc,g as Fc,h as Pc}from"./chunk-AUB5HKS7.js";import{b as _c,c as Mc,d as Sc,e as Tc,f as xc}from"./chunk-KKCAABTQ.js";import{f as Np}from"./chunk-SPZFNIGG.js";import{a as hn,b as Op,c as kp,d as Fp,f as Ec}from"./chunk-F4H6ZFEG.js";import{a as Ac}from"./chunk-NMYJD6OP.js";import{a as Pp,d as Lp}from"./chunk-KY4M3ZA2.js";import{b as zt}from"./chunk-SV7S5NYR.js";import{a as qn,b as wc,c as Ci}from"./chunk-WTCPO44B.js";import{c as Rc}from"./chunk-4EI7TLDT.js";import{m as jp}from"./chunk-FED6QSGK.js";import{a as I,b as P,d as bc,g as ge}from"./chunk-2R6CW7ES.js";function Gt(e){let t=e(r=>{Error.call(r),r.stack=new Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var et=Gt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function k(e){return typeof e=="function"}var bi=Gt(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:
${t.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=t});function pn(e,n){if(e){let t=e.indexOf(n);0<=t&&e.splice(t,1)}}var le=class e{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;let{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(let i of t)i.remove(this);else t.remove(this);let{initialTeardown:r}=this;if(k(r))try{r()}catch(i){n=i instanceof bi?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Vp(i)}catch(s){n=n??[],s instanceof bi?n=[...n,...s.errors]:n.push(s)}}if(n)throw new bi(n)}}add(n){var t;if(n&&n!==this)if(this.closed)Vp(n);else{if(n instanceof e){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=(t=this._finalizers)!==null&&t!==void 0?t:[]).push(n)}}_hasParent(n){let{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){let{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){let{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&pn(t,n)}remove(n){let{_finalizers:t}=this;t&&pn(t,n),n instanceof e&&n._removeParent(this)}};le.EMPTY=(()=>{let e=new le;return e.closed=!0,e})();var jc=le.EMPTY;function wi(e){return e instanceof le||e&&"closed"in e&&k(e.remove)&&k(e.add)&&k(e.unsubscribe)}function Vp(e){k(e)?e():e.unsubscribe()}var tt={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Zn={setTimeout(e,n,...t){let{delegate:r}=Zn;return r?.setTimeout?r.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){let{delegate:n}=Zn;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Ei(e){Zn.setTimeout(()=>{let{onUnhandledError:n}=tt;if(n)n(e);else throw e})}function Qr(){}var Bp=Vc("C",void 0,void 0);function Up(e){return Vc("E",void 0,e)}function Hp(e){return Vc("N",e,void 0)}function Vc(e,n,t){return{kind:e,value:n,error:t}}var gn=null;function Yn(e){if(tt.useDeprecatedSynchronousErrorHandling){let n=!gn;if(n&&(gn={errorThrown:!1,error:null}),e(),n){let{errorThrown:t,error:r}=gn;if(gn=null,t)throw r}}else e()}function $p(e){tt.useDeprecatedSynchronousErrorHandling&&gn&&(gn.errorThrown=!0,gn.error=e)}var mn=class extends le{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,wi(n)&&n.add(this)):this.destination=UC}static create(n,t,r){return new Wt(n,t,r)}next(n){this.isStopped?Uc(Hp(n),this):this._next(n)}error(n){this.isStopped?Uc(Up(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?Uc(Bp,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},VC=Function.prototype.bind;function Bc(e,n){return VC.call(e,n)}var Hc=class{constructor(n){this.partialObserver=n}next(n){let{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(r){_i(r)}}error(n){let{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(r){_i(r)}else _i(n)}complete(){let{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){_i(t)}}},Wt=class extends mn{constructor(n,t,r){super();let o;if(k(n)||!n)o={next:n??void 0,error:t??void 0,complete:r??void 0};else{let i;this&&tt.useDeprecatedNextContext?(i=Object.create(n),i.unsubscribe=()=>this.unsubscribe(),o={next:n.next&&Bc(n.next,i),error:n.error&&Bc(n.error,i),complete:n.complete&&Bc(n.complete,i)}):o=n}this.destination=new Hc(o)}};function _i(e){tt.useDeprecatedSynchronousErrorHandling?$p(e):Ei(e)}function BC(e){throw e}function Uc(e,n){let{onStoppedNotification:t}=tt;t&&Zn.setTimeout(()=>t(e,n))}var UC={closed:!0,next:Qr,error:BC,complete:Qr};function HC(e,n){let t=typeof n=="object";return new Promise((r,o)=>{let i=new Wt({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{t?r(n.defaultValue):o(new et)}});e.subscribe(i)})}var Mi=class extends le{constructor(n,t){super()}schedule(n,t=0){return this}};var Kr={setInterval(e,n,...t){let{delegate:r}=Kr;return r?.setInterval?r.setInterval(e,n,...t):setInterval(e,n,...t)},clearInterval(e){let{delegate:n}=Kr;return(n?.clearInterval||clearInterval)(e)},delegate:void 0};var Si=class extends Mi{constructor(n,t){super(n,t),this.scheduler=n,this.work=t,this.pending=!1}schedule(n,t=0){var r;if(this.closed)return this;this.state=n;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,t)),this.pending=!0,this.delay=t,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,t),this}requestAsyncId(n,t,r=0){return Kr.setInterval(n.flush.bind(n,this),r)}recycleAsyncId(n,t,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return t;t!=null&&Kr.clearInterval(t)}execute(n,t){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(n,t);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(n,t){let r=!1,o;try{this.work(n)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:n,scheduler:t}=this,{actions:r}=t;this.work=this.state=this.scheduler=null,this.pending=!1,pn(r,this),n!=null&&(this.id=this.recycleAsyncId(t,n,null)),this.delay=null,super.unsubscribe()}}};var $c={now(){return($c.delegate||Date).now()},delegate:void 0};var Qn=class e{constructor(n,t=e.now){this.schedulerActionCtor=n,this.now=t}schedule(n,t=0,r){return new this.schedulerActionCtor(this,n).schedule(r,t)}};Qn.now=$c.now;var Ti=class extends Qn{constructor(n,t=Qn.now){super(n,t),this.actions=[],this._active=!1}flush(n){let{actions:t}=this;if(this._active){t.push(n);return}let r;this._active=!0;do if(r=n.execute(n.state,n.delay))break;while(n=t.shift());if(this._active=!1,r){for(;n=t.shift();)n.unsubscribe();throw r}}};var Xr=new Ti(Si),zp=Xr;var Kn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Ie(e){return e}function zc(...e){return Gc(e)}function Gc(e){return e.length===0?Ie:e.length===1?e[0]:function(t){return e.reduce((r,o)=>o(r),t)}}var W=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){let r=new e;return r.source=this,r.operator=t,r}subscribe(t,r,o){let i=zC(t)?t:new Wt(t,r,o);return Yn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(t){try{return this._subscribe(t)}catch(r){t.error(r)}}forEach(t,r){return r=Gp(r),new r((o,i)=>{let s=new Wt({next:a=>{try{t(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)}[Kn](){return this}pipe(...t){return Gc(t)(this)}toPromise(t){return t=Gp(t),new t((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=n=>new e(n),e})();function Gp(e){var n;return(n=e??tt.Promise)!==null&&n!==void 0?n:Promise}function $C(e){return e&&k(e.next)&&k(e.error)&&k(e.complete)}function zC(e){return e&&e instanceof mn||$C(e)&&wi(e)}function xi(e){return e&&k(e.schedule)}function Ai(e){return e instanceof Date&&!isNaN(e)}function Ri(e=0,n,t=zp){let r=-1;return n!=null&&(xi(n)?t=n:r=n),new W(o=>{let i=Ai(e)?+e-t.now():e;i<0&&(i=0);let s=0;return t.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function GC(e=0,n=Xr){return e<0&&(e=0),Ri(e,e,n)}function Wc(e){return k(e?.lift)}function U(e){return n=>{if(Wc(n))return n.lift(function(t){try{return e(t,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function j(e,n,t,r,o){return new qc(e,n,t,r,o)}var qc=class extends mn{constructor(n,t,r,o,i,s){super(n),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(c){n.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){n.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:t}=this;super.unsubscribe(),!t&&((n=this.onFinalize)===null||n===void 0||n.call(this))}}};function Xn(){return U((e,n)=>{let t=null;e._refCount++;let r=j(n,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){t=null;return}let o=e._connection,i=t;t=null,o&&(!i||o===i)&&o.unsubscribe(),n.unsubscribe()});e.subscribe(r),r.closed||(t=e.connect())})}var Jn=class extends W{constructor(n,t){super(),this.source=n,this.subjectFactory=t,this._subject=null,this._refCount=0,this._connection=null,Wc(n)&&(this.lift=n.lift)}_subscribe(n){return this.getSubject().subscribe(n)}getSubject(){let n=this._subject;return(!n||n.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:n}=this;this._subject=this._connection=null,n?.unsubscribe()}connect(){let n=this._connection;if(!n){n=this._connection=new le;let t=this.getSubject();n.add(this.source.subscribe(j(t,void 0,()=>{this._teardown(),t.complete()},r=>{this._teardown(),t.error(r)},()=>this._teardown()))),n.closed&&(this._connection=null,n=le.EMPTY)}return n}refCount(){return Xn()(this)}};var Wp=Gt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var K=(()=>{class e extends W{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){let r=new Ni(this,this);return r.operator=t,r}_throwIfClosed(){if(this.closed)throw new Wp}next(t){Yn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(t)}})}error(t){Yn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;let{observers:r}=this;for(;r.length;)r.shift().error(t)}})}complete(){Yn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return((t=this.observers)===null||t===void 0?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){let{hasError:r,isStopped:o,observers:i}=this;return r||o?jc:(this.currentObservers=null,i.push(t),new le(()=>{this.currentObservers=null,pn(i,t)}))}_checkFinalizedStatuses(t){let{hasError:r,thrownError:o,isStopped:i}=this;r?t.error(o):i&&t.complete()}asObservable(){let t=new W;return t.source=this,t}}return e.create=(n,t)=>new Ni(n,t),e})(),Ni=class extends K{constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.next)===null||r===void 0||r.call(t,n)}error(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.error)===null||r===void 0||r.call(t,n)}complete(){var n,t;(t=(n=this.destination)===null||n===void 0?void 0:n.complete)===null||t===void 0||t.call(n)}_subscribe(n){var t,r;return(r=(t=this.source)===null||t===void 0?void 0:t.subscribe(n))!==null&&r!==void 0?r:jc}};var de=class extends K{constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){let t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){let{hasError:n,thrownError:t,_value:r}=this;if(n)throw t;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}};var Re=new W(e=>e.complete());function qp(e){return e[e.length-1]}function Oi(e){return k(qp(e))?e.pop():void 0}function qt(e){return xi(qp(e))?e.pop():void 0}var Zc=function(e,n){return Zc=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o])},Zc(e,n)};function uF(e,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");Zc(e,n);function t(){this.constructor=e}e.prototype=n===null?Object.create(n):(t.prototype=n.prototype,new t)}function w(e,n,t,r){var o=arguments.length,i=o<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,t):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(e,n,t,r);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(i=(o<3?s(i):o>3?s(n,t,i):s(n,t))||i);return o>3&&i&&Object.defineProperty(n,t,i),i}function Yp(e,n,t,r){function o(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function a(d){try{l(r.next(d))}catch(h){s(h)}}function c(d){try{l(r.throw(d))}catch(h){s(h)}}function l(d){d.done?i(d.value):o(d.value).then(a,c)}l((r=r.apply(e,n||[])).next())})}function Zp(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function vn(e){return this instanceof vn?(this.v=e,this):new vn(e)}function Qp(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=t.apply(e,n||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(y){return Promise.resolve(y).then(f,h)}}function a(f,y){r[f]&&(o[f]=function(S){return new Promise(function(N,z){i.push([f,S,N,z])>1||c(f,S)})},y&&(o[f]=y(o[f])))}function c(f,y){try{l(r[f](y))}catch(S){p(i[0][3],S)}}function l(f){f.value instanceof vn?Promise.resolve(f.value.v).then(d,h):p(i[0][2],f)}function d(f){c("next",f)}function h(f){c("throw",f)}function p(f,y){f(y),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Kp(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=e[Symbol.asyncIterator],t;return n?n.call(e):(e=typeof Zp=="function"?Zp(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(i){t[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var er=e=>e&&typeof e.length=="number"&&typeof e!="function";function ki(e){return k(e?.then)}function Fi(e){return k(e[Kn])}function Pi(e){return Symbol.asyncIterator&&k(e?.[Symbol.asyncIterator])}function Li(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function WC(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var ji=WC();function Vi(e){return k(e?.[ji])}function Bi(e){return Qp(this,arguments,function*(){let t=e.getReader();try{for(;;){let{value:r,done:o}=yield vn(t.read());if(o)return yield vn(void 0);yield yield vn(r)}}finally{t.releaseLock()}})}function Ui(e){return k(e?.getReader)}function te(e){if(e instanceof W)return e;if(e!=null){if(Fi(e))return qC(e);if(er(e))return ZC(e);if(ki(e))return YC(e);if(Pi(e))return Xp(e);if(Vi(e))return QC(e);if(Ui(e))return KC(e)}throw Li(e)}function qC(e){return new W(n=>{let t=e[Kn]();if(k(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function ZC(e){return new W(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}function YC(e){return new W(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,Ei)})}function QC(e){return new W(n=>{for(let t of e)if(n.next(t),n.closed)return;n.complete()})}function Xp(e){return new W(n=>{XC(e,n).catch(t=>n.error(t))})}function KC(e){return Xp(Bi(e))}function XC(e,n){var t,r,o,i;return Yp(this,void 0,void 0,function*(){try{for(t=Kp(e);r=yield t.next(),!r.done;){let s=r.value;if(n.next(s),n.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=t.return)&&(yield i.call(t))}finally{if(o)throw o.error}}n.complete()})}function we(e,n,t,r=0,o=!1){let i=n.schedule(function(){t(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Hi(e,n=0){return U((t,r)=>{t.subscribe(j(r,o=>we(r,e,()=>r.next(o),n),()=>we(r,e,()=>r.complete(),n),o=>we(r,e,()=>r.error(o),n)))})}function $i(e,n=0){return U((t,r)=>{r.add(e.schedule(()=>t.subscribe(r),n))})}function Jp(e,n){return te(e).pipe($i(n),Hi(n))}function eg(e,n){return te(e).pipe($i(n),Hi(n))}function tg(e,n){return new W(t=>{let r=0;return n.schedule(function(){r===e.length?t.complete():(t.next(e[r++]),t.closed||this.schedule())})})}function ng(e,n){return new W(t=>{let r;return we(t,n,()=>{r=e[ji](),we(t,n,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){t.error(s);return}i?t.complete():t.next(o)},0,!0)}),()=>k(r?.return)&&r.return()})}function zi(e,n){if(!e)throw new Error("Iterable cannot be null");return new W(t=>{we(t,n,()=>{let r=e[Symbol.asyncIterator]();we(t,n,()=>{r.next().then(o=>{o.done?t.complete():t.next(o.value)})},0,!0)})})}function rg(e,n){return zi(Bi(e),n)}function og(e,n){if(e!=null){if(Fi(e))return Jp(e,n);if(er(e))return tg(e,n);if(ki(e))return eg(e,n);if(Pi(e))return zi(e,n);if(Vi(e))return ng(e,n);if(Ui(e))return rg(e,n)}throw Li(e)}function ne(e,n){return n?og(e,n):te(e)}function O(...e){let n=qt(e);return ne(e,n)}function tr(e,n){let t=k(e)?e:()=>e,r=o=>o.error(t());return new W(n?o=>n.schedule(r,0,o):r)}function Yc(e){return!!e&&(e instanceof W||k(e.lift)&&k(e.subscribe))}var JC=Gt(e=>function(t=null){e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=t});function eb(e,n){let{first:t,each:r,with:o=tb,scheduler:i=n??Xr,meta:s=null}=Ai(e)?{first:e}:typeof e=="number"?{each:e}:e;if(t==null&&r==null)throw new TypeError("No timeout provided.");return U((a,c)=>{let l,d,h=null,p=0,f=y=>{d=we(c,i,()=>{try{l.unsubscribe(),te(o({meta:s,lastValue:h,seen:p})).subscribe(c)}catch(S){c.error(S)}},y)};l=a.subscribe(j(c,y=>{d?.unsubscribe(),p++,c.next(h=y),r>0&&f(r)},void 0,void 0,()=>{d?.closed||d?.unsubscribe(),h=null})),!p&&f(t!=null?typeof t=="number"?t:+t-i.now():r)})}function tb(e){throw new JC(e)}function H(e,n){return U((t,r)=>{let o=0;t.subscribe(j(r,i=>{r.next(e.call(n,i,o++))}))})}var{isArray:nb}=Array;function rb(e,n){return nb(n)?e(...n):e(n)}function nr(e){return H(n=>rb(e,n))}var{isArray:ob}=Array,{getPrototypeOf:ib,prototype:sb,keys:ab}=Object;function Gi(e){if(e.length===1){let n=e[0];if(ob(n))return{args:n,keys:null};if(cb(n)){let t=ab(n);return{args:t.map(r=>n[r]),keys:t}}}return{args:e,keys:null}}function cb(e){return e&&typeof e=="object"&&ib(e)===sb}function Wi(e,n){return e.reduce((t,r,o)=>(t[r]=n[o],t),{})}function yn(...e){let n=qt(e),t=Oi(e),{args:r,keys:o}=Gi(e);if(r.length===0)return ne([],n);let i=new W(lb(r,n,o?s=>Wi(o,s):Ie));return t?i.pipe(nr(t)):i}function lb(e,n,t=Ie){return r=>{ig(n,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)ig(n,()=>{let l=ne(e[c],n),d=!1;l.subscribe(j(r,h=>{i[c]=h,d||(d=!0,a--),a||r.next(t(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function ig(e,n,t){e?we(t,e,n):n()}function sg(e,n,t,r,o,i,s,a){let c=[],l=0,d=0,h=!1,p=()=>{h&&!c.length&&!l&&n.complete()},f=S=>l<r?y(S):c.push(S),y=S=>{i&&n.next(S),l++;let N=!1;te(t(S,d++)).subscribe(j(n,z=>{o?.(z),i?f(z):n.next(z)},()=>{N=!0},void 0,()=>{if(N)try{for(l--;c.length&&l<r;){let z=c.shift();s?we(n,s,()=>y(z)):y(z)}p()}catch(z){n.error(z)}}))};return e.subscribe(j(n,f,()=>{h=!0,p()})),()=>{a?.()}}function ae(e,n,t=1/0){return k(n)?ae((r,o)=>H((i,s)=>n(r,i,o,s))(te(e(r,o))),t):(typeof n=="number"&&(t=n),U((r,o)=>sg(r,o,e,t)))}function rr(e=1/0){return ae(Ie,e)}function ag(){return rr(1)}function or(...e){return ag()(ne(e,qt(e)))}function qi(e){return new W(n=>{te(e()).subscribe(n)})}function Qc(...e){let n=Oi(e),{args:t,keys:r}=Gi(e),o=new W(i=>{let{length:s}=t;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let d=0;d<s;d++){let h=!1;te(t[d]).subscribe(j(i,p=>{h||(h=!0,l--),a[d]=p},()=>c--,void 0,()=>{(!c||!h)&&(l||i.next(r?Wi(r,a):a),i.complete())}))}});return n?o.pipe(nr(n)):o}var ub=["addListener","removeListener"],db=["addEventListener","removeEventListener"],fb=["on","off"];function Dn(e,n,t,r){if(k(t)&&(r=t,t=void 0),r)return Dn(e,n,t).pipe(nr(r));let[o,i]=gb(e)?db.map(s=>a=>e[s](n,a,t)):hb(e)?ub.map(cg(e,n)):pb(e)?fb.map(cg(e,n)):[];if(!o&&er(e))return ae(s=>Dn(s,n,t))(te(e));if(!o)throw new TypeError("Invalid event target");return new W(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function cg(e,n){return t=>r=>e[t](n,r)}function hb(e){return k(e.addListener)&&k(e.removeListener)}function pb(e){return k(e.on)&&k(e.off)}function gb(e){return k(e.addEventListener)&&k(e.removeEventListener)}function me(e,n){return U((t,r)=>{let o=0;t.subscribe(j(r,i=>e.call(n,i,o++)&&r.next(i)))})}function ut(e){return U((n,t)=>{let r=null,o=!1,i;r=n.subscribe(j(t,void 0,void 0,s=>{i=te(e(s,ut(e)(n))),r?(r.unsubscribe(),r=null,i.subscribe(t)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(t))})}function lg(e,n,t,r,o){return(i,s)=>{let a=t,c=n,l=0;i.subscribe(j(s,d=>{let h=l++;c=a?e(c,d,h):(a=!0,d),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function dt(e,n){return k(n)?ae(e,n,1):ae(e,1)}function Zt(e){return U((n,t)=>{let r=!1;n.subscribe(j(t,o=>{r=!0,t.next(o)},()=>{r||t.next(e),t.complete()}))})}function xt(e){return e<=0?()=>Re:U((n,t)=>{let r=0;n.subscribe(j(t,o=>{++r<=e&&(t.next(o),e<=r&&t.complete())}))})}function Kc(e,n=Ie){return e=e??mb,U((t,r)=>{let o,i=!0;t.subscribe(j(r,s=>{let a=n(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function mb(e,n){return e===n}function Zi(e=vb){return U((n,t)=>{let r=!1;n.subscribe(j(t,o=>{r=!0,t.next(o)},()=>r?t.complete():t.error(e())))})}function vb(){return new et}function Yt(e){return U((n,t)=>{try{n.subscribe(t)}finally{t.add(e)}})}function At(e,n){let t=arguments.length>=2;return r=>r.pipe(e?me((o,i)=>e(o,i,r)):Ie,xt(1),t?Zt(n):Zi(()=>new et))}function ir(e){return e<=0?()=>Re:U((n,t)=>{let r=[];n.subscribe(j(t,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)t.next(o);t.complete()},void 0,()=>{r=null}))})}function Xc(e,n){let t=arguments.length>=2;return r=>r.pipe(e?me((o,i)=>e(o,i,r)):Ie,ir(1),t?Zt(n):Zi(()=>new et))}function yb(e=1/0){let n;e&&typeof e=="object"?n=e:n={count:e};let{count:t=1/0,delay:r,resetOnSuccess:o=!1}=n;return t<=0?Ie:U((i,s)=>{let a=0,c,l=()=>{let d=!1;c=i.subscribe(j(s,h=>{o&&(a=0),s.next(h)},void 0,h=>{if(a++<t){let p=()=>{c?(c.unsubscribe(),c=null,l()):d=!0};if(r!=null){let f=typeof r=="number"?Ri(r):te(r(h,a)),y=j(s,()=>{y.unsubscribe(),p()},()=>{s.complete()});f.subscribe(y)}else p()}else s.error(h)})),d&&(c.unsubscribe(),c=null,l())};l()})}function Jc(e,n){return U(lg(e,n,arguments.length>=2,!0))}function el(...e){let n=qt(e);return U((t,r)=>{(n?or(e,t,n):or(e,t)).subscribe(r)})}function ve(e,n){return U((t,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();t.subscribe(j(r,c=>{o?.unsubscribe();let l=0,d=i++;te(e(c,d)).subscribe(o=j(r,h=>r.next(n?n(c,h,d,l++):h),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function tl(e){return U((n,t)=>{te(e).subscribe(j(t,()=>t.complete(),Qr)),!t.closed&&n.subscribe(t)})}function Ce(e,n,t){let r=k(e)||n||t?{next:e,error:n,complete:t}:e;return r?U((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(j(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):Ie}function il(e,n){return Object.is(e,n)}var he=null,Yi=!1,sl=1,We=Symbol("SIGNAL");function q(e){let n=he;return he=e,n}function al(){return he}var eo={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function to(e){if(Yi)throw new Error("");if(he===null)return;he.consumerOnSignalRead(e);let n=he.nextProducerIndex++;if(Ji(he),n<he.producerNode.length&&he.producerNode[n]!==e&&Jr(he)){let t=he.producerNode[n];Xi(t,he.producerIndexOfThis[n])}he.producerNode[n]!==e&&(he.producerNode[n]=e,he.producerIndexOfThis[n]=Jr(he)?dg(e,he,n):0),he.producerLastReadVersion[n]=e.version}function ug(){sl++}function cl(e){if(!(Jr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===sl)){if(!e.producerMustRecompute(e)&&!fl(e)){ol(e);return}e.producerRecomputeValue(e),ol(e)}}function ll(e){if(e.liveConsumerNode===void 0)return;let n=Yi;Yi=!0;try{for(let t of e.liveConsumerNode)t.dirty||Db(t)}finally{Yi=n}}function ul(){return he?.consumerAllowSignalWrites!==!1}function Db(e){e.dirty=!0,ll(e),e.consumerMarkedDirty?.(e)}function ol(e){e.dirty=!1,e.lastCleanEpoch=sl}function Ki(e){return e&&(e.nextProducerIndex=0),q(e)}function dl(e,n){if(q(n),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Jr(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)Xi(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function fl(e){Ji(e);for(let n=0;n<e.producerNode.length;n++){let t=e.producerNode[n],r=e.producerLastReadVersion[n];if(r!==t.version||(cl(t),r!==t.version))return!0}return!1}function hl(e){if(Ji(e),Jr(e))for(let n=0;n<e.producerNode.length;n++)Xi(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function dg(e,n,t){if(fg(e),e.liveConsumerNode.length===0&&hg(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=dg(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function Xi(e,n){if(fg(e),e.liveConsumerNode.length===1&&hg(e))for(let r=0;r<e.producerNode.length;r++)Xi(e.producerNode[r],e.producerIndexOfThis[r]);let t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[n],o=e.liveConsumerNode[n];Ji(o),o.producerIndexOfThis[r]=n}}function Jr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Ji(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function fg(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function hg(e){return e.producerNode!==void 0}function pl(e,n){let t=Object.create(Ib);t.computation=e,n!==void 0&&(t.equal=n);let r=()=>{if(cl(t),to(t),t.value===Qi)throw t.error;return t.value};return r[We]=t,r}var nl=Symbol("UNSET"),rl=Symbol("COMPUTING"),Qi=Symbol("ERRORED"),Ib=P(I({},eo),{value:nl,dirty:!0,error:null,equal:il,kind:"computed",producerMustRecompute(e){return e.value===nl||e.value===rl},producerRecomputeValue(e){if(e.value===rl)throw new Error("Detected cycle in computations.");let n=e.value;e.value=rl;let t=Ki(e),r,o=!1;try{r=e.computation(),q(null),o=n!==nl&&n!==Qi&&r!==Qi&&e.equal(n,r)}catch(i){r=Qi,e.error=i}finally{dl(e,t)}if(o){e.value=n;return}e.value=r,e.version++}});function Cb(){throw new Error}var pg=Cb;function gg(e){pg(e)}function gl(e){pg=e}var bb=null;function ml(e,n){let t=Object.create(es);t.value=e,n!==void 0&&(t.equal=n);let r=()=>(to(t),t.value);return r[We]=t,r}function no(e,n){ul()||gg(e),e.equal(e.value,n)||(e.value=n,wb(e))}function vl(e,n){ul()||gg(e),no(e,n(e.value))}var es=P(I({},eo),{equal:il,value:void 0,kind:"signal"});function wb(e){e.version++,ug(),ll(e),bb?.()}function yl(e){let n=q(null);try{return e()}finally{q(n)}}var Dl;function ro(){return Dl}function Rt(e){let n=Dl;return Dl=e,n}var ts=Symbol("NotFound");var cm="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",A=class extends Error{code;constructor(n,t){super(lm(n,t)),this.code=n}};function Sb(e){return`NG0${Math.abs(e)}`}function lm(e,n){return`${Sb(e)}${n?": "+n:""}`}var um=Symbol("InputSignalNode#UNSET"),Tb=P(I({},es),{transformFn:void 0,applyValueToInputSignal(e,n){no(e,n)}});function dm(e,n){let t=Object.create(Tb);t.value=e,t.transformFn=n?.transform;function r(){if(to(t),t.value===um){let o=null;throw new A(-950,o)}return t.value}return r[We]=t,r}function vo(e){return{toString:e}.toString()}var ns="__parameters__";function xb(e){return function(...t){if(e){let r=e(...t);for(let o in r)this[o]=r[o]}}}function fm(e,n,t){return vo(()=>{let r=xb(n);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,d){let h=c.hasOwnProperty(ns)?c[ns]:Object.defineProperty(c,ns,{value:[]})[ns];for(;h.length<=d;)h.push(null);return(h[d]=h[d]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Ve=globalThis;function J(e){for(let n in e)if(e[n]===J)return n;throw Error("Could not find renamed property on target object.")}function Ab(e,n){for(let t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function Ne(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(Ne).join(", ")}]`;if(e==null)return""+e;let n=e.overriddenName||e.name;if(n)return`${n}`;let t=e.toString();if(t==null)return""+t;let r=t.indexOf(`
`);return r>=0?t.slice(0,r):t}function mg(e,n){return e?n?`${e} ${n}`:e:n||""}var Rb=J({__forward_ref__:J});function Be(e){return e.__forward_ref__=Be,e.toString=function(){return Ne(this())},e}function Se(e){return hm(e)?e():e}function hm(e){return typeof e=="function"&&e.hasOwnProperty(Rb)&&e.__forward_ref__===Be}function T(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Te(e){return{providers:e.providers||[],imports:e.imports||[]}}function Bs(e){return vg(e,gm)||vg(e,mm)}function pm(e){return Bs(e)!==null}function vg(e,n){return e.hasOwnProperty(n)?e[n]:null}function Nb(e){let n=e&&(e[gm]||e[mm]);return n||null}function yg(e){return e&&(e.hasOwnProperty(Dg)||e.hasOwnProperty(Ob))?e[Dg]:null}var gm=J({\u0275prov:J}),Dg=J({\u0275inj:J}),mm=J({ngInjectableDef:J}),Ob=J({ngInjectorDef:J}),x=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(n,t){this._desc=n,this.\u0275prov=void 0,typeof t=="number"?this.__NG_ELEMENT_ID__=t:t!==void 0&&(this.\u0275prov=T({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function vm(e){return e&&!!e.\u0275providers}var kb=J({\u0275cmp:J}),Fb=J({\u0275dir:J}),Pb=J({\u0275pipe:J}),Lb=J({\u0275mod:J}),hs=J({\u0275fac:J}),ao=J({__NG_ELEMENT_ID__:J}),Ig=J({__NG_ENV_ID__:J});function bn(e){return typeof e=="string"?e:e==null?"":String(e)}function jb(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():bn(e)}function ym(e,n){throw new A(-200,e)}function xu(e,n){throw new A(-201,!1)}var B=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(B||{}),Ol;function Dm(){return Ol}function qe(e){let n=Ol;return Ol=e,n}function Im(e,n,t){let r=Bs(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(t&B.Optional)return null;if(n!==void 0)return n;xu(e,"Injector")}var Vb={},In=Vb,kl="__NG_DI_FLAG__",ps=class{injector;constructor(n){this.injector=n}retrieve(n,t){let r=t;return this.injector.get(n,r.optional?ts:In,r)}},gs="ngTempTokenPath",Bb="ngTokenPath",Ub=/\n/gm,Hb="\u0275",Cg="__source";function $b(e,n=B.Default){if(ro()===void 0)throw new A(-203,!1);if(ro()===null)return Im(e,void 0,n);{let t=ro(),r;return t instanceof ps?r=t.injector:r=t,r.get(e,n&B.Optional?null:void 0,n)}}function R(e,n=B.Default){return(Dm()||$b)(Se(e),n)}function m(e,n=B.Default){return R(e,Us(n))}function Us(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Fl(e){let n=[];for(let t=0;t<e.length;t++){let r=Se(e[t]);if(Array.isArray(r)){if(r.length===0)throw new A(900,!1);let o,i=B.Default;for(let s=0;s<r.length;s++){let a=r[s],c=zb(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}n.push(R(o,i))}else n.push(R(r))}return n}function Cm(e,n){return e[kl]=n,e.prototype[kl]=n,e}function zb(e){return e[kl]}function Gb(e,n,t,r){let o=e[gs];throw n[Cg]&&o.unshift(n[Cg]),e.message=Wb(`
`+e.message,o,t,r),e[Bb]=o,e[gs]=null,e}function Wb(e,n,t,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Hb?e.slice(2):e;let o=Ne(n);if(Array.isArray(n))o=n.map(Ne).join(" -> ");else if(typeof n=="object"){let i=[];for(let s in n)if(n.hasOwnProperty(s)){let a=n[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Ne(a)))}o=`{${i.join(", ")}}`}return`${t}${r?"("+r+")":""}[${o}]: ${e.replace(Ub,`
  `)}`}var qb=Cm(fm("Optional"),8);var Zb=Cm(fm("SkipSelf"),4);function fr(e,n){let t=e.hasOwnProperty(hs);return t?e[hs]:null}function Yb(e,n,t){if(e.length!==n.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=n[r];if(t&&(o=t(o),i=t(i)),i!==o)return!1}return!0}function Qb(e){return e.flat(Number.POSITIVE_INFINITY)}function Au(e,n){e.forEach(t=>Array.isArray(t)?Au(t,n):n(t))}function bm(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function ms(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function Kb(e,n){let t=[];for(let r=0;r<e;r++)t.push(n);return t}function Xb(e,n,t,r){let o=e.length;if(o==n)e.push(t,r);else if(o===1)e.push(r,e[0]),e[0]=t;else{for(o--,e.push(e[o-1],e[o]);o>n;){let i=o-2;e[o]=e[i],o--}e[n]=t,e[n+1]=r}}function Jb(e,n,t){let r=yo(e,n);return r>=0?e[r|1]=t:(r=~r,Xb(e,r,n,t)),r}function Il(e,n){let t=yo(e,n);if(t>=0)return e[t|1]}function yo(e,n){return e0(e,n,1)}function e0(e,n,t){let r=0,o=e.length>>t;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<t];if(n===s)return i<<t;s>n?o=i:r=i+1}return~(o<<t)}var wn={},Ze=[],lo=new x(""),wm=new x("",-1),Em=new x(""),vs=class{get(n,t=In){if(t===In){let r=new Error(`NullInjectorError: No provider for ${Ne(n)}!`);throw r.name="NullInjectorError",r}return t}};function _m(e,n){let t=e[Lb]||null;if(!t&&n===!0)throw new Error(`Type ${Ne(e)} does not have '\u0275mod' property.`);return t}function Xt(e){return e[kb]||null}function t0(e){return e[Fb]||null}function n0(e){return e[Pb]||null}function Hs(e){return{\u0275providers:e}}function r0(...e){return{\u0275providers:Mm(!0,e),\u0275fromNgModule:!0}}function Mm(e,...n){let t=[],r=new Set,o,i=s=>{t.push(s)};return Au(n,s=>{let a=s;Pl(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Sm(o,i),t}function Sm(e,n){for(let t=0;t<e.length;t++){let{ngModule:r,providers:o}=e[t];Ru(o,i=>{n(i,r)})}}function Pl(e,n,t,r){if(e=Se(e),!e)return!1;let o=null,i=yg(e),s=!i&&Xt(e);if(!i&&!s){let c=e.ngModule;if(i=yg(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Pl(l,n,t,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{Au(i.imports,d=>{Pl(d,n,t,r)&&(l||=[],l.push(d))})}finally{}l!==void 0&&Sm(l,n)}if(!a){let l=fr(o)||(()=>new o);n({provide:o,useFactory:l,deps:Ze},o),n({provide:Em,useValue:o,multi:!0},o),n({provide:lo,useValue:()=>R(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;Ru(c,d=>{n(d,l)})}}else return!1;return o!==e&&e.providers!==void 0}function Ru(e,n){for(let t of e)vm(t)&&(t=t.\u0275providers),Array.isArray(t)?Ru(t,n):n(t)}var o0=J({provide:String,useValue:J});function Tm(e){return e!==null&&typeof e=="object"&&o0 in e}function i0(e){return!!(e&&e.useExisting)}function s0(e){return!!(e&&e.useFactory)}function hr(e){return typeof e=="function"}function a0(e){return!!e.useClass}var $s=new x(""),cs={},bg={},Cl;function zs(){return Cl===void 0&&(Cl=new vs),Cl}var ie=class{},uo=class extends ie{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(n,t,r,o){super(),this.parent=t,this.source=r,this.scopes=o,jl(n,s=>this.processProvider(s)),this.records.set(wm,sr(void 0,this)),o.has("environment")&&this.records.set(ie,sr(void 0,this));let i=this.records.get($s);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Em,Ze,B.Self))}retrieve(n,t){let r=t;return this.get(n,r.optional?ts:In,r)}destroy(){io(this),this._destroyed=!0;let n=q(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let t=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of t)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),q(n)}}onDestroy(n){return io(this),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){io(this);let t=Rt(this),r=qe(void 0),o;try{return n()}finally{Rt(t),qe(r)}}get(n,t=In,r=B.Default){if(io(this),n.hasOwnProperty(Ig))return n[Ig](this);r=Us(r);let o,i=Rt(this),s=qe(void 0);try{if(!(r&B.SkipSelf)){let c=this.records.get(n);if(c===void 0){let l=f0(n)&&Bs(n);l&&this.injectableDefInScope(l)?c=sr(Ll(n),cs):c=null,this.records.set(n,c)}if(c!=null)return this.hydrate(n,c,r)}let a=r&B.Self?zs():this.parent;return t=r&B.Optional&&t===In?null:t,a.get(n,t)}catch(a){if(a.name==="NullInjectorError"){if((a[gs]=a[gs]||[]).unshift(Ne(n)),i)throw a;return Gb(a,n,"R3InjectorError",this.source)}else throw a}finally{qe(s),Rt(i)}}resolveInjectorInitializers(){let n=q(null),t=Rt(this),r=qe(void 0),o;try{let i=this.get(lo,Ze,B.Self);for(let s of i)s()}finally{Rt(t),qe(r),q(n)}}toString(){let n=[],t=this.records;for(let r of t.keys())n.push(Ne(r));return`R3Injector[${n.join(", ")}]`}processProvider(n){n=Se(n);let t=hr(n)?n:Se(n&&n.provide),r=l0(n);if(!hr(n)&&n.multi===!0){let o=this.records.get(t);o||(o=sr(void 0,cs,!0),o.factory=()=>Fl(o.multi),this.records.set(t,o)),t=n,o.multi.push(n)}this.records.set(t,r)}hydrate(n,t,r){let o=q(null);try{return t.value===bg?ym(Ne(n)):t.value===cs&&(t.value=bg,t.value=t.factory(void 0,r)),typeof t.value=="object"&&t.value&&d0(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{q(o)}}injectableDefInScope(n){if(!n.providedIn)return!1;let t=Se(n.providedIn);return typeof t=="string"?t==="any"||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){let t=this._onDestroyHooks.indexOf(n);t!==-1&&this._onDestroyHooks.splice(t,1)}};function Ll(e){let n=Bs(e),t=n!==null?n.factory:fr(e);if(t!==null)return t;if(e instanceof x)throw new A(204,!1);if(e instanceof Function)return c0(e);throw new A(204,!1)}function c0(e){if(e.length>0)throw new A(204,!1);let t=Nb(e);return t!==null?()=>t.factory(e):()=>new e}function l0(e){if(Tm(e))return sr(void 0,e.useValue);{let n=xm(e);return sr(n,cs)}}function xm(e,n,t){let r;if(hr(e)){let o=Se(e);return fr(o)||Ll(o)}else if(Tm(e))r=()=>Se(e.useValue);else if(s0(e))r=()=>e.useFactory(...Fl(e.deps||[]));else if(i0(e))r=(o,i)=>R(Se(e.useExisting),i!==void 0&&i&B.Optional?B.Optional:void 0);else{let o=Se(e&&(e.useClass||e.provide));if(u0(e))r=()=>new o(...Fl(e.deps));else return fr(o)||Ll(o)}return r}function io(e){if(e.destroyed)throw new A(205,!1)}function sr(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function u0(e){return!!e.deps}function d0(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function f0(e){return typeof e=="function"||typeof e=="object"&&e instanceof x}function jl(e,n){for(let t of e)Array.isArray(t)?jl(t,n):t&&vm(t)?jl(t.\u0275providers,n):n(t)}function ke(e,n){let t;e instanceof uo?(io(e),t=e):t=new ps(e);let r,o=Rt(t),i=qe(void 0);try{return n()}finally{Rt(o),qe(i)}}function Nu(){return Dm()!==void 0||ro()!=null}function h0(e){if(!Nu())throw new A(-203,!1)}function p0(e){let n=Ve.ng;if(n&&n.\u0275compilerFacade)return n.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function g0(e){return typeof e=="function"}var Ft=0,G=1,L=2,Ee=3,rt=4,Fe=5,fo=6,ys=7,Oe=8,pr=9,Nt=10,ue=11,ho=12,wg=13,br=14,Qe=15,En=16,ar=17,Ot=18,Gs=19,Am=20,Qt=21,bl=22,Ds=23,Ye=24,ur=25,Ke=26,Rm=1;var _n=7,Is=8,gr=9,je=10;function Kt(e){return Array.isArray(e)&&typeof e[Rm]=="object"}function Pt(e){return Array.isArray(e)&&e[Rm]===!0}function Ou(e){return(e.flags&4)!==0}function wr(e){return e.componentOffset>-1}function Ws(e){return(e.flags&1)===1}function ht(e){return!!e.template}function Cs(e){return(e[L]&512)!==0}function Er(e){return(e[L]&256)===256}var Vl=class{previousValue;currentValue;firstChange;constructor(n,t,r){this.previousValue=n,this.currentValue=t,this.firstChange=r}isFirstChange(){return this.firstChange}};function Nm(e,n,t,r){n!==null?n.applyValueToInputSignal(n,r):e[t]=r}var Xe=(()=>{let e=()=>Om;return e.ngInherit=!0,e})();function Om(e){return e.type.prototype.ngOnChanges&&(e.setInput=v0),m0}function m0(){let e=Fm(this),n=e?.current;if(n){let t=e.previous;if(t===wn)e.previous=n;else for(let r in n)t[r]=n[r];e.current=null,this.ngOnChanges(n)}}function v0(e,n,t,r,o){let i=this.declaredInputs[r],s=Fm(e)||y0(e,{previous:wn,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new Vl(l&&l.currentValue,t,c===wn),Nm(e,n,o,t)}var km="__ngSimpleChanges__";function Fm(e){return e[km]||null}function y0(e,n){return e[km]=n}var Eg=null;var re=function(e,n=null,t){Eg?.(e,n,t)},D0="svg",I0="math";function pt(e){for(;Array.isArray(e);)e=e[Ft];return e}function Pm(e,n){return pt(n[e])}function It(e,n){return pt(n[e.index])}function Lm(e,n){return e.data[n]}function gt(e,n){let t=n[e];return Kt(t)?t:t[Ft]}function C0(e){return(e[L]&4)===4}function ku(e){return(e[L]&128)===128}function b0(e){return Pt(e[Ee])}function mr(e,n){return n==null?null:e[n]}function jm(e){e[ar]=0}function Vm(e){e[L]&1024||(e[L]|=1024,ku(e)&&Do(e))}function w0(e,n){for(;e>0;)n=n[br],e--;return n}function qs(e){return!!(e[L]&9216||e[Ye]?.dirty)}function Bl(e){e[Nt].changeDetectionScheduler?.notify(8),e[L]&64&&(e[L]|=1024),qs(e)&&Do(e)}function Do(e){e[Nt].changeDetectionScheduler?.notify(0);let n=Mn(e);for(;n!==null&&!(n[L]&8192||(n[L]|=8192,!ku(n)));)n=Mn(n)}function Bm(e,n){if(Er(e))throw new A(911,!1);e[Qt]===null&&(e[Qt]=[]),e[Qt].push(n)}function E0(e,n){if(e[Qt]===null)return;let t=e[Qt].indexOf(n);t!==-1&&e[Qt].splice(t,1)}function Mn(e){let n=e[Ee];return Pt(n)?n[Ee]:n}function Fu(e){return e[ys]??=[]}function Pu(e){return e.cleanup??=[]}function _0(e,n,t,r){let o=Fu(n);o.push(t),e.firstCreatePass&&Pu(e).push(r,o.length-1)}var $={lFrame:Zm(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Ul=!1;function M0(){return $.lFrame.elementDepthCount}function S0(){$.lFrame.elementDepthCount++}function T0(){$.lFrame.elementDepthCount--}function Lu(){return $.bindingsEnabled}function Um(){return $.skipHydrationRootTNode!==null}function x0(e){return $.skipHydrationRootTNode===e}function A0(){$.skipHydrationRootTNode=null}function Z(){return $.lFrame.lView}function fe(){return $.lFrame.tView}function ju(e){return $.lFrame.contextLView=e,e[Oe]}function Vu(e){return $.lFrame.contextLView=null,e}function _e(){let e=Hm();for(;e!==null&&e.type===64;)e=e.parent;return e}function Hm(){return $.lFrame.currentTNode}function R0(){let e=$.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}function Nn(e,n){let t=$.lFrame;t.currentTNode=e,t.isParent=n}function Bu(){return $.lFrame.isParent}function Uu(){$.lFrame.isParent=!1}function $m(){return Ul}function _g(e){let n=Ul;return Ul=e,n}function N0(){let e=$.lFrame,n=e.bindingRootIndex;return n===-1&&(n=e.bindingRootIndex=e.tView.bindingStartIndex),n}function O0(){return $.lFrame.bindingIndex}function k0(e){return $.lFrame.bindingIndex=e}function Zs(){return $.lFrame.bindingIndex++}function zm(e){let n=$.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function F0(){return $.lFrame.inI18n}function P0(e,n){let t=$.lFrame;t.bindingIndex=t.bindingRootIndex=e,Hl(n)}function L0(){return $.lFrame.currentDirectiveIndex}function Hl(e){$.lFrame.currentDirectiveIndex=e}function j0(e){let n=$.lFrame.currentDirectiveIndex;return n===-1?null:e[n]}function Gm(){return $.lFrame.currentQueryIndex}function Hu(e){$.lFrame.currentQueryIndex=e}function V0(e){let n=e[G];return n.type===2?n.declTNode:n.type===1?e[Fe]:null}function Wm(e,n,t){if(t&B.SkipSelf){let o=n,i=e;for(;o=o.parent,o===null&&!(t&B.Host);)if(o=V0(i),o===null||(i=i[br],o.type&10))break;if(o===null)return!1;n=o,e=i}let r=$.lFrame=qm();return r.currentTNode=n,r.lView=e,!0}function $u(e){let n=qm(),t=e[G];$.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function qm(){let e=$.lFrame,n=e===null?null:e.child;return n===null?Zm(e):n}function Zm(e){let n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=n),n}function Ym(){let e=$.lFrame;return $.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Qm=Ym;function zu(){let e=Ym();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function B0(e){return($.lFrame.contextLView=w0(e,$.lFrame.contextLView))[Oe]}function On(){return $.lFrame.selectedIndex}function Sn(e){$.lFrame.selectedIndex=e}function Ys(){let e=$.lFrame;return Lm(e.tView,e.selectedIndex)}function U0(){return $.lFrame.currentNamespace}var Km=!0;function Qs(){return Km}function Ks(e){Km=e}function H0(e,n,t){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=n.type.prototype;if(r){let s=Om(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}o&&(t.preOrderHooks??=[]).push(0-e,o),i&&((t.preOrderHooks??=[]).push(e,i),(t.preOrderCheckHooks??=[]).push(e,i))}function Gu(e,n){for(let t=n.directiveStart,r=n.directiveEnd;t<r;t++){let i=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:d}=i;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),c&&(e.viewHooks??=[]).push(-t,c),l&&((e.viewHooks??=[]).push(t,l),(e.viewCheckHooks??=[]).push(t,l)),d!=null&&(e.destroyHooks??=[]).push(t,d)}}function ls(e,n,t){Xm(e,n,3,t)}function us(e,n,t,r){(e[L]&3)===t&&Xm(e,n,t,r)}function wl(e,n){let t=e[L];(t&3)===n&&(t&=16383,t+=1,e[L]=t)}function Xm(e,n,t,r){let o=r!==void 0?e[ar]&65535:0,i=r??-1,s=n.length-1,a=0;for(let c=o;c<s;c++)if(typeof n[c+1]=="number"){if(a=n[c],r!=null&&a>=r)break}else n[c]<0&&(e[ar]+=65536),(a<i||i==-1)&&($0(e,t,n,c),e[ar]=(e[ar]&**********)+c+2),c++}function Mg(e,n){re(4,e,n);let t=q(null);try{n.call(e)}finally{q(t),re(5,e,n)}}function $0(e,n,t,r){let o=t[r]<0,i=t[r+1],s=o?-t[r]:t[r],a=e[s];o?e[L]>>14<e[ar]>>16&&(e[L]&3)===n&&(e[L]+=16384,Mg(a,i)):Mg(a,i)}var dr=-1,Tn=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(n,t,r){this.factory=n,this.canSeeViewProviders=t,this.injectImpl=r}};function z0(e){return(e.flags&8)!==0}function G0(e){return(e.flags&16)!==0}function W0(e,n,t){let r=0;for(;r<t.length;){let o=t[r];if(typeof o=="number"){if(o!==0)break;r++;let i=t[r++],s=t[r++],a=t[r++];e.setAttribute(n,s,a,i)}else{let i=o,s=t[++r];q0(i)?e.setProperty(n,i,s):e.setAttribute(n,i,s),r++}}return r}function Jm(e){return e===3||e===4||e===6}function q0(e){return e.charCodeAt(0)===64}function vr(e,n){if(!(n===null||n.length===0))if(e===null||e.length===0)e=n.slice();else{let t=-1;for(let r=0;r<n.length;r++){let o=n[r];typeof o=="number"?t=o:t===0||(t===-1||t===2?Sg(e,t,o,null,n[++r]):Sg(e,t,o,null,null))}}return e}function Sg(e,n,t,r,o){let i=0,s=e.length;if(n===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===n){s=-1;break}else if(a>n){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===t){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,n),i=s+1),e.splice(i++,0,t),o!==null&&e.splice(i++,0,o)}function ev(e){return e!==dr}function bs(e){return e&32767}function Z0(e){return e>>16}function ws(e,n){let t=Z0(e),r=n;for(;t>0;)r=r[br],t--;return r}var $l=!0;function Tg(e){let n=$l;return $l=e,n}var Y0=256,tv=Y0-1,nv=5,Q0=0,ft={};function K0(e,n,t){let r;typeof t=="string"?r=t.charCodeAt(0)||0:t.hasOwnProperty(ao)&&(r=t[ao]),r==null&&(r=t[ao]=Q0++);let o=r&tv,i=1<<o;n.data[e+(o>>nv)]|=i}function Es(e,n){let t=rv(e,n);if(t!==-1)return t;let r=n[G];r.firstCreatePass&&(e.injectorIndex=n.length,El(r.data,e),El(n,null),El(r.blueprint,null));let o=Wu(e,n),i=e.injectorIndex;if(ev(o)){let s=bs(o),a=ws(o,n),c=a[G].data;for(let l=0;l<8;l++)n[i+l]=a[s+l]|c[s+l]}return n[i+8]=o,i}function El(e,n){e.push(0,0,0,0,0,0,0,0,n)}function rv(e,n){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||n[e.injectorIndex+8]===null?-1:e.injectorIndex}function Wu(e,n){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let t=0,r=null,o=n;for(;o!==null;){if(r=cv(o),r===null)return dr;if(t++,o=o[br],r.injectorIndex!==-1)return r.injectorIndex|t<<16}return dr}function zl(e,n,t){K0(e,n,t)}function X0(e,n){if(n==="class")return e.classes;if(n==="style")return e.styles;let t=e.attrs;if(t){let r=t.length,o=0;for(;o<r;){let i=t[o];if(Jm(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof t[o]=="string";)o++;else{if(i===n)return t[o+1];o=o+2}}}return null}function ov(e,n,t){if(t&B.Optional||e!==void 0)return e;xu(n,"NodeInjector")}function iv(e,n,t,r){if(t&B.Optional&&r===void 0&&(r=null),(t&(B.Self|B.Host))===0){let o=e[pr],i=qe(void 0);try{return o?o.get(n,r,t&B.Optional):Im(n,r,t&B.Optional)}finally{qe(i)}}return ov(r,n,t)}function sv(e,n,t,r=B.Default,o){if(e!==null){if(n[L]&2048&&!(r&B.Self)){let s=nw(e,n,t,r,ft);if(s!==ft)return s}let i=av(e,n,t,r,ft);if(i!==ft)return i}return iv(n,t,r,o)}function av(e,n,t,r,o){let i=ew(t);if(typeof i=="function"){if(!Wm(n,e,r))return r&B.Host?ov(o,t,r):iv(n,t,r,o);try{let s;if(s=i(r),s==null&&!(r&B.Optional))xu(t);else return s}finally{Qm()}}else if(typeof i=="number"){let s=null,a=rv(e,n),c=dr,l=r&B.Host?n[Qe][Fe]:null;for((a===-1||r&B.SkipSelf)&&(c=a===-1?Wu(e,n):n[a+8],c===dr||!Ag(r,!1)?a=-1:(s=n[G],a=bs(c),n=ws(c,n)));a!==-1;){let d=n[G];if(xg(i,a,d.data)){let h=J0(a,n,t,s,r,l);if(h!==ft)return h}c=n[a+8],c!==dr&&Ag(r,n[G].data[a+8]===l)&&xg(i,a,n)?(s=d,a=bs(c),n=ws(c,n)):a=-1}}return o}function J0(e,n,t,r,o,i){let s=n[G],a=s.data[e+8],c=r==null?wr(a)&&$l:r!=s&&(a.type&3)!==0,l=o&B.Host&&i===a,d=ds(a,s,t,c,l);return d!==null?po(n,s,d,a,o):ft}function ds(e,n,t,r,o){let i=e.providerIndexes,s=n.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,d=i>>20,h=r?a:a+d,p=o?a+d:l;for(let f=h;f<p;f++){let y=s[f];if(f<c&&t===y||f>=c&&y.type===t)return f}if(o){let f=s[c];if(f&&ht(f)&&f.type===t)return c}return null}function po(e,n,t,r,o){let i=e[t],s=n.data;if(i instanceof Tn){let a=i;a.resolving&&ym(jb(s[t]));let c=Tg(a.canSeeViewProviders);a.resolving=!0;let l,d=a.injectImpl?qe(a.injectImpl):null,h=Wm(e,r,B.Default);try{i=e[t]=a.factory(void 0,o,s,e,r),n.firstCreatePass&&t>=r.directiveStart&&H0(t,s[t],n)}finally{d!==null&&qe(d),Tg(c),a.resolving=!1,Qm()}}return i}function ew(e){if(typeof e=="string")return e.charCodeAt(0)||0;let n=e.hasOwnProperty(ao)?e[ao]:void 0;return typeof n=="number"?n>=0?n&tv:tw:n}function xg(e,n,t){let r=1<<e;return!!(t[n+(e>>nv)]&r)}function Ag(e,n){return!(e&B.Self)&&!(e&B.Host&&n)}var Cn=class{_tNode;_lView;constructor(n,t){this._tNode=n,this._lView=t}get(n,t,r){return sv(this._tNode,this._lView,n,Us(r),t)}};function tw(){return new Cn(_e(),Z())}function be(e){return vo(()=>{let n=e.prototype.constructor,t=n[hs]||Gl(n),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[hs]||Gl(o);if(i&&i!==t)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Gl(e){return hm(e)?()=>{let n=Gl(Se(e));return n&&n()}:fr(e)}function nw(e,n,t,r,o){let i=e,s=n;for(;i!==null&&s!==null&&s[L]&2048&&!Cs(s);){let a=av(i,s,t,r|B.Self,ft);if(a!==ft)return a;let c=i.parent;if(!c){let l=s[Am];if(l){let d=l.get(t,ft,r);if(d!==ft)return d}c=cv(s),s=s[br]}i=c}return o}function cv(e){let n=e[G],t=n.type;return t===2?n.declTNode:t===1?e[Fe]:null}function Lt(e){return X0(_e(),e)}function Rg(e,n=null,t=null,r){let o=lv(e,n,t,r);return o.resolveInjectorInitializers(),o}function lv(e,n=null,t=null,r,o=new Set){let i=[t||Ze,r0(e)];return r=r||(typeof e=="object"?void 0:Ne(e)),new uo(i,n||zs(),r||null,o)}var X=class e{static THROW_IF_NOT_FOUND=In;static NULL=new vs;static create(n,t){if(Array.isArray(n))return Rg({name:""},t,n,"");{let r=n.name??"";return Rg({name:r},n.parent,n.providers,r)}}static \u0275prov=T({token:e,providedIn:"any",factory:()=>R(wm)});static __NG_ELEMENT_ID__=-1};var rw=new x("");rw.__NG_ELEMENT_ID__=e=>{let n=_e();if(n===null)throw new A(204,!1);if(n.type&2)return n.value;if(e&B.Optional)return null;throw new A(204,!1)};var uv=!1,kn=(()=>{class e{static __NG_ELEMENT_ID__=ow;static __NG_ENV_ID__=t=>t}return e})(),Wl=class extends kn{_lView;constructor(n){super(),this._lView=n}onDestroy(n){let t=this._lView;return Er(t)?(n(),()=>{}):(Bm(t,n),()=>E0(t,n))}};function ow(){return new Wl(Z())}var yr=class{},dv=new x("",{providedIn:"root",factory:()=>!1});var fv=new x(""),hv=new x(""),jt=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new de(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let t=this.taskId++;return this.pendingTasks.add(t),t}has(t){return this.pendingTasks.has(t)}remove(t){this.pendingTasks.delete(t),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=T({token:e,providedIn:"root",factory:()=>new e})}return e})();var ql=class extends K{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(n=!1){super(),this.__isAsync=n,Nu()&&(this.destroyRef=m(kn,{optional:!0})??void 0,this.pendingTasks=m(jt,{optional:!0})??void 0)}emit(n){let t=q(null);try{super.next(n)}finally{q(t)}}subscribe(n,t,r){let o=n,i=t||(()=>null),s=r;if(n&&typeof n=="object"){let c=n;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return n instanceof le&&n.add(a),a}wrapInTimeout(n){return t=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{n(t)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},oe=ql;function _s(...e){}function pv(e){let n,t;function r(){e=_s;try{t!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(t),n!==void 0&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(t=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Ng(e){return queueMicrotask(()=>e()),()=>{e=_s}}var qu="isAngularZone",Ms=qu+"_ID",iw=0,g=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new oe(!1);onMicrotaskEmpty=new oe(!1);onStable=new oe(!1);onError=new oe(!1);constructor(n){let{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=uv}=n;if(typeof Zone>"u")throw new A(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,cw(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(qu)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new A(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new A(909,!1)}run(n,t,r){return this._inner.run(n,t,r)}runTask(n,t,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,n,sw,_s,_s);try{return i.runTask(s,t,r)}finally{i.cancelTask(s)}}runGuarded(n,t,r){return this._inner.runGuarded(n,t,r)}runOutsideAngular(n){return this._outer.run(n)}},sw={};function Zu(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function aw(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function n(){pv(()=>{e.callbackScheduled=!1,Zl(e),e.isCheckStableRunning=!0,Zu(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),Zl(e)}function cw(e){let n=()=>{aw(e)},t=iw++;e._inner=e._inner.fork({name:"angular",properties:{[qu]:!0,[Ms]:t,[Ms+t]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(lw(c))return r.invokeTask(i,s,a,c);try{return Og(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&n(),kg(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return Og(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!uw(c)&&n(),kg(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Zl(e),Zu(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Zl(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Og(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function kg(e){e._nesting--,Zu(e)}var Ss=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new oe;onMicrotaskEmpty=new oe;onStable=new oe;onError=new oe;run(n,t,r){return n.apply(t,r)}runGuarded(n,t,r){return n.apply(t,r)}runOutsideAngular(n){return n()}runTask(n,t,r,o){return n.apply(t,r)}};function lw(e){return gv(e,"__ignore_ng_zone__")}function uw(e){return gv(e,"__scheduler_tick__")}function gv(e,n){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[n]===!0}function dw(e="zone.js",n){return e==="noop"?new Ss:e==="zone.js"?new g(n):e}var mt=class{_console=console;handleError(n){this._console.error("ERROR",n)}},fw=new x("",{providedIn:"root",factory:()=>{let e=m(g),n=m(mt);return t=>e.runOutsideAngular(()=>n.handleError(t))}});function Fg(e,n){return dm(e,n)}function hw(e){return dm(um,e)}var mv=(Fg.required=hw,Fg);function pw(){return _r(_e(),Z())}function _r(e,n){return new v(It(e,n))}var v=(()=>{class e{nativeElement;constructor(t){this.nativeElement=t}static __NG_ELEMENT_ID__=pw}return e})();function gw(e){return e instanceof v?e.nativeElement:e}function mw(e){return typeof e=="function"&&e[We]!==void 0}function Io(e,n){let t=ml(e,n?.equal),r=t[We];return t.set=o=>no(r,o),t.update=o=>vl(r,o),t.asReadonly=vw.bind(t),t}function vw(){let e=this[We];if(e.readonlyFn===void 0){let n=()=>this();n[We]=e,e.readonlyFn=n}return e.readonlyFn}function vv(e){return mw(e)&&typeof e.set=="function"}function yw(){return this._results[Symbol.iterator]()}var Yl=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new K}constructor(n=!1){this._emitDistinctChangesOnly=n}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,t){return this._results.reduce(n,t)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,t){this.dirty=!1;let r=Qb(n);(this._changesDetected=!Yb(this._results,r,t))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(n){this._onDirty=n}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=yw};function yv(e){return(e.flags&128)===128}var Dv=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Dv||{}),Iv=new Map,Dw=0;function Iw(){return Dw++}function Cw(e){Iv.set(e[Gs],e)}function Ql(e){Iv.delete(e[Gs])}var Pg="__ngContext__";function Mr(e,n){Kt(n)?(e[Pg]=n[Gs],Cw(n)):e[Pg]=n}function Cv(e){return wv(e[ho])}function bv(e){return wv(e[rt])}function wv(e){for(;e!==null&&!Pt(e);)e=e[rt];return e}var Kl;function Ev(e){Kl=e}function _v(){if(Kl!==void 0)return Kl;if(typeof document<"u")return document;throw new A(210,!1)}var Yu=new x("",{providedIn:"root",factory:()=>bw}),bw="ng",Qu=new x(""),Sr=new x("",{providedIn:"platform",factory:()=>"unknown"});var Ku=new x("",{providedIn:"root",factory:()=>_v().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var ww="h",Ew="b";var Mv=!1,_w=new x("",{providedIn:"root",factory:()=>Mv});var Xu=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Xu||{}),Tr=new x(""),Lg=new Set;function Co(e){Lg.has(e)||(Lg.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Sv=(()=>{class e{view;node;constructor(t,r){this.view=t,this.node=r}static __NG_ELEMENT_ID__=Mw}return e})();function Mw(){return new Sv(Z(),_e())}var cr=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(cr||{}),Tv=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=T({token:e,providedIn:"root",factory:()=>new e})}return e})(),Sw=[cr.EarlyRead,cr.Write,cr.MixedReadWrite,cr.Read],Tw=(()=>{class e{ngZone=m(g);scheduler=m(yr);errorHandler=m(mt,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){m(Tr,{optional:!0})}execute(){let t=this.sequences.size>0;t&&re(16),this.executing=!0;for(let r of Sw)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),t&&re(17)}register(t){let{view:r}=t;r!==void 0?((r[ur]??=[]).push(t),Do(r),r[L]|=8192):this.executing?this.deferredRegistrations.add(t):this.addSequence(t)}addSequence(t){this.sequences.add(t),this.scheduler.notify(7)}unregister(t){this.executing&&this.sequences.has(t)?(t.erroredOrDestroyed=!0,t.pipelinedValue=void 0,t.once=!0):(this.sequences.delete(t),this.deferredRegistrations.delete(t))}maybeTrace(t,r){return r?r.run(Xu.AFTER_NEXT_RENDER,t):t()}static \u0275prov=T({token:e,providedIn:"root",factory:()=>new e})}return e})(),Xl=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(n,t,r,o,i,s=null){this.impl=n,this.hooks=t,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let n=this.view?.[ur];n&&(this.view[ur]=n.filter(t=>t!==this))}};function Ju(e,n){!n?.injector&&h0(Ju);let t=n?.injector??m(X);return Co("NgAfterNextRender"),Aw(e,t,n,!0)}function xw(e,n){if(e instanceof Function){let t=[void 0,void 0,void 0,void 0];return t[n]=e,t}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Aw(e,n,t,r){let o=n.get(Tv);o.impl??=n.get(Tw);let i=n.get(Tr,null,{optional:!0}),s=t?.phase??cr.MixedReadWrite,a=t?.manualCleanup!==!0?n.get(kn):null,c=n.get(Sv,null,{optional:!0}),l=new Xl(o.impl,xw(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(l),l}var Rw=(e,n,t,r)=>{};function Nw(e,n,t,r){Rw(e,n,t,r)}var Ow=()=>null;function xv(e,n,t=!1){return Ow(e,n,t)}function Av(e,n){let t=e.contentQueries;if(t!==null){let r=q(null);try{for(let o=0;o<t.length;o+=2){let i=t[o],s=t[o+1];if(s!==-1){let a=e.data[s];Hu(i),a.contentQueries(2,n[s],s)}}}finally{q(r)}}}function Jl(e,n,t){Hu(0);let r=q(null);try{n(e,t)}finally{q(r)}}function ed(e,n,t){if(Ou(n)){let r=q(null);try{let o=n.directiveStart,i=n.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=t[s];a.contentQueries(1,c,s)}}}finally{q(r)}}}var vt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(vt||{}),rs;function kw(){if(rs===void 0&&(rs=null,Ve.trustedTypes))try{rs=Ve.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return rs}function Xs(e){return kw()?.createHTML(e)||e}var os;function Rv(){if(os===void 0&&(os=null,Ve.trustedTypes))try{os=Ve.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return os}function jg(e){return Rv()?.createHTML(e)||e}function Vg(e){return Rv()?.createScriptURL(e)||e}var Ts=class{changingThisBreaksApplicationSecurity;constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${cm})`}};function bo(e){return e instanceof Ts?e.changingThisBreaksApplicationSecurity:e}function td(e,n){let t=Fw(e);if(t!=null&&t!==n){if(t==="ResourceURL"&&n==="URL")return!0;throw new Error(`Required a safe ${n}, got a ${t} (see ${cm})`)}return t===n}function Fw(e){return e instanceof Ts&&e.getTypeName()||null}function Pw(e){let n=new tu(e);return Lw()?new eu(n):n}var eu=class{inertDocumentHelper;constructor(n){this.inertDocumentHelper=n}getInertBodyElement(n){n="<body><remove></remove>"+n;try{let t=new window.DOMParser().parseFromString(Xs(n),"text/html").body;return t===null?this.inertDocumentHelper.getInertBodyElement(n):(t.firstChild?.remove(),t)}catch{return null}}},tu=class{defaultDoc;inertDocument;constructor(n){this.defaultDoc=n,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(n){let t=this.inertDocument.createElement("template");return t.innerHTML=Xs(n),t}};function Lw(){try{return!!new window.DOMParser().parseFromString(Xs(""),"text/html")}catch{return!1}}var jw=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Nv(e){return e=String(e),e.match(jw)?e:"unsafe:"+e}function Vt(e){let n={};for(let t of e.split(","))n[t]=!0;return n}function wo(...e){let n={};for(let t of e)for(let r in t)t.hasOwnProperty(r)&&(n[r]=!0);return n}var Ov=Vt("area,br,col,hr,img,wbr"),kv=Vt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Fv=Vt("rp,rt"),Vw=wo(Fv,kv),Bw=wo(kv,Vt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Uw=wo(Fv,Vt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Bg=wo(Ov,Bw,Uw,Vw),Pv=Vt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Hw=Vt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),$w=Vt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),zw=wo(Pv,Hw,$w),Gw=Vt("script,style,template"),nu=class{sanitizedSomething=!1;buf=[];sanitizeChildren(n){let t=n.firstChild,r=!0,o=[];for(;t;){if(t.nodeType===Node.ELEMENT_NODE?r=this.startElement(t):t.nodeType===Node.TEXT_NODE?this.chars(t.nodeValue):this.sanitizedSomething=!0,r&&t.firstChild){o.push(t),t=Zw(t);continue}for(;t;){t.nodeType===Node.ELEMENT_NODE&&this.endElement(t);let i=qw(t);if(i){t=i;break}t=o.pop()}}return this.buf.join("")}startElement(n){let t=Ug(n).toLowerCase();if(!Bg.hasOwnProperty(t))return this.sanitizedSomething=!0,!Gw.hasOwnProperty(t);this.buf.push("<"),this.buf.push(t);let r=n.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!zw.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Pv[a]&&(c=Nv(c)),this.buf.push(" ",s,'="',Hg(c),'"')}return this.buf.push(">"),!0}endElement(n){let t=Ug(n).toLowerCase();Bg.hasOwnProperty(t)&&!Ov.hasOwnProperty(t)&&(this.buf.push("</"),this.buf.push(t),this.buf.push(">"))}chars(n){this.buf.push(Hg(n))}};function Ww(e,n){return(e.compareDocumentPosition(n)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function qw(e){let n=e.nextSibling;if(n&&e!==n.previousSibling)throw Lv(n);return n}function Zw(e){let n=e.firstChild;if(n&&Ww(e,n))throw Lv(n);return n}function Ug(e){let n=e.nodeName;return typeof n=="string"?n:"FORM"}function Lv(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var Yw=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Qw=/([^\#-~ |!])/g;function Hg(e){return e.replace(/&/g,"&amp;").replace(Yw,function(n){let t=n.charCodeAt(0),r=n.charCodeAt(1);return"&#"+((t-55296)*1024+(r-56320)+65536)+";"}).replace(Qw,function(n){return"&#"+n.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var is;function Kw(e,n){let t=null;try{is=is||Pw(e);let r=n?String(n):"";t=is.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=t.innerHTML,t=is.getInertBodyElement(r)}while(r!==i);let a=new nu().sanitizeChildren($g(t)||t);return Xs(a)}finally{if(t){let r=$g(t)||t;for(;r.firstChild;)r.firstChild.remove()}}}function $g(e){return"content"in e&&Xw(e)?e.content:null}function Xw(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Js=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Js||{});function bB(e){let n=nd();return n?jg(n.sanitize(Js.HTML,e)||""):td(e,"HTML")?jg(bo(e)):Kw(_v(),bn(e))}function Jw(e){let n=nd();return n?n.sanitize(Js.URL,e)||"":td(e,"URL")?bo(e):Nv(bn(e))}function eE(e){let n=nd();if(n)return Vg(n.sanitize(Js.RESOURCE_URL,e)||"");if(td(e,"ResourceURL"))return Vg(bo(e));throw new A(904,!1)}function tE(e,n){return n==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||n==="href"&&(e==="base"||e==="link")?eE:Jw}function jv(e,n,t){return tE(n,t)(e)}function nd(){let e=Z();return e&&e[Nt].sanitizer}var nE=/^>|^->|<!--|-->|--!>|<!-$/g,rE=/(<|>)/g,oE="\u200B$1\u200B";function iE(e){return e.replace(nE,n=>n.replace(rE,oE))}function Vv(e){return e instanceof Function?e():e}function sE(e,n,t){let r=e.length;for(;;){let o=e.indexOf(n,t);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=n.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}t=o+1}}var Bv="ng-template";function aE(e,n,t,r){let o=0;if(r){for(;o<n.length&&typeof n[o]=="string";o+=2)if(n[o]==="class"&&sE(n[o+1].toLowerCase(),t,0)!==-1)return!0}else if(rd(e))return!1;if(o=n.indexOf(1,o),o>-1){let i;for(;++o<n.length&&typeof(i=n[o])=="string";)if(i.toLowerCase()===t)return!0}return!1}function rd(e){return e.type===4&&e.value!==Bv}function cE(e,n,t){let r=e.type===4&&!t?Bv:e.value;return n===r}function lE(e,n,t){let r=4,o=e.attrs,i=o!==null?fE(o):0,s=!1;for(let a=0;a<n.length;a++){let c=n[a];if(typeof c=="number"){if(!s&&!nt(r)&&!nt(c))return!1;if(s&&nt(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!cE(e,c,t)||c===""&&n.length===1){if(nt(r))return!1;s=!0}}else if(r&8){if(o===null||!aE(e,o,c,t)){if(nt(r))return!1;s=!0}}else{let l=n[++a],d=uE(c,o,rd(e),t);if(d===-1){if(nt(r))return!1;s=!0;continue}if(l!==""){let h;if(d>i?h="":h=o[d+1].toLowerCase(),r&2&&l!==h){if(nt(r))return!1;s=!0}}}}return nt(r)||s}function nt(e){return(e&1)===0}function uE(e,n,t,r){if(n===null)return-1;let o=0;if(r||!t){let i=!1;for(;o<n.length;){let s=n[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=n[++o];for(;typeof a=="string";)a=n[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return hE(n,e)}function Uv(e,n,t=!1){for(let r=0;r<n.length;r++)if(lE(e,n[r],t))return!0;return!1}function dE(e){let n=e.attrs;if(n!=null){let t=n.indexOf(5);if((t&1)===0)return n[t+1]}return null}function fE(e){for(let n=0;n<e.length;n++){let t=e[n];if(Jm(t))return n}return e.length}function hE(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){let r=e[t];if(typeof r=="number")return-1;if(r===n)return t;t++}return-1}function pE(e,n){e:for(let t=0;t<n.length;t++){let r=n[t];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function zg(e,n){return e?":not("+n.trim()+")":n}function gE(e){let n=e[0],t=1,r=2,o="",i=!1;for(;t<e.length;){let s=e[t];if(typeof s=="string")if(r&2){let a=e[++t];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!nt(s)&&(n+=zg(i,o),o=""),r=s,i=i||!nt(r);t++}return o!==""&&(n+=zg(i,o)),n}function mE(e){return e.map(gE).join(",")}function vE(e){let n=[],t=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&n.push(i,e[++r]):o===8&&t.push(i);else{if(!nt(o))break;o=i}r++}return t.length&&n.push(1,...t),n}var Ct={};function yE(e,n){return e.createText(n)}function DE(e,n,t){e.setValue(n,t)}function IE(e,n){return e.createComment(iE(n))}function Hv(e,n,t){return e.createElement(n,t)}function xs(e,n,t,r,o){e.insertBefore(n,t,r,o)}function $v(e,n,t){e.appendChild(n,t)}function Gg(e,n,t,r,o){r!==null?xs(e,n,t,r,o):$v(e,n,t)}function CE(e,n,t){e.removeChild(null,n,t)}function bE(e,n,t){e.setAttribute(n,"style",t)}function wE(e,n,t){t===""?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}function zv(e,n,t){let{mergedAttrs:r,classes:o,styles:i}=t;r!==null&&W0(e,n,r),o!==null&&wE(e,n,o),i!==null&&bE(e,n,i)}function od(e,n,t,r,o,i,s,a,c,l,d){let h=Ke+r,p=h+o,f=EE(h,p),y=typeof l=="function"?l():l;return f[G]={type:e,blueprint:f,template:t,queries:null,viewQuery:a,declTNode:n,data:f.slice().fill(null,h),bindingStartIndex:h,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:y,incompleteFirstPass:!1,ssrId:d}}function EE(e,n){let t=[];for(let r=0;r<n;r++)t.push(r<e?null:Ct);return t}function _E(e){let n=e.tView;return n===null||n.incompleteFirstPass?e.tView=od(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}function id(e,n,t,r,o,i,s,a,c,l,d){let h=n.blueprint.slice();return h[Ft]=o,h[L]=r|4|128|8|64|1024,(l!==null||e&&e[L]&2048)&&(h[L]|=2048),jm(h),h[Ee]=h[br]=e,h[Oe]=t,h[Nt]=s||e&&e[Nt],h[ue]=a||e&&e[ue],h[pr]=c||e&&e[pr]||null,h[Fe]=i,h[Gs]=Iw(),h[fo]=d,h[Am]=l,h[Qe]=n.type==2?e[Qe]:h,h}function ME(e,n,t){let r=It(n,e),o=_E(t),i=e[Nt].rendererFactory,s=sd(e,id(e,o,null,Gv(t),r,n,null,i.createRenderer(r,t),null,null,null));return e[n.index]=s}function Gv(e){let n=16;return e.signals?n=4096:e.onPush&&(n=64),n}function Wv(e,n,t,r){if(t===0)return-1;let o=n.length;for(let i=0;i<t;i++)n.push(r),e.blueprint.push(r),e.data.push(null);return o}function sd(e,n){return e[ho]?e[wg][rt]=n:e[ho]=n,e[wg]=n,n}function ea(e=1){qv(fe(),Z(),On()+e,!1)}function qv(e,n,t,r){if(!r)if((n[L]&3)===3){let i=e.preOrderCheckHooks;i!==null&&ls(n,i,t)}else{let i=e.preOrderHooks;i!==null&&us(n,i,0,t)}Sn(t)}var ta=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(ta||{});function ru(e,n,t,r){let o=q(null);try{let[i,s,a]=e.inputs[t],c=null;(s&ta.SignalBased)!==0&&(c=n[i][We]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(n,r)),e.setInput!==null?e.setInput(n,c,r,t,i):Nm(n,c,i,r)}finally{q(o)}}function Zv(e,n,t,r,o){let i=On(),s=r&2;try{Sn(-1),s&&n.length>Ke&&qv(e,n,Ke,!1),re(s?2:0,o),t(r,o)}finally{Sn(i),re(s?3:1,o)}}function na(e,n,t){NE(e,n,t),(t.flags&64)===64&&OE(e,n,t)}function ad(e,n,t=It){let r=n.localNames;if(r!==null){let o=n.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?t(n,e):e[s];e[o++]=a}}}function SE(e,n,t,r){let i=r.get(_w,Mv)||t===vt.ShadowDom,s=e.selectRootElement(n,i);return TE(s),s}function TE(e){xE(e)}var xE=()=>null;function AE(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function cd(e,n,t,r,o,i,s,a){if(!a&&ud(n,e,t,r,o)){wr(n)&&RE(t,n.index);return}if(n.type&3){let c=It(n,t);r=AE(r),o=s!=null?s(o,n.value||"",r):o,i.setProperty(c,r,o)}else n.type&12}function RE(e,n){let t=gt(n,e);t[L]&16||(t[L]|=64)}function NE(e,n,t){let r=t.directiveStart,o=t.directiveEnd;wr(t)&&ME(n,t,e.data[r+t.componentOffset]),e.firstCreatePass||Es(t,n);let i=t.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=po(n,e,s,t);if(Mr(c,n),i!==null&&LE(n,s-r,c,a,t,i),ht(a)){let l=gt(t.index,n);l[Oe]=po(n,e,s,t)}}}function OE(e,n,t){let r=t.directiveStart,o=t.directiveEnd,i=t.index,s=L0();try{Sn(i);for(let a=r;a<o;a++){let c=e.data[a],l=n[a];Hl(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&kE(c,l)}}finally{Sn(-1),Hl(s)}}function kE(e,n){e.hostBindings!==null&&e.hostBindings(1,n)}function ld(e,n){let t=e.directiveRegistry,r=null;if(t)for(let o=0;o<t.length;o++){let i=t[o];Uv(n,i.selectors,!1)&&(r??=[],ht(i)?r.unshift(i):r.push(i))}return r}function FE(e,n,t,r,o,i){let s=It(e,n);PE(n[ue],s,i,e.value,t,r,o)}function PE(e,n,t,r,o,i,s){if(i==null)e.removeAttribute(n,o,t);else{let a=s==null?bn(i):s(i,r||"",o);e.setAttribute(n,o,a,t)}}function LE(e,n,t,r,o,i){let s=i[n];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];ru(r,t,c,l)}}function jE(e,n){let t=e[pr],r=t?t.get(mt,null):null;r&&r.handleError(n)}function ud(e,n,t,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],d=s[c+1],h=n.data[l];ru(h,t[l],d,o),a=!0}if(i)for(let c of i){let l=t[c],d=n.data[c];ru(d,l,r,o),a=!0}return a}function VE(e,n){let t=gt(n,e),r=t[G];BE(r,t);let o=t[Ft];o!==null&&t[fo]===null&&(t[fo]=xv(o,t[pr])),re(18),dd(r,t,t[Oe]),re(19,t[Oe])}function BE(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}function dd(e,n,t){$u(n);try{let r=e.viewQuery;r!==null&&Jl(1,r,t);let o=e.template;o!==null&&Zv(e,n,o,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[Ot]?.finishViewCreation(e),e.staticContentQueries&&Av(e,n),e.staticViewQueries&&Jl(2,e.viewQuery,t);let i=e.components;i!==null&&UE(n,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{n[L]&=-5,zu()}}function UE(e,n){for(let t=0;t<n.length;t++)VE(e,n[t])}function Yv(e,n,t,r){let o=q(null);try{let i=n.tView,a=e[L]&4096?4096:16,c=id(e,i,t,a,null,n,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[n.index];c[En]=l;let d=e[Ot];return d!==null&&(c[Ot]=d.createEmbeddedView(i)),dd(i,c,t),c}finally{q(o)}}function ou(e,n){return!n||n.firstChild===null||yv(e)}var HE;function fd(e,n){return HE(e,n)}var kt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(kt||{});function hd(e){return(e.flags&32)===32}function lr(e,n,t,r,o){if(r!=null){let i,s=!1;Pt(r)?i=r:Kt(r)&&(s=!0,r=r[Ft]);let a=pt(r);e===0&&t!==null?o==null?$v(n,t,a):xs(n,t,a,o||null,!0):e===1&&t!==null?xs(n,t,a,o||null,!0):e===2?CE(n,a,s):e===3&&n.destroyNode(a),i!=null&&XE(n,e,i,t,o)}}function $E(e,n){Qv(e,n),n[Ft]=null,n[Fe]=null}function zE(e,n,t,r,o,i){r[Ft]=o,r[Fe]=n,oa(e,r,t,1,o,i)}function Qv(e,n){n[Nt].changeDetectionScheduler?.notify(9),oa(e,n,n[ue],2,null,null)}function GE(e){let n=e[ho];if(!n)return _l(e[G],e);for(;n;){let t=null;if(Kt(n))t=n[ho];else{let r=n[je];r&&(t=r)}if(!t){for(;n&&!n[rt]&&n!==e;)Kt(n)&&_l(n[G],n),n=n[Ee];n===null&&(n=e),Kt(n)&&_l(n[G],n),t=n&&n[rt]}n=t}}function pd(e,n){let t=e[gr],r=t.indexOf(n);t.splice(r,1)}function Kv(e,n){if(Er(n))return;let t=n[ue];t.destroyNode&&oa(e,n,t,3,null,null),GE(n)}function _l(e,n){if(Er(n))return;let t=q(null);try{n[L]&=-129,n[L]|=256,n[Ye]&&hl(n[Ye]),qE(e,n),WE(e,n),n[G].type===1&&n[ue].destroy();let r=n[En];if(r!==null&&Pt(n[Ee])){r!==n[Ee]&&pd(r,n);let o=n[Ot];o!==null&&o.detachView(e)}Ql(n)}finally{q(t)}}function WE(e,n){let t=e.cleanup,r=n[ys];if(t!==null)for(let s=0;s<t.length-1;s+=2)if(typeof t[s]=="string"){let a=t[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[t[s+1]];t[s].call(a)}r!==null&&(n[ys]=null);let o=n[Qt];if(o!==null){n[Qt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=n[Ds];if(i!==null){n[Ds]=null;for(let s of i)s.destroy()}}function qE(e,n){let t;if(e!=null&&(t=e.destroyHooks)!=null)for(let r=0;r<t.length;r+=2){let o=n[t[r]];if(!(o instanceof Tn)){let i=t[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];re(4,a,c);try{c.call(a)}finally{re(5,a,c)}}else{re(4,o,i);try{i.call(o)}finally{re(5,o,i)}}}}}function Xv(e,n,t){return ZE(e,n.parent,t)}function ZE(e,n,t){let r=n;for(;r!==null&&r.type&168;)n=r,r=n.parent;if(r===null)return t[Ft];if(wr(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===vt.None||o===vt.Emulated)return null}return It(r,t)}function Jv(e,n,t){return QE(e,n,t)}function YE(e,n,t){return e.type&40?It(e,t):null}var QE=YE,Wg;function ra(e,n,t,r){let o=Xv(e,r,n),i=n[ue],s=r.parent||n[Fe],a=Jv(s,r,n);if(o!=null)if(Array.isArray(t))for(let c=0;c<t.length;c++)Gg(i,o,t[c],a,!1);else Gg(i,o,t,a,!1);Wg!==void 0&&Wg(i,r,n,t,o)}function so(e,n){if(n!==null){let t=n.type;if(t&3)return It(n,e);if(t&4)return iu(-1,e[n.index]);if(t&8){let r=n.child;if(r!==null)return so(e,r);{let o=e[n.index];return Pt(o)?iu(-1,o):pt(o)}}else{if(t&128)return so(e,n.next);if(t&32)return fd(n,e)()||pt(e[n.index]);{let r=ey(e,n);if(r!==null){if(Array.isArray(r))return r[0];let o=Mn(e[Qe]);return so(o,r)}else return so(e,n.next)}}}return null}function ey(e,n){if(n!==null){let r=e[Qe][Fe],o=n.projection;return r.projection[o]}return null}function iu(e,n){let t=je+e+1;if(t<n.length){let r=n[t],o=r[G].firstChild;if(o!==null)return so(r,o)}return n[_n]}function gd(e,n,t,r,o,i,s){for(;t!=null;){if(t.type===128){t=t.next;continue}let a=r[t.index],c=t.type;if(s&&n===0&&(a&&Mr(pt(a),r),t.flags|=2),!hd(t))if(c&8)gd(e,n,t.child,r,o,i,!1),lr(n,e,o,a,i);else if(c&32){let l=fd(t,r),d;for(;d=l();)lr(n,e,o,d,i);lr(n,e,o,a,i)}else c&16?ty(e,n,r,t,o,i):lr(n,e,o,a,i);t=s?t.projectionNext:t.next}}function oa(e,n,t,r,o,i){gd(t,r,e.firstChild,n,o,i,!1)}function KE(e,n,t){let r=n[ue],o=Xv(e,t,n),i=t.parent||n[Fe],s=Jv(i,t,n);ty(r,0,n,t,o,s)}function ty(e,n,t,r,o,i){let s=t[Qe],c=s[Fe].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let d=c[l];lr(n,e,o,d,i)}else{let l=c,d=s[Ee];yv(r)&&(l.flags|=128),gd(e,n,l,d,o,i,!0)}}function XE(e,n,t,r,o){let i=t[_n],s=pt(t);i!==s&&lr(n,e,r,i,o);for(let a=je;a<t.length;a++){let c=t[a];oa(c[G],c,e,n,r,i)}}function JE(e,n,t,r,o){if(n)o?e.addClass(t,r):e.removeClass(t,r);else{let i=r.indexOf("-")===-1?void 0:kt.DashCase;o==null?e.removeStyle(t,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=kt.Important),e.setStyle(t,r,o,i))}}function As(e,n,t,r,o=!1){for(;t!==null;){if(t.type===128){t=o?t.projectionNext:t.next;continue}let i=n[t.index];i!==null&&r.push(pt(i)),Pt(i)&&e_(i,r);let s=t.type;if(s&8)As(e,n,t.child,r);else if(s&32){let a=fd(t,n),c;for(;c=a();)r.push(c)}else if(s&16){let a=ey(n,t);if(Array.isArray(a))r.push(...a);else{let c=Mn(n[Qe]);As(c[G],c,a,r,!0)}}t=o?t.projectionNext:t.next}return r}function e_(e,n){for(let t=je;t<e.length;t++){let r=e[t],o=r[G].firstChild;o!==null&&As(r[G],r,o,n)}e[_n]!==e[Ft]&&n.push(e[_n])}function ny(e){if(e[ur]!==null){for(let n of e[ur])n.impl.addSequence(n);e[ur].length=0}}var ry=[];function t_(e){return e[Ye]??n_(e)}function n_(e){let n=ry.pop()??Object.create(o_);return n.lView=e,n}function r_(e){e.lView[Ye]!==e&&(e.lView=null,ry.push(e))}var o_=P(I({},eo),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Do(e.lView)},consumerOnSignalRead(){this.lView[Ye]=this}});function i_(e){let n=e[Ye]??Object.create(s_);return n.lView=e,n}var s_=P(I({},eo),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let n=Mn(e.lView);for(;n&&!oy(n[G]);)n=Mn(n);n&&Vm(n)},consumerOnSignalRead(){this.lView[Ye]=this}});function oy(e){return e.type!==2}function iy(e){if(e[Ds]===null)return;let n=!0;for(;n;){let t=!1;for(let r of e[Ds])r.dirty&&(t=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));n=t&&!!(e[L]&8192)}}var a_=100;function sy(e,n=!0,t=0){let o=e[Nt].rendererFactory,i=!1;i||o.begin?.();try{c_(e,t)}catch(s){throw n&&jE(e,s),s}finally{i||o.end?.()}}function c_(e,n){let t=$m();try{_g(!0),su(e,n);let r=0;for(;qs(e);){if(r===a_)throw new A(103,!1);r++,su(e,1)}}finally{_g(t)}}function l_(e,n,t,r){if(Er(n))return;let o=n[L],i=!1,s=!1;$u(n);let a=!0,c=null,l=null;i||(oy(e)?(l=t_(n),c=Ki(l)):al()===null?(a=!1,l=i_(n),c=Ki(l)):n[Ye]&&(hl(n[Ye]),n[Ye]=null));try{jm(n),k0(e.bindingStartIndex),t!==null&&Zv(e,n,t,2,r);let d=(o&3)===3;if(!i)if(d){let f=e.preOrderCheckHooks;f!==null&&ls(n,f,null)}else{let f=e.preOrderHooks;f!==null&&us(n,f,0,null),wl(n,0)}if(s||u_(n),iy(n),ay(n,0),e.contentQueries!==null&&Av(e,n),!i)if(d){let f=e.contentCheckHooks;f!==null&&ls(n,f)}else{let f=e.contentHooks;f!==null&&us(n,f,1),wl(n,1)}f_(e,n);let h=e.components;h!==null&&ly(n,h,0);let p=e.viewQuery;if(p!==null&&Jl(2,p,r),!i)if(d){let f=e.viewCheckHooks;f!==null&&ls(n,f)}else{let f=e.viewHooks;f!==null&&us(n,f,2),wl(n,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),n[bl]){for(let f of n[bl])f();n[bl]=null}i||(ny(n),n[L]&=-73)}catch(d){throw i||Do(n),d}finally{l!==null&&(dl(l,c),a&&r_(l)),zu()}}function ay(e,n){for(let t=Cv(e);t!==null;t=bv(t))for(let r=je;r<t.length;r++){let o=t[r];cy(o,n)}}function u_(e){for(let n=Cv(e);n!==null;n=bv(n)){if(!(n[L]&2))continue;let t=n[gr];for(let r=0;r<t.length;r++){let o=t[r];Vm(o)}}}function d_(e,n,t){re(18);let r=gt(n,e);cy(r,t),re(19,r[Oe])}function cy(e,n){ku(e)&&su(e,n)}function su(e,n){let r=e[G],o=e[L],i=e[Ye],s=!!(n===0&&o&16);if(s||=!!(o&64&&n===0),s||=!!(o&1024),s||=!!(i?.dirty&&fl(i)),s||=!1,i&&(i.dirty=!1),e[L]&=-9217,s)l_(r,e,r.template,e[Oe]);else if(o&8192){iy(e),ay(e,1);let a=r.components;a!==null&&ly(e,a,1),ny(e)}}function ly(e,n,t){for(let r=0;r<n.length;r++)d_(e,n[r],t)}function f_(e,n){let t=e.hostBindingOpCodes;if(t!==null)try{for(let r=0;r<t.length;r++){let o=t[r];if(o<0)Sn(~o);else{let i=o,s=t[++r],a=t[++r];P0(s,i);let c=n[i];re(24,c),a(2,c),re(25,c)}}}finally{Sn(-1)}}function md(e,n){let t=$m()?64:1088;for(e[Nt].changeDetectionScheduler?.notify(n);e;){e[L]|=t;let r=Mn(e);if(Cs(e)&&!r)return e;e=r}return null}function uy(e,n,t,r){return[e,!0,0,n,null,r,null,t,null,null]}function dy(e,n,t,r=!0){let o=n[G];if(h_(o,n,e,t),r){let s=iu(t,e),a=n[ue],c=a.parentNode(e[_n]);c!==null&&zE(o,e[Fe],a,n,c,s)}let i=n[fo];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function au(e,n){if(e.length<=je)return;let t=je+n,r=e[t];if(r){let o=r[En];o!==null&&o!==e&&pd(o,r),n>0&&(e[t-1][rt]=r[rt]);let i=ms(e,je+n);$E(r[G],r);let s=i[Ot];s!==null&&s.detachView(i[G]),r[Ee]=null,r[rt]=null,r[L]&=-129}return r}function h_(e,n,t,r){let o=je+r,i=t.length;r>0&&(t[o-1][rt]=n),r<i-je?(n[rt]=t[o],bm(t,je+r,n)):(t.push(n),n[rt]=null),n[Ee]=t;let s=n[En];s!==null&&t!==s&&fy(s,n);let a=n[Ot];a!==null&&a.insertView(e),Bl(n),n[L]|=128}function fy(e,n){let t=e[gr],r=n[Ee];if(Kt(r))e[L]|=2;else{let o=r[Ee][Qe];n[Qe]!==o&&(e[L]|=2)}t===null?e[gr]=[n]:t.push(n)}var go=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let n=this._lView,t=n[G];return As(t,n,t.firstChild,[])}constructor(n,t,r=!0){this._lView=n,this._cdRefInjectingView=t,this.notifyErrorHandler=r}get context(){return this._lView[Oe]}set context(n){this._lView[Oe]=n}get destroyed(){return Er(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let n=this._lView[Ee];if(Pt(n)){let t=n[Is],r=t?t.indexOf(this):-1;r>-1&&(au(n,r),ms(t,r))}this._attachedToViewContainer=!1}Kv(this._lView[G],this._lView)}onDestroy(n){Bm(this._lView,n)}markForCheck(){md(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[L]&=-129}reattach(){Bl(this._lView),this._lView[L]|=128}detectChanges(){this._lView[L]|=1024,sy(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new A(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let n=Cs(this._lView),t=this._lView[En];t!==null&&!n&&pd(t,this._lView),Qv(this._lView[G],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new A(902,!1);this._appRef=n;let t=Cs(this._lView),r=this._lView[En];r!==null&&!t&&fy(r,this._lView),Bl(this._lView)}};var yt=(()=>{class e{static __NG_ELEMENT_ID__=m_}return e})(),p_=yt,g_=class extends p_{_declarationLView;_declarationTContainer;elementRef;constructor(n,t,r){super(),this._declarationLView=n,this._declarationTContainer=t,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,t){return this.createEmbeddedViewImpl(n,t)}createEmbeddedViewImpl(n,t,r){let o=Yv(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:t,dehydratedView:r});return new go(o)}};function m_(){return vd(_e(),Z())}function vd(e,n){return e.type&4?new g_(n,e,_r(e,n)):null}function Eo(e,n,t,r,o){let i=e.data[n];if(i===null)i=v_(e,n,t,r,o),F0()&&(i.flags|=32);else if(i.type&64){i.type=t,i.value=r,i.attrs=o;let s=R0();i.injectorIndex=s===null?-1:s.injectorIndex}return Nn(i,!0),i}function v_(e,n,t,r,o){let i=Hm(),s=Bu(),a=s?i:i&&i.parent,c=e.data[n]=D_(e,a,t,n,r,o);return y_(e,c,i,s),c}function y_(e,n,t,r){e.firstChild===null&&(e.firstChild=n),t!==null&&(r?t.child==null&&n.parent!==null&&(t.child=n):t.next===null&&(t.next=n,n.prev=t))}function D_(e,n,t,r,o,i){let s=n?n.injectorIndex:-1,a=0;return Um()&&(a|=128),{type:t,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var _B=new RegExp(`^(\\d+)*(${Ew}|${ww})*(.*)`);var I_=()=>null;function cu(e,n){return I_(e,n)}var C_=class{},hy=class{},lu=class{resolveComponentFactory(n){throw Error(`No component factory found for ${Ne(n)}.`)}},ia=class{static NULL=new lu},Dr=class{},Jt=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>b_()}return e})();function b_(){let e=Z(),n=_e(),t=gt(n.index,e);return(Kt(t)?t:e)[ue]}var w_=(()=>{class e{static \u0275prov=T({token:e,providedIn:"root",factory:()=>null})}return e})();var Ml={},uu=class{injector;parentInjector;constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,r){r=Us(r);let o=this.injector.get(n,Ml,r);return o!==Ml||t===Ml?o:this.parentInjector.get(n,t,r)}};function du(e,n,t){let r=t?e.styles:null,o=t?e.classes:null,i=0;if(n!==null)for(let s=0;s<n.length;s++){let a=n[s];if(typeof a=="number")i=a;else if(i==1)o=mg(o,a);else if(i==2){let c=a,l=n[++s];r=mg(r,c+": "+l+";")}}t?e.styles=r:e.stylesWithoutHost=r,t?e.classes=o:e.classesWithoutHost=o}function u(e,n=B.Default){let t=Z();if(t===null)return R(e,n);let r=_e();return sv(r,t,Se(e),n)}function py(){let e="invalid";throw new Error(e)}function yd(e,n,t,r,o){let i=r===null?null:{"":-1},s=o(e,t);if(s!==null){let a,c=null,l=null,d=__(s);d===null?a=s:[a,c,l]=d,T_(e,n,t,a,i,c,l)}i!==null&&r!==null&&E_(t,r,i)}function E_(e,n,t){let r=e.localNames=[];for(let o=0;o<n.length;o+=2){let i=t[n[o+1]];if(i==null)throw new A(-301,!1);r.push(n[o],i)}}function __(e){let n=null,t=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&ht(a)&&(n=a),a.findHostDirectiveDefs!==null){t=!0;break}}if(!t)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,M_(s,r,i,o)),s===n&&(r??=[],r.push(s));return r!==null?(r.push(...n===null?e:e.slice(1)),[r,o,i]):null}function M_(e,n,t,r){let o=n.length;e.findHostDirectiveDefs(e,n,r),t.set(e,[o,n.length-1])}function S_(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function T_(e,n,t,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let f=r[p];!c&&ht(f)&&(c=!0,S_(e,t,p)),zl(Es(t,n),e,f.type)}k_(t,e.data.length,a);for(let p=0;p<a;p++){let f=r[p];f.providersResolver&&f.providersResolver(f)}let l=!1,d=!1,h=Wv(e,n,a,null);a>0&&(t.directiveToIndex=new Map);for(let p=0;p<a;p++){let f=r[p];if(t.mergedAttrs=vr(t.mergedAttrs,f.hostAttrs),A_(e,t,n,h,f),O_(h,f,o),s!==null&&s.has(f)){let[S,N]=s.get(f);t.directiveToIndex.set(f.type,[h,S+t.directiveStart,N+t.directiveStart])}else(i===null||!i.has(f))&&t.directiveToIndex.set(f.type,h);f.contentQueries!==null&&(t.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(t.flags|=64);let y=f.type.prototype;!l&&(y.ngOnChanges||y.ngOnInit||y.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),l=!0),!d&&(y.ngOnChanges||y.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),d=!0),h++}x_(e,t,i)}function x_(e,n,t){for(let r=n.directiveStart;r<n.directiveEnd;r++){let o=e.data[r];if(t===null||!t.has(o))qg(0,n,o,r),qg(1,n,o,r),Yg(n,r,!1);else{let i=t.get(o);Zg(0,n,i,r),Zg(1,n,i,r),Yg(n,r,!0)}}}function qg(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=n.inputs??={}:s=n.outputs??={},s[i]??=[],s[i].push(r),gy(n,i)}}function Zg(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=n.hostDirectiveInputs??={}:a=n.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),gy(n,s)}}function gy(e,n){n==="class"?e.flags|=8:n==="style"&&(e.flags|=16)}function Yg(e,n,t){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!t&&o===null||t&&i===null||rd(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!t&&o.hasOwnProperty(c)){let l=o[c];for(let d of l)if(d===n){s??=[],s.push(c,r[a+1]);break}}else if(t&&i.hasOwnProperty(c)){let l=i[c];for(let d=0;d<l.length;d+=2)if(l[d]===n){s??=[],s.push(l[d+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function A_(e,n,t,r,o){e.data[r]=o;let i=o.factory||(o.factory=fr(o.type,!0)),s=new Tn(i,ht(o),u);e.blueprint[r]=s,t[r]=s,R_(e,n,r,Wv(e,t,o.hostVars,Ct),o)}function R_(e,n,t,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~n.index;N_(s)!=a&&s.push(a),s.push(t,r,i)}}function N_(e){let n=e.length;for(;n>0;){let t=e[--n];if(typeof t=="number"&&t<0)return t}return 0}function O_(e,n,t){if(t){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)t[n.exportAs[r]]=e;ht(n)&&(t[""]=e)}}function k_(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}function my(e,n,t,r,o,i,s,a){let c=n.consts,l=mr(c,s),d=Eo(n,e,2,r,l);return i&&yd(n,t,d,mr(c,a),o),d.mergedAttrs=vr(d.mergedAttrs,d.attrs),d.attrs!==null&&du(d,d.attrs,!1),d.mergedAttrs!==null&&du(d,d.mergedAttrs,!0),n.queries!==null&&n.queries.elementStart(n,d),d}function vy(e,n){Gu(e,n),Ou(n)&&e.queries.elementEnd(n)}var Rs=class extends ia{ngModule;constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){let t=Xt(n);return new xn(t,this.ngModule)}};function F_(e){return Object.keys(e).map(n=>{let[t,r,o]=e[n],i={propName:t,templateName:n,isSignal:(r&ta.SignalBased)!==0};return o&&(i.transform=o),i})}function P_(e){return Object.keys(e).map(n=>({propName:e[n],templateName:n}))}function L_(e,n,t){let r=n instanceof ie?n:n?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new uu(t,r):t}function j_(e){let n=e.get(Dr,null);if(n===null)throw new A(407,!1);let t=e.get(w_,null),r=e.get(yr,null);return{rendererFactory:n,sanitizer:t,changeDetectionScheduler:r}}function V_(e,n){let t=(e.selectors[0][0]||"div").toLowerCase();return Hv(n,t,t==="svg"?D0:t==="math"?I0:null)}var xn=class extends hy{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=F_(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=P_(this.componentDef.outputs),this.cachedOutputs}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=mE(n.selectors),this.ngContentSelectors=n.ngContentSelectors??[],this.isBoundToModule=!!t}create(n,t,r,o){re(22);let i=q(null);try{let s=this.componentDef,a=r?["ng-version","19.2.14"]:vE(this.componentDef.selectors[0]),c=od(0,null,null,1,0,null,null,null,null,[a],null),l=L_(s,o||this.ngModule,n),d=j_(l),h=d.rendererFactory.createRenderer(null,s),p=r?SE(h,r,s.encapsulation,l):V_(s,h),f=id(null,c,null,512|Gv(s),null,null,d,h,l,null,xv(p,l,!0));f[Ke]=p,$u(f);let y=null;try{let S=my(Ke,c,f,"#host",()=>[this.componentDef],!0,0);p&&(zv(h,p,S),Mr(p,f)),na(c,f,S),ed(c,S,f),vy(c,S),t!==void 0&&B_(S,this.ngContentSelectors,t),y=gt(S.index,f),f[Oe]=y[Oe],dd(c,f,null)}catch(S){throw y!==null&&Ql(y),Ql(f),S}finally{re(23),zu()}return new fu(this.componentType,f)}finally{q(i)}}},fu=class extends C_{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(n,t){super(),this._rootLView=t,this._tNode=Lm(t[G],Ke),this.location=_r(this._tNode,t),this.instance=gt(this._tNode.index,t)[Oe],this.hostView=this.changeDetectorRef=new go(t,void 0,!1),this.componentType=n}setInput(n,t){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;let o=this._rootLView,i=ud(r,o[G],o,n,t);this.previousInputValues.set(n,t);let s=gt(r.index,o);md(s,1)}get injector(){return new Cn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}};function B_(e,n,t){let r=e.projection=[];for(let o=0;o<n.length;o++){let i=t[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Ue=(()=>{class e{static __NG_ELEMENT_ID__=U_}return e})();function U_(){let e=_e();return Dy(e,Z())}var H_=Ue,yy=class extends H_{_lContainer;_hostTNode;_hostLView;constructor(n,t,r){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=r}get element(){return _r(this._hostTNode,this._hostLView)}get injector(){return new Cn(this._hostTNode,this._hostLView)}get parentInjector(){let n=Wu(this._hostTNode,this._hostLView);if(ev(n)){let t=ws(n,this._hostLView),r=bs(n),o=t[G].data[r+8];return new Cn(o,t)}else return new Cn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){let t=Qg(this._lContainer);return t!==null&&t[n]||null}get length(){return this._lContainer.length-je}createEmbeddedView(n,t,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=cu(this._lContainer,n.ssrId),a=n.createEmbeddedViewImpl(t||{},i,s);return this.insertImpl(a,o,ou(this._hostTNode,s)),a}createComponent(n,t,r,o,i){let s=n&&!g0(n),a;if(s)a=t;else{let y=t||{};a=y.index,r=y.injector,o=y.projectableNodes,i=y.environmentInjector||y.ngModuleRef}let c=s?n:new xn(Xt(n)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let S=(s?l:this.parentInjector).get(ie,null);S&&(i=S)}let d=Xt(c.componentType??{}),h=cu(this._lContainer,d?.id??null),p=h?.firstChild??null,f=c.create(l,o,p,i);return this.insertImpl(f.hostView,a,ou(this._hostTNode,h)),f}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,r){let o=n._lView;if(b0(o)){let a=this.indexOf(n);if(a!==-1)this.detach(a);else{let c=o[Ee],l=new yy(c,c[Fe],c[Ee]);l.detach(l.indexOf(n))}}let i=this._adjustIndex(t),s=this._lContainer;return dy(s,o,i,r),n.attachToViewContainerRef(),bm(Sl(s),i,n),n}move(n,t){return this.insert(n,t)}indexOf(n){let t=Qg(this._lContainer);return t!==null?t.indexOf(n):-1}remove(n){let t=this._adjustIndex(n,-1),r=au(this._lContainer,t);r&&(ms(Sl(this._lContainer),t),Kv(r[G],r))}detach(n){let t=this._adjustIndex(n,-1),r=au(this._lContainer,t);return r&&ms(Sl(this._lContainer),t)!=null?new go(r):null}_adjustIndex(n,t=0){return n??this.length+t}};function Qg(e){return e[Is]}function Sl(e){return e[Is]||(e[Is]=[])}function Dy(e,n){let t,r=n[e.index];return Pt(r)?t=r:(t=uy(r,n,null,e),n[e.index]=t,sd(n,t)),z_(t,n,e,r),new yy(t,e,n)}function $_(e,n){let t=e[ue],r=t.createComment(""),o=It(n,e),i=t.parentNode(o);return xs(t,i,r,t.nextSibling(o),!1),r}var z_=q_,G_=()=>!1;function W_(e,n,t){return G_(e,n,t)}function q_(e,n,t,r){if(e[_n])return;let o;t.type&8?o=pt(r):o=$_(n,t),e[_n]=o}var hu=class e{queryList;matches=null;constructor(n){this.queryList=n}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},pu=class e{queries;constructor(n=[]){this.queries=n}createEmbeddedView(n){let t=n.queries;if(t!==null){let r=n.contentQueries!==null?n.contentQueries[0]:t.length,o=[];for(let i=0;i<r;i++){let s=t.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}finishViewCreation(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let t=0;t<this.queries.length;t++)Dd(n,t).matches!==null&&this.queries[t].setDirty()}},Ns=class{flags;read;predicate;constructor(n,t,r=null){this.flags=t,this.read=r,typeof n=="string"?this.predicate=tM(n):this.predicate=n}},gu=class e{queries;constructor(n=[]){this.queries=n}elementStart(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(n,t)}elementEnd(n){for(let t=0;t<this.queries.length;t++)this.queries[t].elementEnd(n)}embeddedTView(n){let t=null;for(let r=0;r<this.length;r++){let o=t!==null?t.length:0,i=this.getByIndex(r).embeddedTView(n,o);i&&(i.indexInDeclarationView=r,t!==null?t.push(i):t=[i])}return t!==null?new e(t):null}template(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].template(n,t)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}},mu=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(n,t=-1){this.metadata=n,this._declarationNodeIndex=t}elementStart(n,t){this.isApplyingToNode(t)&&this.matchTNode(n,t)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,t){this.elementStart(n,t)}embeddedTView(n,t){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,t),new e(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let t=this._declarationNodeIndex,r=n.parent;for(;r!==null&&r.type&8&&r.index!==t;)r=r.parent;return t===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(n,t){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(n,t,Z_(t,i)),this.matchTNodeWithReadOption(n,t,ds(t,n,i,!1,!1))}else r===yt?t.type&4&&this.matchTNodeWithReadOption(n,t,-1):this.matchTNodeWithReadOption(n,t,ds(t,n,r,!1,!1))}matchTNodeWithReadOption(n,t,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===v||o===Ue||o===yt&&t.type&4)this.addMatch(t.index,-2);else{let i=ds(t,n,o,!1,!1);i!==null&&this.addMatch(t.index,i)}else this.addMatch(t.index,r)}}addMatch(n,t){this.matches===null?this.matches=[n,t]:this.matches.push(n,t)}};function Z_(e,n){let t=e.localNames;if(t!==null){for(let r=0;r<t.length;r+=2)if(t[r]===n)return t[r+1]}return null}function Y_(e,n){return e.type&11?_r(e,n):e.type&4?vd(e,n):null}function Q_(e,n,t,r){return t===-1?Y_(n,e):t===-2?K_(e,n,r):po(e,e[G],t,n)}function K_(e,n,t){if(t===v)return _r(n,e);if(t===yt)return vd(n,e);if(t===Ue)return Dy(n,e)}function Iy(e,n,t,r){let o=n[Ot].queries[r];if(o.matches===null){let i=e.data,s=t.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let d=i[l];a.push(Q_(n,d,s[c+1],t.metadata.read))}}o.matches=a}return o.matches}function vu(e,n,t,r){let o=e.queries.getByIndex(t),i=o.matches;if(i!==null){let s=Iy(e,n,o,t);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],d=n[-c];for(let h=je;h<d.length;h++){let p=d[h];p[En]===p[Ee]&&vu(p[G],p,l,r)}if(d[gr]!==null){let h=d[gr];for(let p=0;p<h.length;p++){let f=h[p];vu(f[G],f,l,r)}}}}}return r}function X_(e,n){return e[Ot].queries[n].queryList}function Cy(e,n,t){let r=new Yl((t&4)===4);return _0(e,n,r,r.destroy),(n[Ot]??=new pu).queries.push(new hu(r))-1}function J_(e,n,t){let r=fe();return r.firstCreatePass&&(by(r,new Ns(e,n,t),-1),(n&2)===2&&(r.staticViewQueries=!0)),Cy(r,Z(),n)}function eM(e,n,t,r){let o=fe();if(o.firstCreatePass){let i=_e();by(o,new Ns(n,t,r),i.index),nM(o,e),(t&2)===2&&(o.staticContentQueries=!0)}return Cy(o,Z(),t)}function tM(e){return e.split(",").map(n=>n.trim())}function by(e,n,t){e.queries===null&&(e.queries=new gu),e.queries.track(new mu(n,t))}function nM(e,n){let t=e.contentQueries||(e.contentQueries=[]),r=t.length?t[t.length-1]:-1;n!==r&&t.push(e.queries.length-1,n)}function Dd(e,n){return e.queries.getByIndex(n)}function rM(e,n){let t=e[G],r=Dd(t,n);return r.crossesNgTemplate?vu(t,e,n,[]):Iy(t,e,r,n)}function oM(e){let n=[],t=new Map;function r(o){let i=t.get(o);if(!i){let s=e(o);t.set(o,i=s.then(cM))}return i}return Os.forEach((o,i)=>{let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(l=>{o.template=l}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&o.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(o.styleUrls?.length){let l=o.styles.length,d=o.styleUrls;o.styleUrls.forEach((h,p)=>{a.push(""),s.push(r(h).then(f=>{a[l+p]=f,d.splice(d.indexOf(h),1),d.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(l=>{a.push(l),o.styleUrl=void 0}));let c=Promise.all(s).then(()=>lM(i));n.push(c)}),sM(),Promise.all(n).then(()=>{})}var Os=new Map,iM=new Set;function sM(){let e=Os;return Os=new Map,e}function aM(){return Os.size===0}function cM(e){return typeof e=="string"?e:e.text()}function lM(e){iM.delete(e)}var Ir=class{},Id=class{};var ks=class extends Ir{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Rs(this);constructor(n,t,r,o=!0){super(),this.ngModuleType=n,this._parent=t;let i=_m(n);this._bootstrapComponents=Vv(i.bootstrap),this._r3Injector=lv(n,t,[{provide:Ir,useValue:this},{provide:ia,useValue:this.componentFactoryResolver},...r],Ne(n),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}},Fs=class extends Id{moduleType;constructor(n){super(),this.moduleType=n}create(n){return new ks(this.moduleType,n,[])}};function uM(e,n,t){return new ks(e,n,t,!1)}var yu=class extends Ir{injector;componentFactoryResolver=new Rs(this);instance=null;constructor(n){super();let t=new uo([...n.providers,{provide:Ir,useValue:this},{provide:ia,useValue:this.componentFactoryResolver}],n.parent||zs(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}};function _o(e,n,t=null){return new yu({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}var dM=(()=>{class e{_injector;cachedInjectors=new Map;constructor(t){this._injector=t}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){let r=Mm(!1,t.type),o=r.length>0?_o([r],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,o)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(let t of this.cachedInjectors.values())t!==null&&t.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=T({token:e,providedIn:"environment",factory:()=>new e(R(ie))})}return e})();function C(e){return vo(()=>{let n=wy(e),t=P(I({},n),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Dv.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:n.standalone?o=>o.get(dM).getOrCreateStandaloneInjector(t):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||vt.Emulated,styles:e.styles||Ze,_:null,schemas:e.schemas||null,tView:null,id:""});n.standalone&&Co("NgStandalone"),Ey(t);let r=e.dependencies;return t.directiveDefs=Kg(r,!1),t.pipeDefs=Kg(r,!0),t.id=mM(t),t})}function fM(e){return Xt(e)||t0(e)}function hM(e){return e!==null}function xe(e){return vo(()=>({type:e.type,bootstrap:e.bootstrap||Ze,declarations:e.declarations||Ze,imports:e.imports||Ze,exports:e.exports||Ze,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function pM(e,n){if(e==null)return wn;let t={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=ta.None,c=null),t[i]=[r,a,c],n[i]=s}return t}function gM(e){if(e==null)return wn;let n={};for(let t in e)e.hasOwnProperty(t)&&(n[e[t]]=t);return n}function V(e){return vo(()=>{let n=wy(e);return Ey(n),n})}function wy(e){let n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputConfig:e.inputs||wn,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Ze,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:pM(e.inputs,n),outputs:gM(e.outputs),debugInfo:null}}function Ey(e){e.features?.forEach(n=>n(e))}function Kg(e,n){if(!e)return null;let t=n?n0:fM;return()=>(typeof e=="function"?e():e).map(r=>t(r)).filter(hM)}function mM(e){let n=0,t=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,t,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))n=Math.imul(31,n)+i.charCodeAt(0)<<0;return n+=2147483648,"c"+n}function vM(e){return Object.getPrototypeOf(e.prototype).constructor}function ee(e){let n=vM(e.type),t=!0,r=[e];for(;n;){let o;if(ht(e))o=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new A(903,!1);o=n.\u0275dir}if(o){if(t){r.push(o);let s=e;s.inputs=Tl(e.inputs),s.declaredInputs=Tl(e.declaredInputs),s.outputs=Tl(e.outputs);let a=o.hostBindings;a&&bM(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&IM(e,c),l&&CM(e,l),yM(e,o),Ab(e.outputs,o.outputs),ht(o)&&o.data.animation){let d=e.data;d.animation=(d.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===ee&&(t=!1)}}n=Object.getPrototypeOf(n)}DM(r)}function yM(e,n){for(let t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;let r=n.inputs[t];r!==void 0&&(e.inputs[t]=r,e.declaredInputs[t]=n.declaredInputs[t])}}function DM(e){let n=0,t=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=n+=o.hostVars,o.hostAttrs=vr(o.hostAttrs,t=vr(t,o.hostAttrs))}}function Tl(e){return e===wn?{}:e===Ze?[]:e}function IM(e,n){let t=e.viewQuery;t?e.viewQuery=(r,o)=>{n(r,o),t(r,o)}:e.viewQuery=n}function CM(e,n){let t=e.contentQueries;t?e.contentQueries=(r,o,i)=>{n(r,o,i),t(r,o,i)}:e.contentQueries=n}function bM(e,n){let t=e.hostBindings;t?e.hostBindings=(r,o)=>{n(r,o),t(r,o)}:e.hostBindings=n}function _y(e){return EM(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function wM(e,n){if(Array.isArray(e))for(let t=0;t<e.length;t++)n(e[t]);else{let t=e[Symbol.iterator](),r;for(;!(r=t.next()).done;)n(r.value)}}function EM(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function _M(e,n,t){return e[n]=t}function MM(e,n){return e[n]}function An(e,n,t){let r=e[n];return Object.is(r,t)?!1:(e[n]=t,!0)}function SM(e,n,t,r){let o=An(e,n,t);return An(e,n+1,r)||o}function TM(e,n,t,r,o,i,s,a,c){let l=n.consts,d=Eo(n,e,4,s||null,a||null);Lu()&&yd(n,t,d,mr(l,c),ld),d.mergedAttrs=vr(d.mergedAttrs,d.attrs),Gu(n,d);let h=d.tView=od(2,d,r,o,i,n.directiveRegistry,n.pipeRegistry,null,n.schemas,l,null);return n.queries!==null&&(n.queries.template(n,d),h.queries=n.queries.embeddedTView(d)),d}function My(e,n,t,r,o,i,s,a,c,l){let d=t+Ke,h=n.firstCreatePass?TM(d,n,e,r,o,i,s,a,c):n.data[d];Nn(h,!1);let p=xM(n,e,h,t);Qs()&&ra(n,e,p,h),Mr(p,e);let f=uy(p,e,p,h);return e[d]=f,sd(e,f),W_(f,h,e),Ws(h)&&na(n,e,h),c!=null&&ad(e,h,l),h}function Mo(e,n,t,r,o,i,s,a){let c=Z(),l=fe(),d=mr(l.consts,i);return My(c,l,e,n,t,r,o,d,s,a),Mo}var xM=AM;function AM(e,n,t,r){return Ks(!0),n[ue].createComment("")}var Sy=(()=>{class e{log(t){console.log(t)}warn(t){console.warn(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Cd=new x(""),So=new x(""),sa=(()=>{class e{_ngZone;registry;_isZoneStable=!0;_callbacks=[];_taskTrackingZone=null;_destroyRef;constructor(t,r,o){this._ngZone=t,this.registry=r,Nu()&&(this._destroyRef=m(kn,{optional:!0})??void 0),bd||(RM(o),o.addToWindow(r)),this._watchAngularEvents(),t.run(()=>{this._taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){let t=this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),r=this._ngZone.runOutsideAngular(()=>this._ngZone.onStable.subscribe({next:()=>{g.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}}));this._destroyRef?.onDestroy(()=>{t.unsubscribe(),r.unsubscribe()})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let t=this._callbacks.pop();clearTimeout(t.timeoutId),t.doneCb()}});else{let t=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(t)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this._taskTrackingZone?this._taskTrackingZone.macroTasks.map(t=>({source:t.source,creationLocation:t.creationLocation,data:t.data})):[]}addCallback(t,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),t()},r)),this._callbacks.push({doneCb:t,timeoutId:i,updateCb:o})}whenStable(t,r,o){if(o&&!this._taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(t,r,o),this._runCallbacksIfReady()}registerApplication(t){this.registry.registerApplication(t,this)}unregisterApplication(t){this.registry.unregisterApplication(t)}findProviders(t,r,o){return[]}static \u0275fac=function(r){return new(r||e)(R(g),R(aa),R(So))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),aa=(()=>{class e{_applications=new Map;registerApplication(t,r){this._applications.set(t,r)}unregisterApplication(t){this._applications.delete(t)}unregisterAllApplications(){this._applications.clear()}getTestability(t){return this._applications.get(t)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(t,r=!0){return bd?.findTestabilityInTree(this,t,r)??null}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();function RM(e){bd=e}var bd,NM=(()=>{class e{static \u0275prov=T({token:e,providedIn:"root",factory:()=>new Du})}return e})(),Du=class{queuedEffectCount=0;queues=new Map;schedule(n){this.enqueue(n)}remove(n){let t=n.zone,r=this.queues.get(t);r.has(n)&&(r.delete(n),this.queuedEffectCount--)}enqueue(n){let t=n.zone;this.queues.has(t)||this.queues.set(t,new Set);let r=this.queues.get(t);r.has(n)||(this.queuedEffectCount++,r.add(n))}flush(){for(;this.queuedEffectCount>0;)for(let[n,t]of this.queues)n===null?this.flushQueue(t):n.run(()=>this.flushQueue(t))}flushQueue(n){for(let t of n)n.delete(t),this.queuedEffectCount--,t.run()}};function Fn(e){return!!e&&typeof e.then=="function"}function Ty(e){return!!e&&typeof e.subscribe=="function"}var ca=new x("");function wd(e){return Hs([{provide:ca,multi:!0,useValue:e}])}var xy=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((t,r)=>{this.resolve=t,this.reject=r});appInits=m(ca,{optional:!0})??[];injector=m(X);constructor(){}runInitializers(){if(this.initialized)return;let t=[];for(let o of this.appInits){let i=ke(this.injector,o);if(Fn(i))t.push(i);else if(Ty(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});t.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{r()}).catch(o=>{this.reject(o)}),t.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ed=new x("");function OM(){gl(()=>{throw new A(600,!1)})}function kM(e){return e.isBoundToModule}var FM=10;function Ay(e,n){return Array.isArray(n)?n.reduce(Ay,e):I(I({},e),n)}var Dt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=m(fw);afterRenderManager=m(Tv);zonelessEnabled=m(dv);rootEffectScheduler=m(NM);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new K;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=m(jt).hasPendingTasks.pipe(H(t=>!t));constructor(){m(Tr,{optional:!0})}whenStable(){let t;return new Promise(r=>{t=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{t.unsubscribe()})}_injector=m(ie);_rendererFactory=null;get injector(){return this._injector}bootstrap(t,r){return this.bootstrapImpl(t,r)}bootstrapImpl(t,r,o=X.NULL){re(10);let i=t instanceof hy;if(!this._injector.get(xy).done){let f="";throw new A(405,f)}let a;i?a=t:a=this._injector.get(ia).resolveComponentFactory(t),this.componentTypes.push(a.componentType);let c=kM(a)?void 0:this._injector.get(Ir),l=r||a.selector,d=a.create(o,[],l,c),h=d.location.nativeElement,p=d.injector.get(Cd,null);return p?.registerApplication(h),d.onDestroy(()=>{this.detachView(d.hostView),fs(this.components,d),p?.unregisterApplication(h)}),this._loadComponent(d),re(11,d),d}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){re(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Xu.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new A(101,!1);let t=q(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,q(t),this.afterTick.next(),re(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Dr,null,{optional:!0}));let t=0;for(;this.dirtyFlags!==0&&t++<FM;)re(14),this.synchronizeOnce(),re(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let t=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)PM(r,o,t,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:t})=>qs(t))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(t){let r=t;this._views.push(r),r.attachToAppRef(this)}detachView(t){let r=t;fs(this._views,r),r.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView),this.tick(),this.components.push(t),this._injector.get(Ed,[]).forEach(o=>o(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>fs(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new A(406,!1);let t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function fs(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function PM(e,n,t,r){if(!t&&!qs(e))return;sy(e,n,t&&!r?0:1)}function ot(e,n,t,r){let o=Z(),i=Zs();if(An(o,i,n)){let s=fe(),a=Ys();FE(a,o,e,n,t,r)}return ot}function Ry(e,n,t,r){return An(e,Zs(),t)?n+bn(t)+r:Ct}function LM(e,n,t,r,o,i){let s=O0(),a=SM(e,s,t,o);return zm(2),a?n+bn(t)+r+bn(o)+i:Ct}function ss(e,n){return e<<17|n<<2}function Rn(e){return e>>17&32767}function jM(e){return(e&2)==2}function VM(e,n){return e&131071|n<<17}function Iu(e){return e|2}function Cr(e){return(e&131068)>>2}function xl(e,n){return e&-131069|n<<2}function BM(e){return(e&1)===1}function Cu(e){return e|1}function UM(e,n,t,r,o,i){let s=i?n.classBindings:n.styleBindings,a=Rn(s),c=Cr(s);e[r]=t;let l=!1,d;if(Array.isArray(t)){let h=t;d=h[1],(d===null||yo(h,d)>0)&&(l=!0)}else d=t;if(o)if(c!==0){let p=Rn(e[a+1]);e[r+1]=ss(p,a),p!==0&&(e[p+1]=xl(e[p+1],r)),e[a+1]=VM(e[a+1],r)}else e[r+1]=ss(a,0),a!==0&&(e[a+1]=xl(e[a+1],r)),a=r;else e[r+1]=ss(c,0),a===0?a=r:e[c+1]=xl(e[c+1],r),c=r;l&&(e[r+1]=Iu(e[r+1])),Xg(e,d,r,!0),Xg(e,d,r,!1),HM(n,d,e,r,i),s=ss(a,c),i?n.classBindings=s:n.styleBindings=s}function HM(e,n,t,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof n=="string"&&yo(i,n)>=0&&(t[r+1]=Cu(t[r+1]))}function Xg(e,n,t,r){let o=e[t+1],i=n===null,s=r?Rn(o):Cr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];$M(c,n)&&(a=!0,e[s+1]=r?Cu(l):Iu(l)),s=r?Rn(l):Cr(l)}a&&(e[t+1]=r?Iu(o):Cu(o))}function $M(e,n){return e===null||n==null||(Array.isArray(e)?e[1]:e)===n?!0:Array.isArray(e)&&typeof n=="string"?yo(e,n)>=0:!1}function en(e,n,t){let r=Z(),o=Zs();if(An(r,o,n)){let i=fe(),s=Ys();cd(i,s,r,e,n,r[ue],t,!1)}return en}function Jg(e,n,t,r,o){ud(n,e,t,o?"class":"style",r)}function la(e,n){return zM(e,n,null,!0),la}function zM(e,n,t,r){let o=Z(),i=fe(),s=zm(2);if(i.firstUpdatePass&&WM(i,e,s,r),n!==Ct&&An(o,s,n)){let a=i.data[On()];KM(i,a,o,o[ue],e,o[s+1]=XM(n,t),r,s)}}function GM(e,n){return n>=e.expandoStartIndex}function WM(e,n,t,r){let o=e.data;if(o[t+1]===null){let i=o[On()],s=GM(e,t);JM(i,r)&&n===null&&!s&&(n=!1),n=qM(o,i,n,r),UM(o,i,n,t,s,r)}}function qM(e,n,t,r){let o=j0(e),i=r?n.residualClasses:n.residualStyles;if(o===null)(r?n.classBindings:n.styleBindings)===0&&(t=Al(null,e,n,t,r),t=mo(t,n.attrs,r),i=null);else{let s=n.directiveStylingLast;if(s===-1||e[s]!==o)if(t=Al(o,e,n,t,r),i===null){let c=ZM(e,n,r);c!==void 0&&Array.isArray(c)&&(c=Al(null,e,n,c[1],r),c=mo(c,n.attrs,r),YM(e,n,r,c))}else i=QM(e,n,r)}return i!==void 0&&(r?n.residualClasses=i:n.residualStyles=i),t}function ZM(e,n,t){let r=t?n.classBindings:n.styleBindings;if(Cr(r)!==0)return e[Rn(r)]}function YM(e,n,t,r){let o=t?n.classBindings:n.styleBindings;e[Rn(o)]=r}function QM(e,n,t){let r,o=n.directiveEnd;for(let i=1+n.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=mo(r,s,t)}return mo(r,n.attrs,t)}function Al(e,n,t,r,o){let i=null,s=t.directiveEnd,a=t.directiveStylingLast;for(a===-1?a=t.directiveStart:a++;a<s&&(i=n[a],r=mo(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(t.directiveStylingLast=a),r}function mo(e,n,t){let r=t?1:2,o=-1;if(n!==null)for(let i=0;i<n.length;i++){let s=n[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Jb(e,s,t?!0:n[++i]))}return e===void 0?null:e}function KM(e,n,t,r,o,i,s,a){if(!(n.type&3))return;let c=e.data,l=c[a+1],d=BM(l)?em(c,n,t,o,Cr(l),s):void 0;if(!Ps(d)){Ps(i)||jM(l)&&(i=em(c,null,t,o,a,s));let h=Pm(On(),t);JE(r,s,h,o,i)}}function em(e,n,t,r,o,i){let s=n===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),d=l?c[1]:c,h=d===null,p=t[o+1];p===Ct&&(p=h?Ze:void 0);let f=h?Il(p,r):d===r?p:void 0;if(l&&!Ps(f)&&(f=Il(c,r)),Ps(f)&&(a=f,s))return a;let y=e[o+1];o=s?Rn(y):Cr(y)}if(n!==null){let c=i?n.residualClasses:n.residualStyles;c!=null&&(a=Il(c,r))}return a}function Ps(e){return e!==void 0}function XM(e,n){return e==null||e===""||(typeof n=="string"?e=e+n:typeof e=="object"&&(e=Ne(bo(e)))),e}function JM(e,n){return(e.flags&(n?8:16))!==0}function xr(e,n,t,r){let o=Z(),i=fe(),s=Ke+e,a=o[ue],c=i.firstCreatePass?my(s,i,o,n,ld,Lu(),t,r):i.data[s],l=eS(i,o,c,a,n,e);o[s]=l;let d=Ws(c);return Nn(c,!0),zv(a,l,c),!hd(c)&&Qs()&&ra(i,o,l,c),(M0()===0||d)&&Mr(l,o),S0(),d&&(na(i,o,c),ed(i,c,o)),r!==null&&ad(o,c),xr}function Ar(){let e=_e();Bu()?Uu():(e=e.parent,Nn(e,!1));let n=e;x0(n)&&A0(),T0();let t=fe();return t.firstCreatePass&&vy(t,n),n.classesWithoutHost!=null&&z0(n)&&Jg(t,n,Z(),n.classesWithoutHost,!0),n.stylesWithoutHost!=null&&G0(n)&&Jg(t,n,Z(),n.stylesWithoutHost,!1),Ar}function _d(e,n,t,r){return xr(e,n,t,r),Ar(),_d}var eS=(e,n,t,r,o,i)=>(Ks(!0),Hv(r,o,U0()));function tS(e,n,t,r,o){let i=n.consts,s=mr(i,r),a=Eo(n,e,8,"ng-container",s);s!==null&&du(a,s,!0);let c=mr(i,o);return Lu()&&yd(n,t,a,c,ld),a.mergedAttrs=vr(a.mergedAttrs,a.attrs),n.queries!==null&&n.queries.elementStart(n,a),a}function ua(e,n,t){let r=Z(),o=fe(),i=e+Ke,s=o.firstCreatePass?tS(i,o,r,n,t):o.data[i];Nn(s,!0);let a=nS(o,r,s,e);return r[i]=a,Qs()&&ra(o,r,a,s),Mr(a,r),Ws(s)&&(na(o,r,s),ed(o,s,r)),t!=null&&ad(r,s),ua}function da(){let e=_e(),n=fe();return Bu()?Uu():(e=e.parent,Nn(e,!1)),n.firstCreatePass&&(Gu(n,e),Ou(e)&&n.queries.elementEnd(e)),da}function fa(e,n,t){return ua(e,n,t),da(),fa}var nS=(e,n,t,r)=>(Ks(!0),IE(n[ue],""));function Ny(){return Z()}var Ls="en-US";var rS=Ls;function oS(e){typeof e=="string"&&(rS=e.toLowerCase().replace(/_/g,"-"))}function tm(e,n,t){return function r(o){if(o===Function)return t;let i=wr(e)?gt(e.index,n):n;md(i,5);let s=n[Oe],a=nm(n,s,t,o),c=r.__ngNextListenerFn__;for(;c;)a=nm(n,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function nm(e,n,t,r){let o=q(null);try{return re(6,n,t),t(r)!==!1}catch(i){return iS(e,i),!1}finally{re(7,n,t),q(o)}}function iS(e,n){let t=e[pr],r=t?t.get(mt,null):null;r&&r.handleError(n)}function rm(e,n,t,r,o,i){let s=n[t],a=n[G],l=a.data[t].outputs[r],d=s[l],h=a.firstCreatePass?Pu(a):null,p=Fu(n),f=d.subscribe(i),y=p.length;p.push(i,f),h&&h.push(o,e.index,y,-(y+1))}function ye(e,n,t,r){let o=Z(),i=fe(),s=_e();return Oy(i,o,o[ue],s,e,n,r),ye}function sS(e,n,t,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===t&&o[i+1]===r){let a=n[ys],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Oy(e,n,t,r,o,i,s){let a=Ws(r),l=e.firstCreatePass?Pu(e):null,d=Fu(n),h=!0;if(r.type&3||s){let p=It(r,n),f=s?s(p):p,y=d.length,S=s?z=>s(pt(z[r.index])):r.index,N=null;if(!s&&a&&(N=sS(e,n,o,r.index)),N!==null){let z=N.__ngLastListenerFn__||N;z.__ngNextListenerFn__=i,N.__ngLastListenerFn__=i,h=!1}else{i=tm(r,n,i),Nw(n,f,o,i);let z=t.listen(f,o,i);d.push(i,z),l&&l.push(o,S,y,y+1)}}else i=tm(r,n,i);if(h){let p=r.outputs?.[o],f=r.hostDirectiveOutputs?.[o];if(f&&f.length)for(let y=0;y<f.length;y+=2){let S=f[y],N=f[y+1];rm(r,n,S,N,o,i)}if(p&&p.length)for(let y of p)rm(r,n,y,o,o,i)}}function To(e=1){return B0(e)}function aS(e,n){let t=null,r=dE(e);for(let o=0;o<n.length;o++){let i=n[o];if(i==="*"){t=o;continue}if(r===null?Uv(e,i,!0):pE(r,i))return o}return t}function E(e){let n=Z()[Qe][Fe];if(!n.projection){let t=e?e.length:1,r=n.projection=Kb(t,null),o=r.slice(),i=n.child;for(;i!==null;){if(i.type!==128){let s=e?aS(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function b(e,n=0,t,r,o,i){let s=Z(),a=fe(),c=r?e+1:null;c!==null&&My(s,a,c,r,o,i,null,t);let l=Eo(a,Ke+e,16,null,t||null);l.projection===null&&(l.projection=n),Uu();let h=!s[fo]||Um();s[Qe][Fe].projection[l.projection]===null&&c!==null?cS(s,a,c):h&&!hd(l)&&KE(a,s,l)}function cS(e,n,t){let r=Ke+t,o=n.data[r],i=e[r],s=cu(i,o.tView.ssrId),a=Yv(e,o,void 0,{dehydratedView:s});dy(i,a,0,ou(o,s))}function lS(e,n,t){return ky(e,"",n,"",t),lS}function ky(e,n,t,r,o){let i=Z(),s=Ry(i,n,t,r);if(s!==Ct){let a=fe(),c=Ys();cd(a,c,i,e,s,i[ue],o,!1)}return ky}function tn(e,n,t,r){eM(e,n,t,r)}function xo(e,n,t){J_(e,n,t)}function it(e){let n=Z(),t=fe(),r=Gm();Hu(r+1);let o=Dd(t,r);if(e.dirty&&C0(n)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=rM(n,r);e.reset(i,gw),e.notifyOnChanges()}return!0}return!1}function st(){return X_(Z(),Gm())}function NB(e,n=""){let t=Z(),r=fe(),o=e+Ke,i=r.firstCreatePass?Eo(r,o,1,n,null):r.data[o],s=uS(r,t,i,n,e);t[o]=s,Qs()&&ra(r,t,s,i),Nn(i,!1)}var uS=(e,n,t,r,o)=>(Ks(!0),yE(n[ue],r));function dS(e){return Fy("",e,""),dS}function Fy(e,n,t){let r=Z(),o=Ry(r,e,n,t);return o!==Ct&&Py(r,On(),o),Fy}function fS(e,n,t,r,o){let i=Z(),s=LM(i,e,n,t,r,o);return s!==Ct&&Py(i,On(),s),fS}function Py(e,n,t){let r=Pm(n,e);DE(e[ue],r,t)}function hS(e,n,t){vv(n)&&(n=n());let r=Z(),o=Zs();if(An(r,o,n)){let i=fe(),s=Ys();cd(i,s,r,e,n,r[ue],t,!1)}return hS}function OB(e,n){let t=vv(e);return t&&e.set(n),t}function pS(e,n){let t=Z(),r=fe(),o=_e();return Oy(r,t,t[ue],o,e,n),pS}function gS(e,n,t){let r=fe();if(r.firstCreatePass){let o=ht(e);bu(t,r.data,r.blueprint,o,!0),bu(n,r.data,r.blueprint,o,!1)}}function bu(e,n,t,r,o){if(e=Se(e),Array.isArray(e))for(let i=0;i<e.length;i++)bu(e[i],n,t,r,o);else{let i=fe(),s=Z(),a=_e(),c=hr(e)?e:Se(e.provide),l=xm(e),d=a.providerIndexes&1048575,h=a.directiveStart,p=a.providerIndexes>>20;if(hr(e)||!e.multi){let f=new Tn(l,o,u),y=Nl(c,n,o?d:d+p,h);y===-1?(zl(Es(a,s),i,c),Rl(i,e,n.length),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(f),s.push(f)):(t[y]=f,s[y]=f)}else{let f=Nl(c,n,d+p,h),y=Nl(c,n,d,d+p),S=f>=0&&t[f],N=y>=0&&t[y];if(o&&!N||!o&&!S){zl(Es(a,s),i,c);let z=yS(o?vS:mS,t.length,o,r,l);!o&&N&&(t[y].providerFactory=z),Rl(i,e,n.length,0),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(z),s.push(z)}else{let z=Ly(t[o?y:f],l,!o&&r);Rl(i,e,f>-1?f:y,z)}!o&&r&&N&&t[y].componentProviders++}}}function Rl(e,n,t,r){let o=hr(n),i=a0(n);if(o||i){let c=(i?Se(n.useClass):n).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&n.multi){let d=l.indexOf(t);d===-1?l.push(t,[r,c]):l[d+1].push(r,c)}else l.push(t,c)}}}function Ly(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function Nl(e,n,t,r){for(let o=t;o<r;o++)if(n[o]===e)return o;return-1}function mS(e,n,t,r,o){return wu(this.multi,[])}function vS(e,n,t,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=po(r,r[G],this.providerFactory.index,o);s=c.slice(0,a),wu(i,s);for(let l=a;l<c.length;l++)s.push(c[l])}else s=[],wu(i,s);return s}function wu(e,n){for(let t=0;t<e.length;t++){let r=e[t];n.push(r())}return n}function yS(e,n,t,r,o){let i=new Tn(e,t,u);return i.multi=[],i.index=n,i.componentProviders=0,Ly(i,o,r&&!t),i}function Ae(e,n=[]){return t=>{t.providersResolver=(r,o)=>gS(r,o?o(e):e,n)}}function kB(e,n,t){let r=N0()+e,o=Z();return o[r]===Ct?_M(o,r,t?n.call(t):n()):MM(o,r)}var as=null;function DS(e){as!==null&&(e.defaultEncapsulation!==as.defaultEncapsulation||e.preserveWhitespaces!==as.preserveWhitespaces)||(as=e)}var Eu=class{ngModuleFactory;componentFactories;constructor(n,t){this.ngModuleFactory=n,this.componentFactories=t}},jy=(()=>{class e{compileModuleSync(t){return new Fs(t)}compileModuleAsync(t){return Promise.resolve(this.compileModuleSync(t))}compileModuleAndAllComponentsSync(t){let r=this.compileModuleSync(t),o=_m(t),i=Vv(o.declarations).reduce((s,a)=>{let c=Xt(a);return c&&s.push(new xn(c)),s},[]);return new Eu(r,i)}compileModuleAndAllComponentsAsync(t){return Promise.resolve(this.compileModuleAndAllComponentsSync(t))}clearCache(){}clearCacheFor(t){}getModuleId(t){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),IS=new x("");function CS(e,n,t){let r=new Fs(t);return Promise.resolve(r)}function om(e){for(let n=e.length-1;n>=0;n--)if(e[n]!==void 0)return e[n]}var bS=(()=>{class e{zone=m(g);changeDetectionScheduler=m(yr);applicationRef=m(Dt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function wS({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new g(P(I({},Vy()),{scheduleInRootZone:t})),[{provide:g,useFactory:e},{provide:lo,multi:!0,useFactory:()=>{let r=m(bS,{optional:!0});return()=>r.initialize()}},{provide:lo,multi:!0,useFactory:()=>{let r=m(ES);return()=>{r.initialize()}}},n===!0?{provide:fv,useValue:!0}:[],{provide:hv,useValue:t??uv}]}function Vy(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var ES=(()=>{class e{subscription=new le;initialized=!1;zone=m(g);pendingTasks=m(jt);initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{g.assertNotInAngularZone(),queueMicrotask(()=>{t!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{g.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var _S=(()=>{class e{appRef=m(Dt);taskService=m(jt);ngZone=m(g);zonelessEnabled=m(dv);tracing=m(Tr,{optional:!0});disableScheduling=m(fv,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new le;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Ms):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(m(hv,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Ss||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&t===5)return;let r=!1;switch(t){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Ng:pv;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(t){return!(this.disableScheduling&&!t||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Ms+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(t),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Ng(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function MS(){return typeof $localize<"u"&&$localize.locale||Ls}var Md=new x("",{providedIn:"root",factory:()=>m(Md,B.Optional|B.SkipSelf)||MS()});var js=new x(""),SS=new x("");function oo(e){return!e.moduleRef}function TS(e){let n=oo(e)?e.r3Injector:e.moduleRef.injector,t=n.get(g);return t.run(()=>{oo(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=n.get(mt,null),o;if(t.runOutsideAngular(()=>{o=t.onError.subscribe({next:i=>{r.handleError(i)}})}),oo(e)){let i=()=>n.destroy(),s=e.platformInjector.get(js);s.add(i),n.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(js);s.add(i),e.moduleRef.onDestroy(()=>{fs(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return AS(r,t,()=>{let i=n.get(xy);return i.runInitializers(),i.donePromise.then(()=>{let s=n.get(Md,Ls);if(oS(s||Ls),!n.get(SS,!0))return oo(e)?n.get(Dt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(oo(e)){let c=n.get(Dt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return xS(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function xS(e,n){let t=e.injector.get(Dt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>t.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(t);else throw new A(-403,!1);n.push(e)}function AS(e,n,t){try{let r=t();return Fn(r)?r.catch(o=>{throw n.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw n.runOutsideAngular(()=>e.handleError(r)),r}}var By=(()=>{class e{_injector;_modules=[];_destroyListeners=[];_destroyed=!1;constructor(t){this._injector=t}bootstrapModuleFactory(t,r){let o=r?.scheduleInRootZone,i=()=>dw(r?.ngZone,P(I({},Vy({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing})),{scheduleInRootZone:o})),s=r?.ignoreChangesOutsideZone,a=[wS({ngZoneFactory:i,ignoreChangesOutsideZone:s}),{provide:yr,useExisting:_S}],c=uM(t.moduleType,this.injector,a);return TS({moduleRef:c,allPlatformModules:this._modules,platformInjector:this.injector})}bootstrapModule(t,r=[]){let o=Ay({},r);return CS(this.injector,o,t).then(i=>this.bootstrapModuleFactory(i,o))}onDestroy(t){this._destroyListeners.push(t)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new A(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let t=this._injector.get(js,null);t&&(t.forEach(r=>r()),t.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static \u0275fac=function(r){return new(r||e)(R(X))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})(),co=null,Uy=new x("");function RS(e){if(co&&!co.get(Uy,!1))throw new A(400,!1);OM(),co=e;let n=e.get(By);return kS(e),n}function Sd(e,n,t=[]){let r=`Platform: ${n}`,o=new x(r);return(i=[])=>{let s=Hy();if(!s||s.injector.get(Uy,!1)){let a=[...t,...i,{provide:o,useValue:!0}];e?e(a):RS(NS(a,r))}return OS(o)}}function NS(e=[],n){return X.create({name:n,providers:[{provide:$s,useValue:"platform"},{provide:js,useValue:new Set([()=>co=null])},...e]})}function OS(e){let n=Hy();if(!n)throw new A(401,!1);return n}function Hy(){return co?.get(By)??null}function kS(e){let n=e.get(Qu,null);ke(e,()=>{n?.forEach(t=>t())})}var D=(()=>{class e{static __NG_ELEMENT_ID__=FS}return e})();function FS(e){return PS(_e(),Z(),(e&16)===16)}function PS(e,n,t){if(wr(e)&&!t){let r=gt(e.index,n);return new go(r,r)}else if(e.type&175){let r=n[Qe];return new go(r,n)}return null}var _u=class{constructor(){}supports(n){return _y(n)}create(n){return new Mu(n)}},LS=(e,n)=>n,Mu=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(n){this._trackByFn=n||LS}forEachItem(n){let t;for(t=this._itHead;t!==null;t=t._next)n(t)}forEachOperation(n){let t=this._itHead,r=this._removalsHead,o=0,i=null;for(;t||r;){let s=!r||t&&t.currentIndex<im(r,o,i)?t:r,a=im(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(t=t._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,d=c-o;if(l!=d){for(let p=0;p<l;p++){let f=p<i.length?i[p]:i[p]=0,y=f+p;d<=y&&y<l&&(i[p]=f+1)}let h=s.previousIndex;i[h]=d-l}}a!==c&&n(s,a,c)}}forEachPreviousItem(n){let t;for(t=this._previousItHead;t!==null;t=t._nextPrevious)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;t!==null;t=t._nextAdded)n(t)}forEachMovedItem(n){let t;for(t=this._movesHead;t!==null;t=t._nextMoved)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;t!==null;t=t._nextRemoved)n(t)}forEachIdentityChange(n){let t;for(t=this._identityChangesHead;t!==null;t=t._nextIdentityChange)n(t)}diff(n){if(n==null&&(n=[]),!_y(n))throw new A(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let t=this._itHead,r=!1,o,i,s;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)i=n[a],s=this._trackByFn(a,i),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,i,s,a),r=!0):(r&&(t=this._verifyReinsertion(t,i,s,a)),Object.is(t.item,i)||this._addIdentityChange(t,i)),t=t._next}else o=0,wM(n,a=>{s=this._trackByFn(o,a),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,a,s,o),r=!0):(r&&(t=this._verifyReinsertion(t,a,s,o)),Object.is(t.item,a)||this._addIdentityChange(t,a)),t=t._next,o++}),this.length=o;return this._truncate(t),this.collection=n,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;n!==null;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;n!==null;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;n!==null;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,t,r,o){let i;return n===null?i=this._itTail:(i=n._prev,this._remove(n)),n=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._reinsertAfter(n,i,o)):(n=this._linkedRecords===null?null:this._linkedRecords.get(r,o),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._moveAfter(n,i,o)):n=this._addAfter(new Su(t,r),i,o)),n}_verifyReinsertion(n,t,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?n=this._reinsertAfter(i,n._prev,o):n.currentIndex!=o&&(n.currentIndex=o,this._addToMoves(n,o)),n}_truncate(n){for(;n!==null;){let t=n._next;this._addToRemovals(this._unlink(n)),n=t}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,t,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(n);let o=n._prevRemoved,i=n._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(n,t,r),this._addToMoves(n,r),n}_moveAfter(n,t,r){return this._unlink(n),this._insertAfter(n,t,r),this._addToMoves(n,r),n}_addAfter(n,t,r){return this._insertAfter(n,t,r),this._additionsTail===null?this._additionsTail=this._additionsHead=n:this._additionsTail=this._additionsTail._nextAdded=n,n}_insertAfter(n,t,r){let o=t===null?this._itHead:t._next;return n._next=o,n._prev=t,o===null?this._itTail=n:o._prev=n,t===null?this._itHead=n:t._next=n,this._linkedRecords===null&&(this._linkedRecords=new Vs),this._linkedRecords.put(n),n.currentIndex=r,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){this._linkedRecords!==null&&this._linkedRecords.remove(n);let t=n._prev,r=n._next;return t===null?this._itHead=r:t._next=r,r===null?this._itTail=t:r._prev=t,n}_addToMoves(n,t){return n.previousIndex===t||(this._movesTail===null?this._movesTail=this._movesHead=n:this._movesTail=this._movesTail._nextMoved=n),n}_addToRemovals(n){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Vs),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,t){return n.item=t,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=n:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=n,n}},Su=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(n,t){this.item=n,this.trackById=t}},Tu=class{_head=null;_tail=null;add(n){this._head===null?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,t){let r;for(r=this._head;r!==null;r=r._nextDup)if((t===null||t<=r.currentIndex)&&Object.is(r.trackById,n))return r;return null}remove(n){let t=n._prevDup,r=n._nextDup;return t===null?this._head=r:t._nextDup=r,r===null?this._tail=t:r._prevDup=t,this._head===null}},Vs=class{map=new Map;put(n){let t=n.trackById,r=this.map.get(t);r||(r=new Tu,this.map.set(t,r)),r.add(n)}get(n,t){let r=n,o=this.map.get(r);return o?o.get(n,t):null}remove(n){let t=n.trackById;return this.map.get(t).remove(n)&&this.map.delete(t),n}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function im(e,n,t){let r=e.previousIndex;if(r===null)return r;let o=0;return t&&r<t.length&&(o=t[r]),r+n+o}function sm(){return new Td([new _u])}var Td=(()=>{class e{factories;static \u0275prov=T({token:e,providedIn:"root",factory:sm});constructor(t){this.factories=t}static create(t,r){if(r!=null){let o=r.factories.slice();t=t.concat(o)}return new e(t)}static extend(t){return{provide:e,useFactory:r=>e.create(t,r||sm()),deps:[[e,new Zb,new qb]]}}find(t){let r=this.factories.find(o=>o.supports(t));if(r!=null)return r;throw new A(901,!1)}}return e})();var $y=Sd(null,"core",[]),zy=(()=>{class e{constructor(t){}static \u0275fac=function(r){return new(r||e)(R(Dt))};static \u0275mod=xe({type:e});static \u0275inj=Te({})}return e})();function nn(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Bt(e){return yl(e)}function Ao(e,n){return pl(e,n?.equal)}var am=class{[We];constructor(n){this[We]=n}destroy(){this[We].destroy()}};function Gy(e,n){let t=Xt(e),r=n.elementInjector||zs();return new xn(t).create(r,n.projectableNodes,n.hostElement,n.environmentInjector)}function ha(e){let n=Xt(e);if(!n)return null;let t=new xn(n);return{get selector(){return t.selector},get type(){return t.componentType},get inputs(){return t.inputs},get outputs(){return t.outputs},get ngContentSelectors(){return t.ngContentSelectors},get isStandalone(){return n.standalone},get isSignal(){return n.signals}}}var se=new x("");var Zy=null;function He(){return Zy}function xd(e){Zy??=e}var Ro=class{},No=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>m(Yy),providedIn:"platform"})}return e})(),Ad=new x(""),Yy=(()=>{class e extends No{_location;_history;_doc=m(se);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return He().getBaseHref(this._doc)}onPopState(t){let r=He().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",t,!1),()=>r.removeEventListener("popstate",t)}onHashChange(t){let r=He().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",t,!1),()=>r.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,r,o){this._history.pushState(t,r,o)}replaceState(t,r,o){this._history.replaceState(t,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function pa(e,n){return e?n?e.endsWith("/")?n.startsWith("/")?e+n.slice(1):e+n:n.startsWith("/")?e+n:`${e}/${n}`:e:n}function Wy(e){let n=e.search(/#|\?|$/);return e[n-1]==="/"?e.slice(0,n-1)+e.slice(n):e}function ct(e){return e&&e[0]!=="?"?`?${e}`:e}var Pe=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>m(ma),providedIn:"root"})}return e})(),ga=new x(""),ma=(()=>{class e extends Pe{_platformLocation;_baseHref;_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??m(se).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return pa(this._baseHref,t)}path(t=!1){let r=this._platformLocation.pathname+ct(this._platformLocation.search),o=this._platformLocation.hash;return o&&t?`${r}${o}`:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+ct(i));this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+ct(i));this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(R(No),R(ga,8))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),$e=(()=>{class e{_subject=new K;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(t){this._locationStrategy=t;let r=this._locationStrategy.getBaseHref();this._basePath=BS(Wy(qy(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,r=""){return this.path()==this.normalize(t+ct(r))}normalize(t){return e.stripTrailingSlash(VS(this._basePath,qy(t)))}prepareExternalUrl(t){return t&&t[0]!=="/"&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,r="",o=null){this._locationStrategy.pushState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+ct(r)),o)}replaceState(t,r="",o=null){this._locationStrategy.replaceState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+ct(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",r){this._urlChangeListeners.forEach(o=>o(t,r))}subscribe(t,r,o){return this._subject.subscribe({next:t,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=ct;static joinWithSlash=pa;static stripTrailingSlash=Wy;static \u0275fac=function(r){return new(r||e)(R(Pe))};static \u0275prov=T({token:e,factory:()=>jS(),providedIn:"root"})}return e})();function jS(){return new $e(R(Pe))}function VS(e,n){if(!e||!n.startsWith(e))return n;let t=n.substring(e.length);return t===""||["/",";","?","#"].includes(t[0])?t:n}function qy(e){return e.replace(/\/index.html$/,"")}function BS(e){if(new RegExp("^(https?:)?//").test(e)){let[,t]=e.split(/\/\/[^\/]+/);return t}return e}var Nd=(()=>{class e extends Pe{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}path(t=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(t){let r=pa(this._baseHref,t);return r.length>0?"#"+r:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+ct(i))||this._platformLocation.pathname;this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+ct(i))||this._platformLocation.pathname;this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(R(No),R(ga,8))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();var Rd=/\s+/,Qy=[],US=(()=>{class e{_ngEl;_renderer;initialClasses=Qy;rawClass;stateMap=new Map;constructor(t,r){this._ngEl=t,this._renderer=r}set klass(t){this.initialClasses=t!=null?t.trim().split(Rd):Qy}set ngClass(t){this.rawClass=typeof t=="string"?t.trim().split(Rd):t}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let t=this.rawClass;if(Array.isArray(t)||t instanceof Set)for(let r of t)this._updateState(r,!0);else if(t!=null)for(let r of Object.keys(t))this._updateState(r,!!t[r]);this._applyStateDiff()}_updateState(t,r){let o=this.stateMap.get(t);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(t,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let t of this.stateMap){let r=t[0],o=t[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(t,r){t=t.trim(),t.length>0&&t.split(Rd).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(u(v),u(Jt))};static \u0275dir=V({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var va=class{$implicit;ngForOf;index;count;constructor(n,t,r,o){this.$implicit=n,this.ngForOf=t,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Jy=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(t,r,o){this._viewContainer=t,this._template=r,this._differs=o}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let t=this._ngForOf;!this._differ&&t&&(this._differ=this._differs.find(t).create(this.ngForTrackBy))}if(this._differ){let t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){let r=this._viewContainer;t.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new va(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Ky(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}t.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Ky(i,o)})}static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(u(Ue),u(yt),u(Td))};static \u0275dir=V({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Ky(e,n){e.context.$implicit=n.item}var Oo=(()=>{class e{_viewContainer;_context=new ya;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(t,r){this._viewContainer=t,this._thenTemplateRef=r}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){Xy(t,!1),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){Xy(t,!1),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(u(Ue),u(yt))};static \u0275dir=V({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),ya=class{$implicit=null;ngIf=null};function Xy(e,n){if(e&&!e.createEmbeddedView)throw new A(2020,!1)}var Da=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(t){this._viewContainerRef=t}ngOnChanges(t){if(this._shouldRecreateView(t)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(t){return!!t.ngTemplateOutlet||!!t.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(t,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(t,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(u(Ue))};static \u0275dir=V({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[Xe]})}return e})();var ko=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Te({})}return e})();function Fo(e,n){n=encodeURIComponent(n);for(let t of e.split(";")){let r=t.indexOf("="),[o,i]=r==-1?[t,""]:[t.slice(0,r),t.slice(r+1)];if(o.trim()===n)return decodeURIComponent(i)}return null}var Od="browser",eD="server";function Ia(e){return e===eD}var Pn=class{};var tD=(()=>{class e{static \u0275prov=T({token:e,providedIn:"root",factory:()=>new kd(m(se),window)})}return e})(),kd=class{document;window;offset=()=>[0,0];constructor(n,t){this.document=n,this.window=t}setOffset(n){Array.isArray(n)?this.offset=()=>n:this.offset=n}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(n){this.window.scrollTo(n[0],n[1])}scrollToAnchor(n){let t=$S(this.document,n);t&&(this.scrollToElement(t),t.focus())}setHistoryScrollRestoration(n){this.window.history.scrollRestoration=n}scrollToElement(n){let t=n.getBoundingClientRect(),r=t.left+this.window.pageXOffset,o=t.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function $S(e,n){let t=e.getElementById(n)||e.getElementsByName(n)[0];if(t)return t;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(n)||i.querySelector(`[name="${n}"]`);if(s)return s}o=r.nextNode()}}return null}var wa=new x(""),Vd=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(t,r){this._zone=r,t.forEach(o=>{o.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,r,o,i){return this._findPluginFor(r).addEventListener(t,r,o,i)}getZone(){return this._zone}_findPluginFor(t){let r=this._eventNameToPlugin.get(t);if(r)return r;if(r=this._plugins.find(i=>i.supports(t)),!r)throw new A(5101,!1);return this._eventNameToPlugin.set(t,r),r}static \u0275fac=function(r){return new(r||e)(R(wa),R(g))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),Po=class{_doc;constructor(n){this._doc=n}manager},Ca="ng-app-id";function nD(e){for(let n of e)n.remove()}function rD(e,n){let t=n.createElement("style");return t.textContent=e,t}function zS(e,n,t,r){let o=e.head?.querySelectorAll(`style[${Ca}="${n}"],link[${Ca}="${n}"]`);if(o)for(let i of o)i.removeAttribute(Ca),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&t.set(i.textContent,{usage:0,elements:[i]})}function Ld(e,n){let t=n.createElement("link");return t.setAttribute("rel","stylesheet"),t.setAttribute("href",e),t}var Bd=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(t,r,o,i={}){this.doc=t,this.appId=r,this.nonce=o,this.isServer=Ia(i),zS(t,r,this.inline,this.external),this.hosts.add(t.head)}addStyles(t,r){for(let o of t)this.addUsage(o,this.inline,rD);r?.forEach(o=>this.addUsage(o,this.external,Ld))}removeStyles(t,r){for(let o of t)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(t,r,o){let i=r.get(t);i?i.usage++:r.set(t,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(t,this.doc)))})}removeUsage(t,r){let o=r.get(t);o&&(o.usage--,o.usage<=0&&(nD(o.elements),r.delete(t)))}ngOnDestroy(){for(let[,{elements:t}]of[...this.inline,...this.external])nD(t);this.hosts.clear()}addHost(t){this.hosts.add(t);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(t,rD(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(t,Ld(r,this.doc)))}removeHost(t){this.hosts.delete(t)}addElement(t,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(Ca,this.appId),t.appendChild(r)}static \u0275fac=function(r){return new(r||e)(R(se),R(Yu),R(Ku,8),R(Sr))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),Pd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Ud=/%COMP%/g;var iD="%COMP%",GS=`_nghost-${iD}`,WS=`_ngcontent-${iD}`,qS=!0,ZS=new x("",{providedIn:"root",factory:()=>qS});function YS(e){return WS.replace(Ud,e)}function QS(e){return GS.replace(Ud,e)}function sD(e,n){return n.map(t=>t.replace(Ud,e))}var Hd=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(t,r,o,i,s,a,c,l=null,d=null){this.eventManager=t,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.tracingService=d,this.platformIsServer=Ia(a),this.defaultRenderer=new Lo(t,s,c,this.platformIsServer,this.tracingService)}createRenderer(t,r){if(!t||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===vt.ShadowDom&&(r=P(I({},r),{encapsulation:vt.Emulated}));let o=this.getOrCreateRenderer(t,r);return o instanceof ba?o.applyToHost(t):o instanceof jo&&o.applyStyles(),o}getOrCreateRenderer(t,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,d=this.removeStylesOnCompDestroy,h=this.platformIsServer,p=this.tracingService;switch(r.encapsulation){case vt.Emulated:i=new ba(c,l,r,this.appId,d,s,a,h,p);break;case vt.ShadowDom:return new jd(c,l,t,r,s,a,this.nonce,h,p);default:i=new jo(c,l,r,d,s,a,h,p);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(t){this.rendererByCompId.delete(t)}static \u0275fac=function(r){return new(r||e)(R(Vd),R(Bd),R(Yu),R(ZS),R(se),R(Sr),R(g),R(Ku),R(Tr,8))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),Lo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(n,t,r,o,i){this.eventManager=n,this.doc=t,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(n,t){return t?this.doc.createElementNS(Pd[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){(oD(n)?n.content:n).appendChild(t)}insertBefore(n,t,r){n&&(oD(n)?n.content:n).insertBefore(t,r)}removeChild(n,t){t.remove()}selectRootElement(n,t){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new A(-5104,!1);return t||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,r,o){if(o){t=o+":"+t;let i=Pd[o];i?n.setAttributeNS(i,t,r):n.setAttribute(t,r)}else n.setAttribute(t,r)}removeAttribute(n,t,r){if(r){let o=Pd[r];o?n.removeAttributeNS(o,t):n.removeAttribute(`${r}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,r,o){o&(kt.DashCase|kt.Important)?n.style.setProperty(t,r,o&kt.Important?"important":""):n.style[t]=r}removeStyle(n,t,r){r&kt.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,r){n!=null&&(n[t]=r)}setValue(n,t){n.nodeValue=t}listen(n,t,r,o){if(typeof n=="string"&&(n=He().getGlobalEventTarget(this.doc,n),!n))throw new A(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(n,t,i)),this.eventManager.addEventListener(n,t,i,o)}decoratePreventDefault(n){return t=>{if(t==="__ngUnwrap__")return n;(this.platformIsServer?this.ngZone.runGuarded(()=>n(t)):n(t))===!1&&t.preventDefault()}}};function oD(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var jd=class extends Lo{sharedStylesHost;hostEl;shadowRoot;constructor(n,t,r,o,i,s,a,c,l){super(n,i,s,c,l),this.sharedStylesHost=t,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let d=o.styles;d=sD(o.id,d);for(let p of d){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=p,this.shadowRoot.appendChild(f)}let h=o.getExternalStyles?.();if(h)for(let p of h){let f=Ld(p,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,r){return super.insertBefore(this.nodeOrShadowRoot(n),t,r)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},jo=class extends Lo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(n,t,r,o,i,s,a,c,l){super(n,i,s,a,c),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=o;let d=r.styles;this.styles=l?sD(l,d):d,this.styleUrls=r.getExternalStyles?.(l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},ba=class extends jo{contentAttr;hostAttr;constructor(n,t,r,o,i,s,a,c,l){let d=o+"-"+r.id;super(n,t,r,i,s,a,c,l,d),this.contentAttr=YS(d),this.hostAttr=QS(d)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){let r=super.createElement(n,t);return super.setAttribute(r,this.contentAttr,""),r}};var Ea=class e extends Ro{supportsDOMEvents=!0;static makeCurrent(){xd(new e)}onAndCancel(n,t,r,o){return n.addEventListener(t,r,o),()=>{n.removeEventListener(t,r,o)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return t=t||this.getDefaultDocument(),t.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return t==="window"?window:t==="document"?n:t==="body"?n.body:null}getBaseHref(n){let t=KS();return t==null?null:XS(t)}resetBaseElement(){Vo=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return Fo(document.cookie,n)}},Vo=null;function KS(){return Vo=Vo||document.head.querySelector("base"),Vo?Vo.getAttribute("href"):null}function XS(e){return new URL(e,document.baseURI).pathname}var _a=class{addToWindow(n){Ve.getAngularTestability=(r,o=!0)=>{let i=n.findTestabilityInTree(r,o);if(i==null)throw new A(5103,!1);return i},Ve.getAllAngularTestabilities=()=>n.getAllTestabilities(),Ve.getAllAngularRootElements=()=>n.getAllRootElements();let t=r=>{let o=Ve.getAllAngularTestabilities(),i=o.length,s=function(){i--,i==0&&r()};o.forEach(a=>{a.whenStable(s)})};Ve.frameworkStabilizers||(Ve.frameworkStabilizers=[]),Ve.frameworkStabilizers.push(t)}findTestabilityInTree(n,t,r){if(t==null)return null;let o=n.getTestability(t);return o??(r?He().isShadowRoot(t)?this.findTestabilityInTree(n,t.host,!0):this.findTestabilityInTree(n,t.parentElement,!0):null)}},JS=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),cD=(()=>{class e extends Po{constructor(t){super(t)}supports(t){return!0}addEventListener(t,r,o,i){return t.addEventListener(r,o,i),()=>this.removeEventListener(t,r,o,i)}removeEventListener(t,r,o,i){return t.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(R(se))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),aD=["alt","control","meta","shift"],eT={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},tT={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},lD=(()=>{class e extends Po{constructor(t){super(t)}supports(t){return e.parseEventName(t)!=null}addEventListener(t,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>He().onAndCancel(t,s.domEventName,a,i))}static parseEventName(t){let r=t.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),aD.forEach(l=>{let d=r.indexOf(l);d>-1&&(r.splice(d,1),s+=l+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(t,r){let o=eT[t.key]||t.key,i="";return r.indexOf("code.")>-1&&(o=t.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),aD.forEach(s=>{if(s!==o){let a=tT[s];a(t)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(t,r,o){return i=>{e.matchEventFullKeyCode(i,t)&&o.runGuarded(()=>r(i))}}static _normalizeKey(t){return t==="esc"?"escape":t}static \u0275fac=function(r){return new(r||e)(R(se))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();function nT(){Ea.makeCurrent()}function rT(){return new mt}function oT(){return Ev(document),document}var iT=[{provide:Sr,useValue:Od},{provide:Qu,useValue:nT,multi:!0},{provide:se,useFactory:oT}],sT=Sd($y,"browser",iT);var aT=[{provide:So,useClass:_a},{provide:Cd,useClass:sa,deps:[g,aa,So]},{provide:sa,useClass:sa,deps:[g,aa,So]}],cT=[{provide:$s,useValue:"root"},{provide:mt,useFactory:rT},{provide:wa,useClass:cD,multi:!0,deps:[se]},{provide:wa,useClass:lD,multi:!0,deps:[se]},Hd,Bd,Vd,{provide:Dr,useExisting:Hd},{provide:Pn,useClass:JS},[]],lT=(()=>{class e{constructor(){}static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Te({providers:[...cT,...aT],imports:[ko,zy]})}return e})();var Nr=class{},Bo=class{},rn=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(n){n?typeof n=="string"?this.lazyInit=()=>{this.headers=new Map,n.split(`
`).forEach(t=>{let r=t.indexOf(":");if(r>0){let o=t.slice(0,r),i=t.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&n instanceof Headers?(this.headers=new Map,n.forEach((t,r)=>{this.addHeaderEntry(r,t)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(n).forEach(([t,r])=>{this.setHeaderEntries(t,r)})}:this.headers=new Map}has(n){return this.init(),this.headers.has(n.toLowerCase())}get(n){this.init();let t=this.headers.get(n.toLowerCase());return t&&t.length>0?t[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(n){return this.init(),this.headers.get(n.toLowerCase())||null}append(n,t){return this.clone({name:n,value:t,op:"a"})}set(n,t){return this.clone({name:n,value:t,op:"s"})}delete(n,t){return this.clone({name:n,value:t,op:"d"})}maybeSetNormalizedName(n,t){this.normalizedNames.has(t)||this.normalizedNames.set(t,n)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(n=>this.applyUpdate(n)),this.lazyUpdate=null))}copyFrom(n){n.init(),Array.from(n.headers.keys()).forEach(t=>{this.headers.set(t,n.headers.get(t)),this.normalizedNames.set(t,n.normalizedNames.get(t))})}clone(n){let t=new e;return t.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,t.lazyUpdate=(this.lazyUpdate||[]).concat([n]),t}applyUpdate(n){let t=n.name.toLowerCase();switch(n.op){case"a":case"s":let r=n.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(n.name,t);let o=(n.op==="a"?this.headers.get(t):void 0)||[];o.push(...r),this.headers.set(t,o);break;case"d":let i=n.value;if(!i)this.headers.delete(t),this.normalizedNames.delete(t);else{let s=this.headers.get(t);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(t),this.normalizedNames.delete(t)):this.headers.set(t,s)}break}}addHeaderEntry(n,t){let r=n.toLowerCase();this.maybeSetNormalizedName(n,r),this.headers.has(r)?this.headers.get(r).push(t):this.headers.set(r,[t])}setHeaderEntries(n,t){let r=(Array.isArray(t)?t:[t]).map(i=>i.toString()),o=n.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(n,o)}forEach(n){this.init(),Array.from(this.normalizedNames.keys()).forEach(t=>n(this.normalizedNames.get(t),this.headers.get(t)))}};var Sa=class{encodeKey(n){return uD(n)}encodeValue(n){return uD(n)}decodeKey(n){return decodeURIComponent(n)}decodeValue(n){return decodeURIComponent(n)}};function uT(e,n){let t=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[n.decodeKey(o),""]:[n.decodeKey(o.slice(0,i)),n.decodeValue(o.slice(i+1))],c=t.get(s)||[];c.push(a),t.set(s,c)}),t}var dT=/%(\d[a-f0-9])/gi,fT={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function uD(e){return encodeURIComponent(e).replace(dT,(n,t)=>fT[t]??n)}function Ma(e){return`${e}`}var Ut=class e{map;encoder;updates=null;cloneFrom=null;constructor(n={}){if(this.encoder=n.encoder||new Sa,n.fromString){if(n.fromObject)throw new A(2805,!1);this.map=uT(n.fromString,this.encoder)}else n.fromObject?(this.map=new Map,Object.keys(n.fromObject).forEach(t=>{let r=n.fromObject[t],o=Array.isArray(r)?r.map(Ma):[Ma(r)];this.map.set(t,o)})):this.map=null}has(n){return this.init(),this.map.has(n)}get(n){this.init();let t=this.map.get(n);return t?t[0]:null}getAll(n){return this.init(),this.map.get(n)||null}keys(){return this.init(),Array.from(this.map.keys())}append(n,t){return this.clone({param:n,value:t,op:"a"})}appendAll(n){let t=[];return Object.keys(n).forEach(r=>{let o=n[r];Array.isArray(o)?o.forEach(i=>{t.push({param:r,value:i,op:"a"})}):t.push({param:r,value:o,op:"a"})}),this.clone(t)}set(n,t){return this.clone({param:n,value:t,op:"s"})}delete(n,t){return this.clone({param:n,value:t,op:"d"})}toString(){return this.init(),this.keys().map(n=>{let t=this.encoder.encodeKey(n);return this.map.get(n).map(r=>t+"="+this.encoder.encodeValue(r)).join("&")}).filter(n=>n!=="").join("&")}clone(n){let t=new e({encoder:this.encoder});return t.cloneFrom=this.cloneFrom||this,t.updates=(this.updates||[]).concat(n),t}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(n=>this.map.set(n,this.cloneFrom.map.get(n))),this.updates.forEach(n=>{switch(n.op){case"a":case"s":let t=(n.op==="a"?this.map.get(n.param):void 0)||[];t.push(Ma(n.value)),this.map.set(n.param,t);break;case"d":if(n.value!==void 0){let r=this.map.get(n.param)||[],o=r.indexOf(Ma(n.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(n.param,r):this.map.delete(n.param)}else{this.map.delete(n.param);break}}}),this.cloneFrom=this.updates=null)}};var Ta=class{map=new Map;set(n,t){return this.map.set(n,t),this}get(n){return this.map.has(n)||this.map.set(n,n.defaultValue()),this.map.get(n)}delete(n){return this.map.delete(n),this}has(n){return this.map.has(n)}keys(){return this.map.keys()}};function hT(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function dD(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function fD(e){return typeof Blob<"u"&&e instanceof Blob}function hD(e){return typeof FormData<"u"&&e instanceof FormData}function pT(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var pD="Content-Type",gD="Accept",vD="X-Request-URL",yD="text/plain",DD="application/json",gT=`${DD}, ${yD}, */*`,Rr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(n,t,r,o){this.url=t,this.method=n.toUpperCase();let i;if(hT(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new rn,this.context??=new Ta,!this.params)this.params=new Ut,this.urlWithParams=t;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=t;else{let a=t.indexOf("?"),c=a===-1?"?":a<t.length-1?"&":"";this.urlWithParams=t+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||dD(this.body)||fD(this.body)||hD(this.body)||pT(this.body)?this.body:this.body instanceof Ut?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||hD(this.body)?null:fD(this.body)?this.body.type||null:dD(this.body)?null:typeof this.body=="string"?yD:this.body instanceof Ut?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?DD:null}clone(n={}){let t=n.method||this.method,r=n.url||this.url,o=n.responseType||this.responseType,i=n.transferCache??this.transferCache,s=n.body!==void 0?n.body:this.body,a=n.withCredentials??this.withCredentials,c=n.reportProgress??this.reportProgress,l=n.headers||this.headers,d=n.params||this.params,h=n.context??this.context;return n.setHeaders!==void 0&&(l=Object.keys(n.setHeaders).reduce((p,f)=>p.set(f,n.setHeaders[f]),l)),n.setParams&&(d=Object.keys(n.setParams).reduce((p,f)=>p.set(f,n.setParams[f]),d)),new e(t,r,s,{params:d,headers:l,context:h,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},Ln=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Ln||{}),Or=class{headers;status;statusText;url;ok;type;constructor(n,t=200,r="OK"){this.headers=n.headers||new rn,this.status=n.status!==void 0?n.status:t,this.statusText=n.statusText||r,this.url=n.url||null,this.ok=this.status>=200&&this.status<300}},xa=class e extends Or{constructor(n={}){super(n)}type=Ln.ResponseHeader;clone(n={}){return new e({headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Uo=class e extends Or{body;constructor(n={}){super(n),this.body=n.body!==void 0?n.body:null}type=Ln.Response;clone(n={}){return new e({body:n.body!==void 0?n.body:this.body,headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Ho=class extends Or{name="HttpErrorResponse";message;error;ok=!1;constructor(n){super(n,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${n.url||"(unknown url)"}`:this.message=`Http failure response for ${n.url||"(unknown url)"}: ${n.status} ${n.statusText}`,this.error=n.error||null}},mT=200,vT=204;function $d(e,n){return{body:n,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var ID=(()=>{class e{handler;constructor(t){this.handler=t}request(t,r,o={}){let i;if(t instanceof Rr)i=t;else{let c;o.headers instanceof rn?c=o.headers:c=new rn(o.headers);let l;o.params&&(o.params instanceof Ut?l=o.params:l=new Ut({fromObject:o.params})),i=new Rr(t,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:l,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=O(i).pipe(dt(c=>this.handler.handle(c)));if(t instanceof Rr||o.observe==="events")return s;let a=s.pipe(me(c=>c instanceof Uo));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(H(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new A(2806,!1);return c.body}));case"blob":return a.pipe(H(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new A(2807,!1);return c.body}));case"text":return a.pipe(H(c=>{if(c.body!==null&&typeof c.body!="string")throw new A(2808,!1);return c.body}));case"json":default:return a.pipe(H(c=>c.body))}case"response":return a;default:throw new A(2809,!1)}}delete(t,r={}){return this.request("DELETE",t,r)}get(t,r={}){return this.request("GET",t,r)}head(t,r={}){return this.request("HEAD",t,r)}jsonp(t,r){return this.request("JSONP",t,{params:new Ut().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(t,r={}){return this.request("OPTIONS",t,r)}patch(t,r,o={}){return this.request("PATCH",t,$d(o,r))}post(t,r,o={}){return this.request("POST",t,$d(o,r))}put(t,r,o={}){return this.request("PUT",t,$d(o,r))}static \u0275fac=function(r){return new(r||e)(R(Nr))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();var yT=new x("");function CD(e,n){return n(e)}function DT(e,n){return(t,r)=>n.intercept(t,{handle:o=>e(o,r)})}function IT(e,n,t){return(r,o)=>ke(t,()=>n(r,i=>e(i,o)))}var bD=new x(""),Gd=new x(""),wD=new x(""),Wd=new x("",{providedIn:"root",factory:()=>!0});function CT(){let e=null;return(n,t)=>{e===null&&(e=(m(bD,{optional:!0})??[]).reduceRight(DT,CD));let r=m(jt);if(m(Wd)){let i=r.add();return e(n,t).pipe(Yt(()=>r.remove(i)))}else return e(n,t)}}var Aa=(()=>{class e extends Nr{backend;injector;chain=null;pendingTasks=m(jt);contributeToStability=m(Wd);constructor(t,r){super(),this.backend=t,this.injector=r}handle(t){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Gd),...this.injector.get(wD,[])]));this.chain=r.reduceRight((o,i)=>IT(o,i,this.injector),CD)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(t,o=>this.backend.handle(o)).pipe(Yt(()=>this.pendingTasks.remove(r)))}else return this.chain(t,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(R(Bo),R(ie))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();var bT=/^\)\]\}',?\n/,wT=RegExp(`^${vD}:`,"m");function ET(e){return"responseURL"in e&&e.responseURL?e.responseURL:wT.test(e.getAllResponseHeaders())?e.getResponseHeader(vD):null}var zd=(()=>{class e{xhrFactory;constructor(t){this.xhrFactory=t}handle(t){if(t.method==="JSONP")throw new A(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?ne(r.\u0275loadImpl()):O(null)).pipe(ve(()=>new W(i=>{let s=r.build();if(s.open(t.method,t.urlWithParams),t.withCredentials&&(s.withCredentials=!0),t.headers.forEach((S,N)=>s.setRequestHeader(S,N.join(","))),t.headers.has(gD)||s.setRequestHeader(gD,gT),!t.headers.has(pD)){let S=t.detectContentTypeHeader();S!==null&&s.setRequestHeader(pD,S)}if(t.responseType){let S=t.responseType.toLowerCase();s.responseType=S!=="json"?S:"text"}let a=t.serializeBody(),c=null,l=()=>{if(c!==null)return c;let S=s.statusText||"OK",N=new rn(s.getAllResponseHeaders()),z=ET(s)||t.url;return c=new xa({headers:N,status:s.status,statusText:S,url:z}),c},d=()=>{let{headers:S,status:N,statusText:z,url:Tt}=l(),ce=null;N!==vT&&(ce=typeof s.response>"u"?s.responseText:s.response),N===0&&(N=ce?mT:0);let Wn=N>=200&&N<300;if(t.responseType==="json"&&typeof ce=="string"){let Ii=ce;ce=ce.replace(bT,"");try{ce=ce!==""?JSON.parse(ce):null}catch(jC){ce=Ii,Wn&&(Wn=!1,ce={error:jC,text:ce})}}Wn?(i.next(new Uo({body:ce,headers:S,status:N,statusText:z,url:Tt||void 0})),i.complete()):i.error(new Ho({error:ce,headers:S,status:N,statusText:z,url:Tt||void 0}))},h=S=>{let{url:N}=l(),z=new Ho({error:S,status:s.status||0,statusText:s.statusText||"Unknown Error",url:N||void 0});i.error(z)},p=!1,f=S=>{p||(i.next(l()),p=!0);let N={type:Ln.DownloadProgress,loaded:S.loaded};S.lengthComputable&&(N.total=S.total),t.responseType==="text"&&s.responseText&&(N.partialText=s.responseText),i.next(N)},y=S=>{let N={type:Ln.UploadProgress,loaded:S.loaded};S.lengthComputable&&(N.total=S.total),i.next(N)};return s.addEventListener("load",d),s.addEventListener("error",h),s.addEventListener("timeout",h),s.addEventListener("abort",h),t.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",y)),s.send(a),i.next({type:Ln.Sent}),()=>{s.removeEventListener("error",h),s.removeEventListener("abort",h),s.removeEventListener("load",d),s.removeEventListener("timeout",h),t.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",y)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(R(Pn))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),ED=new x(""),_T="XSRF-TOKEN",MT=new x("",{providedIn:"root",factory:()=>_T}),ST="X-XSRF-TOKEN",TT=new x("",{providedIn:"root",factory:()=>ST}),$o=class{},xT=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(t,r){this.doc=t,this.cookieName=r}getToken(){let t=this.doc.cookie||"";return t!==this.lastCookieString&&(this.parseCount++,this.lastToken=Fo(t,this.cookieName),this.lastCookieString=t),this.lastToken}static \u0275fac=function(r){return new(r||e)(R(se),R(MT))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();function AT(e,n){let t=e.url.toLowerCase();if(!m(ED)||e.method==="GET"||e.method==="HEAD"||t.startsWith("http://")||t.startsWith("https://"))return n(e);let r=m($o).getToken(),o=m(TT);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),n(e)}var qd=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(qd||{});function RT(e,n){return{\u0275kind:e,\u0275providers:n}}function _D(...e){let n=[ID,zd,Aa,{provide:Nr,useExisting:Aa},{provide:Bo,useFactory:()=>m(yT,{optional:!0})??m(zd)},{provide:Gd,useValue:AT,multi:!0},{provide:ED,useValue:!0},{provide:$o,useClass:xT}];for(let t of e)n.push(...t.\u0275providers);return Hs(n)}var mD=new x("");function MD(){return RT(qd.LegacyInterceptors,[{provide:mD,useFactory:CT},{provide:Gd,useExisting:mD,multi:!0}])}var NT=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Te({providers:[_D(MD())]})}return e})();var SD=(()=>{class e{_doc;constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}static \u0275fac=function(r){return new(r||e)(R(se))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var F="primary",ni=Symbol("RouteTitle"),Xd=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t[0]:t}return null}getAll(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t:[t]}return[]}get keys(){return Object.keys(this.params)}};function Bn(e){return new Xd(e)}function FD(e,n,t){let r=t.path.split("/");if(r.length>e.length||t.pathMatch==="full"&&(n.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function kT(e,n){if(e.length!==n.length)return!1;for(let t=0;t<e.length;++t)if(!bt(e[t],n[t]))return!1;return!0}function bt(e,n){let t=e?Jd(e):void 0,r=n?Jd(n):void 0;if(!t||!r||t.length!=r.length)return!1;let o;for(let i=0;i<t.length;i++)if(o=t[i],!PD(e[o],n[o]))return!1;return!0}function Jd(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function PD(e,n){if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return!1;let t=[...e].sort(),r=[...n].sort();return t.every((o,i)=>r[i]===o)}else return e===n}function LD(e){return e.length>0?e[e.length-1]:null}function cn(e){return Yc(e)?e:Fn(e)?ne(Promise.resolve(e)):O(e)}var FT={exact:VD,subset:BD},jD={exact:PT,subset:LT,ignored:()=>!0};function TD(e,n,t){return FT[t.paths](e.root,n.root,t.matrixParams)&&jD[t.queryParams](e.queryParams,n.queryParams)&&!(t.fragment==="exact"&&e.fragment!==n.fragment)}function PT(e,n){return bt(e,n)}function VD(e,n,t){if(!jn(e.segments,n.segments)||!Oa(e.segments,n.segments,t)||e.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!e.children[r]||!VD(e.children[r],n.children[r],t))return!1;return!0}function LT(e,n){return Object.keys(n).length<=Object.keys(e).length&&Object.keys(n).every(t=>PD(e[t],n[t]))}function BD(e,n,t){return UD(e,n,n.segments,t)}function UD(e,n,t,r){if(e.segments.length>t.length){let o=e.segments.slice(0,t.length);return!(!jn(o,t)||n.hasChildren()||!Oa(o,t,r))}else if(e.segments.length===t.length){if(!jn(e.segments,t)||!Oa(e.segments,t,r))return!1;for(let o in n.children)if(!e.children[o]||!BD(e.children[o],n.children[o],r))return!1;return!0}else{let o=t.slice(0,e.segments.length),i=t.slice(e.segments.length);return!jn(e.segments,o)||!Oa(e.segments,o,r)||!e.children[F]?!1:UD(e.children[F],n,i,r)}}function Oa(e,n,t){return n.every((r,o)=>jD[t](e[o].parameters,r.parameters))}var Et=class{root;queryParams;fragment;_queryParamMap;constructor(n=new Q([],{}),t={},r=null){this.root=n,this.queryParams=t,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Bn(this.queryParams),this._queryParamMap}toString(){return BT.serialize(this)}},Q=class{segments;children;parent=null;constructor(n,t){this.segments=n,this.children=t,Object.values(t).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return ka(this)}},on=class{path;parameters;_parameterMap;constructor(n,t){this.path=n,this.parameters=t}get parameterMap(){return this._parameterMap??=Bn(this.parameters),this._parameterMap}toString(){return $D(this)}};function jT(e,n){return jn(e,n)&&e.every((t,r)=>bt(t.parameters,n[r].parameters))}function jn(e,n){return e.length!==n.length?!1:e.every((t,r)=>t.path===n[r].path)}function VT(e,n){let t=[];return Object.entries(e.children).forEach(([r,o])=>{r===F&&(t=t.concat(n(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==F&&(t=t.concat(n(o,r)))}),t}var Ht=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>new sn,providedIn:"root"})}return e})(),sn=class{parse(n){let t=new tf(n);return new Et(t.parseRootSegment(),t.parseQueryParams(),t.parseFragment())}serialize(n){let t=`/${zo(n.root,!0)}`,r=$T(n.queryParams),o=typeof n.fragment=="string"?`#${UT(n.fragment)}`:"";return`${t}${r}${o}`}},BT=new sn;function ka(e){return e.segments.map(n=>$D(n)).join("/")}function zo(e,n){if(!e.hasChildren())return ka(e);if(n){let t=e.children[F]?zo(e.children[F],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==F&&r.push(`${o}:${zo(i,!1)}`)}),r.length>0?`${t}(${r.join("//")})`:t}else{let t=VT(e,(r,o)=>o===F?[zo(e.children[F],!1)]:[`${o}:${zo(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[F]!=null?`${ka(e)}/${t[0]}`:`${ka(e)}/(${t.join("//")})`}}function HD(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Ra(e){return HD(e).replace(/%3B/gi,";")}function UT(e){return encodeURI(e)}function ef(e){return HD(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Fa(e){return decodeURIComponent(e)}function xD(e){return Fa(e.replace(/\+/g,"%20"))}function $D(e){return`${ef(e.path)}${HT(e.parameters)}`}function HT(e){return Object.entries(e).map(([n,t])=>`;${ef(n)}=${ef(t)}`).join("")}function $T(e){let n=Object.entries(e).map(([t,r])=>Array.isArray(r)?r.map(o=>`${Ra(t)}=${Ra(o)}`).join("&"):`${Ra(t)}=${Ra(r)}`).filter(t=>t);return n.length?`?${n.join("&")}`:""}var zT=/^[^\/()?;#]+/;function Zd(e){let n=e.match(zT);return n?n[0]:""}var GT=/^[^\/()?;=#]+/;function WT(e){let n=e.match(GT);return n?n[0]:""}var qT=/^[^=?&#]+/;function ZT(e){let n=e.match(qT);return n?n[0]:""}var YT=/^[^&#]+/;function QT(e){let n=e.match(YT);return n?n[0]:""}var tf=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new Q([],{}):new Q([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let t={};this.peekStartsWith("/(")&&(this.capture("/"),t=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(t).length>0)&&(r[F]=new Q(n,t)),r}parseSegment(){let n=Zd(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new A(4009,!1);return this.capture(n),new on(Fa(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let t=WT(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let o=Zd(this.remaining);o&&(r=o,this.capture(r))}n[Fa(t)]=Fa(r)}parseQueryParam(n){let t=ZT(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let s=QT(this.remaining);s&&(r=s,this.capture(r))}let o=xD(t),i=xD(r);if(n.hasOwnProperty(o)){let s=n[o];Array.isArray(s)||(s=[s],n[o]=s),s.push(i)}else n[o]=i}parseParens(n){let t={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Zd(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new A(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):n&&(i=F);let s=this.parseChildren();t[i]=Object.keys(s).length===1?s[F]:new Q([],s),this.consumeOptional("//")}return t}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new A(4011,!1)}};function zD(e){return e.segments.length>0?new Q([],{[F]:e}):e}function GD(e){let n={};for(let[r,o]of Object.entries(e.children)){let i=GD(o);if(r===F&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))n[s]=a;else(i.segments.length>0||i.hasChildren())&&(n[r]=i)}let t=new Q(e.segments,n);return KT(t)}function KT(e){if(e.numberOfChildren===1&&e.children[F]){let n=e.children[F];return new Q(e.segments.concat(n.segments),n.children)}return e}function an(e){return e instanceof Et}function WD(e,n,t=null,r=null){let o=qD(e);return ZD(o,n,t,r)}function qD(e){let n;function t(i){let s={};for(let c of i.children){let l=t(c);s[c.outlet]=l}let a=new Q(i.url,s);return i===e&&(n=a),a}let r=t(e.root),o=zD(r);return n??o}function ZD(e,n,t,r){let o=e;for(;o.parent;)o=o.parent;if(n.length===0)return Yd(o,o,o,t,r);let i=XT(n);if(i.toRoot())return Yd(o,o,new Q([],{}),t,r);let s=JT(i,o,e),a=s.processChildren?Wo(s.segmentGroup,s.index,i.commands):QD(s.segmentGroup,s.index,i.commands);return Yd(o,s.segmentGroup,a,t,r)}function La(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Zo(e){return typeof e=="object"&&e!=null&&e.outlets}function Yd(e,n,t,r,o){let i={};r&&Object.entries(r).forEach(([c,l])=>{i[c]=Array.isArray(l)?l.map(d=>`${d}`):`${l}`});let s;e===n?s=t:s=YD(e,n,t);let a=zD(GD(s));return new Et(a,i,o)}function YD(e,n,t){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===n?r[o]=t:r[o]=YD(i,n,t)}),new Q(e.segments,r)}var ja=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,t,r){if(this.isAbsolute=n,this.numberOfDoubleDots=t,this.commands=r,n&&r.length>0&&La(r[0]))throw new A(4003,!1);let o=r.find(Zo);if(o&&o!==LD(r))throw new A(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function XT(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new ja(!0,0,e);let n=0,t=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?t=!0:a===".."?n++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new ja(t,n,r)}var Pr=class{segmentGroup;processChildren;index;constructor(n,t,r){this.segmentGroup=n,this.processChildren=t,this.index=r}};function JT(e,n,t){if(e.isAbsolute)return new Pr(n,!0,0);if(!t)return new Pr(n,!1,NaN);if(t.parent===null)return new Pr(t,!0,0);let r=La(e.commands[0])?0:1,o=t.segments.length-1+r;return ex(t,o,e.numberOfDoubleDots)}function ex(e,n,t){let r=e,o=n,i=t;for(;i>o;){if(i-=o,r=r.parent,!r)throw new A(4005,!1);o=r.segments.length}return new Pr(r,!1,o-i)}function tx(e){return Zo(e[0])?e[0].outlets:{[F]:e}}function QD(e,n,t){if(e??=new Q([],{}),e.segments.length===0&&e.hasChildren())return Wo(e,n,t);let r=nx(e,n,t),o=t.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new Q(e.segments.slice(0,r.pathIndex),{});return i.children[F]=new Q(e.segments.slice(r.pathIndex),e.children),Wo(i,0,o)}else return r.match&&o.length===0?new Q(e.segments,{}):r.match&&!e.hasChildren()?nf(e,n,t):r.match?Wo(e,0,o):nf(e,n,t)}function Wo(e,n,t){if(t.length===0)return new Q(e.segments,{});{let r=tx(t),o={};if(Object.keys(r).some(i=>i!==F)&&e.children[F]&&e.numberOfChildren===1&&e.children[F].segments.length===0){let i=Wo(e.children[F],n,t);return new Q(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=QD(e.children[i],n,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new Q(e.segments,o)}}function nx(e,n,t){let r=0,o=n,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=t.length)return i;let s=e.segments[o],a=t[r];if(Zo(a))break;let c=`${a}`,l=r<t.length-1?t[r+1]:null;if(o>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!RD(c,l,s))return i;r+=2}else{if(!RD(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function nf(e,n,t){let r=e.segments.slice(0,n),o=0;for(;o<t.length;){let i=t[o];if(Zo(i)){let c=rx(i.outlets);return new Q(r,c)}if(o===0&&La(t[0])){let c=e.segments[n];r.push(new on(c.path,AD(t[0]))),o++;continue}let s=Zo(i)?i.outlets[F]:`${i}`,a=o<t.length-1?t[o+1]:null;s&&a&&La(a)?(r.push(new on(s,AD(a))),o+=2):(r.push(new on(s,{})),o++)}return new Q(r,{})}function rx(e){let n={};return Object.entries(e).forEach(([t,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[t]=nf(new Q([],{}),0,r))}),n}function AD(e){let n={};return Object.entries(e).forEach(([t,r])=>n[t]=`${r}`),n}function RD(e,n,t){return e==t.path&&bt(n,t.parameters)}var Pa="imperative",pe=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(pe||{}),Ge=class{id;url;constructor(n,t){this.id=n,this.url=t}},_t=class extends Ge{type=pe.NavigationStart;navigationTrigger;restoredState;constructor(n,t,r="imperative",o=null){super(n,t),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Je=class extends Ge{urlAfterRedirects;type=pe.NavigationEnd;constructor(n,t,r){super(n,t),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Le=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(Le||{}),jr=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(jr||{}),wt=class extends Ge{reason;code;type=pe.NavigationCancel;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Mt=class extends Ge{reason;code;type=pe.NavigationSkipped;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}},Vr=class extends Ge{error;target;type=pe.NavigationError;constructor(n,t,r,o){super(n,t),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Yo=class extends Ge{urlAfterRedirects;state;type=pe.RoutesRecognized;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Va=class extends Ge{urlAfterRedirects;state;type=pe.GuardsCheckStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ba=class extends Ge{urlAfterRedirects;state;shouldActivate;type=pe.GuardsCheckEnd;constructor(n,t,r,o,i){super(n,t),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Ua=class extends Ge{urlAfterRedirects;state;type=pe.ResolveStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ha=class extends Ge{urlAfterRedirects;state;type=pe.ResolveEnd;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},$a=class{route;type=pe.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},za=class{route;type=pe.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Ga=class{snapshot;type=pe.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Wa=class{snapshot;type=pe.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},qa=class{snapshot;type=pe.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Za=class{snapshot;type=pe.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Br=class{routerEvent;position;anchor;type=pe.Scroll;constructor(n,t,r){this.routerEvent=n,this.position=t,this.anchor=r}toString(){let n=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${n}')`}},Qo=class{},Ur=class{url;navigationBehaviorOptions;constructor(n,t){this.url=n,this.navigationBehaviorOptions=t}};function ox(e,n){return e.providers&&!e._injector&&(e._injector=_o(e.providers,n,`Route: ${e.path}`)),e._injector??n}function lt(e){return e.outlet||F}function ix(e,n){let t=e.filter(r=>lt(r)===n);return t.push(...e.filter(r=>lt(r)!==n)),t}function ri(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let n=e.parent;n;n=n.parent){let t=n.routeConfig;if(t?._loadedInjector)return t._loadedInjector;if(t?._injector)return t._injector}return null}var Ya=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return ri(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new St(this.rootInjector)}},St=(()=>{class e{rootInjector;contexts=new Map;constructor(t){this.rootInjector=t}onChildOutletCreated(t,r){let o=this.getOrCreateContext(t);o.outlet=r,this.contexts.set(t,o)}onChildOutletDestroyed(t){let r=this.getContext(t);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let r=this.getContext(t);return r||(r=new Ya(this.rootInjector),this.contexts.set(t,r)),r}getContext(t){return this.contexts.get(t)||null}static \u0275fac=function(r){return new(r||e)(R(ie))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Qa=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let t=this.pathFromRoot(n);return t.length>1?t[t.length-2]:null}children(n){let t=rf(n,this._root);return t?t.children.map(r=>r.value):[]}firstChild(n){let t=rf(n,this._root);return t&&t.children.length>0?t.children[0].value:null}siblings(n){let t=of(n,this._root);return t.length<2?[]:t[t.length-2].children.map(o=>o.value).filter(o=>o!==n)}pathFromRoot(n){return of(n,this._root).map(t=>t.value)}};function rf(e,n){if(e===n.value)return n;for(let t of n.children){let r=rf(e,t);if(r)return r}return null}function of(e,n){if(e===n.value)return[n];for(let t of n.children){let r=of(e,t);if(r.length)return r.unshift(n),r}return[]}var ze=class{value;children;constructor(n,t){this.value=n,this.children=t}toString(){return`TreeNode(${this.value})`}};function Fr(e){let n={};return e&&e.children.forEach(t=>n[t.value.outlet]=t),n}var Ko=class extends Qa{snapshot;constructor(n,t){super(n),this.snapshot=t,hf(this,n)}toString(){return this.snapshot.toString()}};function KD(e){let n=sx(e),t=new de([new on("",{})]),r=new de({}),o=new de({}),i=new de({}),s=new de(""),a=new Me(t,r,i,s,o,F,e,n.root);return a.snapshot=n.root,new Ko(new ze(a,[]),n)}function sx(e){let n={},t={},r={},o="",i=new Vn([],n,r,o,t,F,e,null,{});return new Xo("",new ze(i,[]))}var Me=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,t,r,o,i,s,a,c){this.urlSubject=n,this.paramsSubject=t,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(H(l=>l[ni]))??O(void 0),this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(H(n=>Bn(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(H(n=>Bn(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Ka(e,n,t="emptyOnly"){let r,{routeConfig:o}=e;return n!==null&&(t==="always"||o?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:I(I({},n.params),e.params),data:I(I({},n.data),e.data),resolve:I(I(I(I({},e.data),n.data),o?.data),e._resolvedData)}:r={params:I({},e.params),data:I({},e.data),resolve:I(I({},e.data),e._resolvedData??{})},o&&JD(o)&&(r.resolve[ni]=o.title),r}var Vn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[ni]}constructor(n,t,r,o,i,s,a,c,l){this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Bn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Bn(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),t=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${t}')`}},Xo=class extends Qa{url;constructor(n,t){super(t),this.url=n,hf(this,t)}toString(){return XD(this._root)}};function hf(e,n){n.value._routerState=e,n.children.forEach(t=>hf(e,t))}function XD(e){let n=e.children.length>0?` { ${e.children.map(XD).join(", ")} } `:"";return`${e.value}${n}`}function Qd(e){if(e.snapshot){let n=e.snapshot,t=e._futureSnapshot;e.snapshot=t,bt(n.queryParams,t.queryParams)||e.queryParamsSubject.next(t.queryParams),n.fragment!==t.fragment&&e.fragmentSubject.next(t.fragment),bt(n.params,t.params)||e.paramsSubject.next(t.params),kT(n.url,t.url)||e.urlSubject.next(t.url),bt(n.data,t.data)||e.dataSubject.next(t.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function sf(e,n){let t=bt(e.params,n.params)&&jT(e.url,n.url),r=!e.parent!=!n.parent;return t&&!r&&(!e.parent||sf(e.parent,n.parent))}function JD(e){return typeof e.title=="string"||e.title===null}var eI=new x(""),pf=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=F;activateEvents=new oe;deactivateEvents=new oe;attachEvents=new oe;detachEvents=new oe;routerOutletData=mv(void 0);parentContexts=m(St);location=m(Ue);changeDetector=m(D);inputBinder=m(oi,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(t){if(t.name){let{firstChange:r,previousValue:o}=t.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new A(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new A(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new A(4012,!1);this.location.detach();let t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,r){this.activated=t,this._activatedRoute=r,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){let t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new A(4013,!1);this._activatedRoute=t;let o=this.location,s=t.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new af(t,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=V({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Xe]})}return e})(),af=class{route;childContexts;parent;outletData;constructor(n,t,r,o){this.route=n,this.childContexts=t,this.parent=r,this.outletData=o}get(n,t){return n===Me?this.route:n===St?this.childContexts:n===eI?this.outletData:this.parent.get(n,t)}},oi=new x(""),gf=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,o=yn([r.queryParams,r.params,r.data]).pipe(ve(([i,s,a],c)=>(a=I(I(I({},i),s),a),c===0?O(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=ha(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),mf=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=C({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&_d(0,"router-outlet")},dependencies:[pf],encapsulation:2})}return e})();function vf(e){let n=e.children&&e.children.map(vf),t=n?P(I({},e),{children:n}):I({},e);return!t.component&&!t.loadComponent&&(n||t.loadChildren)&&t.outlet&&t.outlet!==F&&(t.component=mf),t}function ax(e,n,t){let r=Jo(e,n._root,t?t._root:void 0);return new Ko(r,n)}function Jo(e,n,t){if(t&&e.shouldReuseRoute(n.value,t.value.snapshot)){let r=t.value;r._futureSnapshot=n.value;let o=cx(e,n,t);return new ze(r,o)}else{if(e.shouldAttach(n.value)){let i=e.retrieve(n.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>Jo(e,a)),s}}let r=lx(n.value),o=n.children.map(i=>Jo(e,i));return new ze(r,o)}}function cx(e,n,t){return n.children.map(r=>{for(let o of t.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Jo(e,r,o);return Jo(e,r)})}function lx(e){return new Me(new de(e.url),new de(e.params),new de(e.queryParams),new de(e.fragment),new de(e.data),e.outlet,e.component,e)}var Hr=class{redirectTo;navigationBehaviorOptions;constructor(n,t){this.redirectTo=n,this.navigationBehaviorOptions=t}},tI="ngNavigationCancelingError";function Xa(e,n){let{redirectTo:t,navigationBehaviorOptions:r}=an(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,o=nI(!1,Le.Redirect);return o.url=t,o.navigationBehaviorOptions=r,o}function nI(e,n){let t=new Error(`NavigationCancelingError: ${e||""}`);return t[tI]=!0,t.cancellationCode=n,t}function ux(e){return rI(e)&&an(e.url)}function rI(e){return!!e&&e[tI]}var dx=(e,n,t,r)=>H(o=>(new cf(n,o.targetRouterState,o.currentRouterState,t,r).activate(e),o)),cf=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,t,r,o,i){this.routeReuseStrategy=n,this.futureState=t,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(n){let t=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(t,r,n),Qd(this.futureState.root),this.activateChildRoutes(t,r,n)}deactivateChildRoutes(n,t,r){let o=Fr(t);n.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(n,t,s.children)}else this.deactivateChildRoutes(n,t,r);else i&&this.deactivateRouteAndItsChildren(t,r)}deactivateRouteAndItsChildren(n,t){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,t):this.deactivateRouteAndOutlet(n,t)}detachAndStoreRouteSubtree(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=Fr(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=Fr(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,t,r){let o=Fr(t);n.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new Za(i.value.snapshot))}),n.children.length&&this.forwardEvent(new Wa(n.value.snapshot))}activateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(Qd(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(n,t,s.children)}else this.activateChildRoutes(n,t,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Qd(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},Ja=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},Lr=class{component;route;constructor(n,t){this.component=n,this.route=t}};function fx(e,n,t){let r=e._root,o=n?n._root:null;return Go(r,o,t,[r.value])}function hx(e){let n=e.routeConfig?e.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:e,guards:n}}function zr(e,n){let t=Symbol(),r=n.get(e,t);return r===t?typeof e=="function"&&!pm(e)?e:n.get(e):r}function Go(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Fr(n);return e.children.forEach(s=>{px(s,i[s.value.outlet],t,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>qo(a,t.getContext(s),o)),o}function px(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=n?n.value:null,a=t?t.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=gx(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new Ja(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?Go(e,n,a?a.children:null,r,o):Go(e,n,t,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Lr(a.outlet.component,s))}else s&&qo(n,a,o),o.canActivateChecks.push(new Ja(r)),i.component?Go(e,null,a?a.children:null,r,o):Go(e,null,t,r,o);return o}function gx(e,n,t){if(typeof t=="function")return t(e,n);switch(t){case"pathParamsChange":return!jn(e.url,n.url);case"pathParamsOrQueryParamsChange":return!jn(e.url,n.url)||!bt(e.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!sf(e,n)||!bt(e.queryParams,n.queryParams);case"paramsChange":default:return!sf(e,n)}}function qo(e,n,t){let r=Fr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?n?qo(s,n.children.getContext(i),t):qo(s,null,t):qo(s,n,t)}),o.component?n&&n.outlet&&n.outlet.isActivated?t.canDeactivateChecks.push(new Lr(n.outlet.component,o)):t.canDeactivateChecks.push(new Lr(null,o)):t.canDeactivateChecks.push(new Lr(null,o))}function ii(e){return typeof e=="function"}function mx(e){return typeof e=="boolean"}function vx(e){return e&&ii(e.canLoad)}function yx(e){return e&&ii(e.canActivate)}function Dx(e){return e&&ii(e.canActivateChild)}function Ix(e){return e&&ii(e.canDeactivate)}function Cx(e){return e&&ii(e.canMatch)}function oI(e){return e instanceof et||e?.name==="EmptyError"}var Na=Symbol("INITIAL_VALUE");function $r(){return ve(e=>yn(e.map(n=>n.pipe(xt(1),el(Na)))).pipe(H(n=>{for(let t of n)if(t!==!0){if(t===Na)return Na;if(t===!1||bx(t))return t}return!0}),me(n=>n!==Na),xt(1)))}function bx(e){return an(e)||e instanceof Hr}function wx(e,n){return ae(t=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=t;return s.length===0&&i.length===0?O(P(I({},t),{guardsResult:!0})):Ex(s,r,o,e).pipe(ae(a=>a&&mx(a)?_x(r,i,e,n):O(a)),H(a=>P(I({},t),{guardsResult:a})))})}function Ex(e,n,t,r){return ne(e).pipe(ae(o=>Ax(o.component,o.route,t,n,r)),At(o=>o!==!0,!0))}function _x(e,n,t,r){return ne(n).pipe(dt(o=>or(Sx(o.route.parent,r),Mx(o.route,r),xx(e,o.path,t),Tx(e,o.route,t))),At(o=>o!==!0,!0))}function Mx(e,n){return e!==null&&n&&n(new qa(e)),O(!0)}function Sx(e,n){return e!==null&&n&&n(new Ga(e)),O(!0)}function Tx(e,n,t){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return O(!0);let o=r.map(i=>qi(()=>{let s=ri(n)??t,a=zr(i,s),c=yx(a)?a.canActivate(n,e):ke(s,()=>a(n,e));return cn(c).pipe(At())}));return O(o).pipe($r())}function xx(e,n,t){let r=n[n.length-1],i=n.slice(0,n.length-1).reverse().map(s=>hx(s)).filter(s=>s!==null).map(s=>qi(()=>{let a=s.guards.map(c=>{let l=ri(s.node)??t,d=zr(c,l),h=Dx(d)?d.canActivateChild(r,e):ke(l,()=>d(r,e));return cn(h).pipe(At())});return O(a).pipe($r())}));return O(i).pipe($r())}function Ax(e,n,t,r,o){let i=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!i||i.length===0)return O(!0);let s=i.map(a=>{let c=ri(n)??o,l=zr(a,c),d=Ix(l)?l.canDeactivate(e,n,t,r):ke(c,()=>l(e,n,t,r));return cn(d).pipe(At())});return O(s).pipe($r())}function Rx(e,n,t,r){let o=n.canLoad;if(o===void 0||o.length===0)return O(!0);let i=o.map(s=>{let a=zr(s,e),c=vx(a)?a.canLoad(n,t):ke(e,()=>a(n,t));return cn(c)});return O(i).pipe($r(),iI(r))}function iI(e){return zc(Ce(n=>{if(typeof n!="boolean")throw Xa(e,n)}),H(n=>n===!0))}function Nx(e,n,t,r){let o=n.canMatch;if(!o||o.length===0)return O(!0);let i=o.map(s=>{let a=zr(s,e),c=Cx(a)?a.canMatch(n,t):ke(e,()=>a(n,t));return cn(c)});return O(i).pipe($r(),iI(r))}var ei=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},ti=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function kr(e){return tr(new ei(e))}function Ox(e){return tr(new A(4e3,!1))}function kx(e){return tr(nI(!1,Le.GuardRejected))}var lf=class{urlSerializer;urlTree;constructor(n,t){this.urlSerializer=n,this.urlTree=t}lineralizeSegments(n,t){let r=[],o=t.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return O(r);if(o.numberOfChildren>1||!o.children[F])return Ox(`${n.redirectTo}`);o=o.children[F]}}applyRedirectCommands(n,t,r,o,i){if(typeof t!="string"){let a=t,{queryParams:c,fragment:l,routeConfig:d,url:h,outlet:p,params:f,data:y,title:S}=o,N=ke(i,()=>a({params:f,data:y,queryParams:c,fragment:l,routeConfig:d,url:h,outlet:p,title:S}));if(N instanceof Et)throw new ti(N);t=N}let s=this.applyRedirectCreateUrlTree(t,this.urlSerializer.parse(t),n,r);if(t[0]==="/")throw new ti(s);return s}applyRedirectCreateUrlTree(n,t,r,o){let i=this.createSegmentGroup(n,t.root,r,o);return new Et(i,this.createQueryParams(t.queryParams,this.urlTree.queryParams),t.fragment)}createQueryParams(n,t){let r={};return Object.entries(n).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=t[a]}else r[o]=i}),r}createSegmentGroup(n,t,r,o){let i=this.createSegments(n,t.segments,r,o),s={};return Object.entries(t.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(n,c,r,o)}),new Q(i,s)}createSegments(n,t,r,o){return t.map(i=>i.path[0]===":"?this.findPosParam(n,i,o):this.findOrReturn(i,r))}findPosParam(n,t,r){let o=r[t.path.substring(1)];if(!o)throw new A(4001,!1);return o}findOrReturn(n,t){let r=0;for(let o of t){if(o.path===n.path)return t.splice(r),o;r++}return n}},uf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Fx(e,n,t,r,o){let i=sI(e,n,t);return i.matched?(r=ox(n,r),Nx(r,n,t,o).pipe(H(s=>s===!0?i:I({},uf)))):O(i)}function sI(e,n,t){if(n.path==="**")return Px(t);if(n.path==="")return n.pathMatch==="full"&&(e.hasChildren()||t.length>0)?I({},uf):{matched:!0,consumedSegments:[],remainingSegments:t,parameters:{},positionalParamSegments:{}};let o=(n.matcher||FD)(t,e,n);if(!o)return I({},uf);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?I(I({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:t.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function Px(e){return{matched:!0,parameters:e.length>0?LD(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function ND(e,n,t,r){return t.length>0&&Vx(e,t,r)?{segmentGroup:new Q(n,jx(r,new Q(t,e.children))),slicedSegments:[]}:t.length===0&&Bx(e,t,r)?{segmentGroup:new Q(e.segments,Lx(e,t,r,e.children)),slicedSegments:t}:{segmentGroup:new Q(e.segments,e.children),slicedSegments:t}}function Lx(e,n,t,r){let o={};for(let i of t)if(tc(e,n,i)&&!r[lt(i)]){let s=new Q([],{});o[lt(i)]=s}return I(I({},r),o)}function jx(e,n){let t={};t[F]=n;for(let r of e)if(r.path===""&&lt(r)!==F){let o=new Q([],{});t[lt(r)]=o}return t}function Vx(e,n,t){return t.some(r=>tc(e,n,r)&&lt(r)!==F)}function Bx(e,n,t){return t.some(r=>tc(e,n,r))}function tc(e,n,t){return(e.hasChildren()||n.length>0)&&t.pathMatch==="full"?!1:t.path===""}function Ux(e,n,t){return n.length===0&&!e.children[t]}var df=class{};function Hx(e,n,t,r,o,i,s="emptyOnly"){return new ff(e,n,t,r,o,s,i).recognize()}var $x=31,ff=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,t,r,o,i,s,a){this.injector=n,this.configLoader=t,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new lf(this.urlSerializer,this.urlTree)}noMatchError(n){return new A(4002,`'${n.segmentGroup}'`)}recognize(){let n=ND(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(H(({children:t,rootSnapshot:r})=>{let o=new ze(r,t),i=new Xo("",o),s=WD(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(n){let t=new Vn([],Object.freeze({}),Object.freeze(I({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),F,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,F,t).pipe(H(r=>({children:r,rootSnapshot:t})),ut(r=>{if(r instanceof ti)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof ei?this.noMatchError(r):r}))}processSegmentGroup(n,t,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,t,r,i):this.processSegment(n,t,r,r.segments,o,!0,i).pipe(H(s=>s instanceof ze?[s]:[]))}processChildren(n,t,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return ne(i).pipe(dt(s=>{let a=r.children[s],c=ix(t,s);return this.processSegmentGroup(n,c,a,s,o)}),Jc((s,a)=>(s.push(...a),s)),Zt(null),Xc(),ae(s=>{if(s===null)return kr(r);let a=aI(s);return zx(a),O(a)}))}processSegment(n,t,r,o,i,s,a){return ne(t).pipe(dt(c=>this.processSegmentAgainstRoute(c._injector??n,t,c,r,o,i,s,a).pipe(ut(l=>{if(l instanceof ei)return O(null);throw l}))),At(c=>!!c),ut(c=>{if(oI(c))return Ux(r,o,i)?O(new df):kr(r);throw c}))}processSegmentAgainstRoute(n,t,r,o,i,s,a,c){return lt(r)!==s&&(s===F||!tc(o,i,r))?kr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,o,t,r,i,s,c):kr(o)}expandSegmentAgainstRouteUsingRedirect(n,t,r,o,i,s,a){let{matched:c,parameters:l,consumedSegments:d,positionalParamSegments:h,remainingSegments:p}=sI(t,o,i);if(!c)return kr(t);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>$x&&(this.allowRedirects=!1));let f=new Vn(i,l,Object.freeze(I({},this.urlTree.queryParams)),this.urlTree.fragment,OD(o),lt(o),o.component??o._loadedComponent??null,o,kD(o)),y=Ka(f,a,this.paramsInheritanceStrategy);f.params=Object.freeze(y.params),f.data=Object.freeze(y.data);let S=this.applyRedirects.applyRedirectCommands(d,o.redirectTo,h,f,n);return this.applyRedirects.lineralizeSegments(o,S).pipe(ae(N=>this.processSegment(n,r,t,N.concat(p),s,!1,a)))}matchSegmentAgainstRoute(n,t,r,o,i,s){let a=Fx(t,r,o,n,this.urlSerializer);return r.path==="**"&&(t.children={}),a.pipe(ve(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,o).pipe(ve(({routes:l})=>{let d=r._loadedInjector??n,{parameters:h,consumedSegments:p,remainingSegments:f}=c,y=new Vn(p,h,Object.freeze(I({},this.urlTree.queryParams)),this.urlTree.fragment,OD(r),lt(r),r.component??r._loadedComponent??null,r,kD(r)),S=Ka(y,s,this.paramsInheritanceStrategy);y.params=Object.freeze(S.params),y.data=Object.freeze(S.data);let{segmentGroup:N,slicedSegments:z}=ND(t,p,f,l);if(z.length===0&&N.hasChildren())return this.processChildren(d,l,N,y).pipe(H(ce=>new ze(y,ce)));if(l.length===0&&z.length===0)return O(new ze(y,[]));let Tt=lt(r)===i;return this.processSegment(d,l,N,z,Tt?F:i,!0,y).pipe(H(ce=>new ze(y,ce instanceof ze?[ce]:[])))}))):kr(t)))}getChildConfig(n,t,r){return t.children?O({routes:t.children,injector:n}):t.loadChildren?t._loadedRoutes!==void 0?O({routes:t._loadedRoutes,injector:t._loadedInjector}):Rx(n,t,r,this.urlSerializer).pipe(ae(o=>o?this.configLoader.loadChildren(n,t).pipe(Ce(i=>{t._loadedRoutes=i.routes,t._loadedInjector=i.injector})):kx(t))):O({routes:[],injector:n})}};function zx(e){e.sort((n,t)=>n.value.outlet===F?-1:t.value.outlet===F?1:n.value.outlet.localeCompare(t.value.outlet))}function Gx(e){let n=e.value.routeConfig;return n&&n.path===""}function aI(e){let n=[],t=new Set;for(let r of e){if(!Gx(r)){n.push(r);continue}let o=n.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),t.add(o)):n.push(r)}for(let r of t){let o=aI(r.children);n.push(new ze(r.value,o))}return n.filter(r=>!t.has(r))}function OD(e){return e.data||{}}function kD(e){return e.resolve||{}}function Wx(e,n,t,r,o,i){return ae(s=>Hx(e,n,t,r,s.extractedUrl,o,i).pipe(H(({state:a,tree:c})=>P(I({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function qx(e,n){return ae(t=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=t;if(!o.length)return O(t);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let l of cI(c))s.add(l);let a=0;return ne(s).pipe(dt(c=>i.has(c)?Zx(c,r,e,n):(c.data=Ka(c,c.parent,e).resolve,O(void 0))),Ce(()=>a++),ir(1),ae(c=>a===s.size?O(t):Re))})}function cI(e){let n=e.children.map(t=>cI(t)).flat();return[e,...n]}function Zx(e,n,t,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!JD(o)&&(i[ni]=o.title),Yx(i,e,n,r).pipe(H(s=>(e._resolvedData=s,e.data=Ka(e,e.parent,t).resolve,null)))}function Yx(e,n,t,r){let o=Jd(e);if(o.length===0)return O({});let i={};return ne(o).pipe(ae(s=>Qx(e[s],n,t,r).pipe(At(),Ce(a=>{if(a instanceof Hr)throw Xa(new sn,a);i[s]=a}))),ir(1),H(()=>i),ut(s=>oI(s)?Re:tr(s)))}function Qx(e,n,t,r){let o=ri(n)??r,i=zr(e,o),s=i.resolve?i.resolve(n,t):ke(o,()=>i(n,t));return cn(s)}function Kd(e){return ve(n=>{let t=e(n);return t?ne(t).pipe(H(()=>n)):O(n)})}var yf=(()=>{class e{buildTitle(t){let r,o=t.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===F);return r}getResolvedTitleForRoute(t){return t.data[ni]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>m(lI),providedIn:"root"})}return e})(),lI=(()=>{class e extends yf{title;constructor(t){super(),this.title=t}updateTitle(t){let r=this.buildTitle(t);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(R(SD))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Un=new x("",{providedIn:"root",factory:()=>({})}),Gr=new x(""),nc=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=m(jy);loadComponent(t){if(this.componentLoaders.get(t))return this.componentLoaders.get(t);if(t._loadedComponent)return O(t._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(t);let r=cn(t.loadComponent()).pipe(H(dI),Ce(i=>{this.onLoadEndListener&&this.onLoadEndListener(t),t._loadedComponent=i}),Yt(()=>{this.componentLoaders.delete(t)})),o=new Jn(r,()=>new K).pipe(Xn());return this.componentLoaders.set(t,o),o}loadChildren(t,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return O({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=uI(r,this.compiler,t,this.onLoadEndListener).pipe(Yt(()=>{this.childrenLoaders.delete(r)})),s=new Jn(i,()=>new K).pipe(Xn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function uI(e,n,t,r){return cn(e.loadChildren()).pipe(H(dI),ae(o=>o instanceof Id||Array.isArray(o)?O(o):ne(n.compileModuleAsync(o))),H(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(t).injector,s=i.get(Gr,[],{optional:!0,self:!0}).flat()),{routes:s.map(vf),injector:i}}))}function Kx(e){return e&&typeof e=="object"&&"default"in e}function dI(e){return Kx(e)?e.default:e}var rc=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>m(Xx),providedIn:"root"})}return e})(),Xx=(()=>{class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,r){return t}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Df=new x(""),If=new x("");function fI(e,n,t){let r=e.get(If),o=e.get(se);return e.get(g).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(l=>setTimeout(l));let i,s=new Promise(l=>{i=l}),a=o.startViewTransition(()=>(i(),Jx(e))),{onViewTransitionCreated:c}=r;return c&&ke(e,()=>c({transition:a,from:n,to:t})),s})}function Jx(e){return new Promise(n=>{Ju({read:()=>setTimeout(n)},{injector:e})})}var Cf=new x(""),oc=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new K;transitionAbortSubject=new K;configLoader=m(nc);environmentInjector=m(ie);destroyRef=m(kn);urlSerializer=m(Ht);rootContexts=m(St);location=m($e);inputBindingEnabled=m(oi,{optional:!0})!==null;titleStrategy=m(yf);options=m(Un,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=m(rc);createViewTransition=m(Df,{optional:!0});navigationErrorHandler=m(Cf,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>O(void 0);rootComponentType=null;destroyed=!1;constructor(){let t=o=>this.events.next(new $a(o)),r=o=>this.events.next(new za(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=t,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(t){let r=++this.navigationId;this.transitions?.next(P(I({},t),{extractedUrl:this.urlHandlingStrategy.extract(t.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(t){return this.transitions=new de(null),this.transitions.pipe(me(r=>r!==null),ve(r=>{let o=!1,i=!1;return O(r).pipe(ve(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",Le.SupersededByNewNavigation),Re;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?P(I({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??t.onSameUrlNavigation;if(!a&&c!=="reload"){let l="";return this.events.next(new Mt(s.id,this.urlSerializer.serialize(s.rawUrl),l,jr.IgnoredSameUrlNavigation)),s.resolve(!1),Re}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return O(s).pipe(ve(l=>(this.events.next(new _t(l.id,this.urlSerializer.serialize(l.extractedUrl),l.source,l.restoredState)),l.id!==this.navigationId?Re:Promise.resolve(l))),Wx(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),Ce(l=>{r.targetSnapshot=l.targetSnapshot,r.urlAfterRedirects=l.urlAfterRedirects,this.currentNavigation=P(I({},this.currentNavigation),{finalUrl:l.urlAfterRedirects});let d=new Yo(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(d)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:l,extractedUrl:d,source:h,restoredState:p,extras:f}=s,y=new _t(l,this.urlSerializer.serialize(d),h,p);this.events.next(y);let S=KD(this.rootComponentType).snapshot;return this.currentTransition=r=P(I({},s),{targetSnapshot:S,urlAfterRedirects:d,extras:P(I({},f),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=d,O(r)}else{let l="";return this.events.next(new Mt(s.id,this.urlSerializer.serialize(s.extractedUrl),l,jr.IgnoredByUrlHandlingStrategy)),s.resolve(!1),Re}}),Ce(s=>{let a=new Va(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),H(s=>(this.currentTransition=r=P(I({},s),{guards:fx(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),wx(this.environmentInjector,s=>this.events.next(s)),Ce(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw Xa(this.urlSerializer,s.guardsResult);let a=new Ba(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),me(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",Le.GuardRejected),!1)),Kd(s=>{if(s.guards.canActivateChecks.length!==0)return O(s).pipe(Ce(a=>{let c=new Ua(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),ve(a=>{let c=!1;return O(a).pipe(qx(this.paramsInheritanceStrategy,this.environmentInjector),Ce({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",Le.NoDataFromResolver)}}))}),Ce(a=>{let c=new Ha(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),Kd(s=>{let a=c=>{let l=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&l.push(this.configLoader.loadComponent(c.routeConfig).pipe(Ce(d=>{c.component=d}),H(()=>{})));for(let d of c.children)l.push(...a(d));return l};return yn(a(s.targetSnapshot.root)).pipe(Zt(null),xt(1))}),Kd(()=>this.afterPreactivation()),ve(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?ne(c).pipe(H(()=>r)):O(r)}),H(s=>{let a=ax(t.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=P(I({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),Ce(()=>{this.events.next(new Qo)}),dx(this.rootContexts,t.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),xt(1),Ce({next:s=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Je(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{o=!0}}),tl(this.transitionAbortSubject.pipe(Ce(s=>{throw s}))),Yt(()=>{!o&&!i&&this.cancelNavigationTransition(r,"",Le.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),ut(s=>{if(this.destroyed)return r.resolve(!1),Re;if(i=!0,rI(s))this.events.next(new wt(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),ux(s)?this.events.next(new Ur(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new Vr(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=ke(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof Hr){let{message:l,cancellationCode:d}=Xa(this.urlSerializer,c);this.events.next(new wt(r.id,this.urlSerializer.serialize(r.extractedUrl),l,d)),this.events.next(new Ur(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return Re}))}))}cancelNavigationTransition(t,r,o){let i=new wt(t.id,this.urlSerializer.serialize(t.extractedUrl),r,o);this.events.next(i),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let t=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return t.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function eA(e){return e!==Pa}var hI=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>m(tA),providedIn:"root"})}return e})(),ec=class{shouldDetach(n){return!1}store(n,t){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,t){return n.routeConfig===t.routeConfig}},tA=(()=>{class e extends ec{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),pI=(()=>{class e{urlSerializer=m(Ht);options=m(Un,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=m($e);urlHandlingStrategy=m(rc);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Et;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:t,initialUrl:r,targetBrowserUrl:o}){let i=t!==void 0?this.urlHandlingStrategy.merge(t,r):r,s=o??i;return s instanceof Et?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:t,finalUrl:r,initialUrl:o}){r&&t?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=t):this.rawUrlTree=o}routerState=KD(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:t}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>m(nA),providedIn:"root"})}return e})(),nA=(()=>{class e extends pI{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{t(r.url,r.state,"popstate")})})}handleRouterEvent(t,r){t instanceof _t?this.updateStateMemento():t instanceof Mt?this.commitTransition(r):t instanceof Yo?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof Qo?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof wt&&(t.code===Le.GuardRejected||t.code===Le.NoDataFromResolver)?this.restoreHistory(r):t instanceof Vr?this.restoreHistory(r,!0):t instanceof Je&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(t)||i){let a=this.browserPageId,c=I(I({},s),this.generateNgRouterState(o,a));this.location.replaceState(t,"",c)}else{let a=I(I({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(t,"",a)}}restoreHistory(t,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===t.finalUrl&&i===0&&(this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,r){return this.canceledNavigationResolution==="computed"?{navigationId:t,\u0275routerPageId:r}:{navigationId:t}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ic(e,n){e.events.pipe(me(t=>t instanceof Je||t instanceof wt||t instanceof Vr||t instanceof Mt),H(t=>t instanceof Je||t instanceof Mt?0:(t instanceof wt?t.code===Le.Redirect||t.code===Le.SupersededByNewNavigation:!1)?2:1),me(t=>t!==2),xt(1)).subscribe(()=>{n()})}var rA={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},oA={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},De=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=m(Sy);stateManager=m(pI);options=m(Un,{optional:!0})||{};pendingTasks=m(jt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=m(oc);urlSerializer=m(Ht);location=m($e);urlHandlingStrategy=m(rc);_events=new K;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=m(hI);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=m(Gr,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!m(oi,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}eventsSubscription=new le;subscribeToNavigationEvents(){let t=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof wt&&r.code!==Le.Redirect&&r.code!==Le.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Je)this.navigated=!0;else if(r instanceof Ur){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=I({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||eA(o.source)},s);this.scheduleNavigation(a,Pa,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}sA(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Pa,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,r,o)=>{this.navigateToSyncWithBrowser(t,o,r)})}navigateToSyncWithBrowser(t,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=I({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(t);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(vf),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:s,d=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":d=I(I({},this.currentUrlTree.queryParams),i);break;case"preserve":d=this.currentUrlTree.queryParams;break;default:d=i||null}d!==null&&(d=this.removeEmptyProps(d));let h;try{let p=o?o.snapshot:this.routerState.snapshot.root;h=qD(p)}catch{(typeof t[0]!="string"||t[0][0]!=="/")&&(t=[]),h=this.currentUrlTree.root}return ZD(h,t,d,l??null)}navigateByUrl(t,r={skipLocationChange:!1}){let o=an(t)?t:this.parseUrl(t),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Pa,null,r)}navigate(t,r={skipLocationChange:!1}){return iA(t),this.navigateByUrl(this.createUrlTree(t,r),r)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,r){let o;if(r===!0?o=I({},rA):r===!1?o=I({},oA):o=r,an(t))return TD(this.currentUrlTree,t,o);let i=this.parseUrl(t);return TD(this.currentUrlTree,i,o)}removeEmptyProps(t){return Object.entries(t).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(t,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,l;s?(a=s.resolve,c=s.reject,l=s.promise):l=new Promise((h,p)=>{a=h,c=p});let d=this.pendingTasks.add();return ic(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(d))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:i,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(h=>Promise.reject(h))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function iA(e){for(let n=0;n<e.length;n++)if(e[n]==null)throw new A(4008,!1)}function sA(e){return!(e instanceof Qo)&&!(e instanceof Ur)}var ai=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new K;constructor(t,r,o,i,s,a){this.router=t,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=t.events.subscribe(l=>{l instanceof Je&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(t){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",t)}ngOnChanges(t){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(t){t==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(an(t)?this.routerLinkInput=t:this.routerLinkInput=Array.isArray(t)?t:[t],this.setTabIndexIfNotOnNativeEl("0"))}onClick(t,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(t!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let t=this.urlTree;this.href=t!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(t)):null;let r=this.href===null?null:jv(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(t,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,t,r):o.removeAttribute(i,t)}get urlTree(){return this.routerLinkInput===null?null:an(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(u(De),u(Me),Lt("tabindex"),u(Jt),u(v),u(Pe))};static \u0275dir=V({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,o){r&1&&ye("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&ot("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",nn],skipLocationChange:[2,"skipLocationChange","skipLocationChange",nn],replaceUrl:[2,"replaceUrl","replaceUrl",nn],routerLink:"routerLink"},features:[Xe]})}return e})();var si=class{},aA=(()=>{class e{preload(t,r){return r().pipe(ut(()=>O(null)))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var gI=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(t,r,o,i){this.router=t,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(me(t=>t instanceof Je),dt(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=_o(i.providers,t,`Route: ${i.path}`));let s=i._injector??t,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return ne(o).pipe(rr())}preloadConfig(t,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(t,r):o=O(null);let i=o.pipe(ae(s=>s===null?O(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??t,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return ne([i,s]).pipe(rr())}else return i})}static \u0275fac=function(r){return new(r||e)(R(De),R(ie),R(si),R(nc))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),mI=new x(""),cA=(()=>{class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(t,r,o,i,s={}){this.urlSerializer=t,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof _t?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=t.navigationTrigger,this.restoredId=t.restoredState?t.restoredState.navigationId:0):t instanceof Je?(this.lastId=t.id,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.urlAfterRedirects).fragment)):t instanceof Mt&&t.code===jr.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof Br&&(t.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(t.position):t.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(t.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(t,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Br(t,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){py()};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();function lA(e){return e.routerState.root}function ci(e,n){return{\u0275kind:e,\u0275providers:n}}function uA(){let e=m(X);return n=>{let t=e.get(Dt);if(n!==t.components[0])return;let r=e.get(De),o=e.get(vI);e.get(wf)===1&&r.initialNavigation(),e.get(II,null,B.Optional)?.setUpPreloading(),e.get(mI,null,B.Optional)?.init(),r.resetRootComponentType(t.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var vI=new x("",{factory:()=>new K}),wf=new x("",{providedIn:"root",factory:()=>1});function yI(){let e=[{provide:wf,useValue:0},wd(()=>{let n=m(X);return n.get(Ad,Promise.resolve()).then(()=>new Promise(r=>{let o=n.get(De),i=n.get(vI);ic(o,()=>{r(!0)}),n.get(oc).afterPreactivation=()=>(r(!0),i.closed?O(void 0):i),o.initialNavigation()}))})];return ci(2,e)}function DI(){let e=[wd(()=>{m(De).setUpLocationChangeListener()}),{provide:wf,useValue:2}];return ci(3,e)}var II=new x("");function CI(e){return ci(0,[{provide:II,useExisting:gI},{provide:si,useExisting:e}])}function bI(){return ci(8,[gf,{provide:oi,useExisting:gf}])}function wI(e){Co("NgRouterViewTransitions");let n=[{provide:Df,useValue:fI},{provide:If,useValue:I({skipNextTransition:!!e?.skipInitialTransition},e)}];return ci(9,n)}var EI=[$e,{provide:Ht,useClass:sn},De,St,{provide:Me,useFactory:lA,deps:[De]},nc,[]],dA=(()=>{class e{constructor(){}static forRoot(t,r){return{ngModule:e,providers:[EI,[],{provide:Gr,multi:!0,useValue:t},[],r?.errorHandler?{provide:Cf,useValue:r.errorHandler}:[],{provide:Un,useValue:r||{}},r?.useHash?hA():pA(),fA(),r?.preloadingStrategy?CI(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?gA(r):[],r?.bindToComponentInputs?bI().\u0275providers:[],r?.enableViewTransitions?wI().\u0275providers:[],mA()]}}static forChild(t){return{ngModule:e,providers:[{provide:Gr,multi:!0,useValue:t}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Te({})}return e})();function fA(){return{provide:mI,useFactory:()=>{let e=m(tD),n=m(g),t=m(Un),r=m(oc),o=m(Ht);return t.scrollOffset&&e.setOffset(t.scrollOffset),new cA(o,r,e,n,t)}}}function hA(){return{provide:Pe,useClass:Nd}}function pA(){return{provide:Pe,useClass:ma}}function gA(e){return[e.initialNavigation==="disabled"?DI().\u0275providers:[],e.initialNavigation==="enabledBlocking"?yI().\u0275providers:[]]}var bf=new x("");function mA(){return[{provide:bf,useFactory:uA},{provide:Ed,multi:!0,useExisting:bf}]}var kI=(()=>{class e{_renderer;_elementRef;onChange=t=>{};onTouched=()=>{};constructor(t,r){this._renderer=t,this._elementRef=r}setProperty(t,r){this._renderer.setProperty(this._elementRef.nativeElement,t,r)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static \u0275fac=function(r){return new(r||e)(u(Jt),u(v))};static \u0275dir=V({type:e})}return e})(),vA=(()=>{class e extends kI{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,features:[ee]})}return e})(),$n=new x("");var yA={provide:$n,useExisting:Be(()=>FI),multi:!0};function DA(){let e=He()?He().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var IA=new x(""),FI=(()=>{class e extends kI{_compositionMode;_composing=!1;constructor(t,r,o){super(t,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!DA())}writeValue(t){let r=t??"";this.setProperty("value",r)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static \u0275fac=function(r){return new(r||e)(u(Jt),u(v),u(IA,8))};static \u0275dir=V({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&ye("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[Ae([yA]),ee]})}return e})();function CA(e){return e==null||PI(e)===0}function PI(e){return e==null?null:Array.isArray(e)||typeof e=="string"?e.length:e instanceof Set?e.size:null}var $t=new x(""),LI=new x("");function bA(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t<e?{min:{min:e,actual:n.value}}:null}}function wA(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t>e?{max:{max:e,actual:n.value}}:null}}function EA(e){return CA(e.value)?{required:!0}:null}function _A(e){return n=>{let t=n.value?.length??PI(n.value);return t!==null&&t>e?{maxlength:{requiredLength:e,actualLength:t}}:null}}function MI(e){return null}function jI(e){return e!=null}function VI(e){return Fn(e)?ne(e):e}function BI(e){let n={};return e.forEach(t=>{n=t!=null?I(I({},n),t):n}),Object.keys(n).length===0?null:n}function UI(e,n){return n.map(t=>t(e))}function MA(e){return!e.validate}function HI(e){return e.map(n=>MA(n)?n:t=>n.validate(t))}function SA(e){if(!e)return null;let n=e.filter(jI);return n.length==0?null:function(t){return BI(UI(t,n))}}function Sf(e){return e!=null?SA(HI(e)):null}function TA(e){if(!e)return null;let n=e.filter(jI);return n.length==0?null:function(t){let r=UI(t,n).map(VI);return Qc(r).pipe(H(BI))}}function Tf(e){return e!=null?TA(HI(e)):null}function SI(e,n){return e===null?[n]:Array.isArray(e)?[...e,n]:[e,n]}function xA(e){return e._rawValidators}function AA(e){return e._rawAsyncValidators}function Ef(e){return e?Array.isArray(e)?e:[e]:[]}function ac(e,n){return Array.isArray(e)?e.includes(n):e===n}function TI(e,n){let t=Ef(n);return Ef(e).forEach(o=>{ac(t,o)||t.push(o)}),t}function xI(e,n){return Ef(n).filter(t=>!ac(e,t))}var cc=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=Sf(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=Tf(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return this.control?this.control.hasError(n,t):!1}getError(n,t){return this.control?this.control.getError(n,t):null}},Zr=class extends cc{name;get formDirective(){return null}get path(){return null}},Hn=class extends cc{_parent=null;name=null;valueAccessor=null},lc=class{_cd;constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},RA={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},k$=P(I({},RA),{"[class.ng-submitted]":"isSubmitted"}),F$=(()=>{class e extends lc{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(u(Hn,2))};static \u0275dir=V({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&la("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[ee]})}return e})(),P$=(()=>{class e extends lc{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(u(Zr,10))};static \u0275dir=V({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&la("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},standalone:!1,features:[ee]})}return e})();var li="VALID",sc="INVALID",Wr="PENDING",ui="DISABLED",ln=class{},uc=class extends ln{value;source;constructor(n,t){super(),this.value=n,this.source=t}},fi=class extends ln{pristine;source;constructor(n,t){super(),this.pristine=n,this.source=t}},hi=class extends ln{touched;source;constructor(n,t){super(),this.touched=n,this.source=t}},qr=class extends ln{status;source;constructor(n,t){super(),this.status=n,this.source=t}},_f=class extends ln{source;constructor(n){super(),this.source=n}},Mf=class extends ln{source;constructor(n){super(),this.source=n}};function $I(e){return(hc(e)?e.validators:e)||null}function NA(e){return Array.isArray(e)?Sf(e):e||null}function zI(e,n){return(hc(n)?n.asyncValidators:e)||null}function OA(e){return Array.isArray(e)?Tf(e):e||null}function hc(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function kA(e,n,t){let r=e.controls;if(!(n?Object.keys(r):r).length)throw new A(1e3,"");if(!r[t])throw new A(1001,"")}function FA(e,n,t){e._forEachChild((r,o)=>{if(t[o]===void 0)throw new A(1002,"")})}var dc=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(n,t){this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return Bt(this.statusReactive)}set status(n){Bt(()=>this.statusReactive.set(n))}_status=Ao(()=>this.statusReactive());statusReactive=Io(void 0);get valid(){return this.status===li}get invalid(){return this.status===sc}get pending(){return this.status==Wr}get disabled(){return this.status===ui}get enabled(){return this.status!==ui}errors;get pristine(){return Bt(this.pristineReactive)}set pristine(n){Bt(()=>this.pristineReactive.set(n))}_pristine=Ao(()=>this.pristineReactive());pristineReactive=Io(!0);get dirty(){return!this.pristine}get touched(){return Bt(this.touchedReactive)}set touched(n){Bt(()=>this.touchedReactive.set(n))}_touched=Ao(()=>this.touchedReactive());touchedReactive=Io(!1);get untouched(){return!this.touched}_events=new K;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(TI(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(TI(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(xI(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(xI(n,this._rawAsyncValidators))}hasValidator(n){return ac(this._rawValidators,n)}hasAsyncValidator(n){return ac(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){let t=this.touched===!1;this.touched=!0;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched(P(I({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new hi(!0,r))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){let t=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:r})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,r),t&&n.emitEvent!==!1&&this._events.next(new hi(!1,r))}markAsDirty(n={}){let t=this.pristine===!0;this.pristine=!1;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty(P(I({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new fi(!1,r))}markAsPristine(n={}){let t=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,r),t&&n.emitEvent!==!1&&this._events.next(new fi(!0,r))}markAsPending(n={}){this.status=Wr;let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new qr(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending(P(I({},n),{sourceControl:t}))}disable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=ui,this.errors=null,this._forEachChild(o=>{o.disable(P(I({},n),{onlySelf:!0}))}),this._updateValue();let r=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new uc(this.value,r)),this._events.next(new qr(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(P(I({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=li,this._forEachChild(r=>{r.enable(P(I({},n),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors(P(I({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===li||this.status===Wr)&&this._runAsyncValidator(r,n.emitEvent)}let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new uc(this.value,t)),this._events.next(new qr(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity(P(I({},n),{sourceControl:t}))}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?ui:li}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=Wr,this._hasOwnPendingAsyncValidator={emitEvent:t!==!1};let r=VI(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(t.emitEvent!==!1,this,t.shouldHaveEmitted)}get(n){let t=n;return t==null||(Array.isArray(t)||(t=t.split(".")),t.length===0)?null:t.reduce((r,o)=>r&&r._find(o),this)}getError(n,t){let r=t?this.get(t):this;return r&&r.errors?r.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,r){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||r)&&this._events.next(new qr(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,r)}_initObservables(){this.valueChanges=new oe,this.statusChanges=new oe}_calculateStatus(){return this._allControlsDisabled()?ui:this.errors?sc:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Wr)?Wr:this._anyControlsHaveStatus(sc)?sc:li}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),o&&this._events.next(new fi(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new hi(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_onDisabledChange=[];_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){hc(n)&&n.updateOn!=null&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){let t=this._parent&&this._parent.dirty;return!n&&!!t&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=NA(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=OA(this._rawAsyncValidators)}},fc=class extends dc{constructor(n,t,r){super($I(t),zI(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(n,t){return this.controls[n]?this.controls[n]:(this.controls[n]=t,t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange),t)}addControl(n,t,r={}){this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(n,t={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}setControl(n,t,r={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],t&&this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(n){return this.controls.hasOwnProperty(n)&&this.controls[n].enabled}setValue(n,t={}){FA(this,!0,n),Object.keys(n).forEach(r=>{kA(this,!0,r),this.controls[r].setValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(Object.keys(n).forEach(r=>{let o=this.controls[r];o&&o.patchValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n={},t={}){this._forEachChild((r,o)=>{r.reset(n?n[o]:null,{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this._reduceChildren({},(n,t,r)=>(n[r]=t.getRawValue(),n))}_syncPendingControls(){let n=this._reduceChildren(!1,(t,r)=>r._syncPendingControls()?!0:t);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){Object.keys(this.controls).forEach(t=>{let r=this.controls[t];r&&n(r,t)})}_setUpControls(){this._forEachChild(n=>{n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(n){for(let[t,r]of Object.entries(this.controls))if(this.contains(t)&&n(r))return!0;return!1}_reduceValue(){let n={};return this._reduceChildren(n,(t,r,o)=>((r.enabled||this.disabled)&&(t[o]=r.value),t))}_reduceChildren(n,t){let r=n;return this._forEachChild((o,i)=>{r=t(r,o,i)}),r}_allControlsDisabled(){for(let n of Object.keys(this.controls))if(this.controls[n].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(n){return this.controls.hasOwnProperty(n)?this.controls[n]:null}};var xf=new x("",{providedIn:"root",factory:()=>Af}),Af="always";function PA(e,n){return[...n.path,e]}function GI(e,n,t=Af){WI(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||t==="always")&&n.valueAccessor.setDisabledState?.(e.disabled),jA(e,n),BA(e,n),VA(e,n),LA(e,n)}function AI(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function LA(e,n){if(n.valueAccessor.setDisabledState){let t=r=>{n.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}function WI(e,n){let t=xA(e);n.validator!==null?e.setValidators(SI(t,n.validator)):typeof t=="function"&&e.setValidators([t]);let r=AA(e);n.asyncValidator!==null?e.setAsyncValidators(SI(r,n.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();AI(n._rawValidators,o),AI(n._rawAsyncValidators,o)}function jA(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&qI(e,n)})}function VA(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&qI(e,n),e.updateOn!=="submit"&&e.markAsTouched()})}function qI(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function BA(e,n){let t=(r,o)=>{n.valueAccessor.writeValue(r),o&&n.viewToModelUpdate(r)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}function UA(e,n){e==null,WI(e,n)}function HA(e,n){if(!e.hasOwnProperty("model"))return!1;let t=e.model;return t.isFirstChange()?!0:!Object.is(n,t.currentValue)}function $A(e){return Object.getPrototypeOf(e.constructor)===vA}function zA(e,n){e._syncPendingControls(),n.forEach(t=>{let r=t.control;r.updateOn==="submit"&&r._pendingChange&&(t.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function GA(e,n){if(!n)return null;Array.isArray(n);let t,r,o;return n.forEach(i=>{i.constructor===FI?t=i:$A(i)?r=i:o=i}),o||r||t||null}var WA={provide:Zr,useExisting:Be(()=>qA)},di=Promise.resolve(),qA=(()=>{class e extends Zr{callSetDisabledState;get submitted(){return Bt(this.submittedReactive)}_submitted=Ao(()=>this.submittedReactive());submittedReactive=Io(!1);_directives=new Set;form;ngSubmit=new oe;options;constructor(t,r,o){super(),this.callSetDisabledState=o,this.form=new fc({},Sf(t),Tf(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(t){di.then(()=>{let r=this._findContainer(t.path);t.control=r.registerControl(t.name,t.control),GI(t.control,t,this.callSetDisabledState),t.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(t)})}getControl(t){return this.form.get(t.path)}removeControl(t){di.then(()=>{let r=this._findContainer(t.path);r&&r.removeControl(t.name),this._directives.delete(t)})}addFormGroup(t){di.then(()=>{let r=this._findContainer(t.path),o=new fc({});UA(o,t),r.registerControl(t.name,o),o.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(t){di.then(()=>{let r=this._findContainer(t.path);r&&r.removeControl(t.name)})}getFormGroup(t){return this.form.get(t.path)}updateModel(t,r){di.then(()=>{this.form.get(t.path).setValue(r)})}setValue(t){this.control.setValue(t)}onSubmit(t){return this.submittedReactive.set(!0),zA(this.form,this._directives),this.ngSubmit.emit(t),this.form._events.next(new _f(this.control)),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0){this.form.reset(t),this.submittedReactive.set(!1),this.form._events.next(new Mf(this.form))}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(t){return t.pop(),t.length?this.form.get(t):this.form}static \u0275fac=function(r){return new(r||e)(u($t,10),u(LI,10),u(xf,8))};static \u0275dir=V({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,o){r&1&&ye("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[Ae([WA]),ee]})}return e})();function RI(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function NI(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var ZA=class extends dc{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(n=null,t,r){super($I(t),zI(r,t)),this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),hc(t)&&(t.nonNullable||t.initialValueIsDefault)&&(NI(n)?this.defaultValue=n.value:this.defaultValue=n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&t.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,t.emitViewToModelChange!==!1)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){RI(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){RI(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(n){NI(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}};var YA={provide:Hn,useExisting:Be(()=>QA)},OI=Promise.resolve(),QA=(()=>{class e extends Hn{_changeDetectorRef;callSetDisabledState;control=new ZA;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new oe;constructor(t,r,o,i,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this._parent=t,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=GA(this,i)}ngOnChanges(t){if(this._checkForErrors(),!this._registered||"name"in t){if(this._registered&&(this._checkName(),this.formDirective)){let r=t.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in t&&this._updateDisabled(t),HA(t,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){GI(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(t){OI.then(()=>{this.control.setValue(t,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(t){let r=t.isDisabled.currentValue,o=r!==0&&nn(r);OI.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(t){return this._parent?PA(t,this._parent):[t]}static \u0275fac=function(r){return new(r||e)(u(Zr,9),u($t,10),u(LI,10),u($n,10),u(D,8),u(xf,8))};static \u0275dir=V({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[Ae([YA]),ee,Xe]})}return e})();var j$=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=V({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return e})();function KA(e){return typeof e=="number"?e:parseInt(e,10)}function ZI(e){return typeof e=="number"?e:parseFloat(e)}var pc=(()=>{class e{_validator=MI;_onChange;_enabled;ngOnChanges(t){if(this.inputName in t){let r=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):MI,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return t!=null}static \u0275fac=function(r){return new(r||e)};static \u0275dir=V({type:e,features:[Xe]})}return e})(),XA={provide:$t,useExisting:Be(()=>Rf),multi:!0},Rf=(()=>{class e extends pc{max;inputName="max";normalizeInput=t=>ZI(t);createValidator=t=>wA(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&ot("max",o._enabled?o.max:null)},inputs:{max:"max"},standalone:!1,features:[Ae([XA]),ee]})}return e})(),JA={provide:$t,useExisting:Be(()=>Nf),multi:!0},Nf=(()=>{class e extends pc{min;inputName="min";normalizeInput=t=>ZI(t);createValidator=t=>bA(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&ot("min",o._enabled?o.min:null)},inputs:{min:"min"},standalone:!1,features:[Ae([JA]),ee]})}return e})(),eR={provide:$t,useExisting:Be(()=>tR),multi:!0};var tR=(()=>{class e extends pc{required;inputName="required";normalizeInput=nn;createValidator=t=>EA;enabled(t){return t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(r,o){r&2&&ot("required",o._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[Ae([eR]),ee]})}return e})();var nR={provide:$t,useExisting:Be(()=>rR),multi:!0},rR=(()=>{class e extends pc{maxlength;inputName="maxlength";normalizeInput=t=>KA(t);createValidator=t=>_A(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&ot("maxlength",o._enabled?o.maxlength:null)},inputs:{maxlength:"maxlength"},standalone:!1,features:[Ae([nR]),ee]})}return e})();var oR=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Te({})}return e})();var V$=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:xf,useValue:t.callSetDisabledState??Af}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Te({imports:[oR]})}return e})();var gc=e=>iR(e),pi=(e,n)=>(typeof e=="string"&&(n=e,e=void 0),gc(e).includes(n)),iR=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let n=e.Ionic.platforms;return n==null&&(n=e.Ionic.platforms=sR(e),n.forEach(t=>e.document.documentElement.classList.add(`plt-${t}`))),n},sR=e=>{let n=qn.get("platform");return Object.keys(YI).filter(t=>{let r=n?.[t];return typeof r=="function"?r(e):YI[t](e)})},aR=e=>mc(e)&&!KI(e),Of=e=>!!(zn(e,/iPad/i)||zn(e,/Macintosh/i)&&mc(e)),cR=e=>zn(e,/iPhone/i),lR=e=>zn(e,/iPhone|iPod/i)||Of(e),QI=e=>zn(e,/android|sink/i),uR=e=>QI(e)&&!zn(e,/mobile/i),dR=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return r>390&&r<520&&o>620&&o<800},fR=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return Of(e)||uR(e)||r>460&&r<820&&o>780&&o<1400},mc=e=>mR(e,"(any-pointer:coarse)"),hR=e=>!mc(e),KI=e=>XI(e)||JI(e),XI=e=>!!(e.cordova||e.phonegap||e.PhoneGap),JI=e=>{let n=e.Capacitor;return!!(n?.isNative||n?.isNativePlatform&&n.isNativePlatform())},pR=e=>zn(e,/electron/i),gR=e=>{var n;return!!(!((n=e.matchMedia)===null||n===void 0)&&n.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},zn=(e,n)=>n.test(e.navigator.userAgent),mR=(e,n)=>{var t;return(t=e.matchMedia)===null||t===void 0?void 0:t.call(e,n).matches},YI={ipad:Of,iphone:cR,ios:lR,android:QI,phablet:dR,tablet:fR,cordova:XI,capacitor:JI,electron:pR,pwa:gR,mobile:mc,mobileweb:aR,desktop:hR,hybrid:KI},vR,kf=e=>e&&Np(e)||vR;var Q$=e=>{try{if(e instanceof Lf)return e.value;if(!yR()||typeof e!="string"||e==="")return e;if(e.includes("onload="))return"";let n=document.createDocumentFragment(),t=document.createElement("div");n.appendChild(t),t.innerHTML=e,IR.forEach(s=>{let a=n.querySelectorAll(s);for(let c=a.length-1;c>=0;c--){let l=a[c];l.parentNode?l.parentNode.removeChild(l):n.removeChild(l);let d=Pf(l);for(let h=0;h<d.length;h++)Ff(d[h])}});let r=Pf(n);for(let s=0;s<r.length;s++)Ff(r[s]);let o=document.createElement("div");o.appendChild(n);let i=o.querySelector("div");return i!==null?i.innerHTML:o.innerHTML}catch(n){return Ci("sanitizeDOMString",n),""}},Ff=e=>{if(e.nodeType&&e.nodeType!==1)return;if(typeof NamedNodeMap<"u"&&!(e.attributes instanceof NamedNodeMap)){e.remove();return}for(let t=e.attributes.length-1;t>=0;t--){let r=e.attributes.item(t),o=r.name;if(!DR.includes(o.toLowerCase())){e.removeAttribute(o);continue}let i=r.value,s=e[o];(i!=null&&i.toLowerCase().includes("javascript:")||s!=null&&s.toLowerCase().includes("javascript:"))&&e.removeAttribute(o)}let n=Pf(e);for(let t=0;t<n.length;t++)Ff(n[t])},Pf=e=>e.children!=null?e.children:e.childNodes,yR=()=>{var e;let n=window,t=(e=n?.Ionic)===null||e===void 0?void 0:e.config;return t?t.get?t.get("sanitizerEnabled",!0):t.sanitizerEnabled===!0||t.sanitizerEnabled===void 0:!0},DR=["class","id","href","src","name","slot"],IR=["script","style","iframe","meta","link","object","embed"],Lf=class{constructor(n){this.value=n}};var K$=!1;var J$=(e,n)=>typeof e=="string"&&e.length>0?Object.assign({"ion-color":!0,[`ion-color-${e}`]:!0},n):n,CR=e=>e!==void 0?(Array.isArray(e)?e:e.split(" ")).filter(t=>t!=null).map(t=>t.trim()).filter(t=>t!==""):[],ez=e=>{let n={};return CR(e).forEach(t=>n[t]=!0),n};var eC=()=>{let e,n;return{attachViewToDom:(c,l,...d)=>ge(null,[c,l,...d],function*(o,i,s={},a=[]){var h,p;e=o;let f;if(i){let S=typeof i=="string"?(h=e.ownerDocument)===null||h===void 0?void 0:h.createElement(i):i;a.forEach(N=>S.classList.add(N)),Object.assign(S,s),e.appendChild(S),f=S,yield new Promise(N=>hn(S,N))}else if(e.children.length>0&&(e.tagName==="ION-MODAL"||e.tagName==="ION-POPOVER")&&!(f=e.children[0]).classList.contains("ion-delegate-host")){let N=(p=e.ownerDocument)===null||p===void 0?void 0:p.createElement("div");N.classList.add("ion-delegate-host"),a.forEach(z=>N.classList.add(z)),N.append(...e.children),e.appendChild(N),f=N}let y=document.querySelector("ion-app")||document.body;return n=document.createComment("ionic teleport"),e.parentNode.insertBefore(n,e),y.appendChild(e),f??e}),removeViewFromDom:()=>(e&&n&&(n.parentNode.insertBefore(e,n),n.remove()),Promise.resolve())}};var mi='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',tC=(e,n)=>{let t=e.querySelector(mi);iC(t,n??e)},nC=(e,n)=>{let t=Array.from(e.querySelectorAll(mi)),r=t.length>0?t[t.length-1]:null;iC(r,n??e)},iC=(e,n)=>{let t=e,r=e?.shadowRoot;if(r&&(t=r.querySelector(mi)||e),t){let o=t.closest("ion-radio-group");o?o.setFocus():Ec(t)}else n.focus()},jf=0,bR=0,vc=new WeakMap,wR=e=>({create(t){return _R(e,t)},dismiss(t,r,o){return xR(document,t,r,e,o)},getTop(){return ge(this,null,function*(){return gi(document,e)})}});var ER=wR("ion-loading");var hz=e=>{typeof document<"u"&&TR(document);let n=jf++;e.overlayIndex=n},pz=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++bR}`),e.id),_R=(e,n)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let t=document.createElement(e);return t.classList.add("overlay-hidden"),Object.assign(t,Object.assign(Object.assign({},n),{hasController:!0})),aC(document).appendChild(t),new Promise(r=>hn(t,r))}):Promise.resolve(),MR=e=>e.classList.contains("overlay-hidden"),rC=(e,n)=>{let t=e,r=e?.shadowRoot;r&&(t=r.querySelector(mi)||e),t?Ec(t):n.focus()},SR=(e,n)=>{let t=gi(n,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),r=e.target;if(!t||!r||t.classList.contains(PR))return;let o=()=>{if(t===r)t.lastFocus=void 0;else if(r.tagName==="ION-TOAST")rC(t.lastFocus,t);else{let s=Fp(t);if(!s.contains(r))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(r)||r===s.querySelector("ion-backdrop"))t.lastFocus=r;else{let c=t.lastFocus;tC(a,t),c===n.activeElement&&nC(a,t),t.lastFocus=n.activeElement}}},i=()=>{if(t.contains(r))t.lastFocus=r;else if(r.tagName==="ION-TOAST")rC(t.lastFocus,t);else{let s=t.lastFocus;tC(t),s===n.activeElement&&nC(t),t.lastFocus=n.activeElement}};t.shadowRoot?i():o()},TR=e=>{jf===0&&(jf=1,e.addEventListener("focus",n=>{SR(n,e)},!0),e.addEventListener("ionBackButton",n=>{let t=gi(e);t?.backdropDismiss&&n.detail.register(Lp,()=>{t.dismiss(void 0,oC)})}),Pp()||e.addEventListener("keydown",n=>{if(n.key==="Escape"){let t=gi(e);t?.backdropDismiss&&t.dismiss(void 0,oC)}}))},xR=(e,n,t,r,o)=>{let i=gi(e,r,o);return i?i.dismiss(n,t):Promise.reject("overlay does not exist")},AR=(e,n)=>(n===void 0&&(n="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(n)).filter(t=>t.overlayIndex>0)),yc=(e,n)=>AR(e,n).filter(t=>!MR(t)),gi=(e,n,t)=>{let r=yc(e,n);return t===void 0?r[r.length-1]:r.find(o=>o.id===t)},sC=(e=!1)=>{let t=aC(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");t&&(e?t.setAttribute("aria-hidden","true"):t.removeAttribute("aria-hidden"))},gz=(e,n,t,r,o)=>ge(null,null,function*(){var i,s;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(sC(!0),document.body.classList.add(Ac)),kR(e.el),lC(e.el),e.presented=!0,e.willPresent.emit(),(i=e.willPresentShorthand)===null||i===void 0||i.emit();let a=kf(e),c=e.enterAnimation?e.enterAnimation:qn.get(n,a==="ios"?t:r);(yield cC(e,c,e.el,o))&&(e.didPresent.emit(),(s=e.didPresentShorthand)===null||s===void 0||s.emit()),e.el.tagName!=="ION-TOAST"&&RR(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),RR=e=>ge(null,null,function*(){let n=document.activeElement;if(!n)return;let t=n?.shadowRoot;t&&(n=t.querySelector(mi)||n),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&n.focus()}),mz=(e,n,t,r,o,i,s)=>ge(null,null,function*(){var a,c;if(!e.presented)return!1;let d=(zt!==void 0?yc(zt):[]).filter(p=>p.tagName!=="ION-TOAST");d.length===1&&d[0].id===e.el.id&&(sC(!1),document.body.classList.remove(Ac)),e.presented=!1;try{lC(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:n,role:t}),(a=e.willDismissShorthand)===null||a===void 0||a.emit({data:n,role:t});let p=kf(e),f=e.leaveAnimation?e.leaveAnimation:qn.get(r,p==="ios"?o:i);t!==OR&&(yield cC(e,f,e.el,s)),e.didDismiss.emit({data:n,role:t}),(c=e.didDismissShorthand)===null||c===void 0||c.emit({data:n,role:t}),(vc.get(e)||[]).forEach(S=>S.destroy()),vc.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(p){Ci(`[${e.el.tagName.toLowerCase()}] - `,p)}return e.el.remove(),FR(),!0}),aC=e=>e.querySelector("ion-app")||e.body,cC=(e,n,t,r)=>ge(null,null,function*(){t.classList.remove("overlay-hidden");let o=e.el,i=n(o,r);(!e.animated||!qn.getBoolean("animated",!0))&&i.duration(0),e.keyboardClose&&i.beforeAddWrite(()=>{let a=t.ownerDocument.activeElement;a?.matches("input,ion-input, ion-textarea")&&a.blur()});let s=vc.get(e)||[];return vc.set(e,[...s,i]),yield i.play(),!0}),vz=(e,n)=>{let t,r=new Promise(o=>t=o);return NR(e,n,o=>{t(o.detail)}),r},NR=(e,n,t)=>{let r=o=>{kp(e,n,r),t(o)};Op(e,n,r)};var oC="backdrop",OR="gesture";var yz=e=>{let n=!1,t,r=eC(),o=(a=!1)=>{if(t&&!a)return{delegate:t,inline:n};let{el:c,hasController:l,delegate:d}=e;return n=c.parentNode!==null&&!l,t=n?d||r:d,{inline:n,delegate:t}};return{attachViewToDom:a=>ge(null,null,function*(){let{delegate:c}=o(!0);if(c)return yield c.attachViewToDom(e.el,a);let{hasController:l}=e;if(l&&a!==void 0)throw new Error("framework delegate is missing");return null}),removeViewFromDom:()=>{let{delegate:a}=o();a&&e.el!==void 0&&a.removeViewFromDom(e.el.parentElement,e.el)}}},Dz=()=>{let e,n=()=>{e&&(e(),e=void 0)};return{addClickListener:(r,o)=>{n();let i=o!==void 0?document.getElementById(o):null;if(!i){wc(`[${r.tagName.toLowerCase()}] - A trigger element with the ID "${o}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,r);return}e=((a,c)=>{let l=()=>{c.present()};return a.addEventListener("click",l),()=>{a.removeEventListener("click",l)}})(i,r)},removeClickListener:n}},lC=e=>{zt!==void 0&&pi("android")&&e.setAttribute("aria-hidden","true")},kR=e=>{var n;if(zt===void 0)return;let t=yc(zt);for(let r=t.length-1;r>=0;r--){let o=t[r],i=(n=t[r+1])!==null&&n!==void 0?n:e;(i.hasAttribute("aria-hidden")||i.tagName!=="ION-TOAST")&&o.setAttribute("aria-hidden","true")}},FR=()=>{if(zt===void 0)return;let e=yc(zt);for(let n=e.length-1;n>=0;n--){let t=e[n];if(t.removeAttribute("aria-hidden"),t.tagName!=="ION-TOAST")break}},PR="ion-disable-focus-trap";var LR=["tabsInner"];var hC=(()=>{class e{doc;_readyPromise;win;backButton=new K;keyboardDidShow=new K;keyboardDidHide=new K;pause=new K;resume=new K;resize=new K;constructor(t,r){this.doc=t,r.run(()=>{this.win=t.defaultView,this.backButton.subscribeWithPriority=function(i,s){return this.subscribe(a=>a.register(i,c=>r.run(()=>s(c))))},Yr(this.pause,t,"pause",r),Yr(this.resume,t,"resume",r),Yr(this.backButton,t,"ionBackButton",r),Yr(this.resize,this.win,"resize",r),Yr(this.keyboardDidShow,this.win,"ionKeyboardDidShow",r),Yr(this.keyboardDidHide,this.win,"ionKeyboardDidHide",r);let o;this._readyPromise=new Promise(i=>{o=i}),this.win?.cordova?t.addEventListener("deviceready",()=>{o("cordova")},{once:!0}):o("dom")})}is(t){return pi(this.win,t)}platforms(){return gc(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(t){return jR(this.win.location.href,t)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(t){let r=this.win.navigator;return!!(r?.userAgent&&r.userAgent.indexOf(t)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}static \u0275fac=function(r){return new(r||e)(R(se),R(g))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),jR=(e,n)=>{n=n.replace(/[[\]\\]/g,"\\$&");let r=new RegExp("[\\?&]"+n+"=([^&#]*)").exec(e);return r?decodeURIComponent(r[1].replace(/\+/g," ")):null},Yr=(e,n,t,r)=>{n&&n.addEventListener(t,o=>{r.run(()=>{let i=o?.detail;e.next(i)})})},dn=(()=>{class e{location;serializer;router;topOutlet;direction=uC;animated=dC;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(t,r,o,i){this.location=r,this.serializer=o,this.router=i,i&&i.events.subscribe(s=>{if(s instanceof _t){let a=s.restoredState?s.restoredState.navigationId:s.id;this.guessDirection=this.guessAnimation=a<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?s.id:a}}),t.backButton.subscribeWithPriority(0,s=>{this.pop(),s()})}navigateForward(t,r={}){return this.setDirection("forward",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateBack(t,r={}){return this.setDirection("back",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateRoot(t,r={}){return this.setDirection("root",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}back(t={animated:!0,animationDirection:"back"}){return this.setDirection("back",t.animated,t.animationDirection,t.animation),this.location.back()}pop(){return ge(this,null,function*(){let t=this.topOutlet;for(;t;){if(yield t.pop())return!0;t=t.parentOutlet}return!1})}setDirection(t,r,o,i){this.direction=t,this.animated=VR(t,r,o),this.animationBuilder=i}setTopOutlet(t){this.topOutlet=t}consumeTransition(){let t="root",r,o=this.animationBuilder;return this.direction==="auto"?(t=this.guessDirection,r=this.guessAnimation):(r=this.animated,t=this.direction),this.direction=uC,this.animated=dC,this.animationBuilder=void 0,{direction:t,animation:r,animationBuilder:o}}navigate(t,r){if(Array.isArray(t))return this.router.navigate(t,r);{let o=this.serializer.parse(t.toString());return r.queryParams!==void 0&&(o.queryParams=I({},r.queryParams)),r.fragment!==void 0&&(o.fragment=r.fragment),this.router.navigateByUrl(o,r)}}static \u0275fac=function(r){return new(r||e)(R(hC),R($e),R(Ht),R(De,8))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),VR=(e,n,t)=>{if(n!==!1){if(t!==void 0)return t;if(e==="forward"||e==="back")return e;if(e==="root"&&n===!0)return"forward"}},uC="auto",dC=void 0,yi=(()=>{class e{get(t,r){let o=Vf();return o?o.get(t,r):null}getBoolean(t,r){let o=Vf();return o?o.getBoolean(t,r):!1}getNumber(t,r){let o=Vf();return o?o.getNumber(t,r):0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Dc=new x("USERCONFIG"),Vf=()=>{if(typeof window<"u"){let e=window.Ionic;if(e?.config)return e.config}return null},vi=class{data;constructor(n={}){this.data=n,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(n){return this.data[n]}},fn=(()=>{class e{zone=m(g);applicationRef=m(Dt);config=m(Dc);create(t,r,o){return new Uf(t,r,this.applicationRef,this.zone,o,this.config.useSetInputAPI??!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),Uf=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(n,t,r,o,i,s){this.environmentInjector=n,this.injector=t,this.applicationRef=r,this.zone=o,this.elementReferenceKey=i,this.enableSignalsSupport=s}attachViewToDom(n,t,r,o){return this.zone.run(()=>new Promise(i=>{let s=I({},r);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=n);let a=BR(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,n,t,s,o,this.elementReferenceKey,this.enableSignalsSupport);i(a)}))}removeViewFromDom(n,t){return this.zone.run(()=>new Promise(r=>{let o=this.elRefMap.get(t);if(o){o.destroy(),this.elRefMap.delete(t);let i=this.elEventsMap.get(t);i&&(i(),this.elEventsMap.delete(t))}r()}))}},BR=(e,n,t,r,o,i,s,a,c,l,d,h)=>{let p=X.create({providers:HR(c),parent:t}),f=Gy(a,{environmentInjector:n,elementInjector:p}),y=f.instance,S=f.location.nativeElement;if(c)if(d&&y[d]!==void 0&&console.error(`[Ionic Error]: ${d} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${d}" property from ${a.name}.`),h===!0&&f.setInput!==void 0){let z=c,{modal:Tt,popover:ce}=z,Wn=bc(z,["modal","popover"]);for(let Ii in Wn)f.setInput(Ii,Wn[Ii]);Tt!==void 0&&Object.assign(y,{modal:Tt}),ce!==void 0&&Object.assign(y,{popover:ce})}else Object.assign(y,c);if(l)for(let Tt of l)S.classList.add(Tt);let N=pC(e,y,S);return s.appendChild(S),r.attachView(f.hostView),o.set(S,f),i.set(S,N),S},UR=[_c,Mc,Sc,Tc,xc],pC=(e,n,t)=>e.run(()=>{let r=UR.filter(o=>typeof n[o]=="function").map(o=>{let i=s=>n[o](s.detail);return t.addEventListener(o,i),()=>t.removeEventListener(o,i)});return()=>r.forEach(o=>o())}),fC=new x("NavParamsToken"),HR=e=>[{provide:fC,useValue:e},{provide:vi,useFactory:$R,deps:[fC]}],$R=e=>new vi(e),zR=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)}})})},GR=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})},Qf=(e,n,t)=>{t.forEach(r=>e[r]=Dn(n,r))};function Ic(e){return function(t){let{defineCustomElementFn:r,inputs:o,methods:i}=e;return r!==void 0&&r(),o&&zR(t,o),i&&GR(t,i),t}}var WR=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","focusTrap","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],qR=["present","dismiss","onDidDismiss","onWillDismiss"],gC=(()=>{let e=class Hf{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),Qf(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Hf)(u(D),u(v),u(g))};static \u0275dir=V({type:Hf,selectors:[["ion-popover"]],contentQueries:function(r,o,i){if(r&1&&tn(i,yt,5),r&2){let s;it(s=st())&&(o.template=s.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",focusTrap:"focusTrap",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"},standalone:!1})};return e=w([Ic({inputs:WR,methods:qR})],e),e})(),ZR=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","expandToScroll","event","focusTrap","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],YR=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"],mC=(()=>{let e=class $f{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),Qf(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||$f)(u(D),u(v),u(g))};static \u0275dir=V({type:$f,selectors:[["ion-modal"]],contentQueries:function(r,o,i){if(r&1&&tn(i,yt,5),r&2){let s;it(s=st())&&(o.template=s.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",expandToScroll:"expandToScroll",event:"event",focusTrap:"focusTrap",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"},standalone:!1})};return e=w([Ic({inputs:ZR,methods:YR})],e),e})(),QR=(e,n,t)=>t==="root"?vC(e,n):t==="forward"?KR(e,n):XR(e,n),vC=(e,n)=>(e=e.filter(t=>t.stackId!==n.stackId),e.push(n),e),KR=(e,n)=>(e.indexOf(n)>=0?e=e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):e.push(n),e),XR=(e,n)=>e.indexOf(n)>=0?e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):vC(e,n),zf=(e,n)=>{let t=e.createUrlTree(["."],{relativeTo:n});return e.serializeUrl(t)},yC=(e,n)=>n?e.stackId!==n.stackId:!0,JR=(e,n)=>{if(!e)return;let t=DC(n);for(let r=0;r<t.length;r++){if(r>=e.length)return t[r];if(t[r]!==e[r])return}},DC=e=>e.split("/").map(n=>n.trim()).filter(n=>n!==""),IC=e=>{e&&(e.ref.destroy(),e.unlistenEvents())},Gf=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(n,t,r,o,i,s){this.containerEl=t,this.router=r,this.navCtrl=o,this.zone=i,this.location=s,this.tabsPrefix=n!==void 0?DC(n):void 0}createView(n,t){let r=zf(this.router,t),o=n?.location?.nativeElement,i=pC(this.zone,n.instance,o);return{id:this.nextId++,stackId:JR(this.tabsPrefix,r),unlistenEvents:i,element:o,ref:n,url:r}}getExistingView(n){let t=zf(this.router,n),r=this.views.find(o=>o.url===t);return r&&r.ref.changeDetectorRef.reattach(),r}setActive(n){let t=this.navCtrl.consumeTransition(),{direction:r,animation:o,animationBuilder:i}=t,s=this.activeView,a=yC(n,s);a&&(r="back",o=void 0);let c=this.views.slice(),l,d=this.router;d.getCurrentNavigation?l=d.getCurrentNavigation():d.navigations?.value&&(l=d.navigations.value),l?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let h=this.views.includes(n),p=this.insertView(n,r);h||n.ref.changeDetectorRef.detectChanges();let f=n.animationBuilder;return i===void 0&&r==="back"&&!a&&f!==void 0&&(i=f),s&&(s.animationBuilder=i),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),n.ref.changeDetectorRef.reattach(),this.transition(n,s,o,this.canGoBack(1),!1,i).then(()=>eN(n,p,c,this.location,this.zone)).then(()=>({enteringView:n,direction:r,animation:o,tabSwitch:a})))))}canGoBack(n,t=this.getActiveStackId()){return this.getStack(t).length>n}pop(n,t=this.getActiveStackId()){return this.zone.run(()=>{let r=this.getStack(t);if(r.length<=n)return Promise.resolve(!1);let o=r[r.length-n-1],i=o.url,s=o.savedData;if(s){let c=s.get("primary");c?.route?._routerState?.snapshot.url&&(i=c.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(i,P(I({},o.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let n=this.activeView;if(n){let t=this.getStack(n.stackId),r=t[t.length-2],o=r.animationBuilder;return this.wait(()=>this.transition(r,n,"back",this.canGoBack(2),!0,o))}return Promise.resolve()}endBackTransition(n){n?(this.skipTransition=!0,this.pop(1)):this.activeView&&CC(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(n){let t=this.getStack(n);return t.length>0?t[t.length-1]:void 0}getRootUrl(n){let t=this.getStack(n);return t.length>0?t[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(IC),this.activeView=void 0,this.views=[]}getStack(n){return this.views.filter(t=>t.stackId===n)}insertView(n,t){return this.activeView=n,this.views=QR(this.views,n,t),this.views.slice()}transition(n,t,r,o,i,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(t===n)return Promise.resolve(!1);let a=n?n.element:void 0,c=t?t.element:void 0,l=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),l.commit)?l.commit(a,c,{duration:r===void 0?0:void 0,direction:r,showGoBack:o,progressAnimation:i,animationBuilder:s}):Promise.resolve(!1)}wait(n){return ge(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let t=this.runningTask=n();return t.finally(()=>this.runningTask=void 0),t})}},eN=(e,n,t,r,o)=>typeof requestAnimationFrame=="function"?new Promise(i=>{requestAnimationFrame(()=>{CC(e,n,t,r,o),i()})}):Promise.resolve(),CC=(e,n,t,r,o)=>{o.run(()=>t.filter(i=>!n.includes(i)).forEach(IC)),n.forEach(i=>{let a=r.path().split("?")[0].split("#")[0];if(i!==e&&i.url!==a){let c=i.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),i.ref.changeDetectorRef.detach()}})},Kf=(()=>{class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new de(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=F;stackWillChange=new oe;stackDidChange=new oe;activateEvents=new oe;deactivateEvents=new oe;parentContexts=m(St);location=m(Ue);environmentInjector=m(ie);inputBinder=m(bC,{optional:!0});supportsBindingToComponentInputs=!0;config=m(yi);navCtrl=m(dn);set animation(t){this.nativeEl.animation=t}set animated(t){this.nativeEl.animated=t}set swipeGesture(t){this._swipeGesture=t,this.nativeEl.swipeHandler=t?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:r=>this.stackCtrl.endBackTransition(r)}:void 0}constructor(t,r,o,i,s,a,c,l){this.parentOutlet=l,this.nativeEl=i.nativeElement,this.name=t||F,this.tabsPrefix=r==="true"?zf(s,c):void 0,this.stackCtrl=new Gf(this.tabsPrefix,this.nativeEl,s,this.navCtrl,a,o),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let t=this.getContext();t?.route&&this.activateWith(t.route,t.injector)}new Promise(t=>hn(this.nativeEl,t)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(t,r){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let r=this.getContext();this.activatedView.savedData=new Map(r.children.contexts);let o=this.activatedView.savedData.get("primary");if(o&&r.route&&(o.route=I({},r.route)),this.activatedView.savedExtras={},r.route){let i=r.route.snapshot;this.activatedView.savedExtras.queryParams=i.queryParams,this.activatedView.savedExtras.fragment=i.fragment}}let t=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;let o,i=this.stackCtrl.getExistingView(t);if(i){o=this.activated=i.ref;let a=i.savedData;if(a){let c=this.getContext();c.children.contexts=a}this.updateActivatedRouteProxy(o.instance,t)}else{let a=t._futureSnapshot,c=this.parentContexts.getOrCreateContext(this.name).children,l=new de(null),d=this.createActivatedRouteProxy(l,t),h=new Wf(d,c,this.location.injector),p=a.routeConfig.component??a.component;o=this.activated=this.outletContent.createComponent(p,{index:this.outletContent.length,injector:h,environmentInjector:r??this.environmentInjector}),l.next(o.instance),i=this.stackCtrl.createView(this.activated,t),this.proxyMap.set(o.instance,d),this.currentActivatedRoute$.next({component:o.instance,activatedRoute:t})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=i,this.navCtrl.setTopOutlet(this);let s=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:i,tabSwitch:yC(i,s)}),this.stackCtrl.setActive(i).then(a=>{this.activateEvents.emit(o.instance),this.stackDidChange.emit(a)})}canGoBack(t=1,r){return this.stackCtrl.canGoBack(t,r)}pop(t=1,r){return this.stackCtrl.pop(t,r)}getLastUrl(t){let r=this.stackCtrl.getLastUrl(t);return r?r.url:void 0}getLastRouteView(t){return this.stackCtrl.getLastUrl(t)}getRootView(t){return this.stackCtrl.getRootUrl(t)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(t,r){let o=new Me;return o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,o._paramMap=this.proxyObservable(t,"paramMap"),o._queryParamMap=this.proxyObservable(t,"queryParamMap"),o.url=this.proxyObservable(t,"url"),o.params=this.proxyObservable(t,"params"),o.queryParams=this.proxyObservable(t,"queryParams"),o.fragment=this.proxyObservable(t,"fragment"),o.data=this.proxyObservable(t,"data"),o}proxyObservable(t,r){return t.pipe(me(o=>!!o),ve(o=>this.currentActivatedRoute$.pipe(me(i=>i!==null&&i.component===o),ve(i=>i&&i.activatedRoute[r]),Kc())))}updateActivatedRouteProxy(t,r){let o=this.proxyMap.get(t);if(!o)throw new Error("Could not find activated route proxy for view");o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,this.currentActivatedRoute$.next({component:t,activatedRoute:r})}static \u0275fac=function(r){return new(r||e)(Lt("name"),Lt("tabs"),u($e),u(v),u(De),u(g),u(Me),u(e,12))};static \u0275dir=V({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"],standalone:!1})}return e})(),Wf=class{route;childContexts;parent;constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===Me?this.route:n===St?this.childContexts:this.parent.get(n,t)}},bC=new x(""),tN=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,o=yn([r.queryParams,r.params,r.data]).pipe(ve(([i,s,a],c)=>(a=I(I(I({},i),s),a),c===0?O(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=ha(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),wC=()=>({provide:bC,useFactory:nN,deps:[De]});function nN(e){return e?.componentInputBindingEnabled?new tN:null}var rN=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"],EC=(()=>{let e=class qf{routerOutlet;navCtrl;config;r;z;el;constructor(t,r,o,i,s,a){this.routerOutlet=t,this.navCtrl=r,this.config=o,this.r=i,this.z=s,a.detach(),this.el=this.r.nativeElement}onClick(t){let r=this.defaultHref||this.config.get("backButtonDefaultHref");this.routerOutlet?.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),t.preventDefault()):r!=null&&(this.navCtrl.navigateBack(r,{animation:this.routerAnimation}),t.preventDefault())}static \u0275fac=function(r){return new(r||qf)(u(Kf,8),u(dn),u(yi),u(v),u(g),u(D))};static \u0275dir=V({type:qf,hostBindings:function(r,o){r&1&&ye("click",function(s){return o.onClick(s)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"},standalone:!1})};return e=w([Ic({inputs:rN})],e),e})(),_C=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref(),this.updateTabindex()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTabindex(){let t=["ION-BACK-BUTTON","ION-BREADCRUMB","ION-BUTTON","ION-CARD","ION-FAB-BUTTON","ION-ITEM","ION-ITEM-OPTION","ION-MENU-BUTTON","ION-SEGMENT-BUTTON","ION-TAB-BUTTON"],r=this.elementRef.nativeElement;t.includes(r.tagName)&&r.getAttribute("tabindex")==="0"&&r.removeAttribute("tabindex")}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(t){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),t.preventDefault()}static \u0275fac=function(r){return new(r||e)(u(Pe),u(dn),u(v),u(De),u(ai,8))};static \u0275dir=V({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(r,o){r&1&&ye("click",function(s){return o.onClick(s)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[Xe]})}return e})(),MC=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}static \u0275fac=function(r){return new(r||e)(u(Pe),u(dn),u(v),u(De),u(ai,8))};static \u0275dir=V({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(r,o){r&1&&ye("click",function(){return o.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[Xe]})}return e})(),oN=["animated","animation","root","rootParams","swipeGesture"],iN=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"],SC=(()=>{let e=class Zf{z;el;constructor(t,r,o,i,s,a){this.z=s,a.detach(),this.el=t.nativeElement,t.nativeElement.delegate=i.create(r,o),Qf(this,this.el,["ionNavDidChange","ionNavWillChange"])}static \u0275fac=function(r){return new(r||Zf)(u(v),u(ie),u(X),u(fn),u(g),u(D))};static \u0275dir=V({type:Zf,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"},standalone:!1})};return e=w([Ic({inputs:oN,methods:iN})],e),e})(),TC=(()=>{class e{navCtrl;tabsInner;ionTabsWillChange=new oe;ionTabsDidChange=new oe;tabBarSlot="bottom";hasTab=!1;selectedTab;leavingTab;constructor(t){this.navCtrl=t}ngAfterViewInit(){let t=this.tabs.length>0?this.tabs.first:void 0;t&&(this.hasTab=!0,this.setActiveTab(t.tab),this.tabSwitch())}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&this.ionTabsWillChange.emit({tab:o})}onStackDidChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&(this.tabBar&&(this.tabBar.selectedTab=o),this.ionTabsDidChange.emit({tab:o}))}select(t){let r=typeof t=="string",o=r?t:t.detail.tab;if(this.hasTab){this.setActiveTab(o),this.tabSwitch();return}let i=this.outlet.getActiveStackId()===o,s=`${this.outlet.tabsPrefix}/${o}`;if(r||t.stopPropagation(),i){let a=this.outlet.getActiveStackId();if(this.outlet.getLastRouteView(a)?.url===s)return;let l=this.outlet.getRootView(o),d=l&&s===l.url&&l.savedExtras;return this.navCtrl.navigateRoot(s,P(I({},d),{animated:!0,animationDirection:"back"}))}else{let a=this.outlet.getLastRouteView(o),c=a?.url||s,l=a?.savedExtras;return this.navCtrl.navigateRoot(c,P(I({},l),{animated:!0,animationDirection:"back"}))}}setActiveTab(t){let o=this.tabs.find(i=>i.tab===t);if(!o){console.error(`[Ionic Error]: Tab with id: "${t}" does not exist`);return}this.leavingTab=this.selectedTab,this.selectedTab=o,this.ionTabsWillChange.emit({tab:t}),o.el.active=!0}tabSwitch(){let{selectedTab:t,leavingTab:r}=this;this.tabBar&&t&&(this.tabBar.selectedTab=t.tab),r?.tab!==t?.tab&&r?.el&&(r.el.active=!1),t&&this.ionTabsDidChange.emit({tab:t.tab})}getSelected(){return this.hasTab?this.selectedTab?.tab:this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(t=>{let r=t.el.getAttribute("slot");r!==this.tabBarSlot&&(this.tabBarSlot=r,this.relocateTabBar())})}relocateTabBar(){let t=this.tabBar.el;this.tabBarSlot==="top"?this.tabsInner.nativeElement.before(t):this.tabsInner.nativeElement.after(t)}static \u0275fac=function(r){return new(r||e)(u(dn))};static \u0275dir=V({type:e,selectors:[["ion-tabs"]],viewQuery:function(r,o){if(r&1&&xo(LR,7,v),r&2){let i;it(i=st())&&(o.tabsInner=i.first)}},hostBindings:function(r,o){r&1&&ye("ionTabButtonClick",function(s){return o.select(s)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"},standalone:!1})}return e})(),Xf=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),Di=(()=>{class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(t,r){this.injector=t,this.elementRef=r}writeValue(t){this.elementRef.nativeElement.value=this.lastValue=t,Gn(this.elementRef)}handleValueChange(t,r){t===this.elementRef.nativeElement&&(r!==this.lastValue&&(this.lastValue=r,this.onChange(r)),Gn(this.elementRef))}_handleBlurEvent(t){t===this.elementRef.nativeElement?(this.onTouched(),Gn(this.elementRef)):t.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.elementRef.nativeElement.disabled=t}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let t;try{t=this.injector.get(Hn)}catch{}if(!t)return;t.statusChanges&&(this.statusChanges=t.statusChanges.subscribe(()=>Gn(this.elementRef)));let r=t.control;r&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(i=>{if(typeof r[i]<"u"){let s=r[i].bind(r);r[i]=(...a)=>{s(...a),Gn(this.elementRef)}}})}static \u0275fac=function(r){return new(r||e)(u(X),u(v))};static \u0275dir=V({type:e,hostBindings:function(r,o){r&1&&ye("ionBlur",function(s){return o._handleBlurEvent(s.target)})},standalone:!1})}return e})(),Gn=e=>{Xf(()=>{let n=e.nativeElement,t=n.value!=null&&n.value.toString().length>0,r=sN(n);Bf(n,r);let o=n.closest("ion-item");o&&(t?Bf(o,[...r,"item-has-value"]):Bf(o,r))})},sN=e=>{let n=e.classList,t=[];for(let r=0;r<n.length;r++){let o=n.item(r);o!==null&&aN(o,"ng-")&&t.push(`ion-${o.substring(3)}`)}return t},Bf=(e,n)=>{let t=e.classList;t.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),t.add(...n)},aN=(e,n)=>e.substring(0,n.length)===n,Yf=class{shouldDetach(n){return!1}shouldAttach(n){return!1}store(n,t){}retrieve(n){return null}shouldReuseRoute(n,t){if(n.routeConfig!==t.routeConfig)return!1;let r=n.params,o=t.params,i=Object.keys(r),s=Object.keys(o);if(i.length!==s.length)return!1;for(let a of i)if(o[a]!==r[a])return!1;return!0}},un=class{ctrl;constructor(n){this.ctrl=n}create(n){return this.ctrl.create(n||{})}dismiss(n,t,r){return this.ctrl.dismiss(n,t,r)}getTop(){return this.ctrl.getTop()}};function NC(){var e=[];if(typeof window<"u"){var n=window;(!n.customElements||n.Element&&(!n.Element.prototype.closest||!n.Element.prototype.matches||!n.Element.prototype.remove||!n.Element.prototype.getRootNode))&&e.push(import("./chunk-5X4HMWFG.js"));var t=function(){try{var r=new URL("b","http://a");return r.pathname="c%20d",r.href==="http://a/c%20d"&&r.searchParams}catch{return!1}};(typeof Object.assign!="function"||!Object.entries||!Array.prototype.find||!Array.prototype.includes||!String.prototype.startsWith||!String.prototype.endsWith||n.NodeList&&!n.NodeList.prototype.forEach||!n.fetch||!t()||typeof WeakMap>"u")&&e.push(import("./chunk-LQX7KJ2R.js"))}return Promise.all(e)}var OC=Rc;var kC=(e,n)=>ge(null,null,function*(){if(!(typeof window>"u"))return yield OC(),jp(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16],"beforeLeave":[16],"beforeEnter":[16]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16],"readonly":[4],"isDateEnabled":[16],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16],"multiple":[4],"highlightedDates":[16],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16],"leaveAnimation":[16],"component":[1],"componentProps":[16],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),n)});var _=["*"],DN=["outletContent"],IN=["outlet"],CN=[[["","slot","top"]],"*",[["ion-tab"]]],bN=["[slot=top]","*","ion-tab"];function wN(e,n){if(e&1){let t=Ny();xr(0,"ion-router-outlet",5,1),ye("stackWillChange",function(o){ju(t);let i=To();return Vu(i.onStackWillChange(o))})("stackDidChange",function(o){ju(t);let i=To();return Vu(i.onStackDidChange(o))}),Ar()}}function EN(e,n){e&1&&b(0,2,["*ngIf","tabs.length > 0"])}function _N(e,n){if(e&1&&(xr(0,"div",1),fa(1,2),Ar()),e&2){let t=To();ea(),en("ngTemplateOutlet",t.template)}}function MN(e,n){if(e&1&&fa(0,1),e&2){let t=To();en("ngTemplateOutlet",t.template)}}var SN=(()=>{class e extends Di{constructor(t,r){super(t,r)}writeValue(t){this.elementRef.nativeElement.checked=this.lastValue=t,Gn(this.elementRef)}_handleIonChange(t){this.handleValueChange(t,t.checked)}static \u0275fac=function(r){return new(r||e)(u(X),u(v))};static \u0275dir=V({type:e,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(r,o){r&1&&ye("ionChange",function(s){return o._handleIonChange(s.target)})},standalone:!1,features:[Ae([{provide:$n,useExisting:e,multi:!0}]),ee]})}return e})(),TN=(()=>{class e extends Di{el;constructor(t,r){super(t,r),this.el=r}handleInputEvent(t){this.handleValueChange(t,t.value)}registerOnChange(t){this.el.nativeElement.tagName==="ION-INPUT"?super.registerOnChange(r=>{t(r===""?null:parseFloat(r))}):super.registerOnChange(t)}static \u0275fac=function(r){return new(r||e)(u(X),u(v))};static \u0275dir=V({type:e,selectors:[["ion-input","type","number"],["ion-range"]],hostBindings:function(r,o){r&1&&ye("ionInput",function(s){return o.handleInputEvent(s.target)})},standalone:!1,features:[Ae([{provide:$n,useExisting:e,multi:!0}]),ee]})}return e})(),xN=(()=>{class e extends Di{constructor(t,r){super(t,r)}_handleChangeEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(r){return new(r||e)(u(X),u(v))};static \u0275dir=V({type:e,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(r,o){r&1&&ye("ionChange",function(s){return o._handleChangeEvent(s.target)})},standalone:!1,features:[Ae([{provide:$n,useExisting:e,multi:!0}]),ee]})}return e})(),AN=(()=>{class e extends Di{constructor(t,r){super(t,r)}_handleInputEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(r){return new(r||e)(u(X),u(v))};static \u0275dir=V({type:e,selectors:[["ion-input",3,"type","number"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(r,o){r&1&&ye("ionInput",function(s){return o._handleInputEvent(s.target)})},standalone:!1,features:[Ae([{provide:$n,useExisting:e,multi:!0}]),ee]})}return e})(),RN=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)},configurable:!0})})},NN=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})},Y=(e,n,t)=>{t.forEach(r=>e[r]=Dn(n,r))};function M(e){return function(t){let{defineCustomElementFn:r,inputs:o,methods:i}=e;return r!==void 0&&r(),o&&RN(t,o),i&&NN(t,i),t}}var ON=(()=>{let e=class Jf{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Jf)(u(D),u(v),u(g))};static \u0275cmp=C({type:Jf,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],e),e})(),kN=(()=>{let e=class eh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||eh)(u(D),u(v),u(g))};static \u0275cmp=C({type:eh,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],e),e})(),FN=(()=>{let e=class th{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||th)(u(D),u(v),u(g))};static \u0275cmp=C({type:th,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),PN=(()=>{let e=class nh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||nh)(u(D),u(v),u(g))};static \u0275cmp=C({type:nh,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),LN=(()=>{let e=class rh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||rh)(u(D),u(v),u(g))};static \u0275cmp=C({type:rh,selectors:[["ion-app"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({methods:["setFocus"]})],e),e})(),jN=(()=>{let e=class oh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||oh)(u(D),u(v),u(g))};static \u0275cmp=C({type:oh,selectors:[["ion-avatar"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({})],e),e})(),VN=(()=>{let e=class ih{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionBackdropTap"])}static \u0275fac=function(r){return new(r||ih)(u(D),u(v),u(g))};static \u0275cmp=C({type:ih,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["stopPropagation","tappable","visible"]})],e),e})(),BN=(()=>{let e=class sh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||sh)(u(D),u(v),u(g))};static \u0275cmp=C({type:sh,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode"]})],e),e})(),UN=(()=>{let e=class ah{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||ah)(u(D),u(v),u(g))};static \u0275cmp=C({type:ah,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],e),e})(),HN=(()=>{let e=class ch{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionCollapsedClick"])}static \u0275fac=function(r){return new(r||ch)(u(D),u(v),u(g))};static \u0275cmp=C({type:ch,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],e),e})(),$N=(()=>{let e=class lh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||lh)(u(D),u(v),u(g))};static \u0275cmp=C({type:lh,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],e),e})(),zN=(()=>{let e=class uh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||uh)(u(D),u(v),u(g))};static \u0275cmp=C({type:uh,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["collapse"]})],e),e})(),GN=(()=>{let e=class dh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||dh)(u(D),u(v),u(g))};static \u0275cmp=C({type:dh,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),WN=(()=>{let e=class fh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||fh)(u(D),u(v),u(g))};static \u0275cmp=C({type:fh,selectors:[["ion-card-content"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["mode"]})],e),e})(),qN=(()=>{let e=class hh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||hh)(u(D),u(v),u(g))};static \u0275cmp=C({type:hh,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode","translucent"]})],e),e})(),ZN=(()=>{let e=class ph{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ph)(u(D),u(v),u(g))};static \u0275cmp=C({type:ph,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode"]})],e),e})(),YN=(()=>{let e=class gh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||gh)(u(D),u(v),u(g))};static \u0275cmp=C({type:gh,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode"]})],e),e})(),QN=(()=>{let e=class mh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||mh)(u(D),u(v),u(g))};static \u0275cmp=C({type:mh,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",errorText:"errorText",helperText:"helperText",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["alignment","checked","color","disabled","errorText","helperText","indeterminate","justify","labelPlacement","mode","name","required","value"]})],e),e})(),KN=(()=>{let e=class vh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||vh)(u(D),u(v),u(g))};static \u0275cmp=C({type:vh,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","disabled","mode","outline"]})],e),e})(),XN=(()=>{let e=class yh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||yh)(u(D),u(v),u(g))};static \u0275cmp=C({type:yh,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],e),e})(),JN=(()=>{let e=class Dh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(r){return new(r||Dh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Dh,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],e),e})(),eO=(()=>{let e=class Ih{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||Ih)(u(D),u(v),u(g))};static \u0275cmp=C({type:Ih,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],e),e})(),tO=(()=>{let e=class Ch{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ch)(u(D),u(v),u(g))};static \u0275cmp=C({type:Ch,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","datetime","disabled","mode"]})],e),e})(),nO=(()=>{let e=class bh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||bh)(u(D),u(v),u(g))};static \u0275cmp=C({type:bh,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],e),e})(),rO=(()=>{let e=class wh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||wh)(u(D),u(v),u(g))};static \u0275cmp=C({type:wh,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],e),e})(),oO=(()=>{let e=class Eh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Eh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Eh,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["activated","side"]})],e),e})(),iO=(()=>{let e=class _h{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||_h)(u(D),u(v),u(g))};static \u0275cmp=C({type:_h,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["collapse","mode","translucent"]})],e),e})(),sO=(()=>{let e=class Mh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Mh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Mh,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["fixed"]})],e),e})(),aO=(()=>{let e=class Sh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Sh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Sh,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["collapse","mode","translucent"]})],e),e})(),cO=(()=>{let e=class Th{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Th)(u(D),u(v),u(g))};static \u0275cmp=C({type:Th,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],e),e})(),lO=(()=>{let e=class xh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}static \u0275fac=function(r){return new(r||xh)(u(D),u(v),u(g))};static \u0275cmp=C({type:xh,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["alt","src"]})],e),e})(),uO=(()=>{let e=class Ah{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionInfinite"])}static \u0275fac=function(r){return new(r||Ah)(u(D),u(v),u(g))};static \u0275cmp=C({type:Ah,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled","position","threshold"],methods:["complete"]})],e),e})(),dO=(()=>{let e=class Rh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Rh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Rh,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["loadingSpinner","loadingText"]})],e),e})(),fO=(()=>{let e=class Nh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||Nh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Nh,selectors:[["ion-input"]],inputs:{autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearInputIcon:"clearInputIcon",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearInputIcon","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),hO=(()=>{let e=class Oh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Oh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Oh,selectors:[["ion-input-password-toggle"]],inputs:{color:"color",hideIcon:"hideIcon",mode:"mode",showIcon:"showIcon"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","hideIcon","mode","showIcon"]})],e),e})(),pO=(()=>{let e=class kh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||kh)(u(D),u(v),u(g))};static \u0275cmp=C({type:kh,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),gO=(()=>{let e=class Fh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Fh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Fh,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode","sticky"]})],e),e})(),mO=(()=>{let e=class Ph{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ph)(u(D),u(v),u(g))};static \u0275cmp=C({type:Ph,selectors:[["ion-item-group"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({})],e),e})(),vO=(()=>{let e=class Lh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Lh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Lh,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],e),e})(),yO=(()=>{let e=class jh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionSwipe"])}static \u0275fac=function(r){return new(r||jh)(u(D),u(v),u(g))};static \u0275cmp=C({type:jh,selectors:[["ion-item-options"]],inputs:{side:"side"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["side"]})],e),e})(),DO=(()=>{let e=class Vh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionDrag"])}static \u0275fac=function(r){return new(r||Vh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Vh,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],e),e})(),IO=(()=>{let e=class Bh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Bh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Bh,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode","position"]})],e),e})(),CO=(()=>{let e=class Uh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Uh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Uh,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],e),e})(),bO=(()=>{let e=class Hh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Hh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Hh,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","lines","mode"]})],e),e})(),wO=(()=>{let e=class $h{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||$h)(u(D),u(v),u(g))};static \u0275cmp=C({type:$h,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),EO=(()=>{let e=class zh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}static \u0275fac=function(r){return new(r||zh)(u(D),u(v),u(g))};static \u0275cmp=C({type:zh,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],e),e})(),_O=(()=>{let e=class Gh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Gh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Gh,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["autoHide","color","disabled","menu","mode","type"]})],e),e})(),MO=(()=>{let e=class Wh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Wh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Wh,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["autoHide","menu"]})],e),e})(),SO=(()=>{let e=class qh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||qh)(u(D),u(v),u(g))};static \u0275cmp=C({type:qh,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["component","componentProps","routerAnimation","routerDirection"]})],e),e})(),TO=(()=>{let e=class Zh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Zh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Zh,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode"]})],e),e})(),xO=(()=>{let e=class Yh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Yh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Yh,selectors:[["ion-picker"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["mode"]})],e),e})(),AO=(()=>{let e=class Qh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||Qh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Qh,selectors:[["ion-picker-column"]],inputs:{color:"color",disabled:"disabled",mode:"mode",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","disabled","mode","value"],methods:["setFocus"]})],e),e})(),RO=(()=>{let e=class Kh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Kh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Kh,selectors:[["ion-picker-column-option"]],inputs:{color:"color",disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","disabled","value"]})],e),e})(),NO=(()=>{let e=class Xh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Xh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Xh,selectors:[["ion-picker-legacy"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],e),e})(),OO=(()=>{let e=class Jh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Jh)(u(D),u(v),u(g))};static \u0275cmp=C({type:Jh,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["buffer","color","mode","reversed","type","value"]})],e),e})(),kO=(()=>{let e=class ep{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||ep)(u(D),u(v),u(g))};static \u0275cmp=C({type:ep,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["alignment","color","disabled","justify","labelPlacement","mode","name","value"]})],e),e})(),FO=(()=>{let e=class tp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||tp)(u(D),u(v),u(g))};static \u0275cmp=C({type:tp,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",errorText:"errorText",helperText:"helperText",name:"name",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["allowEmptySelection","compareWith","errorText","helperText","name","value"]})],e),e})(),PO=(()=>{let e=class np{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}static \u0275fac=function(r){return new(r||np)(u(D),u(v),u(g))};static \u0275cmp=C({type:np,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],e),e})(),LO=(()=>{let e=class rp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionRefresh","ionPull","ionStart"])}static \u0275fac=function(r){return new(r||rp)(u(D),u(v),u(g))};static \u0275cmp=C({type:rp,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],e),e})(),jO=(()=>{let e=class op{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||op)(u(D),u(v),u(g))};static \u0275cmp=C({type:op,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],e),e})(),VO=(()=>{let e=class ip{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ip)(u(D),u(v),u(g))};static \u0275cmp=C({type:ip,selectors:[["ion-reorder"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({})],e),e})(),BO=(()=>{let e=class sp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionItemReorder"])}static \u0275fac=function(r){return new(r||sp)(u(D),u(v),u(g))};static \u0275cmp=C({type:sp,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled"],methods:["complete"]})],e),e})(),UO=(()=>{let e=class ap{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ap)(u(D),u(v),u(g))};static \u0275cmp=C({type:ap,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["type"],methods:["addRipple"]})],e),e})(),HO=(()=>{let e=class cp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||cp)(u(D),u(v),u(g))};static \u0275cmp=C({type:cp,selectors:[["ion-row"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({})],e),e})(),$O=(()=>{let e=class lp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||lp)(u(D),u(v),u(g))};static \u0275cmp=C({type:lp,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),zO=(()=>{let e=class up{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||up)(u(D),u(v),u(g))};static \u0275cmp=C({type:up,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],e),e})(),GO=(()=>{let e=class dp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||dp)(u(D),u(v),u(g))};static \u0275cmp=C({type:dp,selectors:[["ion-segment-button"]],inputs:{contentId:"contentId",disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["contentId","disabled","layout","mode","type","value"]})],e),e})(),WO=(()=>{let e=class fp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||fp)(u(D),u(v),u(g))};static \u0275cmp=C({type:fp,selectors:[["ion-segment-content"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({})],e),e})(),qO=(()=>{let e=class hp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionSegmentViewScroll"])}static \u0275fac=function(r){return new(r||hp)(u(D),u(v),u(g))};static \u0275cmp=C({type:hp,selectors:[["ion-segment-view"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled"]})],e),e})(),ZO=(()=>{let e=class pp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||pp)(u(D),u(v),u(g))};static \u0275cmp=C({type:pp,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",errorText:"errorText",expandedIcon:"expandedIcon",fill:"fill",helperText:"helperText",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",required:"required",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["cancelText","color","compareWith","disabled","errorText","expandedIcon","fill","helperText","interface","interfaceOptions","justify","label","labelPlacement","mode","multiple","name","okText","placeholder","required","selectedText","shape","toggleIcon","value"],methods:["open"]})],e),e})(),YO=(()=>{let e=class gp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||gp)(u(D),u(v),u(g))};static \u0275cmp=C({type:gp,selectors:[["ion-select-modal"]],inputs:{header:"header",multiple:"multiple",options:"options"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["header","multiple","options"]})],e),e})(),QO=(()=>{let e=class mp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||mp)(u(D),u(v),u(g))};static \u0275cmp=C({type:mp,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled","value"]})],e),e})(),KO=(()=>{let e=class vp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||vp)(u(D),u(v),u(g))};static \u0275cmp=C({type:vp,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated"]})],e),e})(),XO=(()=>{let e=class yp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||yp)(u(D),u(v),u(g))};static \u0275cmp=C({type:yp,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","duration","name","paused"]})],e),e})(),JO=(()=>{let e=class Dp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionSplitPaneVisible"])}static \u0275fac=function(r){return new(r||Dp)(u(D),u(v),u(g))};static \u0275cmp=C({type:Dp,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["contentId","disabled","when"]})],e),e})(),FC=(()=>{let e=class Ip{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ip)(u(D),u(v),u(g))};static \u0275cmp=C({type:Ip,selectors:[["ion-tab"]],inputs:{component:"component",tab:"tab"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["component","tab"],methods:["setActive"]})],e),e})(),Cp=(()=>{let e=class bp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||bp)(u(D),u(v),u(g))};static \u0275cmp=C({type:bp,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode","selectedTab","translucent"]})],e),e})(),ek=(()=>{let e=class wp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||wp)(u(D),u(v),u(g))};static \u0275cmp=C({type:wp,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],e),e})(),tk=(()=>{let e=class Ep{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ep)(u(D),u(v),u(g))};static \u0275cmp=C({type:Ep,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode"]})],e),e})(),nk=(()=>{let e=class _p{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||_p)(u(D),u(v),u(g))};static \u0275cmp=C({type:_p,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],e),e})(),rk=(()=>{let e=class Mp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Mp)(u(D),u(v),u(g))};static \u0275cmp=C({type:Mp,selectors:[["ion-thumbnail"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({})],e),e})(),ok=(()=>{let e=class Sp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Sp)(u(D),u(v),u(g))};static \u0275cmp=C({type:Sp,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","size"]})],e),e})(),ik=(()=>{let e=class Tp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Tp)(u(D),u(v),u(g))};static \u0275cmp=C({type:Tp,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),sk=(()=>{let e=class xp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||xp)(u(D),u(v),u(g))};static \u0275cmp=C({type:xp,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],e),e})(),ak=(()=>{let e=class Ap{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ap)(u(D),u(v),u(g))};static \u0275cmp=C({type:Ap,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode"]})],e),e})(),Cc=(()=>{class e extends Kf{parentOutlet;outletContent;constructor(t,r,o,i,s,a,c,l){super(t,r,o,i,s,a,c,l),this.parentOutlet=l}static \u0275fac=function(r){return new(r||e)(Lt("name"),Lt("tabs"),u($e),u(v),u(De),u(g),u(Me),u(e,12))};static \u0275cmp=C({type:e,selectors:[["ion-router-outlet"]],viewQuery:function(r,o){if(r&1&&xo(DN,7,Ue),r&2){let i;it(i=st())&&(o.outletContent=i.first)}},standalone:!1,features:[ee],ngContentSelectors:_,decls:3,vars:0,consts:[["outletContent",""]],template:function(r,o){r&1&&(E(),ua(0,null,0),b(2),da())},encapsulation:2})}return e})(),ck=(()=>{class e extends TC{outlet;tabBar;tabBars;tabs;static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275cmp=C({type:e,selectors:[["ion-tabs"]],contentQueries:function(r,o,i){if(r&1&&(tn(i,Cp,5),tn(i,Cp,4),tn(i,FC,4)),r&2){let s;it(s=st())&&(o.tabBar=s.first),it(s=st())&&(o.tabBars=s),it(s=st())&&(o.tabs=s)}},viewQuery:function(r,o){if(r&1&&xo(IN,5,Cc),r&2){let i;it(i=st())&&(o.outlet=i.first)}},standalone:!1,features:[ee],ngContentSelectors:bN,decls:6,vars:2,consts:[["tabsInner",""],["outlet",""],[1,"tabs-inner"],["tabs","true",3,"stackWillChange","stackDidChange",4,"ngIf"],[4,"ngIf"],["tabs","true",3,"stackWillChange","stackDidChange"]],template:function(r,o){r&1&&(E(CN),b(0),xr(1,"div",2,0),Mo(3,wN,2,0,"ion-router-outlet",3)(4,EN,1,0,"ng-content",4),Ar(),b(5,1)),r&2&&(ea(3),en("ngIf",o.tabs.length===0),ea(),en("ngIf",o.tabs.length>0))},dependencies:[Oo,Cc],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]})}return e})(),lk=(()=>{class e extends EC{constructor(t,r,o,i,s,a){super(t,r,o,i,s,a)}static \u0275fac=function(r){return new(r||e)(u(Cc,8),u(dn),u(yi),u(v),u(g),u(D))};static \u0275cmp=C({type:e,selectors:[["ion-back-button"]],standalone:!1,features:[ee],ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})}return e})(),uk=(()=>{class e extends SC{constructor(t,r,o,i,s,a){super(t,r,o,i,s,a)}static \u0275fac=function(r){return new(r||e)(u(v),u(ie),u(X),u(fn),u(g),u(D))};static \u0275cmp=C({type:e,selectors:[["ion-nav"]],standalone:!1,features:[ee],ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})}return e})(),dk=(()=>{class e extends _C{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],standalone:!1,features:[ee]})}return e})(),fk=(()=>{class e extends MC{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],standalone:!1,features:[ee]})}return e})(),hk=(()=>{class e extends mC{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275cmp=C({type:e,selectors:[["ion-modal"]],standalone:!1,features:[ee],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(r,o){r&1&&Mo(0,_N,2,1,"div",0),r&2&&en("ngIf",o.isCmpOpen||o.keepContentsMounted)},dependencies:[Oo,Da],encapsulation:2,changeDetection:0})}return e})(),pk=(()=>{class e extends gC{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275cmp=C({type:e,selectors:[["ion-popover"]],standalone:!1,features:[ee],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(r,o){r&1&&Mo(0,MN,1,1,"ng-container",0),r&2&&en("ngIf",o.isCmpOpen||o.keepContentsMounted)},dependencies:[Oo,Da],encapsulation:2,changeDetection:0})}return e})(),gk={provide:$t,useExisting:Be(()=>PC),multi:!0},PC=(()=>{class e extends Rf{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&ot("max",o._enabled?o.max:null)},standalone:!1,features:[Ae([gk]),ee]})}return e})(),mk={provide:$t,useExisting:Be(()=>LC),multi:!0},LC=(()=>{class e extends Nf{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&ot("min",o._enabled?o.min:null)},standalone:!1,features:[Ae([mk]),ee]})}return e})(),d4=(()=>{class e extends un{constructor(){super(Nc)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var f4=(()=>{class e extends un{constructor(){super(Oc)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var vk=(()=>{class e extends un{angularDelegate=m(fn);injector=m(X);environmentInjector=m(ie);constructor(){super(kc)}create(t){return super.create(P(I({},t),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();var Rp=class extends un{angularDelegate=m(fn);injector=m(X);environmentInjector=m(ie);constructor(){super(Fc)}create(n){return super.create(P(I({},n),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},h4=(()=>{class e extends un{constructor(){super(Pc)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),yk=(e,n,t)=>()=>{let r=n.defaultView;if(r&&typeof window<"u"){Lc(P(I({},e),{_zoneGate:i=>t.run(i)}));let o="__zone_symbol__addEventListener"in n.body?"__zone_symbol__addEventListener":"addEventListener";return NC().then(()=>kC(r,{exclude:["ion-tabs"],syncQueue:!0,raf:Xf,jmp:i=>t.runOutsideAngular(i),ael(i,s,a,c){i[o](s,a,c)},rel(i,s,a,c){i.removeEventListener(s,a,c)}}))}},Dk=[ON,kN,FN,PN,LN,jN,VN,BN,UN,HN,$N,zN,GN,WN,qN,ZN,YN,QN,KN,XN,JN,eO,tO,nO,rO,oO,iO,sO,aO,cO,lO,uO,dO,fO,hO,pO,gO,mO,vO,yO,DO,IO,CO,bO,wO,EO,_O,MO,SO,TO,xO,AO,RO,NO,OO,kO,FO,PO,LO,jO,VO,BO,UO,HO,$O,zO,GO,WO,qO,ZO,YO,QO,KO,XO,JO,FC,Cp,ek,tk,nk,rk,ok,ik,sk,ak],p4=[...Dk,hk,pk,SN,TN,xN,AN,ck,Cc,lk,uk,dk,fk,LC,PC],g4=(()=>{class e{static forRoot(t={}){return{ngModule:e,providers:[{provide:Dc,useValue:t},{provide:ca,useFactory:yk,multi:!0,deps:[Dc,se,g]},fn,wC()]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Te({providers:[vk,Rp],imports:[ko]})}return e})();export{W as a,K as b,de as c,uF as d,w as e,tr as f,HC as g,eb as h,Dn as i,GC as j,ut as k,yb as l,T as m,Te as n,R as o,m as p,ju as q,Vu as r,be as s,oe as t,mt as u,bB as v,Jw as w,ea as x,u as y,C as z,xe as A,Mo as B,en as C,la as D,xr as E,Ar as F,_d as G,Ny as H,ye as I,To as J,lS as K,NB as L,dS as M,Fy as N,fS as O,hS as P,OB as Q,pS as R,kB as S,US as T,Jy as U,Oo as V,ko as W,sT as X,lT as Y,rn as Z,ID as _,bD as $,NT as aa,Me as ba,hI as ca,De as da,aA as ea,dA as fa,F$ as ga,P$ as ha,qA as ia,QA as ja,j$ as ka,tR as la,rR as ma,V$ as na,kf as oa,Q$ as pa,K$ as qa,J$ as ra,ez as sa,ER as ta,hz as ua,pz as va,gz as wa,mz as xa,vz as ya,oC as za,yz as Aa,Dz as Ba,hC as Ca,Yf as Da,un as Ea,SN as Fa,TN as Ga,xN as Ha,AN as Ia,PN as Ja,LN as Ka,BN as La,$N as Ma,zN as Na,GN as Oa,WN as Pa,qN as Qa,ZN as Ra,YN as Sa,QN as Ta,KN as Ua,JN as Va,nO as Wa,rO as Xa,oO as Ya,iO as Za,aO as _a,cO as $a,fO as ab,pO as bb,gO as cb,mO as db,IO as eb,CO as fb,TO as gb,OO as hb,LO as ib,jO as jb,$O as kb,zO as lb,GO as mb,ZO as nb,QO as ob,XO as pb,Cp as qb,ek as rb,tk as sb,ok as tb,ak as ub,Cc as vb,ck as wb,hk as xb,d4 as yb,f4 as zb,vk as Ab,h4 as Bb,g4 as Cb};

{"version": 3, "sources": ["src/app/services/environment-switcher.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { BehaviorSubject } from 'rxjs';\r\n\r\nexport interface ApiEndpoint {\r\n  name: string;\r\n  url: string;\r\n  type: 'ngrok' | 'local-ip' | 'localhost';\r\n  description: string;\r\n  isActive?: boolean;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class EnvironmentSwitcherService {\r\n  private currentApiUrlSubject = new BehaviorSubject<string>('');\r\n  public currentApiUrl$ = this.currentApiUrlSubject.asObservable();\r\n\r\n  // Available API endpoints for testing\r\n  private apiEndpoints: ApiEndpoint[] = [\r\n    {\r\n      name: 'ngrok (Recommended)',\r\n      url: 'https://*************************************************.ngrok-free.app/api',\r\n      type: 'ngrok',\r\n      description: 'Secure tunnel, works from anywhere'\r\n    },\r\n    {\r\n      name: 'Home IP (Current)',\r\n      url: 'http://***************:8000/api',\r\n      type: 'local-ip',\r\n      description: '🏠 Home network IP - current location'\r\n    },\r\n    {\r\n      name: 'School IP',\r\n      url: 'http://*************:8000/api',\r\n      type: 'local-ip',\r\n      description: '🏫 School network IP - when at school'\r\n    },\r\n    {\r\n      name: 'Localhost (Web Only)',\r\n      url: 'http://localhost:8000/api',\r\n      type: 'localhost',\r\n      description: 'Only works in web browser'\r\n    }\r\n  ];\r\n\r\n  constructor(private http: HttpClient) {\r\n    // Initialize with the first endpoint\r\n    this.setApiUrl(this.apiEndpoints[1].url); // Start with local IP\r\n  }\r\n\r\n  /**\r\n   * Get all available API endpoints\r\n   */\r\n  getApiEndpoints(): ApiEndpoint[] {\r\n    return this.apiEndpoints.map(endpoint => ({\r\n      ...endpoint,\r\n      isActive: endpoint.url === this.getCurrentApiUrl()\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Set the current API URL\r\n   */\r\n  setApiUrl(url: string): void {\r\n    this.currentApiUrlSubject.next(url);\r\n    localStorage.setItem('selectedApiUrl', url);\r\n    console.log('🔄 API URL switched to:', url);\r\n  }\r\n\r\n  /**\r\n   * Get the current API URL\r\n   */\r\n  getCurrentApiUrl(): string {\r\n    const stored = localStorage.getItem('selectedApiUrl');\r\n    if (stored) {\r\n      this.currentApiUrlSubject.next(stored);\r\n      return stored;\r\n    }\r\n    return this.currentApiUrlSubject.value || this.apiEndpoints[1].url;\r\n  }\r\n\r\n  /**\r\n   * Test connectivity to an API endpoint\r\n   */\r\n  async testEndpoint(url: string): Promise<{ success: boolean; message: string; responseTime?: number }> {\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      // Test the health endpoint\r\n      const healthUrl = url.replace('/api', '/up');\r\n\r\n      await this.http.get(healthUrl, {\r\n        responseType: 'text'\r\n      }).toPromise();\r\n\r\n      const responseTime = Date.now() - startTime;\r\n\r\n      return {\r\n        success: true,\r\n        message: `✅ Connected successfully (${responseTime}ms)`,\r\n        responseTime\r\n      };\r\n\r\n    } catch (error: any) {\r\n      const responseTime = Date.now() - startTime;\r\n\r\n      let message = '❌ Connection failed';\r\n\r\n      if (error.status === 0) {\r\n        message = '❌ Network error - Cannot reach server';\r\n      } else if (error.status === 404) {\r\n        message = '❌ Server found but endpoint not available';\r\n      } else if (error.status >= 500) {\r\n        message = '❌ Server error';\r\n      } else {\r\n        message = `❌ Error ${error.status}: ${error.statusText}`;\r\n      }\r\n\r\n      return {\r\n        success: false,\r\n        message: `${message} (${responseTime}ms)`,\r\n        responseTime\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Test all endpoints and return results\r\n   */\r\n  async testAllEndpoints(): Promise<(ApiEndpoint & { testResult: any })[]> {\r\n    const results = [];\r\n\r\n    for (const endpoint of this.apiEndpoints) {\r\n      const testResult = await this.testEndpoint(endpoint.url);\r\n      results.push({\r\n        ...endpoint,\r\n        testResult\r\n      });\r\n    }\r\n\r\n    return results;\r\n  }\r\n\r\n  /**\r\n   * Auto-detect the best working endpoint\r\n   */\r\n  async autoDetectBestEndpoint(): Promise<ApiEndpoint | null> {\r\n    console.log('🔍 Auto-detecting best API endpoint...');\r\n\r\n    // Test endpoints in order of preference\r\n    const preferredOrder = ['ngrok', 'local-ip', 'localhost'];\r\n\r\n    for (const type of preferredOrder) {\r\n      const endpoints = this.apiEndpoints.filter(e => e.type === type);\r\n\r\n      for (const endpoint of endpoints) {\r\n        const result = await this.testEndpoint(endpoint.url);\r\n\r\n        if (result.success) {\r\n          console.log('✅ Found working endpoint:', endpoint.name);\r\n          this.setApiUrl(endpoint.url);\r\n          return endpoint;\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log('❌ No working endpoints found');\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Get connection diagnostics\r\n   */\r\n  getConnectionDiagnostics(): string[] {\r\n    const diagnostics = [];\r\n    const currentUrl = this.getCurrentApiUrl();\r\n    const currentEndpoint = this.apiEndpoints.find(e => e.url === currentUrl);\r\n\r\n    diagnostics.push('🔗 CONNECTION DIAGNOSTICS');\r\n    diagnostics.push('========================');\r\n    diagnostics.push('');\r\n    diagnostics.push(`📡 Current API: ${currentUrl}`);\r\n    diagnostics.push(`🏷️ Type: ${currentEndpoint?.type || 'unknown'}`);\r\n    diagnostics.push('');\r\n\r\n    if (currentEndpoint?.type === 'ngrok') {\r\n      diagnostics.push('🔧 NGROK TROUBLESHOOTING:');\r\n      diagnostics.push('1. Check if ngrok is running: http://127.0.0.1:4040');\r\n      diagnostics.push('2. Verify Laravel backend: php artisan serve --host=0.0.0.0 --port=8000');\r\n      diagnostics.push('3. Update ngrok URL if expired');\r\n    } else if (currentEndpoint?.type === 'local-ip') {\r\n      diagnostics.push('🔧 LOCAL IP TROUBLESHOOTING:');\r\n      diagnostics.push('1. Both devices must be on same WiFi');\r\n      diagnostics.push('2. Check Windows Firewall settings');\r\n      diagnostics.push('3. Verify backend server is running');\r\n      diagnostics.push('4. Try switching to ngrok for easier setup');\r\n    }\r\n\r\n    diagnostics.push('');\r\n    diagnostics.push('💡 QUICK ACTIONS:');\r\n    diagnostics.push('- Use Environment Switcher to test different URLs');\r\n    diagnostics.push('- Run auto-detection to find working endpoint');\r\n    diagnostics.push('- Check network diagnostics page');\r\n\r\n    return diagnostics;\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;AAeM,IAAO,6BAAP,MAAO,4BAA0B;EAgCrC,YAAoB,MAAgB;AAAhB,SAAA,OAAA;AA/BZ,SAAA,uBAAuB,IAAI,gBAAwB,EAAE;AACtD,SAAA,iBAAiB,KAAK,qBAAqB,aAAY;AAGtD,SAAA,eAA8B;MACpC;QACE,MAAM;QACN,KAAK;QACL,MAAM;QACN,aAAa;;MAEf;QACE,MAAM;QACN,KAAK;QACL,MAAM;QACN,aAAa;;MAEf;QACE,MAAM;QACN,KAAK;QACL,MAAM;QACN,aAAa;;MAEf;QACE,MAAM;QACN,KAAK;QACL,MAAM;QACN,aAAa;;;AAMf,SAAK,UAAU,KAAK,aAAa,CAAC,EAAE,GAAG;EACzC;;;;EAKA,kBAAe;AACb,WAAO,KAAK,aAAa,IAAI,cAAa,iCACrC,WADqC;MAExC,UAAU,SAAS,QAAQ,KAAK,iBAAgB;MAChD;EACJ;;;;EAKA,UAAU,KAAW;AACnB,SAAK,qBAAqB,KAAK,GAAG;AAClC,iBAAa,QAAQ,kBAAkB,GAAG;AAC1C,YAAQ,IAAI,kCAA2B,GAAG;EAC5C;;;;EAKA,mBAAgB;AACd,UAAM,SAAS,aAAa,QAAQ,gBAAgB;AACpD,QAAI,QAAQ;AACV,WAAK,qBAAqB,KAAK,MAAM;AACrC,aAAO;IACT;AACA,WAAO,KAAK,qBAAqB,SAAS,KAAK,aAAa,CAAC,EAAE;EACjE;;;;EAKM,aAAa,KAAW;;AAC5B,YAAM,YAAY,KAAK,IAAG;AAE1B,UAAI;AAEF,cAAM,YAAY,IAAI,QAAQ,QAAQ,KAAK;AAE3C,cAAM,KAAK,KAAK,IAAI,WAAW;UAC7B,cAAc;SACf,EAAE,UAAS;AAEZ,cAAM,eAAe,KAAK,IAAG,IAAK;AAElC,eAAO;UACL,SAAS;UACT,SAAS,kCAA6B,YAAY;UAClD;;MAGJ,SAAS,OAAY;AACnB,cAAM,eAAe,KAAK,IAAG,IAAK;AAElC,YAAI,UAAU;AAEd,YAAI,MAAM,WAAW,GAAG;AACtB,oBAAU;QACZ,WAAW,MAAM,WAAW,KAAK;AAC/B,oBAAU;QACZ,WAAW,MAAM,UAAU,KAAK;AAC9B,oBAAU;QACZ,OAAO;AACL,oBAAU,gBAAW,MAAM,MAAM,KAAK,MAAM,UAAU;QACxD;AAEA,eAAO;UACL,SAAS;UACT,SAAS,GAAG,OAAO,KAAK,YAAY;UACpC;;MAEJ;IACF;;;;;EAKM,mBAAgB;;AACpB,YAAM,UAAU,CAAA;AAEhB,iBAAW,YAAY,KAAK,cAAc;AACxC,cAAM,aAAa,MAAM,KAAK,aAAa,SAAS,GAAG;AACvD,gBAAQ,KAAK,iCACR,WADQ;UAEX;UACD;MACH;AAEA,aAAO;IACT;;;;;EAKM,yBAAsB;;AAC1B,cAAQ,IAAI,+CAAwC;AAGpD,YAAM,iBAAiB,CAAC,SAAS,YAAY,WAAW;AAExD,iBAAW,QAAQ,gBAAgB;AACjC,cAAM,YAAY,KAAK,aAAa,OAAO,OAAK,EAAE,SAAS,IAAI;AAE/D,mBAAW,YAAY,WAAW;AAChC,gBAAM,SAAS,MAAM,KAAK,aAAa,SAAS,GAAG;AAEnD,cAAI,OAAO,SAAS;AAClB,oBAAQ,IAAI,kCAA6B,SAAS,IAAI;AACtD,iBAAK,UAAU,SAAS,GAAG;AAC3B,mBAAO;UACT;QACF;MACF;AAEA,cAAQ,IAAI,mCAA8B;AAC1C,aAAO;IACT;;;;;EAKA,2BAAwB;AACtB,UAAM,cAAc,CAAA;AACpB,UAAM,aAAa,KAAK,iBAAgB;AACxC,UAAM,kBAAkB,KAAK,aAAa,KAAK,OAAK,EAAE,QAAQ,UAAU;AAExE,gBAAY,KAAK,kCAA2B;AAC5C,gBAAY,KAAK,0BAA0B;AAC3C,gBAAY,KAAK,EAAE;AACnB,gBAAY,KAAK,0BAAmB,UAAU,EAAE;AAChD,gBAAY,KAAK,yBAAa,iBAAiB,QAAQ,SAAS,EAAE;AAClE,gBAAY,KAAK,EAAE;AAEnB,QAAI,iBAAiB,SAAS,SAAS;AACrC,kBAAY,KAAK,kCAA2B;AAC5C,kBAAY,KAAK,qDAAqD;AACtE,kBAAY,KAAK,yEAAyE;AAC1F,kBAAY,KAAK,gCAAgC;IACnD,WAAW,iBAAiB,SAAS,YAAY;AAC/C,kBAAY,KAAK,qCAA8B;AAC/C,kBAAY,KAAK,sCAAsC;AACvD,kBAAY,KAAK,oCAAoC;AACrD,kBAAY,KAAK,qCAAqC;AACtD,kBAAY,KAAK,4CAA4C;IAC/D;AAEA,gBAAY,KAAK,EAAE;AACnB,gBAAY,KAAK,0BAAmB;AACpC,gBAAY,KAAK,mDAAmD;AACpE,gBAAY,KAAK,+CAA+C;AAChE,gBAAY,KAAK,kCAAkC;AAEnD,WAAO;EACT;;;uCAhMW,6BAA0B,mBAAA,UAAA,CAAA;IAAA;EAAA;;4EAA1B,6BAA0B,SAA1B,4BAA0B,WAAA,YAFzB,OAAM,CAAA;EAAA;;;sEAEP,4BAA0B,CAAA;UAHtC;WAAW;MACV,YAAY;KACb;;;", "names": []}
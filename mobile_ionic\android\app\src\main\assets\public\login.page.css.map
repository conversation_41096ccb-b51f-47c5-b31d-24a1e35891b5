{"version": 3, "sources": ["src/app/pages/login/login.page.scss"], "sourcesContent": [".login-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 80vh;\r\n}\r\n\r\n.login-wrapper {\r\n  width: 100%;\r\n  max-width: 420px;\r\n  padding: 32px 28px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.login-logo {\r\n  width: 300px;\r\n  height: 300px;\r\n}\r\n\r\n.login-title {\r\n  font-size: 2.2rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.login-desc {\r\n  font-size: 1.1rem;\r\n  color: #888;\r\n\r\n}\r\n\r\n.login-form ion-item {\r\n  font-size: 1.1rem;\r\n  border-radius: 16px;\r\n\r\n}\r\n\r\n.login-btn {\r\n  --border-radius: 25px;\r\n  font-size: 1.2rem;\r\n  height: 48px;\r\n\r\n}\r\n\r\n.login-link {\r\n  margin-top: 20px;\r\n  font-size: 1.1rem;\r\n}\r\n.login-link a {\r\n  font-size: 1.1rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.troubleshooting-section {\r\n  margin-top: 24px;\r\n  padding-top: 16px;\r\n  border-top: 1px solid var(--ion-color-light);\r\n}\r\n\r\n.troubleshooting-section ion-button {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n/* Alert styling */\r\n.login-alert {\r\n  --backdrop-opacity: 0.8;\r\n\r\n  .alert-wrapper {\r\n    border-radius: 15px;\r\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\r\n  }\r\n\r\n  .alert-head {\r\n    padding-bottom: 10px;\r\n  }\r\n\r\n  .alert-title {\r\n    font-size: 1.2rem;\r\n    font-weight: 600;\r\n    color: #d9534f;\r\n  }\r\n\r\n  .alert-message {\r\n    font-size: 1rem;\r\n    color: #333;\r\n  }\r\n\r\n  .alert-button {\r\n    color: #3880ff;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n/* Success Alert styling */\r\n.login-success-alert {\r\n  --backdrop-opacity: 0.8;\r\n\r\n  .alert-wrapper {\r\n    border-radius: 15px;\r\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\r\n  }\r\n\r\n  .alert-head {\r\n    padding-bottom: 10px;\r\n  }\r\n\r\n  .alert-title {\r\n    font-size: 1.2rem;\r\n    font-weight: 600;\r\n    color: #5cb85c; /* Green color for success */\r\n  }\r\n\r\n  .alert-message {\r\n    font-size: 1rem;\r\n    color: #333;\r\n  }\r\n\r\n  .alert-button {\r\n    color: #3880ff;\r\n    font-weight: 500;\r\n  }\r\n}"], "mappings": ";AAAA,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,WAAA,KAAA;AACA,UAAA,EAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;;AAIF,CAAA,WAAA;AACE,aAAA;AACA,iBAAA;;AAIF,CAAA;AACE,mBAAA;AACA,aAAA;AACA,UAAA;;AAIF,CAAA;AACE,cAAA;AACA,aAAA;;AAEF,CAJA,WAIA;AACE,aAAA;AACA,eAAA;;AAGF,CAAA;AACE,cAAA;AACA,eAAA;AACA,cAAA,IAAA,MAAA,IAAA;;AAGF,CANA,wBAMA;AACE,iBAAA;;AAIF,CAAA;AACE,sBAAA;;AAEA,CAHF,YAGE,CAAA;AACE,iBAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CARF,YAQE,CAAA;AACE,kBAAA;;AAGF,CAZF,YAYE,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAlBF,YAkBE,CAAA;AACE,aAAA;AACA,SAAA;;AAGF,CAvBF,YAuBE,CAAA;AACE,SAAA;AACA,eAAA;;AAKJ,CAAA;AACE,sBAAA;;AAEA,CAHF,oBAGE,CA9BA;AA+BE,iBAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CARF,oBAQE,CA9BA;AA+BE,kBAAA;;AAGF,CAZF,oBAYE,CA9BA;AA+BE,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAlBF,oBAkBE,CA9BA;AA+BE,aAAA;AACA,SAAA;;AAGF,CAvBF,oBAuBE,CA9BA;AA+BE,SAAA;AACA,eAAA;;", "names": []}
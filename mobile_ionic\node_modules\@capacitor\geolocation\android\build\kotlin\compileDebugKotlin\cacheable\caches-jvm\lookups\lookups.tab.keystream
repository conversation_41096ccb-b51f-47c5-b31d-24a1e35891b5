  Manifest android  
permission android.Manifest  ACCESS_COARSE_LOCATION android.Manifest.permission  ACCESS_FINE_LOCATION android.Manifest.permission  registerForActivityResult android.app.Activity  Context android.content  registerForActivityResult android.content.Context  registerForActivityResult android.content.ContextWrapper  Build 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  registerForActivityResult  android.view.ContextThemeWrapper  registerForActivityResult #androidx.activity.ComponentActivity  ActivityResult androidx.activity.result  ActivityResultLauncher androidx.activity.result  IntentSenderRequest androidx.activity.result  
resultCode 'androidx.activity.result.ActivityResult  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  ActivityResultContracts !androidx.activity.result.contract  StartIntentSenderForResult 9androidx.activity.result.contract.ActivityResultContracts  invoke ^androidx.activity.result.contract.ActivityResultContracts.StartIntentSenderForResult.Companion  AppCompatActivity androidx.appcompat.app  registerForActivityResult (androidx.appcompat.app.AppCompatActivity  registerForActivityResult #androidx.core.app.ComponentActivity  registerForActivityResult &androidx.fragment.app.FragmentActivity  ActivityResultContracts #com.capacitorjs.plugins.geolocation  Boolean #com.capacitorjs.plugins.geolocation  Build #com.capacitorjs.plugins.geolocation  COARSE_LOCATION_ALIAS #com.capacitorjs.plugins.geolocation  CoroutineScope #com.capacitorjs.plugins.geolocation  Dispatchers #com.capacitorjs.plugins.geolocation  	ErrorInfo #com.capacitorjs.plugins.geolocation  GeolocationErrors #com.capacitorjs.plugins.geolocation  GeolocationPlugin #com.capacitorjs.plugins.geolocation  IONGLOCController #com.capacitorjs.plugins.geolocation  IONGLOCLocationOptions #com.capacitorjs.plugins.geolocation  Int #com.capacitorjs.plugins.geolocation  JSObject #com.capacitorjs.plugins.geolocation  LOCATION_ALIAS #com.capacitorjs.plugins.geolocation  LocationServices #com.capacitorjs.plugins.geolocation  Long #com.capacitorjs.plugins.geolocation  Manifest #com.capacitorjs.plugins.geolocation  
MutableMap #com.capacitorjs.plugins.geolocation  PermissionState #com.capacitorjs.plugins.geolocation  PluginMethod #com.capacitorjs.plugins.geolocation  String #com.capacitorjs.plugins.geolocation  	Throwable #com.capacitorjs.plugins.geolocation  Unit #com.capacitorjs.plugins.geolocation  activity #com.capacitorjs.plugins.geolocation  apply #com.capacitorjs.plugins.geolocation  cancel #com.capacitorjs.plugins.geolocation  
controller #com.capacitorjs.plugins.geolocation  
createOptions #com.capacitorjs.plugins.geolocation  forEach #com.capacitorjs.plugins.geolocation  getJSObjectForLocation #com.capacitorjs.plugins.geolocation  
isNullOrBlank #com.capacitorjs.plugins.geolocation  launch #com.capacitorjs.plugins.geolocation  let #com.capacitorjs.plugins.geolocation  mutableMapOf #com.capacitorjs.plugins.geolocation  	onFailure #com.capacitorjs.plugins.geolocation  onLocationError #com.capacitorjs.plugins.geolocation  	onSuccess #com.capacitorjs.plugins.geolocation  padStart #com.capacitorjs.plugins.geolocation  sendSuccess #com.capacitorjs.plugins.geolocation  set #com.capacitorjs.plugins.geolocation  	ErrorInfo 5com.capacitorjs.plugins.geolocation.GeolocationErrors  GET_LOCATION_TIMEOUT 5com.capacitorjs.plugins.geolocation.GeolocationErrors  GOOGLE_SERVICES_ERROR 5com.capacitorjs.plugins.geolocation.GeolocationErrors  GOOGLE_SERVICES_RESOLVABLE 5com.capacitorjs.plugins.geolocation.GeolocationErrors  INVALID_TIMEOUT 5com.capacitorjs.plugins.geolocation.GeolocationErrors  Int 5com.capacitorjs.plugins.geolocation.GeolocationErrors  LOCATION_DISABLED 5com.capacitorjs.plugins.geolocation.GeolocationErrors  LOCATION_ENABLE_REQUEST_DENIED 5com.capacitorjs.plugins.geolocation.GeolocationErrors  LOCATION_PERMISSIONS_DENIED 5com.capacitorjs.plugins.geolocation.GeolocationErrors  LOCATION_SETTINGS_ERROR 5com.capacitorjs.plugins.geolocation.GeolocationErrors  POSITION_UNAVAILABLE 5com.capacitorjs.plugins.geolocation.GeolocationErrors  String 5com.capacitorjs.plugins.geolocation.GeolocationErrors  WATCH_ID_NOT_FOUND 5com.capacitorjs.plugins.geolocation.GeolocationErrors  WATCH_ID_NOT_PROVIDED 5com.capacitorjs.plugins.geolocation.GeolocationErrors  formatErrorCode 5com.capacitorjs.plugins.geolocation.GeolocationErrors  getPADStart 5com.capacitorjs.plugins.geolocation.GeolocationErrors  getPadStart 5com.capacitorjs.plugins.geolocation.GeolocationErrors  padStart 5com.capacitorjs.plugins.geolocation.GeolocationErrors  String ?com.capacitorjs.plugins.geolocation.GeolocationErrors.ErrorInfo  code ?com.capacitorjs.plugins.geolocation.GeolocationErrors.ErrorInfo  message ?com.capacitorjs.plugins.geolocation.GeolocationErrors.ErrorInfo  ActivityResultContracts 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  Boolean 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  Build 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  COARSE_LOCATION_ALIAS 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  CoroutineScope 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  Dispatchers 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  GeolocationErrors 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  IONGLOCController 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  IONGLOCException 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  IONGLOCLocationOptions 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  IONGLOCLocationResult 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  JSObject 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  LOCATION_ALIAS 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  LocationServices 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  Long 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  
MutableMap 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  PermissionCallback 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  PermissionState 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  
PluginCall 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  PluginMethod 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  String 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  	Throwable 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  Unit 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  activity 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  apply 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  bridge 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  cancel 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  checkLocationState 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  context 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  
controller 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  coroutineScope 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  
createOptions 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getACTIVITY 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getAPPLY 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getActivity 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getAlias 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getApply 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  	getCANCEL 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  
getCONTEXT 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  	getCancel 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  
getContext 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getISNullOrBlank 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getIsNullOrBlank 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getJSObjectForLocation 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  	getLAUNCH 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getLET 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  	getLaunch 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getLet 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getMUTABLEMapOf 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getMutableMapOf 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  	getNumber 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getONFailure 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getONSuccess 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getOnFailure 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getOnSuccess 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getPermissionState 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getPosition 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getSET 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  getSet 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  handlePermissionRequest 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  handlePermissionResult 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  invoke 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  
isNullOrBlank 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  launch 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  let 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  mutableMapOf 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  	onFailure 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  onLocationError 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  	onSuccess 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  requestPermissionForAlias 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  	sendError 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  sendSuccess 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  set 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  setActivity 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  
setContext 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  
startWatch 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  
watchingCalls 5com.capacitorjs.plugins.geolocation.GeolocationPlugin  ActivityResultContracts ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  Boolean ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  Build ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  COARSE_LOCATION_ALIAS ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  CoroutineScope ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  Dispatchers ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  GeolocationErrors ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  IONGLOCController ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  IONGLOCException ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  IONGLOCLocationOptions ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  IONGLOCLocationResult ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  JSObject ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  LOCATION_ALIAS ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  LocationServices ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  Long ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  
MutableMap ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  PermissionCallback ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  PermissionState ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  
PluginCall ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  PluginMethod ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  String ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  	Throwable ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  Unit ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  activity ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  apply ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  cancel ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  
controller ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  
createOptions ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getAPPLY ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getApply ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  	getCANCEL ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  	getCancel ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getISNullOrBlank ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getIsNullOrBlank ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getJSObjectForLocation ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  	getLAUNCH ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getLET ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  	getLaunch ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getLet ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getMUTABLEMapOf ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getMutableMapOf ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getONFailure ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getONSuccess ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getOnFailure ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getOnSuccess ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getSET ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  getSet ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  invoke ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  
isNullOrBlank ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  launch ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  let ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  mutableMapOf ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  	onFailure ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  onLocationError ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  	onSuccess ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  sendSuccess ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  set ?com.capacitorjs.plugins.geolocation.GeolocationPlugin.Companion  Bridge com.getcapacitor  JSObject com.getcapacitor  PermissionState com.getcapacitor  Plugin com.getcapacitor  
PluginCall com.getcapacitor  PluginMethod com.getcapacitor  apply com.getcapacitor.JSObject  equals com.getcapacitor.JSObject  getAPPLY com.getcapacitor.JSObject  getApply com.getcapacitor.JSObject  getLET com.getcapacitor.JSObject  getLet com.getcapacitor.JSObject  let com.getcapacitor.JSObject  put com.getcapacitor.JSObject  GRANTED  com.getcapacitor.PermissionState  equals  com.getcapacitor.PermissionState  ActivityResultContracts com.getcapacitor.Plugin  Boolean com.getcapacitor.Plugin  Build com.getcapacitor.Plugin  COARSE_LOCATION_ALIAS com.getcapacitor.Plugin  CoroutineScope com.getcapacitor.Plugin  Dispatchers com.getcapacitor.Plugin  GeolocationErrors com.getcapacitor.Plugin  IONGLOCController com.getcapacitor.Plugin  IONGLOCException com.getcapacitor.Plugin  IONGLOCLocationOptions com.getcapacitor.Plugin  IONGLOCLocationResult com.getcapacitor.Plugin  JSObject com.getcapacitor.Plugin  LOCATION_ALIAS com.getcapacitor.Plugin  LocationServices com.getcapacitor.Plugin  Long com.getcapacitor.Plugin  
MutableMap com.getcapacitor.Plugin  PermissionCallback com.getcapacitor.Plugin  PermissionState com.getcapacitor.Plugin  
PluginCall com.getcapacitor.Plugin  PluginMethod com.getcapacitor.Plugin  String com.getcapacitor.Plugin  	Throwable com.getcapacitor.Plugin  Unit com.getcapacitor.Plugin  activity com.getcapacitor.Plugin  apply com.getcapacitor.Plugin  cancel com.getcapacitor.Plugin  checkLocationState com.getcapacitor.Plugin  checkPermissions com.getcapacitor.Plugin  
controller com.getcapacitor.Plugin  
createOptions com.getcapacitor.Plugin  getAlias com.getcapacitor.Plugin  getJSObjectForLocation com.getcapacitor.Plugin  	getNumber com.getcapacitor.Plugin  getPermissionState com.getcapacitor.Plugin  getPosition com.getcapacitor.Plugin  handleOnDestroy com.getcapacitor.Plugin  handlePermissionRequest com.getcapacitor.Plugin  handlePermissionResult com.getcapacitor.Plugin  invoke com.getcapacitor.Plugin  
isNullOrBlank com.getcapacitor.Plugin  launch com.getcapacitor.Plugin  let com.getcapacitor.Plugin  load com.getcapacitor.Plugin  mutableMapOf com.getcapacitor.Plugin  	onFailure com.getcapacitor.Plugin  onLocationError com.getcapacitor.Plugin  	onSuccess com.getcapacitor.Plugin  requestPermissionForAlias com.getcapacitor.Plugin  requestPermissions com.getcapacitor.Plugin  	sendError com.getcapacitor.Plugin  sendSuccess com.getcapacitor.Plugin  set com.getcapacitor.Plugin  
startWatch com.getcapacitor.Plugin  
callbackId com.getcapacitor.PluginCall  
getBoolean com.getcapacitor.PluginCall  
getCALLBACKId com.getcapacitor.PluginCall  
getCallbackId com.getcapacitor.PluginCall  getGETNumber com.getcapacitor.PluginCall  getGetNumber com.getcapacitor.PluginCall  getInt com.getcapacitor.PluginCall  getLong com.getcapacitor.PluginCall  	getNumber com.getcapacitor.PluginCall  getSENDError com.getcapacitor.PluginCall  getSENDSuccess com.getcapacitor.PluginCall  getSendError com.getcapacitor.PluginCall  getSendSuccess com.getcapacitor.PluginCall  	getString com.getcapacitor.PluginCall  reject com.getcapacitor.PluginCall  release com.getcapacitor.PluginCall  resolve com.getcapacitor.PluginCall  	sendError com.getcapacitor.PluginCall  sendSuccess com.getcapacitor.PluginCall  
setCallbackId com.getcapacitor.PluginCall  setKeepAlive com.getcapacitor.PluginCall  RETURN_CALLBACK com.getcapacitor.PluginMethod  CapacitorPlugin com.getcapacitor.annotation  
Permission com.getcapacitor.annotation  PermissionCallback com.getcapacitor.annotation  FusedLocationProviderClient com.google.android.gms.location  LocationServices com.google.android.gms.location  getFusedLocationProviderClient 0com.google.android.gms.location.LocationServices  IONGLOCController *io.ionic.libs.iongeolocationlib.controller  addWatch <io.ionic.libs.iongeolocationlib.controller.IONGLOCController  areLocationServicesEnabled <io.ionic.libs.iongeolocationlib.controller.IONGLOCController  
clearWatch <io.ionic.libs.iongeolocationlib.controller.IONGLOCController  getCurrentPosition <io.ionic.libs.iongeolocationlib.controller.IONGLOCController  onResolvableExceptionResult <io.ionic.libs.iongeolocationlib.controller.IONGLOCController  invoke Fio.ionic.libs.iongeolocationlib.controller.IONGLOCController.Companion  IONGLOCException %io.ionic.libs.iongeolocationlib.model  IONGLOCLocationOptions %io.ionic.libs.iongeolocationlib.model  IONGLOCLocationResult %io.ionic.libs.iongeolocationlib.model  IONGLOCGoogleServicesException 6io.ionic.libs.iongeolocationlib.model.IONGLOCException  IONGLOCInvalidTimeoutException 6io.ionic.libs.iongeolocationlib.model.IONGLOCException  (IONGLOCLocationRetrievalTimeoutException 6io.ionic.libs.iongeolocationlib.model.IONGLOCException  IONGLOCRequestDeniedException 6io.ionic.libs.iongeolocationlib.model.IONGLOCException  IONGLOCSettingsException 6io.ionic.libs.iongeolocationlib.model.IONGLOCException  
resolvable Uio.ionic.libs.iongeolocationlib.model.IONGLOCException.IONGLOCGoogleServicesException  accuracy ;io.ionic.libs.iongeolocationlib.model.IONGLOCLocationResult  altitude ;io.ionic.libs.iongeolocationlib.model.IONGLOCLocationResult  altitudeAccuracy ;io.ionic.libs.iongeolocationlib.model.IONGLOCLocationResult  heading ;io.ionic.libs.iongeolocationlib.model.IONGLOCLocationResult  latitude ;io.ionic.libs.iongeolocationlib.model.IONGLOCLocationResult  	longitude ;io.ionic.libs.iongeolocationlib.model.IONGLOCLocationResult  speed ;io.ionic.libs.iongeolocationlib.model.IONGLOCLocationResult  	timestamp ;io.ionic.libs.iongeolocationlib.model.IONGLOCLocationResult  ActivityResultContracts 	java.lang  Build 	java.lang  COARSE_LOCATION_ALIAS 	java.lang  CoroutineScope 	java.lang  Dispatchers 	java.lang  	ErrorInfo 	java.lang  GeolocationErrors 	java.lang  IONGLOCController 	java.lang  IONGLOCLocationOptions 	java.lang  JSObject 	java.lang  LOCATION_ALIAS 	java.lang  LocationServices 	java.lang  Manifest 	java.lang  PermissionState 	java.lang  PluginMethod 	java.lang  activity 	java.lang  apply 	java.lang  cancel 	java.lang  
controller 	java.lang  
createOptions 	java.lang  forEach 	java.lang  getJSObjectForLocation 	java.lang  
isNullOrBlank 	java.lang  launch 	java.lang  let 	java.lang  mutableMapOf 	java.lang  	onFailure 	java.lang  onLocationError 	java.lang  	onSuccess 	java.lang  padStart 	java.lang  sendSuccess 	java.lang  set 	java.lang  ActivityResultContracts kotlin  Array kotlin  Boolean kotlin  Build kotlin  COARSE_LOCATION_ALIAS kotlin  Char kotlin  CoroutineScope kotlin  Dispatchers kotlin  Double kotlin  	ErrorInfo kotlin  Float kotlin  	Function0 kotlin  	Function1 kotlin  GeolocationErrors kotlin  IONGLOCController kotlin  IONGLOCLocationOptions kotlin  Int kotlin  JSObject kotlin  LOCATION_ALIAS kotlin  LocationServices kotlin  Long kotlin  Manifest kotlin  Nothing kotlin  PermissionState kotlin  PluginMethod kotlin  Result kotlin  String kotlin  	Throwable kotlin  Unit kotlin  activity kotlin  apply kotlin  arrayOf kotlin  cancel kotlin  
controller kotlin  
createOptions kotlin  forEach kotlin  getJSObjectForLocation kotlin  
isNullOrBlank kotlin  launch kotlin  let kotlin  mutableMapOf kotlin  	onFailure kotlin  onLocationError kotlin  	onSuccess kotlin  padStart kotlin  sendSuccess kotlin  set kotlin  getLET kotlin.Float  getLet kotlin.Float  getONFailure 
kotlin.Result  getONSuccess 
kotlin.Result  getOnFailure 
kotlin.Result  getOnSuccess 
kotlin.Result  	onFailure 
kotlin.Result  	onSuccess 
kotlin.Result  getISNullOrBlank 
kotlin.String  getIsNullOrBlank 
kotlin.String  getPADStart 
kotlin.String  getPadStart 
kotlin.String  
isNullOrBlank 
kotlin.String  ActivityResultContracts kotlin.annotation  Build kotlin.annotation  COARSE_LOCATION_ALIAS kotlin.annotation  CoroutineScope kotlin.annotation  Dispatchers kotlin.annotation  	ErrorInfo kotlin.annotation  GeolocationErrors kotlin.annotation  IONGLOCController kotlin.annotation  IONGLOCLocationOptions kotlin.annotation  JSObject kotlin.annotation  LOCATION_ALIAS kotlin.annotation  LocationServices kotlin.annotation  Manifest kotlin.annotation  PermissionState kotlin.annotation  PluginMethod kotlin.annotation  activity kotlin.annotation  apply kotlin.annotation  cancel kotlin.annotation  
controller kotlin.annotation  
createOptions kotlin.annotation  forEach kotlin.annotation  getJSObjectForLocation kotlin.annotation  
isNullOrBlank kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  mutableMapOf kotlin.annotation  	onFailure kotlin.annotation  onLocationError kotlin.annotation  	onSuccess kotlin.annotation  padStart kotlin.annotation  sendSuccess kotlin.annotation  set kotlin.annotation  ActivityResultContracts kotlin.collections  Build kotlin.collections  COARSE_LOCATION_ALIAS kotlin.collections  CoroutineScope kotlin.collections  Dispatchers kotlin.collections  	ErrorInfo kotlin.collections  GeolocationErrors kotlin.collections  IONGLOCController kotlin.collections  IONGLOCLocationOptions kotlin.collections  JSObject kotlin.collections  LOCATION_ALIAS kotlin.collections  List kotlin.collections  LocationServices kotlin.collections  Manifest kotlin.collections  
MutableMap kotlin.collections  PermissionState kotlin.collections  PluginMethod kotlin.collections  activity kotlin.collections  apply kotlin.collections  cancel kotlin.collections  
controller kotlin.collections  
createOptions kotlin.collections  forEach kotlin.collections  getJSObjectForLocation kotlin.collections  
isNullOrBlank kotlin.collections  launch kotlin.collections  let kotlin.collections  mutableMapOf kotlin.collections  	onFailure kotlin.collections  onLocationError kotlin.collections  	onSuccess kotlin.collections  padStart kotlin.collections  sendSuccess kotlin.collections  set kotlin.collections  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  ActivityResultContracts kotlin.comparisons  Build kotlin.comparisons  COARSE_LOCATION_ALIAS kotlin.comparisons  CoroutineScope kotlin.comparisons  Dispatchers kotlin.comparisons  	ErrorInfo kotlin.comparisons  GeolocationErrors kotlin.comparisons  IONGLOCController kotlin.comparisons  IONGLOCLocationOptions kotlin.comparisons  JSObject kotlin.comparisons  LOCATION_ALIAS kotlin.comparisons  LocationServices kotlin.comparisons  Manifest kotlin.comparisons  PermissionState kotlin.comparisons  PluginMethod kotlin.comparisons  activity kotlin.comparisons  apply kotlin.comparisons  cancel kotlin.comparisons  
controller kotlin.comparisons  
createOptions kotlin.comparisons  forEach kotlin.comparisons  getJSObjectForLocation kotlin.comparisons  
isNullOrBlank kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  mutableMapOf kotlin.comparisons  	onFailure kotlin.comparisons  onLocationError kotlin.comparisons  	onSuccess kotlin.comparisons  padStart kotlin.comparisons  sendSuccess kotlin.comparisons  set kotlin.comparisons  SuspendFunction1 kotlin.coroutines  ActivityResultContracts 	kotlin.io  Build 	kotlin.io  COARSE_LOCATION_ALIAS 	kotlin.io  CoroutineScope 	kotlin.io  Dispatchers 	kotlin.io  	ErrorInfo 	kotlin.io  GeolocationErrors 	kotlin.io  IONGLOCController 	kotlin.io  IONGLOCLocationOptions 	kotlin.io  JSObject 	kotlin.io  LOCATION_ALIAS 	kotlin.io  LocationServices 	kotlin.io  Manifest 	kotlin.io  PermissionState 	kotlin.io  PluginMethod 	kotlin.io  activity 	kotlin.io  apply 	kotlin.io  cancel 	kotlin.io  
controller 	kotlin.io  
createOptions 	kotlin.io  forEach 	kotlin.io  getJSObjectForLocation 	kotlin.io  
isNullOrBlank 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  mutableMapOf 	kotlin.io  	onFailure 	kotlin.io  onLocationError 	kotlin.io  	onSuccess 	kotlin.io  padStart 	kotlin.io  sendSuccess 	kotlin.io  set 	kotlin.io  ActivityResultContracts 
kotlin.jvm  Build 
kotlin.jvm  COARSE_LOCATION_ALIAS 
kotlin.jvm  CoroutineScope 
kotlin.jvm  Dispatchers 
kotlin.jvm  	ErrorInfo 
kotlin.jvm  GeolocationErrors 
kotlin.jvm  IONGLOCController 
kotlin.jvm  IONGLOCLocationOptions 
kotlin.jvm  JSObject 
kotlin.jvm  LOCATION_ALIAS 
kotlin.jvm  LocationServices 
kotlin.jvm  Manifest 
kotlin.jvm  PermissionState 
kotlin.jvm  PluginMethod 
kotlin.jvm  activity 
kotlin.jvm  apply 
kotlin.jvm  cancel 
kotlin.jvm  
controller 
kotlin.jvm  
createOptions 
kotlin.jvm  forEach 
kotlin.jvm  getJSObjectForLocation 
kotlin.jvm  
isNullOrBlank 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  mutableMapOf 
kotlin.jvm  	onFailure 
kotlin.jvm  onLocationError 
kotlin.jvm  	onSuccess 
kotlin.jvm  padStart 
kotlin.jvm  sendSuccess 
kotlin.jvm  set 
kotlin.jvm  ActivityResultContracts 
kotlin.ranges  Build 
kotlin.ranges  COARSE_LOCATION_ALIAS 
kotlin.ranges  CoroutineScope 
kotlin.ranges  Dispatchers 
kotlin.ranges  	ErrorInfo 
kotlin.ranges  GeolocationErrors 
kotlin.ranges  IONGLOCController 
kotlin.ranges  IONGLOCLocationOptions 
kotlin.ranges  JSObject 
kotlin.ranges  LOCATION_ALIAS 
kotlin.ranges  LocationServices 
kotlin.ranges  Manifest 
kotlin.ranges  PermissionState 
kotlin.ranges  PluginMethod 
kotlin.ranges  activity 
kotlin.ranges  apply 
kotlin.ranges  cancel 
kotlin.ranges  
controller 
kotlin.ranges  
createOptions 
kotlin.ranges  forEach 
kotlin.ranges  getJSObjectForLocation 
kotlin.ranges  
isNullOrBlank 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  mutableMapOf 
kotlin.ranges  	onFailure 
kotlin.ranges  onLocationError 
kotlin.ranges  	onSuccess 
kotlin.ranges  padStart 
kotlin.ranges  sendSuccess 
kotlin.ranges  set 
kotlin.ranges  ActivityResultContracts kotlin.sequences  Build kotlin.sequences  COARSE_LOCATION_ALIAS kotlin.sequences  CoroutineScope kotlin.sequences  Dispatchers kotlin.sequences  	ErrorInfo kotlin.sequences  GeolocationErrors kotlin.sequences  IONGLOCController kotlin.sequences  IONGLOCLocationOptions kotlin.sequences  JSObject kotlin.sequences  LOCATION_ALIAS kotlin.sequences  LocationServices kotlin.sequences  Manifest kotlin.sequences  PermissionState kotlin.sequences  PluginMethod kotlin.sequences  activity kotlin.sequences  apply kotlin.sequences  cancel kotlin.sequences  
controller kotlin.sequences  
createOptions kotlin.sequences  forEach kotlin.sequences  getJSObjectForLocation kotlin.sequences  
isNullOrBlank kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  mutableMapOf kotlin.sequences  	onFailure kotlin.sequences  onLocationError kotlin.sequences  	onSuccess kotlin.sequences  padStart kotlin.sequences  sendSuccess kotlin.sequences  set kotlin.sequences  ActivityResultContracts kotlin.text  Build kotlin.text  COARSE_LOCATION_ALIAS kotlin.text  CoroutineScope kotlin.text  Dispatchers kotlin.text  	ErrorInfo kotlin.text  GeolocationErrors kotlin.text  IONGLOCController kotlin.text  IONGLOCLocationOptions kotlin.text  JSObject kotlin.text  LOCATION_ALIAS kotlin.text  LocationServices kotlin.text  Manifest kotlin.text  PermissionState kotlin.text  PluginMethod kotlin.text  activity kotlin.text  apply kotlin.text  cancel kotlin.text  
controller kotlin.text  
createOptions kotlin.text  forEach kotlin.text  getJSObjectForLocation kotlin.text  
isNullOrBlank kotlin.text  launch kotlin.text  let kotlin.text  mutableMapOf kotlin.text  	onFailure kotlin.text  onLocationError kotlin.text  	onSuccess kotlin.text  padStart kotlin.text  sendSuccess kotlin.text  set kotlin.text  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  cancel kotlinx.coroutines  launch kotlinx.coroutines  activity !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  
controller !kotlinx.coroutines.CoroutineScope  
createOptions !kotlinx.coroutines.CoroutineScope  getACTIVITY !kotlinx.coroutines.CoroutineScope  getActivity !kotlinx.coroutines.CoroutineScope  	getCANCEL !kotlinx.coroutines.CoroutineScope  
getCONTROLLER !kotlinx.coroutines.CoroutineScope  getCREATEOptions !kotlinx.coroutines.CoroutineScope  	getCancel !kotlinx.coroutines.CoroutineScope  
getController !kotlinx.coroutines.CoroutineScope  getCreateOptions !kotlinx.coroutines.CoroutineScope  getGetJSObjectForLocation !kotlinx.coroutines.CoroutineScope  getJSObjectForLocation !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getONFailure !kotlinx.coroutines.CoroutineScope  getONLocationError !kotlinx.coroutines.CoroutineScope  getONSuccess !kotlinx.coroutines.CoroutineScope  getOnFailure !kotlinx.coroutines.CoroutineScope  getOnLocationError !kotlinx.coroutines.CoroutineScope  getOnSuccess !kotlinx.coroutines.CoroutineScope  getSENDSuccess !kotlinx.coroutines.CoroutineScope  getSendSuccess !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  onLocationError !kotlinx.coroutines.CoroutineScope  	onSuccess !kotlinx.coroutines.CoroutineScope  sendSuccess !kotlinx.coroutines.CoroutineScope  Main kotlinx.coroutines.Dispatchers  collect kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  apply org.json.JSONObject  put org.json.JSONObject                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
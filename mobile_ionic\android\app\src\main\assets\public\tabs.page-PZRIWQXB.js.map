{"version": 3, "sources": ["src/app/pages/tabs/tabs.page.ts", "src/app/pages/tabs/tabs.page.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-tabs',\r\n  templateUrl: './tabs.page.html',\r\n  //styleUrls: ['./tabs.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, RouterModule]\r\n})\r\nexport class TabsPage {\r\n  constructor() {}\r\n} ", "<ion-tabs>\r\n  <ion-tab-bar slot=\"bottom\">\r\n    <ion-tab-button tab=\"home\">\r\n      <img src=\"assets/homePage.png\" style=\"width:24px; height:24px; display:block; margin:auto;\" />\r\n      <ion-label>Home</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab=\"search\">\r\n      <img src=\"assets/searchPlace.png\" style=\"width:24px; height:24px; display:block; margin:auto;\" />\r\n      <ion-label>Search</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab=\"map\">\r\n      <img src=\"assets/map.png\" style=\"width:24px; height:24px; display:block; margin:auto;\" />\r\n      <ion-label>Map</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab=\"profile\">\r\n      <img src=\"assets/setting.png\" style=\"width:24px; height:24px; display:block; margin:auto;\" />\r\n      <ion-label>Settings</ion-label>\r\n    </ion-tab-button>\r\n  </ion-tab-bar>\r\n</ion-tabs> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYM,IAAO,WAAP,MAAO,UAAQ;EACnB,cAAA;EAAe;;;uCADJ,WAAQ;IAAA;EAAA;;yEAAR,WAAQ,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,QAAA,QAAA,GAAA,CAAA,OAAA,MAAA,GAAA,CAAA,OAAA,uBAAA,GAAA,SAAA,QAAA,UAAA,QAAA,WAAA,SAAA,UAAA,MAAA,GAAA,CAAA,OAAA,QAAA,GAAA,CAAA,OAAA,0BAAA,GAAA,SAAA,QAAA,UAAA,QAAA,WAAA,SAAA,UAAA,MAAA,GAAA,CAAA,OAAA,KAAA,GAAA,CAAA,OAAA,kBAAA,GAAA,SAAA,QAAA,UAAA,QAAA,WAAA,SAAA,UAAA,MAAA,GAAA,CAAA,OAAA,SAAA,GAAA,CAAA,OAAA,sBAAA,GAAA,SAAA,QAAA,UAAA,QAAA,WAAA,SAAA,UAAA,MAAA,CAAA,GAAA,UAAA,SAAA,kBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACZrB,QAAA,yBAAA,GAAA,UAAA,EAAU,GAAA,eAAA,CAAA,EACmB,GAAA,kBAAA,CAAA;AAEvB,QAAA,oBAAA,GAAA,OAAA,CAAA;AACA,QAAA,yBAAA,GAAA,WAAA;AAAW,QAAA,iBAAA,GAAA,MAAA;AAAI,QAAA,uBAAA,EAAY;AAG7B,QAAA,yBAAA,GAAA,kBAAA,CAAA;AACE,QAAA,oBAAA,GAAA,OAAA,CAAA;AACA,QAAA,yBAAA,GAAA,WAAA;AAAW,QAAA,iBAAA,GAAA,QAAA;AAAM,QAAA,uBAAA,EAAY;AAG/B,QAAA,yBAAA,IAAA,kBAAA,CAAA;AACE,QAAA,oBAAA,IAAA,OAAA,CAAA;AACA,QAAA,yBAAA,IAAA,WAAA;AAAW,QAAA,iBAAA,IAAA,KAAA;AAAG,QAAA,uBAAA,EAAY;AAG5B,QAAA,yBAAA,IAAA,kBAAA,CAAA;AACE,QAAA,oBAAA,IAAA,OAAA,CAAA;AACA,QAAA,yBAAA,IAAA,WAAA;AAAW,QAAA,iBAAA,IAAA,UAAA;AAAQ,QAAA,uBAAA,EAAY,EAChB,EACL;;sBDXJ,aAAW,UAAA,WAAA,cAAA,SAAE,cAAc,YAAY,GAAA,eAAA,EAAA,CAAA;EAAA;;;sEAEtC,UAAQ,CAAA;UAPpB;uBACW,YAAU,YAGR,MAAI,SACP,CAAC,aAAa,cAAc,YAAY,GAAC,UAAA,w4BAAA,CAAA;;;;6EAEvC,UAAQ,EAAA,WAAA,YAAA,UAAA,mCAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ionic-global-b26f573e.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getMode, a as setMode } from './index-527b9e34.js';\nimport { c as config, a as configFromSession, b as configFromURL, s as saveConfig, p as printIonWarning } from './index-cfd9c1f2.js';\nconst getPlatforms = win => setupPlatforms(win);\nconst isPlatform = (winOrPlatform, platform) => {\n  if (typeof winOrPlatform === 'string') {\n    platform = winOrPlatform;\n    winOrPlatform = undefined;\n  }\n  return getPlatforms(winOrPlatform).includes(platform);\n};\nconst setupPlatforms = (win = window) => {\n  if (typeof win === 'undefined') {\n    return [];\n  }\n  win.Ionic = win.Ionic || {};\n  let platforms = win.Ionic.platforms;\n  if (platforms == null) {\n    platforms = win.Ionic.platforms = detectPlatforms(win);\n    platforms.forEach(p => win.document.documentElement.classList.add(`plt-${p}`));\n  }\n  return platforms;\n};\nconst detectPlatforms = win => {\n  const customPlatformMethods = config.get('platform');\n  return Object.keys(PLATFORMS_MAP).filter(p => {\n    const customMethod = customPlatformMethods === null || customPlatformMethods === void 0 ? void 0 : customPlatformMethods[p];\n    return typeof customMethod === 'function' ? customMethod(win) : PLATFORMS_MAP[p](win);\n  });\n};\nconst isMobileWeb = win => isMobile(win) && !isHybrid(win);\nconst isIpad = win => {\n  // iOS 12 and below\n  if (testUserAgent(win, /iPad/i)) {\n    return true;\n  }\n  // iOS 13+\n  if (testUserAgent(win, /Macintosh/i) && isMobile(win)) {\n    return true;\n  }\n  return false;\n};\nconst isIphone = win => testUserAgent(win, /iPhone/i);\nconst isIOS = win => testUserAgent(win, /iPhone|iPod/i) || isIpad(win);\nconst isAndroid = win => testUserAgent(win, /android|sink/i);\nconst isAndroidTablet = win => {\n  return isAndroid(win) && !testUserAgent(win, /mobile/i);\n};\nconst isPhablet = win => {\n  const width = win.innerWidth;\n  const height = win.innerHeight;\n  const smallest = Math.min(width, height);\n  const largest = Math.max(width, height);\n  return smallest > 390 && smallest < 520 && largest > 620 && largest < 800;\n};\nconst isTablet = win => {\n  const width = win.innerWidth;\n  const height = win.innerHeight;\n  const smallest = Math.min(width, height);\n  const largest = Math.max(width, height);\n  return isIpad(win) || isAndroidTablet(win) || smallest > 460 && smallest < 820 && largest > 780 && largest < 1400;\n};\nconst isMobile = win => matchMedia(win, '(any-pointer:coarse)');\nconst isDesktop = win => !isMobile(win);\nconst isHybrid = win => isCordova(win) || isCapacitorNative(win);\nconst isCordova = win => !!(win['cordova'] || win['phonegap'] || win['PhoneGap']);\nconst isCapacitorNative = win => {\n  const capacitor = win['Capacitor'];\n  // TODO(ROU-11693): Remove when we no longer support Capacitor 2, which does not have isNativePlatform\n  return !!((capacitor === null || capacitor === void 0 ? void 0 : capacitor.isNative) || (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isNativePlatform) && !!capacitor.isNativePlatform());\n};\nconst isElectron = win => testUserAgent(win, /electron/i);\nconst isPWA = win => {\n  var _a;\n  return !!(((_a = win.matchMedia) === null || _a === void 0 ? void 0 : _a.call(win, '(display-mode: standalone)').matches) || win.navigator.standalone);\n};\nconst testUserAgent = (win, expr) => expr.test(win.navigator.userAgent);\nconst matchMedia = (win, query) => {\n  var _a;\n  return (_a = win.matchMedia) === null || _a === void 0 ? void 0 : _a.call(win, query).matches;\n};\nconst PLATFORMS_MAP = {\n  ipad: isIpad,\n  iphone: isIphone,\n  ios: isIOS,\n  android: isAndroid,\n  phablet: isPhablet,\n  tablet: isTablet,\n  cordova: isCordova,\n  capacitor: isCapacitorNative,\n  electron: isElectron,\n  pwa: isPWA,\n  mobile: isMobile,\n  mobileweb: isMobileWeb,\n  desktop: isDesktop,\n  hybrid: isHybrid\n};\n\n// TODO(FW-2832): types\nlet defaultMode;\nconst getIonMode = ref => {\n  return ref && getMode(ref) || defaultMode;\n};\nconst initialize = (userConfig = {}) => {\n  if (typeof window === 'undefined') {\n    return;\n  }\n  const doc = window.document;\n  const win = window;\n  const Ionic = win.Ionic = win.Ionic || {};\n  // create the Ionic.config from raw config object (if it exists)\n  // and convert Ionic.config into a ConfigApi that has a get() fn\n  const configObj = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, configFromSession(win)), {\n    persistConfig: false\n  }), Ionic.config), configFromURL(win)), userConfig);\n  config.reset(configObj);\n  if (config.getBoolean('persistConfig')) {\n    saveConfig(win, configObj);\n  }\n  // Setup platforms\n  setupPlatforms(win);\n  // first see if the mode was set as an attribute on <html>\n  // which could have been set by the user, or by pre-rendering\n  // otherwise get the mode via config settings, and fallback to md\n  Ionic.config = config;\n  Ionic.mode = defaultMode = config.get('mode', doc.documentElement.getAttribute('mode') || (isPlatform(win, 'ios') ? 'ios' : 'md'));\n  config.set('mode', defaultMode);\n  doc.documentElement.setAttribute('mode', defaultMode);\n  doc.documentElement.classList.add(defaultMode);\n  if (config.getBoolean('_testing')) {\n    config.set('animated', false);\n  }\n  const isIonicElement = elm => {\n    var _a;\n    return (_a = elm.tagName) === null || _a === void 0 ? void 0 : _a.startsWith('ION-');\n  };\n  const isAllowedIonicModeValue = elmMode => ['ios', 'md'].includes(elmMode);\n  setMode(elm => {\n    while (elm) {\n      const elmMode = elm.mode || elm.getAttribute('mode');\n      if (elmMode) {\n        if (isAllowedIonicModeValue(elmMode)) {\n          return elmMode;\n        } else if (isIonicElement(elm)) {\n          printIonWarning('Invalid ionic mode: \"' + elmMode + '\", expected: \"ios\" or \"md\"');\n        }\n      }\n      elm = elm.parentElement;\n    }\n    return defaultMode;\n  });\n};\nexport { isPlatform as a, getIonMode as b, getPlatforms as g, initialize as i };"], "mappings": ";;;;;;;;;;;;;AAKA,IAAM,eAAe,SAAO,eAAe,GAAG;AAC9C,IAAM,aAAa,CAAC,eAAe,aAAa;AAC9C,MAAI,OAAO,kBAAkB,UAAU;AACrC,eAAW;AACX,oBAAgB;AAAA,EAClB;AACA,SAAO,aAAa,aAAa,EAAE,SAAS,QAAQ;AACtD;AACA,IAAM,iBAAiB,CAAC,MAAM,WAAW;AACvC,MAAI,OAAO,QAAQ,aAAa;AAC9B,WAAO,CAAC;AAAA,EACV;AACA,MAAI,QAAQ,IAAI,SAAS,CAAC;AAC1B,MAAI,YAAY,IAAI,MAAM;AAC1B,MAAI,aAAa,MAAM;AACrB,gBAAY,IAAI,MAAM,YAAY,gBAAgB,GAAG;AACrD,cAAU,QAAQ,OAAK,IAAI,SAAS,gBAAgB,UAAU,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,EAC/E;AACA,SAAO;AACT;AACA,IAAM,kBAAkB,SAAO;AAC7B,QAAM,wBAAwB,OAAO,IAAI,UAAU;AACnD,SAAO,OAAO,KAAK,aAAa,EAAE,OAAO,OAAK;AAC5C,UAAM,eAAe,0BAA0B,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,CAAC;AAC1H,WAAO,OAAO,iBAAiB,aAAa,aAAa,GAAG,IAAI,cAAc,CAAC,EAAE,GAAG;AAAA,EACtF,CAAC;AACH;AACA,IAAM,cAAc,SAAO,SAAS,GAAG,KAAK,CAAC,SAAS,GAAG;AACzD,IAAM,SAAS,SAAO;AAEpB,MAAI,cAAc,KAAK,OAAO,GAAG;AAC/B,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,KAAK,YAAY,KAAK,SAAS,GAAG,GAAG;AACrD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,WAAW,SAAO,cAAc,KAAK,SAAS;AACpD,IAAM,QAAQ,SAAO,cAAc,KAAK,cAAc,KAAK,OAAO,GAAG;AACrE,IAAM,YAAY,SAAO,cAAc,KAAK,eAAe;AAC3D,IAAM,kBAAkB,SAAO;AAC7B,SAAO,UAAU,GAAG,KAAK,CAAC,cAAc,KAAK,SAAS;AACxD;AACA,IAAM,YAAY,SAAO;AACvB,QAAM,QAAQ,IAAI;AAClB,QAAM,SAAS,IAAI;AACnB,QAAM,WAAW,KAAK,IAAI,OAAO,MAAM;AACvC,QAAM,UAAU,KAAK,IAAI,OAAO,MAAM;AACtC,SAAO,WAAW,OAAO,WAAW,OAAO,UAAU,OAAO,UAAU;AACxE;AACA,IAAM,WAAW,SAAO;AACtB,QAAM,QAAQ,IAAI;AAClB,QAAM,SAAS,IAAI;AACnB,QAAM,WAAW,KAAK,IAAI,OAAO,MAAM;AACvC,QAAM,UAAU,KAAK,IAAI,OAAO,MAAM;AACtC,SAAO,OAAO,GAAG,KAAK,gBAAgB,GAAG,KAAK,WAAW,OAAO,WAAW,OAAO,UAAU,OAAO,UAAU;AAC/G;AACA,IAAM,WAAW,SAAO,WAAW,KAAK,sBAAsB;AAC9D,IAAM,YAAY,SAAO,CAAC,SAAS,GAAG;AACtC,IAAM,WAAW,SAAO,UAAU,GAAG,KAAK,kBAAkB,GAAG;AAC/D,IAAM,YAAY,SAAO,CAAC,EAAE,IAAI,SAAS,KAAK,IAAI,UAAU,KAAK,IAAI,UAAU;AAC/E,IAAM,oBAAoB,SAAO;AAC/B,QAAM,YAAY,IAAI,WAAW;AAEjC,SAAO,CAAC,GAAG,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,cAAc,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,qBAAqB,CAAC,CAAC,UAAU,iBAAiB;AAC7M;AACA,IAAM,aAAa,SAAO,cAAc,KAAK,WAAW;AACxD,IAAM,QAAQ,SAAO;AACnB,MAAI;AACJ,SAAO,CAAC,IAAI,KAAK,IAAI,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,4BAA4B,EAAE,YAAY,IAAI,UAAU;AAC7I;AACA,IAAM,gBAAgB,CAAC,KAAK,SAAS,KAAK,KAAK,IAAI,UAAU,SAAS;AACtE,IAAM,aAAa,CAAC,KAAK,UAAU;AACjC,MAAI;AACJ,UAAQ,KAAK,IAAI,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,KAAK,EAAE;AACxF;AACA,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AACV;AAGA,IAAI;AACJ,IAAM,aAAa,SAAO;AACxB,SAAO,OAAO,QAAQ,GAAG,KAAK;AAChC;AACA,IAAM,aAAa,CAAC,aAAa,CAAC,MAAM;AACtC,MAAI,OAAO,WAAW,aAAa;AACjC;AAAA,EACF;AACA,QAAM,MAAM,OAAO;AACnB,QAAM,MAAM;AACZ,QAAM,QAAQ,IAAI,QAAQ,IAAI,SAAS,CAAC;AAGxC,QAAM,YAAY,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG;AAAA,IACnH,eAAe;AAAA,EACjB,CAAC,GAAG,MAAM,MAAM,GAAG,cAAc,GAAG,CAAC,GAAG,UAAU;AAClD,SAAO,MAAM,SAAS;AACtB,MAAI,OAAO,WAAW,eAAe,GAAG;AACtC,eAAW,KAAK,SAAS;AAAA,EAC3B;AAEA,iBAAe,GAAG;AAIlB,QAAM,SAAS;AACf,QAAM,OAAO,cAAc,OAAO,IAAI,QAAQ,IAAI,gBAAgB,aAAa,MAAM,MAAM,WAAW,KAAK,KAAK,IAAI,QAAQ,KAAK;AACjI,SAAO,IAAI,QAAQ,WAAW;AAC9B,MAAI,gBAAgB,aAAa,QAAQ,WAAW;AACpD,MAAI,gBAAgB,UAAU,IAAI,WAAW;AAC7C,MAAI,OAAO,WAAW,UAAU,GAAG;AACjC,WAAO,IAAI,YAAY,KAAK;AAAA,EAC9B;AACA,QAAM,iBAAiB,SAAO;AAC5B,QAAI;AACJ,YAAQ,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,MAAM;AAAA,EACrF;AACA,QAAM,0BAA0B,aAAW,CAAC,OAAO,IAAI,EAAE,SAAS,OAAO;AACzE,UAAQ,SAAO;AACb,WAAO,KAAK;AACV,YAAM,UAAU,IAAI,QAAQ,IAAI,aAAa,MAAM;AACnD,UAAI,SAAS;AACX,YAAI,wBAAwB,OAAO,GAAG;AACpC,iBAAO;AAAA,QACT,WAAW,eAAe,GAAG,GAAG;AAC9B,0BAAgB,0BAA0B,UAAU,4BAA4B;AAAA,QAClF;AAAA,MACF;AACA,YAAM,IAAI;AAAA,IACZ;AACA,WAAO;AAAA,EACT,CAAC;AACH;", "names": [], "x_google_ignoreList": [0]}
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8"/>
  <title>Alerto</title>

  <base href="/"/>

  <meta name="color-scheme" content="light dark"/>
  <meta name="viewport" content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
  <meta name="format-detection" content="telephone=no"/>
  <meta name="msapplication-tap-highlight" content="no"/>
  <meta http-equiv="Content-Security-Policy" content="default-src * 'self' data: gap: https://ssl.gstatic.com 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; media-src *; connect-src * 'self' http://***************:3000 http://localhost:3000"/>

  <link rel="icon" type="image/png" href="assets/icon/favicon.png"/>

  <!-- add to homescreen for ios -->
  <meta name="mobile-web-app-capable" content="yes"/>
  <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
  <meta name="apple-mobile-web-app-capable" content="yes"/>
  <meta name="apple-mobile-web-app-title" content="Alerto"/>
<link rel="stylesheet" href="styles.css"></head>

<body>
  <app-root></app-root>


  <!-- Add styles for notification toasts -->
  <style>
    .notification-toast {
      --background: #ffffff;
      --color: #333333;
      --border-radius: 8px;
      --box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    .notification-toast.low {
      --background: #e8f5e9;
      --border-color: #4caf50;
    }

    .notification-toast.medium {
      --background: #fff8e1;
      --border-color: #ffc107;
    }

    .notification-toast.high {
      --background: #ffebee;
      --border-color: #f44336;
    }
  </style>
<link rel="modulepreload" href="chunk-RDFT5QPW.js"><link rel="modulepreload" href="chunk-I7MI46CM.js"><link rel="modulepreload" href="chunk-TAZAZ6IP.js"><link rel="modulepreload" href="chunk-NS3G4TP7.js"><link rel="modulepreload" href="chunk-VI7H4G7Y.js"><link rel="modulepreload" href="chunk-S72IRO7V.js"><link rel="modulepreload" href="chunk-C6K4MQWC.js"><link rel="modulepreload" href="chunk-7YVUC4YJ.js"><link rel="modulepreload" href="chunk-6NM256MY.js"><link rel="modulepreload" href="chunk-JK35ET3X.js"><script src="polyfills.js" type="module"></script><script src="main.js" type="module"></script></body>

</html>

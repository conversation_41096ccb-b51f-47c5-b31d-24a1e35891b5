{"version": 3, "sources": ["src/app/components/offline-banner.component.ts"], "sourcesContent": ["\n    .offline-banner {\n      padding: 12px 16px;\n      margin: 8px 16px;\n      border-radius: 8px;\n      transition: all 0.3s ease;\n    }\n\n    .offline-banner.online {\n      background: linear-gradient(135deg, #28a745, #20c997);\n      color: white;\n    }\n\n    .offline-banner.offline {\n      background: linear-gradient(135deg, #dc3545, #fd7e14);\n      color: white;\n    }\n\n    .offline-banner.preparing {\n      background: linear-gradient(135deg, #007bff, #6610f2);\n      color: white;\n    }\n\n    .offline-banner.warning {\n      background: linear-gradient(135deg, #ffc107, #fd7e14);\n      color: #212529;\n    }\n\n    .banner-content {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .banner-icon {\n      font-size: 24px;\n      flex-shrink: 0;\n    }\n\n    .banner-text {\n      flex: 1;\n    }\n\n    .banner-title {\n      font-weight: 600;\n      font-size: 14px;\n      margin-bottom: 2px;\n    }\n\n    .banner-subtitle {\n      font-size: 12px;\n      opacity: 0.9;\n    }\n\n    .banner-actions {\n      display: flex;\n      gap: 8px;\n    }\n\n    .preparation-progress {\n      margin-top: 12px;\n    }\n\n    .progress-text {\n      font-size: 12px;\n      text-align: center;\n      margin-top: 4px;\n      opacity: 0.9;\n    }\n\n    ion-progress-bar {\n      height: 4px;\n      border-radius: 2px;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA,KAAA;AACA,UAAA,IAAA;AACA,iBAAA;AACA,cAAA,IAAA,KAAA;;AAGF,CAPA,cAOA,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,SAAA;;AAGF,CAZA,cAYA,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,SAAA;;AAGF,CAjBA,cAiBA,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,SAAA;;AAGF,CAtBA,cAsBA,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;;AAGF,CAAA;AACE,QAAA;;AAGF,CAAA;AACE,eAAA;AACA,aAAA;AACA,iBAAA;;AAGF,CAAA;AACE,aAAA;AACA,WAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,aAAA;AACA,cAAA;AACA,cAAA;AACA,WAAA;;AAGF;AACE,UAAA;AACA,iBAAA;;", "names": []}
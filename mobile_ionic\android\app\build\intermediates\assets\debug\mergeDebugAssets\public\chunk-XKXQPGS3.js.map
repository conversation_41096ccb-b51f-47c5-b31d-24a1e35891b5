{"version": 3, "sources": ["src/app/services/auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from '../../environments/environment';\r\nimport { EnvironmentSwitcherService } from './environment-switcher.service';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class AuthService {\r\n  private get apiUrl(): string {\r\n    // Use dynamic API URL if available, fallback to environment\r\n    const dynamicUrl = this.envSwitcher?.getCurrentApiUrl();\r\n    const baseUrl = dynamicUrl || environment.apiUrl;\r\n    return `${baseUrl}/auth`;\r\n  }\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private envSwitcher: EnvironmentSwitcherService\r\n  ) {\r\n    console.log('Auth Service initialized');\r\n    console.log('Environment API URL:', environment.apiUrl);\r\n    console.log('Dynamic API URL:', this.envSwitcher.getCurrentApiUrl());\r\n    console.log('Final API URL:', this.apiUrl);\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      'Accept': 'application/json',\r\n      'X-Requested-With': 'XMLHttpRequest'\r\n    });\r\n  }\r\n\r\n  login(credentials: { email: string, password: string }): Observable<any> {\r\n    console.log('🔐 Making login request to:', `${this.apiUrl}/auth/login`);\r\n    console.log('📧 Credentials:', { email: credentials.email, password: '***' });\r\n\r\n    return this.http.post(`${this.apiUrl}/auth/login`, credentials, {\r\n      headers: this.getHeaders()\r\n    });\r\n  }\r\n\r\n  register(data: { full_name: string, email: string, password: string, password_confirmation?: string }): Observable<any> {\r\n    console.log('📝 Making registration request to:', `${this.apiUrl}/auth/signup`);\r\n    console.log('👤 Registration data:', { ...data, password: '***', password_confirmation: '***' });\r\n\r\n    return this.http.post(`${this.apiUrl}/auth/signup`, data, {\r\n      headers: this.getHeaders()\r\n    });\r\n  }\r\n\r\n  setToken(token: string) {\r\n    // Store token consistently as 'token' to match what's used in authGuard\r\n    localStorage.setItem('token', token);\r\n    console.log('🔑 Token stored successfully');\r\n  }\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAOM,IAAO,cAAP,MAAO,aAAW;EACtB,IAAY,SAAM;AAEhB,UAAM,aAAa,KAAK,aAAa,iBAAgB;AACrD,UAAM,UAAU,cAAc,YAAY;AAC1C,WAAO,GAAG,OAAO;EACnB;EAEA,YACU,MACA,aAAuC;AADvC,SAAA,OAAA;AACA,SAAA,cAAA;AAER,YAAQ,IAAI,0BAA0B;AACtC,YAAQ,IAAI,wBAAwB,YAAY,MAAM;AACtD,YAAQ,IAAI,oBAAoB,KAAK,YAAY,iBAAgB,CAAE;AACnE,YAAQ,IAAI,kBAAkB,KAAK,MAAM;EAC3C;EAEQ,aAAU;AAChB,WAAO,IAAI,YAAY;MACrB,gBAAgB;MAChB,UAAU;MACV,oBAAoB;KACrB;EACH;EAEA,MAAM,aAAgD;AACpD,YAAQ,IAAI,sCAA+B,GAAG,KAAK,MAAM,aAAa;AACtE,YAAQ,IAAI,0BAAmB,EAAE,OAAO,YAAY,OAAO,UAAU,MAAK,CAAE;AAE5E,WAAO,KAAK,KAAK,KAAK,GAAG,KAAK,MAAM,eAAe,aAAa;MAC9D,SAAS,KAAK,WAAU;KACzB;EACH;EAEA,SAAS,MAA4F;AACnG,YAAQ,IAAI,6CAAsC,GAAG,KAAK,MAAM,cAAc;AAC9E,YAAQ,IAAI,gCAAyB,iCAAK,OAAL,EAAW,UAAU,OAAO,uBAAuB,MAAK,EAAE;AAE/F,WAAO,KAAK,KAAK,KAAK,GAAG,KAAK,MAAM,gBAAgB,MAAM;MACxD,SAAS,KAAK,WAAU;KACzB;EACH;EAEA,SAAS,OAAa;AAEpB,iBAAa,QAAQ,SAAS,KAAK;AACnC,YAAQ,IAAI,qCAA8B;EAC5C;;;uCAhDW,cAAW,mBAAA,UAAA,GAAA,mBAAA,0BAAA,CAAA;IAAA;EAAA;;4EAAX,cAAW,SAAX,aAAW,WAAA,YADE,OAAM,CAAA;EAAA;;;sEACnB,aAAW,CAAA;UADvB;WAAW,EAAE,YAAY,OAAM,CAAE;;;", "names": []}
{"version": 3, "sources": ["src/app/pages/notification-test/notification-test.page.ts", "src/app/pages/notification-test/notification-test.page.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { IonicModule, AlertController, LoadingController } from '@ionic/angular';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { FcmService, FCMNotification } from '../../services/fcm.service';\r\n\r\n@Component({\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule],\r\n  selector: 'app-notification-test',\r\n  templateUrl: './notification-test.page.html',\r\n  styleUrls: ['./notification-test.page.scss']\r\n})\r\nexport class NotificationTestPage {\r\n  testNotifications = [\r\n    {\r\n      title: 'EARTHQUAKE ALERT',\r\n      body: 'Magnitude 7.2 earthquake detected. Evacuate to nearest safe area immediately.',\r\n      category: 'earthquake',\r\n      severity: 'high'\r\n    },\r\n    {\r\n      title: 'FLOOD WARNING',\r\n      body: 'Flash flood warning in your area. Move to higher ground immediately.',\r\n      category: 'flood',\r\n      severity: 'high'\r\n    },\r\n    {\r\n      title: 'TYPHOON ALERT',\r\n      body: 'Typhoon approaching. Seek shelter in a sturdy building.',\r\n      category: 'typhoon',\r\n      severity: 'medium'\r\n    },\r\n    {\r\n      title: 'FIRE EMERGENCY',\r\n      body: 'Fire reported in your vicinity. Evacuate the area immediately.',\r\n      category: 'fire',\r\n      severity: 'high'\r\n    },\r\n    {\r\n      title: 'GENERAL ALERT',\r\n      body: 'Emergency situation detected. Follow local authorities instructions.',\r\n      category: 'general',\r\n      severity: 'medium'\r\n    }\r\n  ];\r\n\r\n  constructor(\r\n    private fcmService: FcmService,\r\n    private alertController: AlertController,\r\n    private loadingController: LoadingController,\r\n    private router: Router\r\n  ) {}\r\n\r\n  async testForegroundNotification(notification: any) {\r\n    const loading = await this.loadingController.create({\r\n      message: 'Testing foreground notification...',\r\n      duration: 3000\r\n    });\r\n    await loading.present();\r\n\r\n    try {\r\n      // Create a mock FCM notification\r\n      const mockNotification: FCMNotification = {\r\n        title: notification.title,\r\n        body: notification.body,\r\n        category: notification.category,\r\n        severity: notification.severity,\r\n        wasTapped: false,\r\n        data: {\r\n          category: notification.category,\r\n          severity: notification.severity\r\n        }\r\n      };\r\n\r\n      // Simulate foreground notification\r\n      await this.fcmService.simulateForegroundNotification(mockNotification);\r\n      \r\n      await loading.dismiss();\r\n      \r\n      const alert = await this.alertController.create({\r\n        header: 'Test Complete',\r\n        message: 'Foreground notification test completed. Check if the emergency modal appeared.',\r\n        buttons: ['OK']\r\n      });\r\n      await alert.present();\r\n\r\n    } catch (error) {\r\n      await loading.dismiss();\r\n      \r\n      const alert = await this.alertController.create({\r\n        header: 'Test Failed',\r\n        message: `Error testing notification: ${error}`,\r\n        buttons: ['OK']\r\n      });\r\n      await alert.present();\r\n    }\r\n  }\r\n\r\n  async testBackgroundNotification(notification: any) {\r\n    const loading = await this.loadingController.create({\r\n      message: 'Testing background notification...',\r\n      duration: 3000\r\n    });\r\n    await loading.present();\r\n\r\n    try {\r\n      // Create a mock FCM notification\r\n      const mockNotification: FCMNotification = {\r\n        title: notification.title,\r\n        body: notification.body,\r\n        category: notification.category,\r\n        severity: notification.severity,\r\n        wasTapped: true, // Simulate background tap\r\n        data: {\r\n          category: notification.category,\r\n          severity: notification.severity\r\n        }\r\n      };\r\n\r\n      // Simulate background notification tap\r\n      await this.fcmService.simulateBackgroundNotification(mockNotification);\r\n      \r\n      await loading.dismiss();\r\n      \r\n      const alert = await this.alertController.create({\r\n        header: 'Test Complete',\r\n        message: 'Background notification test completed. Check if the emergency modal appeared.',\r\n        buttons: ['OK']\r\n      });\r\n      await alert.present();\r\n\r\n    } catch (error) {\r\n      await loading.dismiss();\r\n      \r\n      const alert = await this.alertController.create({\r\n        header: 'Test Failed',\r\n        message: `Error testing notification: ${error}`,\r\n        buttons: ['OK']\r\n      });\r\n      await alert.present();\r\n    }\r\n  }\r\n\r\n  async testAllNotifications() {\r\n    const loading = await this.loadingController.create({\r\n      message: 'Testing all notification types...',\r\n      duration: 15000\r\n    });\r\n    await loading.present();\r\n\r\n    try {\r\n      for (let i = 0; i < this.testNotifications.length; i++) {\r\n        const notification = this.testNotifications[i];\r\n        \r\n        // Wait between notifications\r\n        if (i > 0) {\r\n          await new Promise(resolve => setTimeout(resolve, 3000));\r\n        }\r\n\r\n        const mockNotification: FCMNotification = {\r\n          title: notification.title,\r\n          body: notification.body,\r\n          category: notification.category,\r\n          severity: notification.severity,\r\n          wasTapped: false,\r\n          data: {\r\n            category: notification.category,\r\n            severity: notification.severity\r\n          }\r\n        };\r\n\r\n        await this.fcmService.simulateForegroundNotification(mockNotification);\r\n      }\r\n      \r\n      await loading.dismiss();\r\n      \r\n      const alert = await this.alertController.create({\r\n        header: 'All Tests Complete',\r\n        message: 'All notification types have been tested. Check if emergency modals appeared for each.',\r\n        buttons: ['OK']\r\n      });\r\n      await alert.present();\r\n\r\n    } catch (error) {\r\n      await loading.dismiss();\r\n      \r\n      const alert = await this.alertController.create({\r\n        header: 'Test Failed',\r\n        message: `Error during batch testing: ${error}`,\r\n        buttons: ['OK']\r\n      });\r\n      await alert.present();\r\n    }\r\n  }\r\n\r\n  getDisasterIcon(category: string): string {\r\n    switch (category.toLowerCase()) {\r\n      case 'earthquake': return 'warning-outline';\r\n      case 'flood': return 'water-outline';\r\n      case 'typhoon': return 'cloudy-outline';\r\n      case 'fire': return 'flame-outline';\r\n      default: return 'notifications-outline';\r\n    }\r\n  }\r\n\r\n  getDisasterColor(category: string): string {\r\n    switch (category.toLowerCase()) {\r\n      case 'earthquake': return 'warning';\r\n      case 'flood': return 'primary';\r\n      case 'typhoon': return 'success';\r\n      case 'fire': return 'danger';\r\n      default: return 'medium';\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigate(['/tabs/profile']);\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar>\r\n    <ion-buttons slot=\"start\">\r\n      <ion-button (click)=\"goBack()\">\r\n        <ion-icon name=\"chevron-back-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n    <ion-title>Emergency Notification Test</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\" class=\"ion-padding\">\r\n  <div class=\"test-container\">\r\n    \r\n    <!-- Info Card -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>🚨 Emergency Notification Testing</ion-card-title>\r\n        <ion-card-subtitle>Test emergency modals and notifications</ion-card-subtitle>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <p>This page allows you to test emergency notifications to ensure they work properly when the app is active.</p>\r\n        <p><strong>What to expect:</strong></p>\r\n        <ul>\r\n          <li>Emergency modal should appear immediately</li>\r\n          <li>Device should vibrate</li>\r\n          <li>Modal should have disaster-specific colors</li>\r\n          <li>App should NOT crash or close</li>\r\n        </ul>\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Quick Test Button -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Quick Test</ion-card-title>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <ion-button \r\n          expand=\"block\" \r\n          color=\"danger\" \r\n          (click)=\"testAllNotifications()\">\r\n          <ion-icon name=\"flash-outline\" slot=\"start\"></ion-icon>\r\n          Test All Notification Types\r\n        </ion-button>\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Individual Tests -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Individual Tests</ion-card-title>\r\n        <ion-card-subtitle>Test specific disaster types</ion-card-subtitle>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        \r\n        <ion-list>\r\n          <ion-item \r\n            *ngFor=\"let notification of testNotifications\" \r\n            class=\"notification-item\">\r\n            \r\n            <ion-icon \r\n              [name]=\"getDisasterIcon(notification.category)\" \r\n              [color]=\"getDisasterColor(notification.category)\"\r\n              slot=\"start\">\r\n            </ion-icon>\r\n            \r\n            <ion-label>\r\n              <h2>{{ notification.title }}</h2>\r\n              <p>{{ notification.body }}</p>\r\n              <p class=\"category-badge\">\r\n                <ion-badge [color]=\"getDisasterColor(notification.category)\">\r\n                  {{ notification.category.toUpperCase() }}\r\n                </ion-badge>\r\n                <ion-badge [color]=\"notification.severity === 'high' ? 'danger' : 'warning'\">\r\n                  {{ notification.severity.toUpperCase() }}\r\n                </ion-badge>\r\n              </p>\r\n            </ion-label>\r\n\r\n            <ion-buttons slot=\"end\">\r\n              <ion-button \r\n                fill=\"clear\" \r\n                [color]=\"getDisasterColor(notification.category)\"\r\n                (click)=\"testForegroundNotification(notification)\">\r\n                <ion-icon name=\"play-outline\"></ion-icon>\r\n              </ion-button>\r\n            </ion-buttons>\r\n          </ion-item>\r\n        </ion-list>\r\n\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Test Types -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Test Types</ion-card-title>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        \r\n        <ion-list>\r\n          <ion-item>\r\n            <ion-icon name=\"phone-portrait-outline\" slot=\"start\" color=\"primary\"></ion-icon>\r\n            <ion-label>\r\n              <h3>Foreground Test</h3>\r\n              <p>Tests notifications when app is active and visible</p>\r\n            </ion-label>\r\n          </ion-item>\r\n          \r\n          <ion-item>\r\n            <ion-icon name=\"moon-outline\" slot=\"start\" color=\"secondary\"></ion-icon>\r\n            <ion-label>\r\n              <h3>Background Test</h3>\r\n              <p>Simulates notifications when app was in background</p>\r\n            </ion-label>\r\n          </ion-item>\r\n        </ion-list>\r\n\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Troubleshooting -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Troubleshooting</ion-card-title>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <ion-list>\r\n          <ion-item>\r\n            <ion-icon name=\"checkmark-circle-outline\" slot=\"start\" color=\"success\"></ion-icon>\r\n            <ion-label>\r\n              <h3>✅ Working Correctly</h3>\r\n              <p>Modal appears, device vibrates, app stays open</p>\r\n            </ion-label>\r\n          </ion-item>\r\n          \r\n          <ion-item>\r\n            <ion-icon name=\"close-circle-outline\" slot=\"start\" color=\"danger\"></ion-icon>\r\n            <ion-label>\r\n              <h3>❌ App Crashes</h3>\r\n              <p>Check console logs, modal creation might be failing</p>\r\n            </ion-label>\r\n          </ion-item>\r\n          \r\n          <ion-item>\r\n            <ion-icon name=\"warning-outline\" slot=\"start\" color=\"warning\"></ion-icon>\r\n            <ion-label>\r\n              <h3>⚠️ No Modal</h3>\r\n              <p>Check if fallback toast appears instead</p>\r\n            </ion-label>\r\n          </ion-item>\r\n        </ion-list>\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyDU,IAAA,yBAAA,GAAA,YAAA,EAAA;AAIE,IAAA,oBAAA,GAAA,YAAA,EAAA;AAMA,IAAA,yBAAA,GAAA,WAAA,EAAW,GAAA,IAAA;AACL,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAAuB,IAAA,uBAAA;AAC1B,IAAA,yBAAA,GAAA,KAAA,EAAA,EAA0B,GAAA,aAAA,EAAA;AAEtB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,aAAA,EAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAY,EACV;AAGN,IAAA,yBAAA,IAAA,eAAA,EAAA,EAAwB,IAAA,cAAA,EAAA;AAIpB,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,YAAA,kBAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,2BAAA,eAAA,CAAwC;IAAA,CAAA;AACjD,IAAA,oBAAA,IAAA,YAAA,EAAA;AACF,IAAA,uBAAA,EAAa,EACD;;;;;AAzBZ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,gBAAA,gBAAA,QAAA,CAAA,EAA+C,SAAA,OAAA,iBAAA,gBAAA,QAAA,CAAA;AAM3C,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,gBAAA,KAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,gBAAA,IAAA;AAEU,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,iBAAA,gBAAA,QAAA,CAAA;AACT,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,gBAAA,SAAA,YAAA,GAAA,GAAA;AAES,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,gBAAA,aAAA,SAAA,WAAA,SAAA;AACT,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,gBAAA,SAAA,YAAA,GAAA,GAAA;AAQF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,iBAAA,gBAAA,QAAA,CAAA;;;ADrEV,IAAO,uBAAP,MAAO,sBAAoB;EAkC/B,YACU,YACA,iBACA,mBACA,QAAc;AAHd,SAAA,aAAA;AACA,SAAA,kBAAA;AACA,SAAA,oBAAA;AACA,SAAA,SAAA;AArCV,SAAA,oBAAoB;MAClB;QACE,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;;MAEZ;QACE,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;;MAEZ;QACE,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;;MAEZ;QACE,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;;MAEZ;QACE,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;;;EASX;EAEG,2BAA2B,cAAiB;;AAChD,YAAM,UAAU,MAAM,KAAK,kBAAkB,OAAO;QAClD,SAAS;QACT,UAAU;OACX;AACD,YAAM,QAAQ,QAAO;AAErB,UAAI;AAEF,cAAM,mBAAoC;UACxC,OAAO,aAAa;UACpB,MAAM,aAAa;UACnB,UAAU,aAAa;UACvB,UAAU,aAAa;UACvB,WAAW;UACX,MAAM;YACJ,UAAU,aAAa;YACvB,UAAU,aAAa;;;AAK3B,cAAM,KAAK,WAAW,+BAA+B,gBAAgB;AAErE,cAAM,QAAQ,QAAO;AAErB,cAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;UAC9C,QAAQ;UACR,SAAS;UACT,SAAS,CAAC,IAAI;SACf;AACD,cAAM,MAAM,QAAO;MAErB,SAAS,OAAO;AACd,cAAM,QAAQ,QAAO;AAErB,cAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;UAC9C,QAAQ;UACR,SAAS,+BAA+B,KAAK;UAC7C,SAAS,CAAC,IAAI;SACf;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;EAEM,2BAA2B,cAAiB;;AAChD,YAAM,UAAU,MAAM,KAAK,kBAAkB,OAAO;QAClD,SAAS;QACT,UAAU;OACX;AACD,YAAM,QAAQ,QAAO;AAErB,UAAI;AAEF,cAAM,mBAAoC;UACxC,OAAO,aAAa;UACpB,MAAM,aAAa;UACnB,UAAU,aAAa;UACvB,UAAU,aAAa;UACvB,WAAW;;UACX,MAAM;YACJ,UAAU,aAAa;YACvB,UAAU,aAAa;;;AAK3B,cAAM,KAAK,WAAW,+BAA+B,gBAAgB;AAErE,cAAM,QAAQ,QAAO;AAErB,cAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;UAC9C,QAAQ;UACR,SAAS;UACT,SAAS,CAAC,IAAI;SACf;AACD,cAAM,MAAM,QAAO;MAErB,SAAS,OAAO;AACd,cAAM,QAAQ,QAAO;AAErB,cAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;UAC9C,QAAQ;UACR,SAAS,+BAA+B,KAAK;UAC7C,SAAS,CAAC,IAAI;SACf;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;EAEM,uBAAoB;;AACxB,YAAM,UAAU,MAAM,KAAK,kBAAkB,OAAO;QAClD,SAAS;QACT,UAAU;OACX;AACD,YAAM,QAAQ,QAAO;AAErB,UAAI;AACF,iBAAS,IAAI,GAAG,IAAI,KAAK,kBAAkB,QAAQ,KAAK;AACtD,gBAAM,eAAe,KAAK,kBAAkB,CAAC;AAG7C,cAAI,IAAI,GAAG;AACT,kBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;UACxD;AAEA,gBAAM,mBAAoC;YACxC,OAAO,aAAa;YACpB,MAAM,aAAa;YACnB,UAAU,aAAa;YACvB,UAAU,aAAa;YACvB,WAAW;YACX,MAAM;cACJ,UAAU,aAAa;cACvB,UAAU,aAAa;;;AAI3B,gBAAM,KAAK,WAAW,+BAA+B,gBAAgB;QACvE;AAEA,cAAM,QAAQ,QAAO;AAErB,cAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;UAC9C,QAAQ;UACR,SAAS;UACT,SAAS,CAAC,IAAI;SACf;AACD,cAAM,MAAM,QAAO;MAErB,SAAS,OAAO;AACd,cAAM,QAAQ,QAAO;AAErB,cAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;UAC9C,QAAQ;UACR,SAAS,+BAA+B,KAAK;UAC7C,SAAS,CAAC,IAAI;SACf;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;EAEA,gBAAgB,UAAgB;AAC9B,YAAQ,SAAS,YAAW,GAAI;MAC9B,KAAK;AAAc,eAAO;MAC1B,KAAK;AAAS,eAAO;MACrB,KAAK;AAAW,eAAO;MACvB,KAAK;AAAQ,eAAO;MACpB;AAAS,eAAO;IAClB;EACF;EAEA,iBAAiB,UAAgB;AAC/B,YAAQ,SAAS,YAAW,GAAI;MAC9B,KAAK;AAAc,eAAO;MAC1B,KAAK;AAAS,eAAO;MACrB,KAAK;AAAW,eAAO;MACvB,KAAK;AAAQ,eAAO;MACpB;AAAS,eAAO;IAClB;EACF;EAEA,SAAM;AACJ,SAAK,OAAO,SAAS,CAAC,eAAe,CAAC;EACxC;;;uCA7MW,uBAAoB,4BAAA,UAAA,GAAA,4BAAA,eAAA,GAAA,4BAAA,iBAAA,GAAA,4BAAA,MAAA,CAAA;IAAA;EAAA;;yEAApB,uBAAoB,WAAA,CAAA,CAAA,uBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,OAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,sBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,UAAA,SAAA,SAAA,UAAA,GAAA,OAAA,GAAA,CAAA,QAAA,iBAAA,QAAA,OAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,QAAA,0BAAA,QAAA,SAAA,SAAA,SAAA,GAAA,CAAA,QAAA,gBAAA,QAAA,SAAA,SAAA,WAAA,GAAA,CAAA,QAAA,4BAAA,QAAA,SAAA,SAAA,SAAA,GAAA,CAAA,QAAA,wBAAA,QAAA,SAAA,SAAA,QAAA,GAAA,CAAA,QAAA,mBAAA,QAAA,SAAA,SAAA,SAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,QAAA,SAAA,GAAA,QAAA,OAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,KAAA,GAAA,CAAA,QAAA,SAAA,GAAA,SAAA,OAAA,GAAA,CAAA,QAAA,cAAA,CAAA,GAAA,UAAA,SAAA,8BAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACdjC,QAAA,yBAAA,GAAA,cAAA,CAAA,EAAiC,GAAA,aAAA,EAClB,GAAA,eAAA,CAAA,EACe,GAAA,cAAA,CAAA;AACZ,QAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,iBAAS,IAAA,OAAA;QAAQ,CAAA;AAC3B,QAAA,oBAAA,GAAA,YAAA,CAAA;AACF,QAAA,uBAAA,EAAa;AAEf,QAAA,yBAAA,GAAA,WAAA;AAAW,QAAA,iBAAA,GAAA,6BAAA;AAA2B,QAAA,uBAAA,EAAY,EACtC;AAGhB,QAAA,yBAAA,GAAA,eAAA,CAAA,EAAqD,GAAA,OAAA,CAAA,EACvB,GAAA,UAAA,EAGhB,IAAA,iBAAA,EACS,IAAA,gBAAA;AACC,QAAA,iBAAA,IAAA,0CAAA;AAAiC,QAAA,uBAAA;AACjD,QAAA,yBAAA,IAAA,mBAAA;AAAmB,QAAA,iBAAA,IAAA,yCAAA;AAAuC,QAAA,uBAAA,EAAoB;AAEhF,QAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,GAAA;AACb,QAAA,iBAAA,IAAA,2GAAA;AAAyG,QAAA,uBAAA;AAC5G,QAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,QAAA,iBAAA,IAAA,iBAAA;AAAe,QAAA,uBAAA,EAAS;AACnC,QAAA,yBAAA,IAAA,IAAA,EAAI,IAAA,IAAA;AACE,QAAA,iBAAA,IAAA,2CAAA;AAAyC,QAAA,uBAAA;AAC7C,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,uBAAA;AAAqB,QAAA,uBAAA;AACzB,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,4CAAA;AAA0C,QAAA,uBAAA;AAC9C,QAAA,yBAAA,IAAA,IAAA;AAAI,QAAA,iBAAA,IAAA,+BAAA;AAA6B,QAAA,uBAAA,EAAK,EACnC,EACY;AAIrB,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,iBAAA,EACS,IAAA,gBAAA;AACC,QAAA,iBAAA,IAAA,YAAA;AAAU,QAAA,uBAAA,EAAiB;AAE7C,QAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,cAAA,CAAA;AAId,QAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,iBAAS,IAAA,qBAAA;QAAsB,CAAA;AAC/B,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,iBAAA,IAAA,+BAAA;AACF,QAAA,uBAAA,EAAa,EACI;AAIrB,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,iBAAA,EACS,IAAA,gBAAA;AACC,QAAA,iBAAA,IAAA,kBAAA;AAAgB,QAAA,uBAAA;AAChC,QAAA,yBAAA,IAAA,mBAAA;AAAmB,QAAA,iBAAA,IAAA,8BAAA;AAA4B,QAAA,uBAAA,EAAoB;AAErE,QAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA;AAGd,QAAA,qBAAA,IAAA,2CAAA,IAAA,GAAA,YAAA,CAAA;AAgCF,QAAA,uBAAA,EAAW,EAEM;AAIrB,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,iBAAA,EACS,IAAA,gBAAA;AACC,QAAA,iBAAA,IAAA,YAAA;AAAU,QAAA,uBAAA,EAAiB;AAE7C,QAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA,EAEN,IAAA,UAAA;AAEN,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,QAAA,iBAAA,IAAA,iBAAA;AAAe,QAAA,uBAAA;AACnB,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,oDAAA;AAAkD,QAAA,uBAAA,EAAI,EAC/C;AAGd,QAAA,yBAAA,IAAA,UAAA;AACE,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,QAAA,iBAAA,IAAA,iBAAA;AAAe,QAAA,uBAAA;AACnB,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,oDAAA;AAAkD,QAAA,uBAAA,EAAI,EAC/C,EACH,EACF,EAEM;AAIrB,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,iBAAA,EACS,IAAA,gBAAA;AACC,QAAA,iBAAA,IAAA,iBAAA;AAAe,QAAA,uBAAA,EAAiB;AAElD,QAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA,EACN,IAAA,UAAA;AAEN,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,QAAA,iBAAA,IAAA,0BAAA;AAAmB,QAAA,uBAAA;AACvB,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,gDAAA;AAA8C,QAAA,uBAAA,EAAI,EAC3C;AAGd,QAAA,yBAAA,IAAA,UAAA;AACE,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,QAAA,iBAAA,IAAA,oBAAA;AAAa,QAAA,uBAAA;AACjB,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,qDAAA;AAAmD,QAAA,uBAAA,EAAI,EAChD;AAGd,QAAA,yBAAA,IAAA,UAAA;AACE,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,QAAA,iBAAA,IAAA,uBAAA;AAAW,QAAA,uBAAA;AACf,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,yCAAA;AAAuC,QAAA,uBAAA,EAAI,EACpC,EACH,EACF,EACM,EACV,EAEP;;;AA5JI,QAAA,qBAAA,eAAA,IAAA;AAWC,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,cAAA,IAAA;AA+CwB,QAAA,oBAAA,EAAA;AAAA,QAAA,qBAAA,WAAA,IAAA,iBAAA;;sBDjDzB,aAAW,UAAA,WAAA,YAAA,SAAA,gBAAA,eAAA,iBAAA,cAAA,YAAA,WAAA,SAAA,SAAA,UAAA,SAAA,UAAA,YAAE,cAAY,SAAE,WAAW,GAAA,QAAA,CAAA,08BAAA,EAAA,CAAA;EAAA;;;sEAKrC,sBAAoB,CAAA;UAPhC;yBACa,MAAI,SACP,CAAC,aAAa,cAAc,WAAW,GAAC,UACvC,yBAAuB,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,0zBAAA,EAAA,CAAA;;;;6EAItB,sBAAoB,EAAA,WAAA,wBAAA,UAAA,6DAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
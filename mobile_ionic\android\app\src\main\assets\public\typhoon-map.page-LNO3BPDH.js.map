{"version": 3, "sources": ["src/app/pages/disaster-maps/typhoon-map.page.ts", "src/app/pages/disaster-maps/typhoon-map.page.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IonicModule, LoadingController, ToastController, AlertController } from '@ionic/angular';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Geolocation } from '@capacitor/geolocation';\r\nimport * as L from 'leaflet';\r\nimport { MapboxRoutingService } from '../../services/mapbox-routing.service';\r\nimport { OfflineStorageService } from '../../services/offline-storage.service';\r\n\r\ninterface EvacuationCenter {\r\n  id: number;\r\n  name: string;\r\n  address: string;\r\n  latitude: number;\r\n  longitude: number;\r\n  capacity?: number;\r\n  status?: string;\r\n  disaster_type?: string;\r\n  contact?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-typhoon-map',\r\n  templateUrl: './typhoon-map.page.html',\r\n  styleUrls: ['./typhoon-map.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule]\r\n})\r\nexport class TyphoonMapPage implements OnInit, AfterViewInit {\r\n  private map!: L.Map;\r\n  private userMarker: L.Marker<any> | null = null;\r\n  public evacuationCenters: EvacuationCenter[] = [];\r\n  public userLocation: { lat: number; lng: number } | null = null;\r\n\r\n  // Properties for highlighting new centers\r\n  public newCenterId: string | null = null;\r\n  public highlightCenter: boolean = false;\r\n  public centerLat: number | null = null;\r\n  public centerLng: number | null = null;\r\n\r\n  private loadingCtrl = inject(LoadingController);\r\n  private toastCtrl = inject(ToastController);\r\n  private alertCtrl = inject(AlertController);\r\n  private http = inject(HttpClient);\r\n  private router = inject(Router);\r\n  private route = inject(ActivatedRoute);\r\n  private mapboxRouting = inject(MapboxRoutingService);\r\n  private offlineStorage = inject(OfflineStorageService);\r\n\r\n  ngOnInit() {\r\n    console.log('🟢 TYPHOON MAP: Component initialized...');\r\n    // Don't initialize map here - wait for view to be ready\r\n\r\n    // Check for query parameters to highlight new center\r\n    this.route.queryParams.subscribe((params: any) => {\r\n      if (params['newCenterId']) {\r\n        this.newCenterId = params['newCenterId'];\r\n        this.highlightCenter = params['highlightCenter'] === 'true';\r\n        this.centerLat = params['centerLat'] ? parseFloat(params['centerLat']) : null;\r\n        this.centerLng = params['centerLng'] ? parseFloat(params['centerLng']) : null;\r\n        console.log('🟢 TYPHOON MAP: New center to highlight:', this.newCenterId);\r\n      }\r\n    });\r\n  }\r\n\r\n  async ngAfterViewInit() {\r\n    console.log('🟢 TYPHOON MAP: View initialized, loading map...');\r\n    // Small delay to ensure DOM is fully rendered\r\n    setTimeout(async () => {\r\n      await this.loadTyphoonMap();\r\n    }, 100);\r\n  }\r\n\r\n  async loadTyphoonMap() {\r\n    const loading = await this.loadingCtrl.create({\r\n      message: 'Loading typhoon evacuation centers...',\r\n      spinner: 'crescent'\r\n    });\r\n    await loading.present();\r\n\r\n    try {\r\n      // Get user location\r\n      const position = await Geolocation.getCurrentPosition({\r\n        enableHighAccuracy: true,\r\n        timeout: 20000\r\n      });\r\n\r\n      const userLat = position.coords.latitude;\r\n      const userLng = position.coords.longitude;\r\n\r\n      console.log(`🟢 TYPHOON MAP: User location [${userLat}, ${userLng}]`);\r\n\r\n      // Store user location\r\n      this.userLocation = { lat: userLat, lng: userLng };\r\n\r\n      // Initialize map\r\n      this.initializeMap(userLat, userLng);\r\n\r\n      // Load ONLY typhoon centers\r\n      await this.loadTyphoonCenters(userLat, userLng);\r\n\r\n      await loading.dismiss();\r\n\r\n      // Show success message\r\n      const toast = await this.toastCtrl.create({\r\n        message: `🟢 Showing ${this.evacuationCenters.length} typhoon evacuation centers`,\r\n        duration: 3000,\r\n        color: 'success',\r\n        position: 'top'\r\n      });\r\n      await toast.present();\r\n\r\n    } catch (error) {\r\n      await loading.dismiss();\r\n      console.error('🟢 TYPHOON MAP: Error loading map', error);\r\n\r\n      const alert = await this.alertCtrl.create({\r\n        header: 'Location Error',\r\n        message: 'Unable to get your location. Please enable GPS and try again.',\r\n        buttons: [\r\n          {\r\n            text: 'Retry',\r\n            handler: () => this.loadTyphoonMap()\r\n          },\r\n          {\r\n            text: 'Go Back',\r\n            handler: () => this.router.navigate(['/tabs/home'])\r\n          }\r\n        ]\r\n      });\r\n      await alert.present();\r\n    }\r\n  }\r\n\r\n  initializeMap(lat: number, lng: number) {\r\n    console.log(`🟢 TYPHOON MAP: Initializing map at [${lat}, ${lng}]`);\r\n\r\n    // Check if container exists\r\n    const container = document.getElementById('typhoon-map');\r\n    if (!container) {\r\n      console.error('🟢 TYPHOON MAP: Container #typhoon-map not found!');\r\n      throw new Error('Map container not found. Please ensure the view is properly loaded.');\r\n    }\r\n\r\n    if (this.map) {\r\n      this.map.remove();\r\n    }\r\n\r\n    this.map = L.map('typhoon-map').setView([lat, lng], 13);\r\n\r\n    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {\r\n      attribution: 'OpenStreetMap contributors'\r\n    }).addTo(this.map);\r\n\r\n    // Add user marker\r\n    this.userMarker = L.marker([lat, lng], {\r\n      icon: L.icon({\r\n        iconUrl: 'assets/Location.png',\r\n        iconSize: [30, 30],\r\n        iconAnchor: [15, 30]\r\n      })\r\n    }).addTo(this.map);\r\n\r\n    this.userMarker.bindPopup('📍 You are here!').openPopup();\r\n  }\r\n\r\n  async loadTyphoonCenters(userLat: number, userLng: number) {\r\n    try {\r\n      console.log('🟢 TYPHOON MAP: Fetching typhoon centers...');\r\n\r\n      let allCenters: EvacuationCenter[] = [];\r\n\r\n      // Check if offline mode is enabled or if we're offline\r\n      if (this.offlineStorage.isOfflineMode() || !navigator.onLine) {\r\n        console.log('🔄 Loading typhoon centers from offline storage');\r\n        allCenters = await this.offlineStorage.getEvacuationCenters();\r\n        console.log('📱 OFFLINE DATA:', allCenters);\r\n\r\n        if (allCenters.length === 0) {\r\n          console.warn('⚠️ No cached evacuation centers found');\r\n          const alert = await this.alertCtrl.create({\r\n            header: 'No Offline Data',\r\n            message: 'No offline evacuation data available. Please sync data when online.',\r\n            buttons: ['OK']\r\n          });\r\n          await alert.present();\r\n          return;\r\n        }\r\n      } else {\r\n        // Try to get data from API when online\r\n        try {\r\n          allCenters = await firstValueFrom(\r\n            this.http.get<EvacuationCenter[]>(`${environment.apiUrl}/evacuation-centers`)\r\n          );\r\n          console.log('🟢 TYPHOON MAP: Total centers received from API:', allCenters?.length || 0);\r\n        } catch (apiError) {\r\n          console.error('❌ API failed, falling back to offline data:', apiError);\r\n          allCenters = await this.offlineStorage.getEvacuationCenters();\r\n\r\n          if (allCenters.length === 0) {\r\n            const alert = await this.alertCtrl.create({\r\n              header: 'Connection Error',\r\n              message: 'Cannot connect to server and no offline data available. Please check your connection or sync data when online.',\r\n              buttons: ['OK']\r\n            });\r\n            await alert.present();\r\n            return;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Filter for TYPHOON ONLY\r\n      this.evacuationCenters = allCenters.filter(center =>\r\n        center.disaster_type === 'Typhoon'\r\n      );\r\n\r\n      console.log(`🟢 TYPHOON MAP: Filtered to ${this.evacuationCenters.length} typhoon centers`);\r\n\r\n      if (this.evacuationCenters.length === 0) {\r\n        const alert = await this.alertCtrl.create({\r\n          header: 'No Typhoon Centers',\r\n          message: 'No typhoon evacuation centers found in the database.',\r\n          buttons: ['OK']\r\n        });\r\n        await alert.present();\r\n        return;\r\n      }\r\n\r\n      // Check if we're in offline mode\r\n      const isOfflineMode = this.offlineStorage.isOfflineMode() || !navigator.onLine;\r\n\r\n      // Add typhoon markers (green)\r\n      this.evacuationCenters.forEach(center => {\r\n        const lat = Number(center.latitude);\r\n        const lng = Number(center.longitude);\r\n\r\n        if (!isNaN(lat) && !isNaN(lng)) {\r\n          const marker = L.marker([lat, lng], {\r\n            icon: L.icon({\r\n              iconUrl: 'assets/forTyphoon.png',\r\n              iconSize: [40, 40],\r\n              iconAnchor: [20, 40],\r\n              popupAnchor: [0, -40]\r\n            })\r\n          });\r\n\r\n          const distance = this.calculateDistance(userLat, userLng, lat, lng);\r\n\r\n          // Make marker clickable with transportation options (only if online)\r\n          marker.on('click', () => {\r\n            if (isOfflineMode) {\r\n              this.showOfflineMarkerInfo(center, distance);\r\n            } else {\r\n              this.showTransportationOptions(center);\r\n            }\r\n          });\r\n\r\n          // Check if this is the new center to highlight\r\n          const isNewCenter = this.newCenterId && center.id.toString() === this.newCenterId;\r\n\r\n          // Create popup content based on online/offline status\r\n          const offlineIndicator = isOfflineMode ? '<p><em>📱 Offline Mode - Limited functionality</em></p>' : '<p><em>Click marker for route options</em></p>';\r\n\r\n          marker.bindPopup(`\r\n            <div class=\"evacuation-popup\">\r\n              <h3>🟢 ${center.name} ${isNewCenter ? '⭐ NEW!' : ''}</h3>\r\n              <p><strong>Type:</strong> Typhoon Center</p>\r\n              <p><strong>Distance:</strong> ${(distance / 1000).toFixed(2)} km</p>\r\n              <p><strong>Capacity:</strong> ${center.capacity || 'N/A'}</p>\r\n              ${offlineIndicator}\r\n              ${isNewCenter ? '<p><strong>🆕 Recently Added!</strong></p>' : ''}\r\n            </div>\r\n          `);\r\n\r\n          // If this is the new center, open its popup and center map on it\r\n          if (isNewCenter) {\r\n            marker.openPopup();\r\n            this.map.setView([lat, lng], 15); // Zoom in on the new center\r\n\r\n            // Show a toast notification\r\n            this.toastCtrl.create({\r\n              message: `🆕 New typhoon evacuation center: ${center.name}`,\r\n              duration: 5000,\r\n              color: 'success',\r\n              position: 'top'\r\n            }).then(toast => toast.present());\r\n          }\r\n\r\n          marker.addTo(this.map);\r\n          console.log(`🟢 Added typhoon marker: ${center.name}`);\r\n        }\r\n      });\r\n\r\n      // Only auto-route if online\r\n      if (!isOfflineMode) {\r\n        console.log('🟢 Online mode: Auto-routing to 2 nearest typhoon centers...');\r\n        await this.routeToTwoNearestCenters();\r\n      } else {\r\n        console.log('🟢 Offline mode: Showing markers only (no routing)');\r\n      }\r\n\r\n      // Fit map to show all typhoon centers\r\n      if (this.evacuationCenters.length > 0) {\r\n        const bounds = L.latLngBounds([]);\r\n        bounds.extend([userLat, userLng]);\r\n\r\n        this.evacuationCenters.forEach(center => {\r\n          bounds.extend([Number(center.latitude), Number(center.longitude)]);\r\n        });\r\n\r\n        this.map.fitBounds(bounds, { padding: [50, 50] });\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('🟢 TYPHOON MAP: Error loading centers', error);\r\n\r\n      const toast = await this.toastCtrl.create({\r\n        message: 'Error loading typhoon centers. Please check your connection.',\r\n        duration: 3000,\r\n        color: 'danger'\r\n      });\r\n      await toast.present();\r\n    }\r\n  }\r\n\r\n  // Auto-route to 2 nearest typhoon centers\r\n  async routeToTwoNearestCenters() {\r\n    if (!this.userLocation || this.evacuationCenters.length === 0) {\r\n      console.log('🟢 TYPHOON MAP: No user location or evacuation centers available');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      console.log('🟢 TYPHOON MAP: Finding 2 nearest typhoon centers...');\r\n\r\n      // Find 2 nearest centers\r\n      const nearestCenters = this.getTwoNearestCenters(\r\n        this.userLocation.lat,\r\n        this.userLocation.lng\r\n      );\r\n\r\n      if (nearestCenters.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // Clear previous routes\r\n      this.clearRoutes();\r\n\r\n      // Calculate and display routes with typhoon color (green)\r\n      await this.calculateRoutes(nearestCenters);\r\n\r\n    } catch (error) {\r\n      console.error('🟢 TYPHOON MAP: Error calculating routes', error);\r\n    }\r\n  }\r\n\r\n  getTwoNearestCenters(userLat: number, userLng: number) {\r\n    // Sort by distance and get the 2 nearest\r\n    const sorted = [...this.evacuationCenters].sort((a, b) => {\r\n      const distA = this.calculateDistance(userLat, userLng, Number(a.latitude), Number(a.longitude));\r\n      const distB = this.calculateDistance(userLat, userLng, Number(b.latitude), Number(b.longitude));\r\n      return distA - distB;\r\n    });\r\n\r\n    return sorted.slice(0, 2);\r\n  }\r\n\r\n  clearRoutes() {\r\n    this.map.eachLayer(layer => {\r\n      if (layer instanceof L.GeoJSON) {\r\n        this.map.removeLayer(layer);\r\n      }\r\n    });\r\n  }\r\n\r\n  async calculateRoutes(centers: EvacuationCenter[]) {\r\n    for (const center of centers) {\r\n      await this.calculateRoute(center, 'walking'); // Default to walking\r\n    }\r\n  }\r\n\r\n  async calculateRoute(center: EvacuationCenter, travelMode: string) {\r\n    try {\r\n      if (!this.userLocation) {\r\n        console.error('🟢 TYPHOON MAP: No user location available for routing');\r\n        return;\r\n      }\r\n\r\n      const response = await fetch(\r\n        `https://api.mapbox.com/directions/v5/mapbox/${travelMode}/${this.userLocation.lng},${this.userLocation.lat};${center.longitude},${center.latitude}?geometries=geojson&access_token=${environment.mapboxAccessToken}`\r\n      );\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.routes && data.routes.length > 0) {\r\n        const route = data.routes[0];\r\n        const routeGeoJSON = {\r\n          type: 'Feature' as const,\r\n          geometry: route.geometry,\r\n          properties: {}\r\n        };\r\n\r\n        // Add route to map with typhoon color (green)\r\n        L.geoJSON(routeGeoJSON as any, {\r\n          style: {\r\n            color: '#008000', // Green for typhoon\r\n            weight: 4,\r\n            opacity: 0.8\r\n          }\r\n        }).addTo(this.map);\r\n\r\n        console.log(`🟢 TYPHOON MAP: Route added to ${center.name}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('🟢 TYPHOON MAP: Error calculating route:', error);\r\n    }\r\n  }\r\n\r\n  // Show offline marker information when clicked in offline mode\r\n  async showOfflineMarkerInfo(center: EvacuationCenter, distance: number) {\r\n    const alert = await this.alertCtrl.create({\r\n      header: `📱 ${center.name}`,\r\n      message: `\r\n        <div style=\"text-align: left;\">\r\n          <p><strong>Type:</strong> Typhoon Center</p>\r\n          <p><strong>Distance:</strong> ${(distance / 1000).toFixed(2)} km</p>\r\n          <p><strong>Address:</strong> ${center.address || 'N/A'}</p>\r\n          <p><strong>Capacity:</strong> ${center.capacity || 'N/A'}</p>\r\n          <p><strong>Status:</strong> ${center.status || 'N/A'}</p>\r\n          <br>\r\n          <p><em>📱 Offline Mode: Routing not available. Use external navigation apps for directions.</em></p>\r\n        </div>\r\n      `,\r\n      buttons: [\r\n        {\r\n          text: 'Open in Maps',\r\n          handler: () => {\r\n            this.openInExternalMaps(center);\r\n          }\r\n        },\r\n        {\r\n          text: 'Close',\r\n          role: 'cancel'\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  // Open evacuation center in external maps app\r\n  async openInExternalMaps(center: EvacuationCenter) {\r\n    const lat = Number(center.latitude);\r\n    const lng = Number(center.longitude);\r\n\r\n    // Create maps URL that works on both Android and iOS\r\n    const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&travelmode=walking`;\r\n\r\n    try {\r\n      window.open(mapsUrl, '_system');\r\n    } catch (error) {\r\n      console.error('Error opening external maps:', error);\r\n      const toast = await this.toastCtrl.create({\r\n        message: 'Could not open external maps app',\r\n        duration: 3000,\r\n        color: 'danger'\r\n      });\r\n      await toast.present();\r\n    }\r\n  }\r\n\r\n  // Show transportation options when marker is clicked (online mode)\r\n  async showTransportationOptions(center: EvacuationCenter) {\r\n    // Check if we're offline before showing transportation options\r\n    const isOfflineMode = this.offlineStorage.isOfflineMode() || !navigator.onLine;\r\n\r\n    if (isOfflineMode) {\r\n      const distance = this.calculateDistance(\r\n        this.userLocation?.lat || 0,\r\n        this.userLocation?.lng || 0,\r\n        Number(center.latitude),\r\n        Number(center.longitude)\r\n      );\r\n      await this.showOfflineMarkerInfo(center, distance);\r\n      return;\r\n    }\r\n    const alert = await this.alertCtrl.create({\r\n      header: `Route to ${center.name}`,\r\n      message: 'Choose your transportation mode:',\r\n      buttons: [\r\n        {\r\n          text: '🚶‍♂️ Walk',\r\n          handler: () => {\r\n            this.routeToCenter(center, 'walking');\r\n          }\r\n        },\r\n        {\r\n          text: '🚴‍♂️ Cycle',\r\n          handler: () => {\r\n            this.routeToCenter(center, 'cycling');\r\n          }\r\n        },\r\n        {\r\n          text: '🚗 Drive',\r\n          handler: () => {\r\n            this.routeToCenter(center, 'driving');\r\n          }\r\n        },\r\n        {\r\n          text: 'Cancel',\r\n          role: 'cancel'\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  // Route to specific center with chosen transportation mode\r\n  async routeToCenter(center: EvacuationCenter, travelMode: 'walking' | 'cycling' | 'driving') {\r\n    if (!this.userLocation) return;\r\n\r\n    // Check if we're offline\r\n    const isOfflineMode = this.offlineStorage.isOfflineMode() || !navigator.onLine;\r\n\r\n    if (isOfflineMode) {\r\n      console.log('🟢 Offline mode: Cannot calculate routes');\r\n      const toast = await this.toastCtrl.create({\r\n        message: '📱 Offline mode: Routing not available. Use external navigation apps.',\r\n        duration: 4000,\r\n        color: 'warning'\r\n      });\r\n      await toast.present();\r\n\r\n      // Offer to open in external maps\r\n      await this.openInExternalMaps(center);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Clear existing routes\r\n      this.clearRoutes();\r\n\r\n      // Map travel modes to Mapbox API\r\n      let mapboxMode = 'walking';\r\n      switch (travelMode) {\r\n        case 'walking':\r\n          mapboxMode = 'walking';\r\n          break;\r\n        case 'cycling':\r\n          mapboxMode = 'cycling';\r\n          break;\r\n        case 'driving':\r\n          mapboxMode = 'driving';\r\n          break;\r\n      }\r\n\r\n      const response = await fetch(\r\n        `https://api.mapbox.com/directions/v5/mapbox/${mapboxMode}/${this.userLocation.lng},${this.userLocation.lat};${center.longitude},${center.latitude}?geometries=geojson&access_token=${environment.mapboxAccessToken}`\r\n      );\r\n\r\n      if (response.ok) {\r\n        const routeData = await response.json();\r\n\r\n        if (routeData && routeData.routes && routeData.routes.length > 0) {\r\n          const route = routeData.routes[0];\r\n\r\n          // Use typhoon color (green)\r\n          const routeColor = '#008000';\r\n\r\n          // Draw route\r\n          const routeLine = L.polyline(\r\n            route.geometry.coordinates.map((coord: number[]) => [coord[1], coord[0]]),\r\n            {\r\n              color: routeColor,\r\n              weight: 5,\r\n              opacity: 0.8\r\n            }\r\n          );\r\n\r\n          routeLine.addTo(this.map);\r\n\r\n          // Show route info\r\n          const toast = await this.toastCtrl.create({\r\n            message: `🟢 Route: ${(route.distance/1000).toFixed(2)}km, ${(route.duration/60).toFixed(0)}min via ${travelMode}`,\r\n            duration: 4000,\r\n            color: 'success'\r\n          });\r\n          await toast.present();\r\n\r\n          // Fit map to route\r\n          this.map.fitBounds(routeLine.getBounds(), { padding: [50, 50] });\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('🟢 TYPHOON MAP: Error calculating individual route:', error);\r\n\r\n      const toast = await this.toastCtrl.create({\r\n        message: 'Error calculating route. Please try again.',\r\n        duration: 3000,\r\n        color: 'danger'\r\n      });\r\n      await toast.present();\r\n    }\r\n  }\r\n\r\n  calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {\r\n    const R = 6371e3; // meters\r\n    const φ1 = lat1 * Math.PI / 180;\r\n    const φ2 = lat2 * Math.PI / 180;\r\n    const Δφ = (lat2 - lat1) * Math.PI / 180;\r\n    const Δλ = (lon2 - lon1) * Math.PI / 180;\r\n\r\n    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +\r\n              Math.cos(φ1) * Math.cos(φ2) *\r\n              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);\r\n\r\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\r\n    return R * c;\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigate(['/tabs/home']);\r\n  }\r\n\r\n  ionViewWillLeave() {\r\n    if (this.map) {\r\n      this.map.remove();\r\n    }\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"success\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-button (click)=\"goBack()\">\r\n        <ion-icon name=\"chevron-back-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n    <ion-title>🟢 Typhoon Evacuation Centers</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div id=\"typhoon-map\" style=\"height: 100%; width: 100%;\"></div>\r\n  \r\n  <!-- Floating info card -->\r\n  <div class=\"floating-info\">\r\n    <ion-card>\r\n      <ion-card-content>\r\n        <div class=\"info-row\">\r\n          <ion-icon name=\"thunderstorm\" color=\"success\"></ion-icon>\r\n          <span>Typhoon Centers: {{ evacuationCenters.length }}</span>\r\n        </div>\r\n        <div class=\"info-text\">\r\n          Showing evacuation centers specifically for typhoon disasters\r\n        </div>\r\n      </ion-card-content>\r\n    </ion-card>\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,QAAmB;AAuBb,IAAO,iBAAP,MAAO,gBAAc;EAP3B,cAAA;AASU,SAAA,aAAmC;AACpC,SAAA,oBAAwC,CAAA;AACxC,SAAA,eAAoD;AAGpD,SAAA,cAA6B;AAC7B,SAAA,kBAA2B;AAC3B,SAAA,YAA2B;AAC3B,SAAA,YAA2B;AAE1B,SAAA,cAAc,OAAO,iBAAiB;AACtC,SAAA,YAAY,OAAO,eAAe;AAClC,SAAA,YAAY,OAAO,eAAe;AAClC,SAAA,OAAO,OAAO,UAAU;AACxB,SAAA,SAAS,OAAO,MAAM;AACtB,SAAA,QAAQ,OAAO,cAAc;AAC7B,SAAA,gBAAgB,OAAO,oBAAoB;AAC3C,SAAA,iBAAiB,OAAO,qBAAqB;;EAErD,WAAQ;AACN,YAAQ,IAAI,iDAA0C;AAItD,SAAK,MAAM,YAAY,UAAU,CAAC,WAAe;AAC/C,UAAI,OAAO,aAAa,GAAG;AACzB,aAAK,cAAc,OAAO,aAAa;AACvC,aAAK,kBAAkB,OAAO,iBAAiB,MAAM;AACrD,aAAK,YAAY,OAAO,WAAW,IAAI,WAAW,OAAO,WAAW,CAAC,IAAI;AACzE,aAAK,YAAY,OAAO,WAAW,IAAI,WAAW,OAAO,WAAW,CAAC,IAAI;AACzE,gBAAQ,IAAI,mDAA4C,KAAK,WAAW;MAC1E;IACF,CAAC;EACH;EAEM,kBAAe;;AACnB,cAAQ,IAAI,yDAAkD;AAE9D,iBAAW,MAAW;AACpB,cAAM,KAAK,eAAc;MAC3B,IAAG,GAAG;IACR;;EAEM,iBAAc;;AAClB,YAAM,UAAU,MAAM,KAAK,YAAY,OAAO;QAC5C,SAAS;QACT,SAAS;OACV;AACD,YAAM,QAAQ,QAAO;AAErB,UAAI;AAEF,cAAM,WAAW,MAAM,YAAY,mBAAmB;UACpD,oBAAoB;UACpB,SAAS;SACV;AAED,cAAM,UAAU,SAAS,OAAO;AAChC,cAAM,UAAU,SAAS,OAAO;AAEhC,gBAAQ,IAAI,yCAAkC,OAAO,KAAK,OAAO,GAAG;AAGpE,aAAK,eAAe,EAAE,KAAK,SAAS,KAAK,QAAO;AAGhD,aAAK,cAAc,SAAS,OAAO;AAGnC,cAAM,KAAK,mBAAmB,SAAS,OAAO;AAE9C,cAAM,QAAQ,QAAO;AAGrB,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS,qBAAc,KAAK,kBAAkB,MAAM;UACpD,UAAU;UACV,OAAO;UACP,UAAU;SACX;AACD,cAAM,MAAM,QAAO;MAErB,SAAS,OAAO;AACd,cAAM,QAAQ,QAAO;AACrB,gBAAQ,MAAM,4CAAqC,KAAK;AAExD,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,QAAQ;UACR,SAAS;UACT,SAAS;YACP;cACE,MAAM;cACN,SAAS,MAAM,KAAK,eAAc;;YAEpC;cACE,MAAM;cACN,SAAS,MAAM,KAAK,OAAO,SAAS,CAAC,YAAY,CAAC;;;SAGvD;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;EAEA,cAAc,KAAa,KAAW;AACpC,YAAQ,IAAI,+CAAwC,GAAG,KAAK,GAAG,GAAG;AAGlE,UAAM,YAAY,SAAS,eAAe,aAAa;AACvD,QAAI,CAAC,WAAW;AACd,cAAQ,MAAM,0DAAmD;AACjE,YAAM,IAAI,MAAM,qEAAqE;IACvF;AAEA,QAAI,KAAK,KAAK;AACZ,WAAK,IAAI,OAAM;IACjB;AAEA,SAAK,MAAQ,MAAI,aAAa,EAAE,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE;AAEtD,IAAE,YAAU,sDAAsD;MAChE,aAAa;KACd,EAAE,MAAM,KAAK,GAAG;AAGjB,SAAK,aAAe,SAAO,CAAC,KAAK,GAAG,GAAG;MACrC,MAAQ,OAAK;QACX,SAAS;QACT,UAAU,CAAC,IAAI,EAAE;QACjB,YAAY,CAAC,IAAI,EAAE;OACpB;KACF,EAAE,MAAM,KAAK,GAAG;AAEjB,SAAK,WAAW,UAAU,yBAAkB,EAAE,UAAS;EACzD;EAEM,mBAAmB,SAAiB,SAAe;;AACvD,UAAI;AACF,gBAAQ,IAAI,oDAA6C;AAEzD,YAAI,aAAiC,CAAA;AAGrC,YAAI,KAAK,eAAe,cAAa,KAAM,CAAC,UAAU,QAAQ;AAC5D,kBAAQ,IAAI,wDAAiD;AAC7D,uBAAa,MAAM,KAAK,eAAe,qBAAoB;AAC3D,kBAAQ,IAAI,2BAAoB,UAAU;AAE1C,cAAI,WAAW,WAAW,GAAG;AAC3B,oBAAQ,KAAK,iDAAuC;AACpD,kBAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;cACxC,QAAQ;cACR,SAAS;cACT,SAAS,CAAC,IAAI;aACf;AACD,kBAAM,MAAM,QAAO;AACnB;UACF;QACF,OAAO;AAEL,cAAI;AACF,yBAAa,MAAM,eACjB,KAAK,KAAK,IAAwB,GAAG,YAAY,MAAM,qBAAqB,CAAC;AAE/E,oBAAQ,IAAI,2DAAoD,YAAY,UAAU,CAAC;UACzF,SAAS,UAAU;AACjB,oBAAQ,MAAM,oDAA+C,QAAQ;AACrE,yBAAa,MAAM,KAAK,eAAe,qBAAoB;AAE3D,gBAAI,WAAW,WAAW,GAAG;AAC3B,oBAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;gBACxC,QAAQ;gBACR,SAAS;gBACT,SAAS,CAAC,IAAI;eACf;AACD,oBAAM,MAAM,QAAO;AACnB;YACF;UACF;QACF;AAGA,aAAK,oBAAoB,WAAW,OAAO,YACzC,OAAO,kBAAkB,SAAS;AAGpC,gBAAQ,IAAI,sCAA+B,KAAK,kBAAkB,MAAM,kBAAkB;AAE1F,YAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,gBAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;YACxC,QAAQ;YACR,SAAS;YACT,SAAS,CAAC,IAAI;WACf;AACD,gBAAM,MAAM,QAAO;AACnB;QACF;AAGA,cAAM,gBAAgB,KAAK,eAAe,cAAa,KAAM,CAAC,UAAU;AAGxE,aAAK,kBAAkB,QAAQ,YAAS;AACtC,gBAAM,MAAM,OAAO,OAAO,QAAQ;AAClC,gBAAM,MAAM,OAAO,OAAO,SAAS;AAEnC,cAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;AAC9B,kBAAMA,UAAW,SAAO,CAAC,KAAK,GAAG,GAAG;cAClC,MAAQ,OAAK;gBACX,SAAS;gBACT,UAAU,CAAC,IAAI,EAAE;gBACjB,YAAY,CAAC,IAAI,EAAE;gBACnB,aAAa,CAAC,GAAG,GAAG;eACrB;aACF;AAED,kBAAM,WAAW,KAAK,kBAAkB,SAAS,SAAS,KAAK,GAAG;AAGlE,YAAAA,QAAO,GAAG,SAAS,MAAK;AACtB,kBAAI,eAAe;AACjB,qBAAK,sBAAsB,QAAQ,QAAQ;cAC7C,OAAO;AACL,qBAAK,0BAA0B,MAAM;cACvC;YACF,CAAC;AAGD,kBAAM,cAAc,KAAK,eAAe,OAAO,GAAG,SAAQ,MAAO,KAAK;AAGtE,kBAAM,mBAAmB,gBAAgB,mEAA4D;AAErG,YAAAA,QAAO,UAAU;;8BAEJ,OAAO,IAAI,IAAI,cAAc,gBAAW,EAAE;;+CAElB,WAAW,KAAM,QAAQ,CAAC,CAAC;8CAC5B,OAAO,YAAY,KAAK;gBACtD,gBAAgB;gBAChB,cAAc,sDAA+C,EAAE;;WAEpE;AAGD,gBAAI,aAAa;AACf,cAAAA,QAAO,UAAS;AAChB,mBAAK,IAAI,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE;AAG/B,mBAAK,UAAU,OAAO;gBACpB,SAAS,4CAAqC,OAAO,IAAI;gBACzD,UAAU;gBACV,OAAO;gBACP,UAAU;eACX,EAAE,KAAK,WAAS,MAAM,QAAO,CAAE;YAClC;AAEA,YAAAA,QAAO,MAAM,KAAK,GAAG;AACrB,oBAAQ,IAAI,mCAA4B,OAAO,IAAI,EAAE;UACvD;QACF,CAAC;AAGD,YAAI,CAAC,eAAe;AAClB,kBAAQ,IAAI,qEAA8D;AAC1E,gBAAM,KAAK,yBAAwB;QACrC,OAAO;AACL,kBAAQ,IAAI,2DAAoD;QAClE;AAGA,YAAI,KAAK,kBAAkB,SAAS,GAAG;AACrC,gBAAM,SAAW,eAAa,CAAA,CAAE;AAChC,iBAAO,OAAO,CAAC,SAAS,OAAO,CAAC;AAEhC,eAAK,kBAAkB,QAAQ,YAAS;AACtC,mBAAO,OAAO,CAAC,OAAO,OAAO,QAAQ,GAAG,OAAO,OAAO,SAAS,CAAC,CAAC;UACnE,CAAC;AAED,eAAK,IAAI,UAAU,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,EAAC,CAAE;QAClD;MAEF,SAAS,OAAO;AACd,gBAAQ,MAAM,gDAAyC,KAAK;AAE5D,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS;UACT,UAAU;UACV,OAAO;SACR;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;;EAGM,2BAAwB;;AAC5B,UAAI,CAAC,KAAK,gBAAgB,KAAK,kBAAkB,WAAW,GAAG;AAC7D,gBAAQ,IAAI,yEAAkE;AAC9E;MACF;AAEA,UAAI;AACF,gBAAQ,IAAI,6DAAsD;AAGlE,cAAM,iBAAiB,KAAK,qBAC1B,KAAK,aAAa,KAClB,KAAK,aAAa,GAAG;AAGvB,YAAI,eAAe,WAAW,GAAG;AAC/B;QACF;AAGA,aAAK,YAAW;AAGhB,cAAM,KAAK,gBAAgB,cAAc;MAE3C,SAAS,OAAO;AACd,gBAAQ,MAAM,mDAA4C,KAAK;MACjE;IACF;;EAEA,qBAAqB,SAAiB,SAAe;AAEnD,UAAM,SAAS,CAAC,GAAG,KAAK,iBAAiB,EAAE,KAAK,CAAC,GAAG,MAAK;AACvD,YAAM,QAAQ,KAAK,kBAAkB,SAAS,SAAS,OAAO,EAAE,QAAQ,GAAG,OAAO,EAAE,SAAS,CAAC;AAC9F,YAAM,QAAQ,KAAK,kBAAkB,SAAS,SAAS,OAAO,EAAE,QAAQ,GAAG,OAAO,EAAE,SAAS,CAAC;AAC9F,aAAO,QAAQ;IACjB,CAAC;AAED,WAAO,OAAO,MAAM,GAAG,CAAC;EAC1B;EAEA,cAAW;AACT,SAAK,IAAI,UAAU,WAAQ;AACzB,UAAI,iBAAmB,WAAS;AAC9B,aAAK,IAAI,YAAY,KAAK;MAC5B;IACF,CAAC;EACH;EAEM,gBAAgB,SAA2B;;AAC/C,iBAAW,UAAU,SAAS;AAC5B,cAAM,KAAK,eAAe,QAAQ,SAAS;MAC7C;IACF;;EAEM,eAAe,QAA0B,YAAkB;;AAC/D,UAAI;AACF,YAAI,CAAC,KAAK,cAAc;AACtB,kBAAQ,MAAM,+DAAwD;AACtE;QACF;AAEA,cAAM,WAAW,MAAM,MACrB,+CAA+C,UAAU,IAAI,KAAK,aAAa,GAAG,IAAI,KAAK,aAAa,GAAG,IAAI,OAAO,SAAS,IAAI,OAAO,QAAQ,oCAAoC,YAAY,iBAAiB,EAAE;AAGvN,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM,IAAI,MAAM,uBAAuB,SAAS,MAAM,EAAE;QAC1D;AAEA,cAAM,OAAO,MAAM,SAAS,KAAI;AAEhC,YAAI,KAAK,UAAU,KAAK,OAAO,SAAS,GAAG;AACzC,gBAAM,QAAQ,KAAK,OAAO,CAAC;AAC3B,gBAAM,eAAe;YACnB,MAAM;YACN,UAAU,MAAM;YAChB,YAAY,CAAA;;AAId,UAAE,UAAQ,cAAqB;YAC7B,OAAO;cACL,OAAO;;cACP,QAAQ;cACR,SAAS;;WAEZ,EAAE,MAAM,KAAK,GAAG;AAEjB,kBAAQ,IAAI,yCAAkC,OAAO,IAAI,EAAE;QAC7D;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,mDAA4C,KAAK;MACjE;IACF;;;EAGM,sBAAsB,QAA0B,UAAgB;;AACpE,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC,QAAQ,aAAM,OAAO,IAAI;QACzB,SAAS;;;2CAG4B,WAAW,KAAM,QAAQ,CAAC,CAAC;yCAC7B,OAAO,WAAW,KAAK;0CACtB,OAAO,YAAY,KAAK;wCAC1B,OAAO,UAAU,KAAK;;;;;QAKxD,SAAS;UACP;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,mBAAmB,MAAM;YAChC;;UAEF;YACE,MAAM;YACN,MAAM;;;OAGX;AAED,YAAM,MAAM,QAAO;IACrB;;;EAGM,mBAAmB,QAAwB;;AAC/C,YAAM,MAAM,OAAO,OAAO,QAAQ;AAClC,YAAM,MAAM,OAAO,OAAO,SAAS;AAGnC,YAAM,UAAU,sDAAsD,GAAG,IAAI,GAAG;AAEhF,UAAI;AACF,eAAO,KAAK,SAAS,SAAS;MAChC,SAAS,OAAO;AACd,gBAAQ,MAAM,gCAAgC,KAAK;AACnD,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS;UACT,UAAU;UACV,OAAO;SACR;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;;EAGM,0BAA0B,QAAwB;;AAEtD,YAAM,gBAAgB,KAAK,eAAe,cAAa,KAAM,CAAC,UAAU;AAExE,UAAI,eAAe;AACjB,cAAM,WAAW,KAAK,kBACpB,KAAK,cAAc,OAAO,GAC1B,KAAK,cAAc,OAAO,GAC1B,OAAO,OAAO,QAAQ,GACtB,OAAO,OAAO,SAAS,CAAC;AAE1B,cAAM,KAAK,sBAAsB,QAAQ,QAAQ;AACjD;MACF;AACA,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC,QAAQ,YAAY,OAAO,IAAI;QAC/B,SAAS;QACT,SAAS;UACP;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,cAAc,QAAQ,SAAS;YACtC;;UAEF;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,cAAc,QAAQ,SAAS;YACtC;;UAEF;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,cAAc,QAAQ,SAAS;YACtC;;UAEF;YACE,MAAM;YACN,MAAM;;;OAGX;AAED,YAAM,MAAM,QAAO;IACrB;;;EAGM,cAAc,QAA0B,YAA6C;;AACzF,UAAI,CAAC,KAAK;AAAc;AAGxB,YAAM,gBAAgB,KAAK,eAAe,cAAa,KAAM,CAAC,UAAU;AAExE,UAAI,eAAe;AACjB,gBAAQ,IAAI,iDAA0C;AACtD,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS;UACT,UAAU;UACV,OAAO;SACR;AACD,cAAM,MAAM,QAAO;AAGnB,cAAM,KAAK,mBAAmB,MAAM;AACpC;MACF;AAEA,UAAI;AAEF,aAAK,YAAW;AAGhB,YAAI,aAAa;AACjB,gBAAQ,YAAY;UAClB,KAAK;AACH,yBAAa;AACb;UACF,KAAK;AACH,yBAAa;AACb;UACF,KAAK;AACH,yBAAa;AACb;QACJ;AAEA,cAAM,WAAW,MAAM,MACrB,+CAA+C,UAAU,IAAI,KAAK,aAAa,GAAG,IAAI,KAAK,aAAa,GAAG,IAAI,OAAO,SAAS,IAAI,OAAO,QAAQ,oCAAoC,YAAY,iBAAiB,EAAE;AAGvN,YAAI,SAAS,IAAI;AACf,gBAAM,YAAY,MAAM,SAAS,KAAI;AAErC,cAAI,aAAa,UAAU,UAAU,UAAU,OAAO,SAAS,GAAG;AAChE,kBAAM,QAAQ,UAAU,OAAO,CAAC;AAGhC,kBAAM,aAAa;AAGnB,kBAAM,YAAc,WAClB,MAAM,SAAS,YAAY,IAAI,CAAC,UAAoB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GACxE;cACE,OAAO;cACP,QAAQ;cACR,SAAS;aACV;AAGH,sBAAU,MAAM,KAAK,GAAG;AAGxB,kBAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;cACxC,SAAS,qBAAc,MAAM,WAAS,KAAM,QAAQ,CAAC,CAAC,QAAQ,MAAM,WAAS,IAAI,QAAQ,CAAC,CAAC,WAAW,UAAU;cAChH,UAAU;cACV,OAAO;aACR;AACD,kBAAM,MAAM,QAAO;AAGnB,iBAAK,IAAI,UAAU,UAAU,UAAS,GAAI,EAAE,SAAS,CAAC,IAAI,EAAE,EAAC,CAAE;UACjE;QACF;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,8DAAuD,KAAK;AAE1E,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS;UACT,UAAU;UACV,OAAO;SACR;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;EAEA,kBAAkB,MAAc,MAAc,MAAc,MAAY;AACtE,UAAM,IAAI;AACV,UAAM,UAAK,OAAO,KAAK,KAAK;AAC5B,UAAM,UAAK,OAAO,KAAK,KAAK;AAC5B,UAAM,gBAAM,OAAO,QAAQ,KAAK,KAAK;AACrC,UAAM,gBAAM,OAAO,QAAQ,KAAK,KAAK;AAErC,UAAM,IAAI,KAAK,IAAI,eAAK,CAAC,IAAI,KAAK,IAAI,eAAK,CAAC,IAClC,KAAK,IAAI,OAAE,IAAI,KAAK,IAAI,OAAE,IAC1B,KAAK,IAAI,eAAK,CAAC,IAAI,KAAK,IAAI,eAAK,CAAC;AAE5C,UAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AACvD,WAAO,IAAI;EACb;EAEA,SAAM;AACJ,SAAK,OAAO,SAAS,CAAC,YAAY,CAAC;EACrC;EAEA,mBAAgB;AACd,QAAI,KAAK,KAAK;AACZ,WAAK,IAAI,OAAM;IACjB;EACF;;;uCA7lBW,iBAAc;IAAA;EAAA;;yEAAd,iBAAc,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,QAAA,OAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,sBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,MAAA,eAAA,GAAA,UAAA,QAAA,SAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,QAAA,gBAAA,SAAA,SAAA,GAAA,CAAA,GAAA,WAAA,CAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;AC/B3B,QAAA,yBAAA,GAAA,cAAA,CAAA,EAAiC,GAAA,eAAA,CAAA,EACF,GAAA,eAAA,CAAA,EACD,GAAA,cAAA,CAAA;AACZ,QAAA,qBAAA,SAAA,SAAA,sDAAA;AAAA,iBAAS,IAAA,OAAA;QAAQ,CAAA;AAC3B,QAAA,oBAAA,GAAA,YAAA,CAAA;AACF,QAAA,uBAAA,EAAa;AAEf,QAAA,yBAAA,GAAA,WAAA;AAAW,QAAA,iBAAA,GAAA,sCAAA;AAA6B,QAAA,uBAAA,EAAY,EACxC;AAGhB,QAAA,yBAAA,GAAA,eAAA,CAAA;AACE,QAAA,oBAAA,GAAA,OAAA,CAAA;AAGA,QAAA,yBAAA,GAAA,OAAA,CAAA,EAA2B,IAAA,UAAA,EACf,IAAA,kBAAA,EACU,IAAA,OAAA,CAAA;AAEd,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,yBAAA,IAAA,MAAA;AAAM,QAAA,iBAAA,EAAA;AAA+C,QAAA,uBAAA,EAAO;AAE9D,QAAA,yBAAA,IAAA,OAAA,EAAA;AACE,QAAA,iBAAA,IAAA,iEAAA;AACF,QAAA,uBAAA,EAAM,EACW,EACV,EACP;;;AA3BI,QAAA,qBAAA,eAAA,IAAA;AAWC,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,cAAA,IAAA;AASG,QAAA,oBAAA,CAAA;AAAA,QAAA,6BAAA,qBAAA,IAAA,kBAAA,QAAA,EAAA;;sBDSJ,aAAW,WAAA,YAAA,SAAA,gBAAA,YAAA,WAAA,SAAA,UAAA,YAAE,YAAY,GAAA,QAAA,CAAA,y6DAAA,EAAA,CAAA;EAAA;;;sEAExB,gBAAc,CAAA;UAP1B;uBACW,mBAAiB,YAGf,MAAI,SACP,CAAC,aAAa,YAAY,GAAC,UAAA,g+BAAA,QAAA,CAAA,m8CAAA,EAAA,CAAA;;;;6EAEzB,gBAAc,EAAA,WAAA,kBAAA,UAAA,mDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": ["marker"]}
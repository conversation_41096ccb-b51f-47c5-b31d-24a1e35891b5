{"version": 3, "sources": ["src/app/services/network.service.ts", "src/app/pages/login/login.page.ts", "src/app/pages/login/login.page.html"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Platform, ToastController } from '@ionic/angular';\r\nimport { environment } from '../../environments/environment';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { timeout } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class NetworkService {\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private platform: Platform,\r\n    private toastCtrl: ToastController\r\n  ) {}\r\n\r\n  /**\r\n   * Check if the backend API is reachable\r\n   */\r\n  async checkBackendConnectivity(): Promise<boolean> {\r\n    try {\r\n      console.log('Checking backend connectivity to:', environment.apiUrl);\r\n\r\n      // First try the test endpoint with timeout using RxJS\r\n      const testResponse = await firstValueFrom(\r\n        this.http.get(`${environment.apiUrl.replace('/api', '')}/api/test`).pipe(\r\n          timeout(10000)\r\n        )\r\n      );\r\n      console.log('Backend test endpoint successful:', testResponse);\r\n\r\n      // Then try evacuation centers endpoint with timeout\r\n      const response = await firstValueFrom(\r\n        this.http.get(`${environment.apiUrl}/evacuation-centers`).pipe(\r\n          timeout(10000)\r\n        )\r\n      );\r\n      console.log('Backend connectivity check successful');\r\n      return true;\r\n    } catch (error: any) {\r\n      console.error('Backend connectivity check failed:', error);\r\n      console.error('Error details:', {\r\n        status: error.status,\r\n        statusText: error.statusText,\r\n        url: error.url,\r\n        message: error.message\r\n      });\r\n\r\n      if (error.status === 0) {\r\n        // DISABLED: Network errors are now handled by offline banner\r\n        // this.showConnectivityError('Backend Server',\r\n        //   'Cannot connect to the backend server.\\n\\n' +\r\n        //   'Troubleshooting steps:\\n' +\r\n        //   '• Check if both devices are on the same WiFi\\n' +\r\n        //   '• Verify server is running on computer\\n' +\r\n        //   '• Check Windows Firewall settings\\n' +\r\n        //   '• Try restarting the backend server\\n\\n' +\r\n        //   `Server: ${environment.apiUrl}`);\r\n        console.log('Backend server not reachable - offline mode available');\r\n      } else {\r\n        console.log('Backend is reachable but returned an error:', error.status);\r\n        return true; // Server is reachable, just returned an error\r\n      }\r\n\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if Mapbox API is reachable\r\n   */\r\n  async checkMapboxConnectivity(): Promise<boolean> {\r\n    try {\r\n      console.log('Checking Mapbox connectivity...');\r\n\r\n      // Simple test request to Mapbox API\r\n      const testUrl = `https://api.mapbox.com/directions/v5/mapbox/walking/121.7740,12.8797;121.7750,12.8807?access_token=${environment.mapboxAccessToken}&overview=simplified`;\r\n\r\n      const response = await firstValueFrom(\r\n        this.http.get(testUrl)\r\n      );\r\n\r\n      console.log('Mapbox connectivity check successful');\r\n      return true;\r\n    } catch (error: any) {\r\n      console.error('Mapbox connectivity check failed:', error);\r\n\r\n      if (error.status === 0) {\r\n        this.showConnectivityError('Mapbox', 'Cannot connect to Mapbox API. Please check your internet connection.');\r\n      } else if (error.status === 401) {\r\n        this.showConnectivityError('Mapbox', 'Invalid Mapbox access token. Please check your token.');\r\n      } else if (error.status === 403) {\r\n        this.showConnectivityError('Mapbox', 'Mapbox access denied. Please check your token permissions.');\r\n      } else if (error.status === 429) {\r\n        this.showConnectivityError('Mapbox', 'Too many requests to Mapbox. Please wait and try again.');\r\n      }\r\n\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check overall network connectivity\r\n   */\r\n  async checkNetworkConnectivity(): Promise<{backend: boolean, routing: boolean}> {\r\n    console.log('Starting comprehensive network connectivity check...');\r\n\r\n    const results = {\r\n      backend: false,\r\n      routing: false\r\n    };\r\n\r\n    // Check backend connectivity\r\n    results.backend = await this.checkBackendConnectivity();\r\n\r\n    // Check routing service connectivity\r\n    results.routing = await this.checkMapboxConnectivity();\r\n\r\n    console.log('Network connectivity check results:', results);\r\n    return results;\r\n  }\r\n\r\n  /**\r\n   * Show connectivity error message\r\n   */\r\n  private async showConnectivityError(service: string, message: string) {\r\n    const toast = await this.toastCtrl.create({\r\n      header: `${service} Connection Error`,\r\n      message: message,\r\n      duration: 5000,\r\n      color: 'danger',\r\n      buttons: [\r\n        {\r\n          text: 'Dismiss',\r\n          role: 'cancel'\r\n        }\r\n      ]\r\n    });\r\n    await toast.present();\r\n  }\r\n\r\n  /**\r\n   * Test network connectivity with a simple ping\r\n   */\r\n  async pingTest(): Promise<boolean> {\r\n    try {\r\n      // Use a reliable service for ping test\r\n      const response = await firstValueFrom(\r\n        this.http.get('https://httpbin.org/get')\r\n      );\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Ping test failed:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get network status information\r\n   */\r\n  getNetworkInfo(): any {\r\n    if (this.platform.is('capacitor')) {\r\n      // On mobile devices, you could use Capacitor Network plugin\r\n      // For now, return basic info\r\n      return {\r\n        platform: 'mobile',\r\n        userAgent: navigator.userAgent\r\n      };\r\n    } else {\r\n      return {\r\n        platform: 'web',\r\n        online: navigator.onLine,\r\n        userAgent: navigator.userAgent\r\n      };\r\n    }\r\n  }\r\n}\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { IonicModule, AlertController, Platform } from '@ionic/angular';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { FCM } from '@awesome-cordova-plugins/fcm/ngx';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { environment } from '../../../environments/environment';\r\nimport { FcmService } from '../../services/fcm.service';\r\nimport { OfflineStorageService } from '../../services/offline-storage.service';\r\nimport { NetworkService } from '../../services/network.service';\r\n\r\n@Component({\r\n  standalone: true,\r\n  imports: [IonicModule, FormsModule],\r\n  selector: 'app-login',\r\n  templateUrl: './login.page.html',\r\n  styleUrls: ['./login.page.scss'],\r\n})\r\nexport class LoginPage implements OnInit {\r\n  credentials = {\r\n    email: '',\r\n    password: ''\r\n  };\r\n  errorMessage = '';\r\n  fcmToken: string = '';\r\n  private fcmTokenReady = false;\r\n\r\n  // Add the goToRegister method\r\n  goToRegister() {\r\n    this.router.navigate(['/register']);\r\n  }\r\n\r\n  openNetworkDiagnostics() {\r\n    this.router.navigate(['/network-diagnostics']);\r\n  }\r\n\r\n  openEnvironmentSwitcher() {\r\n    this.router.navigate(['/environment-switcher']);\r\n  }\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private authService: AuthService,\r\n    private fcm: FCM,\r\n    private http: HttpClient,\r\n    private alertController: AlertController,\r\n    private platform: Platform,\r\n    private fcmService: FcmService,\r\n    private offlineStorage: OfflineStorageService,\r\n    private networkService: NetworkService\r\n  ) {}\r\n\r\n  async ngOnInit() {\r\n    console.log('🔥 Login page initializing...');\r\n    // Initialize FCM and get token\r\n    await this.initializeFCM();\r\n  }\r\n\r\n  /**\r\n   * Initialize FCM service and get token\r\n   */\r\n  async initializeFCM() {\r\n    try {\r\n      console.log('🔥 Initializing FCM for login...');\r\n\r\n      // Initialize FCM service first\r\n      await this.fcmService.initPush();\r\n\r\n      // Get FCM token\r\n      await this.getFCMToken();\r\n\r\n      console.log('✅ FCM initialization complete, token ready:', !!this.fcmToken);\r\n      this.fcmTokenReady = true;\r\n    } catch (error) {\r\n      console.error('❌ FCM initialization failed:', error);\r\n      // Continue without FCM - app should still work\r\n      this.fcmTokenReady = false;\r\n    }\r\n  }\r\n\r\n  async getFCMToken() {\r\n    try {\r\n      // For browser testing, create a mock token\r\n      if (!this.platform.is('cordova') && !this.platform.is('capacitor')) {\r\n        console.log('Running in browser, using mock FCM token');\r\n        this.fcmToken = 'browser-mock-token-' + Math.random().toString(36).substring(2, 15);\r\n        console.log('Mock FCM Token:', this.fcmToken);\r\n        return;\r\n      }\r\n\r\n      // For real devices, use the FCM service (preferred method)\r\n      console.log('Getting FCM token from service...');\r\n      this.fcmToken = await this.fcmService.getToken();\r\n      console.log('✅ FCM Token obtained:', this.fcmToken.substring(0, 20) + '...');\r\n\r\n    } catch (error) {\r\n      console.error('❌ Error getting FCM token from service:', error);\r\n\r\n      // Fallback: try direct FCM plugin\r\n      try {\r\n        console.log('Trying direct FCM plugin as fallback...');\r\n        this.fcmToken = await this.fcm.getToken();\r\n        console.log('✅ FCM Token from direct plugin:', this.fcmToken.substring(0, 20) + '...');\r\n      } catch (fallbackError) {\r\n        console.error('❌ All FCM token methods failed:', fallbackError);\r\n        // Continue without token - app should still work\r\n        this.fcmToken = '';\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Helper method to register a token with a specific endpoint\r\n   * @param endpoint The API endpoint to use\r\n   * @param payload The token payload to send\r\n   * @param onSuccess Callback for successful registration\r\n   * @param onError Callback for failed registration\r\n   */\r\n  registerTokenWithEndpoint(endpoint: string, payload: any, onSuccess: () => void, onError: () => void) {\r\n    // Check if this token is already registered\r\n    const storedToken = localStorage.getItem('fcm_token');\r\n    if (storedToken === this.fcmToken) {\r\n      console.log('Token already registered, skipping registration');\r\n      if (onSuccess) onSuccess();\r\n      return;\r\n    }\r\n\r\n    // Check if we're currently registering this token\r\n    if (localStorage.getItem('fcm_token_registering') === 'true') {\r\n      console.log('Token registration already in progress, skipping');\r\n      if (onSuccess) onSuccess();\r\n      return;\r\n    }\r\n\r\n    // Set a flag to indicate we're currently registering this token\r\n    localStorage.setItem('fcm_token_registering', 'true');\r\n\r\n    this.http.post(endpoint, payload).subscribe({\r\n      next: (res) => {\r\n        console.log(`FCM token registered with ${endpoint}:`, res);\r\n        // Store the token in localStorage for potential recovery\r\n        localStorage.setItem('fcm_token', this.fcmToken);\r\n        // Clear the registering flag\r\n        localStorage.removeItem('fcm_token_registering');\r\n        if (onSuccess) onSuccess();\r\n      },\r\n      error: (err) => {\r\n        console.error(`Error registering token with ${endpoint}:`, err);\r\n        // Clear the registering flag\r\n        localStorage.removeItem('fcm_token_registering');\r\n        if (onError) onError();\r\n      }\r\n    });\r\n  }\r\n\r\n  async onLogin() {\r\n    // Validate inputs\r\n    if (!this.credentials.email || !this.credentials.password) {\r\n      await this.presentAlert('Login Failed', 'Please enter both email and password.');\r\n      return;\r\n    }\r\n\r\n    console.log('🔐 Login attempt started');\r\n    console.log('📧 Email:', this.credentials.email);\r\n    console.log('🌐 API URL:', environment.apiUrl);\r\n    console.log('📱 Platform:', this.platform.is('android') ? 'Android' : this.platform.is('ios') ? 'iOS' : 'Browser');\r\n    console.log('🌍 Network status:', navigator.onLine ? 'Online' : 'Offline');\r\n    console.log('� Offline mode:', this.offlineStorage.isOfflineMode());\r\n    console.log('�🔥 FCM Token ready:', this.fcmTokenReady, 'Token:', this.fcmToken ? this.fcmToken.substring(0, 20) + '...' : 'None');\r\n\r\n    // Check if we're in offline mode or have no network connectivity\r\n    const isOfflineMode = this.offlineStorage.isOfflineMode();\r\n    const isNetworkAvailable = navigator.onLine;\r\n\r\n    if (!isNetworkAvailable || isOfflineMode) {\r\n      console.log('🔄 Attempting offline authentication...');\r\n      const offlineLoginSuccess = await this.attemptOfflineLogin();\r\n      if (offlineLoginSuccess) {\r\n        return; // Successfully logged in offline\r\n      }\r\n\r\n      if (!isNetworkAvailable) {\r\n        await this.presentOfflineAlert();\r\n        return;\r\n      }\r\n    }\r\n\r\n    // If FCM token is not ready yet, try to get it one more time (only when online)\r\n    if (isNetworkAvailable && !isOfflineMode && !this.fcmTokenReady && !this.fcmToken) {\r\n      console.log('🔥 FCM token not ready, attempting to get it now...');\r\n      try {\r\n        await this.getFCMToken();\r\n      } catch (error) {\r\n        console.warn('⚠️ Could not get FCM token, continuing without it:', error);\r\n      }\r\n    }\r\n\r\n    // Test API connectivity first (only when online and not in offline mode)\r\n    if (isNetworkAvailable && !isOfflineMode) {\r\n      console.log('🧪 Testing API connectivity...');\r\n      const backendConnected = await this.networkService.checkBackendConnectivity();\r\n      if (!backendConnected) {\r\n        await this.presentConnectionErrorAlert();\r\n        return;\r\n      }\r\n    }\r\n\r\n    this.authService.login(this.credentials).subscribe({\r\n      next: async (response) => {\r\n        console.log('✅ Login successful:', response);\r\n\r\n        // Show success alert\r\n        await this.presentSuccessAlert('Login Successful', 'Welcome, ' + response.user.full_name);\r\n\r\n        // Store the authentication token using the auth service\r\n        this.authService.setToken(response.token);\r\n\r\n        // Register the FCM token with the backend\r\n        if (this.fcmToken) {\r\n          console.log('Registering FCM token with backend:', this.fcmToken);\r\n\r\n          // Include Firebase project ID in the request\r\n          const payload: any = {\r\n            token: this.fcmToken,\r\n            device_type: this.platform.is('ios') ? 'ios' : 'android',\r\n            project_id: environment.firebase.projectId\r\n          };\r\n\r\n          // Only include user_id if it exists\r\n          if (response.user && response.user.id) {\r\n            payload.user_id = response.user.id; // Associate the token with the user\r\n          }\r\n\r\n          console.log('Token registration payload:', payload);\r\n          console.log('API URL:', `${environment.apiUrl}/device-token`);\r\n\r\n          // Use the FCM service to register the token with the user ID\r\n          this.fcmService.registerTokenWithBackend(this.fcmToken, response.user.id);\r\n\r\n          // Navigate to welcome page\r\n          this.router.navigate(['/welcome']);\r\n        } else {\r\n          console.warn('No FCM token available to register');\r\n          // If no FCM token, just navigate to welcome page\r\n          this.router.navigate(['/welcome']);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Login error:', error);\r\n        console.error('📊 Error details:', {\r\n          status: error.status,\r\n          statusText: error.statusText,\r\n          message: error.message,\r\n          url: error.url,\r\n          error: error.error\r\n        });\r\n\r\n        this.errorMessage = error.error?.message || 'Login failed';\r\n\r\n        // Show detailed alert based on error type\r\n        if (error.status === 0) {\r\n          this.presentAlert('Network Error',\r\n            'Cannot connect to the server. Please check:\\n' +\r\n            '• Your internet connection\\n' +\r\n            '• If you\\'re on the same WiFi network as the server\\n' +\r\n            '• If the backend server is running\\n\\n' +\r\n            `Server URL: ${environment.apiUrl}`);\r\n        } else if (error.status === 401) {\r\n          this.presentAlert('Login Failed', 'Invalid email or password. Please try again.');\r\n        } else if (error.status === 404) {\r\n          this.presentAlert('Server Error', 'Login endpoint not found. Please check server configuration.');\r\n        } else if (error.status >= 500) {\r\n          this.presentAlert('Server Error', 'The server encountered an error. Please try again later.');\r\n        } else {\r\n          this.presentAlert('Login Error',\r\n            `An error occurred during login (${error.status}).\\n\\n` +\r\n            'Please check the console for more details or try again later.');\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  async presentAlert(header: string, message: string) {\r\n    const alert = await this.alertController.create({\r\n      header: header,\r\n      message: message,\r\n      buttons: ['OK'],\r\n      cssClass: 'login-alert'\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async presentSuccessAlert(header: string, message: string) {\r\n    const alert = await this.alertController.create({\r\n      header: header,\r\n      message: message,\r\n      buttons: ['OK'],\r\n      cssClass: 'login-success-alert'\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async attemptOfflineLogin(): Promise<boolean> {\r\n    try {\r\n      // Check if user credentials are stored offline\r\n      const storedCredentials = localStorage.getItem('offline_credentials');\r\n      if (!storedCredentials) {\r\n        console.log('❌ No offline credentials stored');\r\n        return false;\r\n      }\r\n\r\n      const credentials = JSON.parse(storedCredentials);\r\n\r\n      // Simple credential check (in production, use proper hashing)\r\n      if (credentials.email === this.credentials.email &&\r\n          credentials.password === this.credentials.password) {\r\n\r\n        console.log('✅ Offline login successful');\r\n\r\n        // Set offline mode token\r\n        this.authService.setToken('offline_token_' + Date.now());\r\n\r\n        await this.presentSuccessAlert('Offline Login', 'Logged in using cached credentials');\r\n\r\n        // Navigate to welcome page\r\n        this.router.navigate(['/welcome']);\r\n        return true;\r\n      }\r\n\r\n      console.log('❌ Offline credentials do not match');\r\n      return false;\r\n    } catch (error) {\r\n      console.error('❌ Error during offline login:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  async presentOfflineAlert() {\r\n    const alert = await this.alertController.create({\r\n      header: 'No Internet Connection',\r\n      message: 'You are currently offline. Please check your internet connection and try again, or continue in offline mode if you have previously logged in.',\r\n      buttons: [\r\n        {\r\n          text: 'Retry',\r\n          handler: () => {\r\n            this.onLogin();\r\n          }\r\n        },\r\n        {\r\n          text: 'Continue Offline',\r\n          handler: () => {\r\n            this.attemptOfflineLogin();\r\n          }\r\n        }\r\n      ],\r\n      cssClass: 'offline-alert'\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async presentConnectionErrorAlert() {\r\n    const alert = await this.alertController.create({\r\n      header: 'Connection Error',\r\n      message: `Cannot connect to the server at ${environment.apiUrl}.\\n\\nPlease check:\\n• Your internet connection\\n• If the backend server is running\\n• If you're on the same network as the server`,\r\n      buttons: [\r\n        {\r\n          text: 'Retry',\r\n          handler: () => {\r\n            this.onLogin();\r\n          }\r\n        },\r\n        {\r\n          text: 'Network Diagnostics',\r\n          handler: () => {\r\n            this.router.navigate(['/network-diagnostics']);\r\n          }\r\n        }\r\n      ],\r\n      cssClass: 'connection-error-alert'\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n}", "\r\n\r\n<ion-content class=\"ion-padding\">\r\n  <div class=\"login-container\">\r\n\r\n\r\n\r\n      <ion-card-content>\r\n        <div class=\"login-wrapper\">\r\n          <img src=\"assets/ALERTO.png\" alt=\"App Logo\" class=\"login-logo\" />\r\n          <h1 class=\"login-title\">Log In Here!</h1>\r\n          <p></p>\r\n          <form (ngSubmit)=\"onLogin()\" class=\"login-form\">\r\n            <ion-item>\r\n              <ion-label position=\"floating\">Email:</ion-label>\r\n              <ion-input type=\"email\" [(ngModel)]=\"credentials.email\" name=\"email\" required></ion-input>\r\n            </ion-item>\r\n            <ion-item>\r\n              <ion-label position=\"floating\">Password:</ion-label>\r\n              <ion-input type=\"password\" [(ngModel)]=\"credentials.password\" name=\"password\" required></ion-input>\r\n            </ion-item>\r\n            <br><br>\r\n            <ion-button expand=\"block\" type=\"submit\" class=\"login-btn\">Log In</ion-button>\r\n          </form>\r\n\r\n          <!-- Connection Troubleshooting -->\r\n          <div class=\"troubleshooting-section\">\r\n            <ion-button\r\n              expand=\"block\"\r\n              fill=\"outline\"\r\n              color=\"warning\"\r\n              (click)=\"openEnvironmentSwitcher()\">\r\n              <ion-icon name=\"settings-outline\" slot=\"start\"></ion-icon>\r\n              Switch API Endpoint\r\n            </ion-button>\r\n\r\n            <ion-button\r\n              expand=\"block\"\r\n              fill=\"clear\"\r\n              size=\"small\"\r\n              (click)=\"openNetworkDiagnostics()\">\r\n              <ion-icon name=\"bug-outline\" slot=\"start\"></ion-icon>\r\n              Network Diagnostics\r\n            </ion-button>\r\n          </div>\r\n\r\n          <!-- Debug button for troubleshooting -->\r\n          <ion-button expand=\"block\" fill=\"outline\" color=\"secondary\" (click)=\"openNetworkDiagnostics()\">\r\n            🔧 Network Diagnostics\r\n          </ion-button>\r\n\r\n          <div class=\"login-link\">\r\n            Don't have an account?\r\n            <a (click)=\"goToRegister()\"><strong><u>Sign Up</u></strong></a>\r\n          </div>\r\n        </div>\r\n      </ion-card-content>\r\n\r\n  </div>\r\n</ion-content>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUM,IAAO,iBAAP,MAAO,gBAAc;EAEzB,YACU,MACA,UACA,WAA0B;AAF1B,SAAA,OAAA;AACA,SAAA,WAAA;AACA,SAAA,YAAA;EACP;;;;EAKG,2BAAwB;;AAC5B,UAAI;AACF,gBAAQ,IAAI,qCAAqC,YAAY,MAAM;AAGnE,cAAM,eAAe,MAAM,eACzB,KAAK,KAAK,IAAI,GAAG,YAAY,OAAO,QAAQ,QAAQ,EAAE,CAAC,WAAW,EAAE,KAClE,QAAQ,GAAK,CAAC,CACf;AAEH,gBAAQ,IAAI,qCAAqC,YAAY;AAG7D,cAAM,WAAW,MAAM,eACrB,KAAK,KAAK,IAAI,GAAG,YAAY,MAAM,qBAAqB,EAAE,KACxD,QAAQ,GAAK,CAAC,CACf;AAEH,gBAAQ,IAAI,uCAAuC;AACnD,eAAO;MACT,SAAS,OAAY;AACnB,gBAAQ,MAAM,sCAAsC,KAAK;AACzD,gBAAQ,MAAM,kBAAkB;UAC9B,QAAQ,MAAM;UACd,YAAY,MAAM;UAClB,KAAK,MAAM;UACX,SAAS,MAAM;SAChB;AAED,YAAI,MAAM,WAAW,GAAG;AAUtB,kBAAQ,IAAI,uDAAuD;QACrE,OAAO;AACL,kBAAQ,IAAI,+CAA+C,MAAM,MAAM;AACvE,iBAAO;QACT;AAEA,eAAO;MACT;IACF;;;;;EAKM,0BAAuB;;AAC3B,UAAI;AACF,gBAAQ,IAAI,iCAAiC;AAG7C,cAAM,UAAU,sGAAsG,YAAY,iBAAiB;AAEnJ,cAAM,WAAW,MAAM,eACrB,KAAK,KAAK,IAAI,OAAO,CAAC;AAGxB,gBAAQ,IAAI,sCAAsC;AAClD,eAAO;MACT,SAAS,OAAY;AACnB,gBAAQ,MAAM,qCAAqC,KAAK;AAExD,YAAI,MAAM,WAAW,GAAG;AACtB,eAAK,sBAAsB,UAAU,sEAAsE;QAC7G,WAAW,MAAM,WAAW,KAAK;AAC/B,eAAK,sBAAsB,UAAU,uDAAuD;QAC9F,WAAW,MAAM,WAAW,KAAK;AAC/B,eAAK,sBAAsB,UAAU,4DAA4D;QACnG,WAAW,MAAM,WAAW,KAAK;AAC/B,eAAK,sBAAsB,UAAU,yDAAyD;QAChG;AAEA,eAAO;MACT;IACF;;;;;EAKM,2BAAwB;;AAC5B,cAAQ,IAAI,sDAAsD;AAElE,YAAM,UAAU;QACd,SAAS;QACT,SAAS;;AAIX,cAAQ,UAAU,MAAM,KAAK,yBAAwB;AAGrD,cAAQ,UAAU,MAAM,KAAK,wBAAuB;AAEpD,cAAQ,IAAI,uCAAuC,OAAO;AAC1D,aAAO;IACT;;;;;EAKc,sBAAsB,SAAiB,SAAe;;AAClE,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC,QAAQ,GAAG,OAAO;QAClB;QACA,UAAU;QACV,OAAO;QACP,SAAS;UACP;YACE,MAAM;YACN,MAAM;;;OAGX;AACD,YAAM,MAAM,QAAO;IACrB;;;;;EAKM,WAAQ;;AACZ,UAAI;AAEF,cAAM,WAAW,MAAM,eACrB,KAAK,KAAK,IAAI,yBAAyB,CAAC;AAE1C,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,qBAAqB,KAAK;AACxC,eAAO;MACT;IACF;;;;;EAKA,iBAAc;AACZ,QAAI,KAAK,SAAS,GAAG,WAAW,GAAG;AAGjC,aAAO;QACL,UAAU;QACV,WAAW,UAAU;;IAEzB,OAAO;AACL,aAAO;QACL,UAAU;QACV,QAAQ,UAAU;QAClB,WAAW,UAAU;;IAEzB;EACF;;;uCAvKW,iBAAc,mBAAA,UAAA,GAAA,mBAAA,QAAA,GAAA,mBAAA,eAAA,CAAA;IAAA;EAAA;;4EAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;EAAA;;;sEAEP,gBAAc,CAAA;UAH1B;WAAW;MACV,YAAY;KACb;;;;;ACUK,IAAO,YAAP,MAAO,WAAS;;EAUpB,eAAY;AACV,SAAK,OAAO,SAAS,CAAC,WAAW,CAAC;EACpC;EAEA,yBAAsB;AACpB,SAAK,OAAO,SAAS,CAAC,sBAAsB,CAAC;EAC/C;EAEA,0BAAuB;AACrB,SAAK,OAAO,SAAS,CAAC,uBAAuB,CAAC;EAChD;EAEA,YACU,QACA,aACA,KACA,MACA,iBACA,UACA,YACA,gBACA,gBAA8B;AAR9B,SAAA,SAAA;AACA,SAAA,cAAA;AACA,SAAA,MAAA;AACA,SAAA,OAAA;AACA,SAAA,kBAAA;AACA,SAAA,WAAA;AACA,SAAA,aAAA;AACA,SAAA,iBAAA;AACA,SAAA,iBAAA;AA9BV,SAAA,cAAc;MACZ,OAAO;MACP,UAAU;;AAEZ,SAAA,eAAe;AACf,SAAA,WAAmB;AACX,SAAA,gBAAgB;EAyBrB;EAEG,WAAQ;;AACZ,cAAQ,IAAI,sCAA+B;AAE3C,YAAM,KAAK,cAAa;IAC1B;;;;;EAKM,gBAAa;;AACjB,UAAI;AACF,gBAAQ,IAAI,yCAAkC;AAG9C,cAAM,KAAK,WAAW,SAAQ;AAG9B,cAAM,KAAK,YAAW;AAEtB,gBAAQ,IAAI,oDAA+C,CAAC,CAAC,KAAK,QAAQ;AAC1E,aAAK,gBAAgB;MACvB,SAAS,OAAO;AACd,gBAAQ,MAAM,qCAAgC,KAAK;AAEnD,aAAK,gBAAgB;MACvB;IACF;;EAEM,cAAW;;AACf,UAAI;AAEF,YAAI,CAAC,KAAK,SAAS,GAAG,SAAS,KAAK,CAAC,KAAK,SAAS,GAAG,WAAW,GAAG;AAClE,kBAAQ,IAAI,0CAA0C;AACtD,eAAK,WAAW,wBAAwB,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAClF,kBAAQ,IAAI,mBAAmB,KAAK,QAAQ;AAC5C;QACF;AAGA,gBAAQ,IAAI,mCAAmC;AAC/C,aAAK,WAAW,MAAM,KAAK,WAAW,SAAQ;AAC9C,gBAAQ,IAAI,8BAAyB,KAAK,SAAS,UAAU,GAAG,EAAE,IAAI,KAAK;MAE7E,SAAS,OAAO;AACd,gBAAQ,MAAM,gDAA2C,KAAK;AAG9D,YAAI;AACF,kBAAQ,IAAI,yCAAyC;AACrD,eAAK,WAAW,MAAM,KAAK,IAAI,SAAQ;AACvC,kBAAQ,IAAI,wCAAmC,KAAK,SAAS,UAAU,GAAG,EAAE,IAAI,KAAK;QACvF,SAAS,eAAe;AACtB,kBAAQ,MAAM,wCAAmC,aAAa;AAE9D,eAAK,WAAW;QAClB;MACF;IACF;;;;;;;;;EASA,0BAA0B,UAAkB,SAAc,WAAuB,SAAmB;AAElG,UAAM,cAAc,aAAa,QAAQ,WAAW;AACpD,QAAI,gBAAgB,KAAK,UAAU;AACjC,cAAQ,IAAI,iDAAiD;AAC7D,UAAI;AAAW,kBAAS;AACxB;IACF;AAGA,QAAI,aAAa,QAAQ,uBAAuB,MAAM,QAAQ;AAC5D,cAAQ,IAAI,kDAAkD;AAC9D,UAAI;AAAW,kBAAS;AACxB;IACF;AAGA,iBAAa,QAAQ,yBAAyB,MAAM;AAEpD,SAAK,KAAK,KAAK,UAAU,OAAO,EAAE,UAAU;MAC1C,MAAM,CAAC,QAAO;AACZ,gBAAQ,IAAI,6BAA6B,QAAQ,KAAK,GAAG;AAEzD,qBAAa,QAAQ,aAAa,KAAK,QAAQ;AAE/C,qBAAa,WAAW,uBAAuB;AAC/C,YAAI;AAAW,oBAAS;MAC1B;MACA,OAAO,CAAC,QAAO;AACb,gBAAQ,MAAM,gCAAgC,QAAQ,KAAK,GAAG;AAE9D,qBAAa,WAAW,uBAAuB;AAC/C,YAAI;AAAS,kBAAO;MACtB;KACD;EACH;EAEM,UAAO;;AAEX,UAAI,CAAC,KAAK,YAAY,SAAS,CAAC,KAAK,YAAY,UAAU;AACzD,cAAM,KAAK,aAAa,gBAAgB,uCAAuC;AAC/E;MACF;AAEA,cAAQ,IAAI,iCAA0B;AACtC,cAAQ,IAAI,oBAAa,KAAK,YAAY,KAAK;AAC/C,cAAQ,IAAI,sBAAe,YAAY,MAAM;AAC7C,cAAQ,IAAI,uBAAgB,KAAK,SAAS,GAAG,SAAS,IAAI,YAAY,KAAK,SAAS,GAAG,KAAK,IAAI,QAAQ,SAAS;AACjH,cAAQ,IAAI,6BAAsB,UAAU,SAAS,WAAW,SAAS;AACzE,cAAQ,IAAI,wBAAmB,KAAK,eAAe,cAAa,CAAE;AAClE,cAAQ,IAAI,oCAAwB,KAAK,eAAe,UAAU,KAAK,WAAW,KAAK,SAAS,UAAU,GAAG,EAAE,IAAI,QAAQ,MAAM;AAGjI,YAAM,gBAAgB,KAAK,eAAe,cAAa;AACvD,YAAM,qBAAqB,UAAU;AAErC,UAAI,CAAC,sBAAsB,eAAe;AACxC,gBAAQ,IAAI,gDAAyC;AACrD,cAAM,sBAAsB,MAAM,KAAK,oBAAmB;AAC1D,YAAI,qBAAqB;AACvB;QACF;AAEA,YAAI,CAAC,oBAAoB;AACvB,gBAAM,KAAK,oBAAmB;AAC9B;QACF;MACF;AAGA,UAAI,sBAAsB,CAAC,iBAAiB,CAAC,KAAK,iBAAiB,CAAC,KAAK,UAAU;AACjF,gBAAQ,IAAI,4DAAqD;AACjE,YAAI;AACF,gBAAM,KAAK,YAAW;QACxB,SAAS,OAAO;AACd,kBAAQ,KAAK,gEAAsD,KAAK;QAC1E;MACF;AAGA,UAAI,sBAAsB,CAAC,eAAe;AACxC,gBAAQ,IAAI,uCAAgC;AAC5C,cAAM,mBAAmB,MAAM,KAAK,eAAe,yBAAwB;AAC3E,YAAI,CAAC,kBAAkB;AACrB,gBAAM,KAAK,4BAA2B;AACtC;QACF;MACF;AAEA,WAAK,YAAY,MAAM,KAAK,WAAW,EAAE,UAAU;QACjD,MAAM,CAAO,aAAY;AACvB,kBAAQ,IAAI,4BAAuB,QAAQ;AAG3C,gBAAM,KAAK,oBAAoB,oBAAoB,cAAc,SAAS,KAAK,SAAS;AAGxF,eAAK,YAAY,SAAS,SAAS,KAAK;AAGxC,cAAI,KAAK,UAAU;AACjB,oBAAQ,IAAI,uCAAuC,KAAK,QAAQ;AAGhE,kBAAM,UAAe;cACnB,OAAO,KAAK;cACZ,aAAa,KAAK,SAAS,GAAG,KAAK,IAAI,QAAQ;cAC/C,YAAY,YAAY,SAAS;;AAInC,gBAAI,SAAS,QAAQ,SAAS,KAAK,IAAI;AACrC,sBAAQ,UAAU,SAAS,KAAK;YAClC;AAEA,oBAAQ,IAAI,+BAA+B,OAAO;AAClD,oBAAQ,IAAI,YAAY,GAAG,YAAY,MAAM,eAAe;AAG5D,iBAAK,WAAW,yBAAyB,KAAK,UAAU,SAAS,KAAK,EAAE;AAGxE,iBAAK,OAAO,SAAS,CAAC,UAAU,CAAC;UACnC,OAAO;AACL,oBAAQ,KAAK,oCAAoC;AAEjD,iBAAK,OAAO,SAAS,CAAC,UAAU,CAAC;UACnC;QACF;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,uBAAkB,KAAK;AACrC,kBAAQ,MAAM,4BAAqB;YACjC,QAAQ,MAAM;YACd,YAAY,MAAM;YAClB,SAAS,MAAM;YACf,KAAK,MAAM;YACX,OAAO,MAAM;WACd;AAED,eAAK,eAAe,MAAM,OAAO,WAAW;AAG5C,cAAI,MAAM,WAAW,GAAG;AACtB,iBAAK,aAAa,iBAChB;;;;;cAIe,YAAY,MAAM,EAAE;UACvC,WAAW,MAAM,WAAW,KAAK;AAC/B,iBAAK,aAAa,gBAAgB,8CAA8C;UAClF,WAAW,MAAM,WAAW,KAAK;AAC/B,iBAAK,aAAa,gBAAgB,8DAA8D;UAClG,WAAW,MAAM,UAAU,KAAK;AAC9B,iBAAK,aAAa,gBAAgB,0DAA0D;UAC9F,OAAO;AACL,iBAAK,aAAa,eAChB,mCAAmC,MAAM,MAAM;;8DACgB;UACnE;QACF;OACD;IACH;;EAEM,aAAa,QAAgB,SAAe;;AAChD,YAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;QAC9C;QACA;QACA,SAAS,CAAC,IAAI;QACd,UAAU;OACX;AAED,YAAM,MAAM,QAAO;IACrB;;EAEM,oBAAoB,QAAgB,SAAe;;AACvD,YAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;QAC9C;QACA;QACA,SAAS,CAAC,IAAI;QACd,UAAU;OACX;AAED,YAAM,MAAM,QAAO;IACrB;;EAEM,sBAAmB;;AACvB,UAAI;AAEF,cAAM,oBAAoB,aAAa,QAAQ,qBAAqB;AACpE,YAAI,CAAC,mBAAmB;AACtB,kBAAQ,IAAI,sCAAiC;AAC7C,iBAAO;QACT;AAEA,cAAM,cAAc,KAAK,MAAM,iBAAiB;AAGhD,YAAI,YAAY,UAAU,KAAK,YAAY,SACvC,YAAY,aAAa,KAAK,YAAY,UAAU;AAEtD,kBAAQ,IAAI,iCAA4B;AAGxC,eAAK,YAAY,SAAS,mBAAmB,KAAK,IAAG,CAAE;AAEvD,gBAAM,KAAK,oBAAoB,iBAAiB,oCAAoC;AAGpF,eAAK,OAAO,SAAS,CAAC,UAAU,CAAC;AACjC,iBAAO;QACT;AAEA,gBAAQ,IAAI,yCAAoC;AAChD,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,sCAAiC,KAAK;AACpD,eAAO;MACT;IACF;;EAEM,sBAAmB;;AACvB,YAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;QAC9C,QAAQ;QACR,SAAS;QACT,SAAS;UACP;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,QAAO;YACd;;UAEF;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,oBAAmB;YAC1B;;;QAGJ,UAAU;OACX;AAED,YAAM,MAAM,QAAO;IACrB;;EAEM,8BAA2B;;AAC/B,YAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;QAC9C,QAAQ;QACR,SAAS,mCAAmC,YAAY,MAAM;;;;;;QAC9D,SAAS;UACP;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,QAAO;YACd;;UAEF;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,OAAO,SAAS,CAAC,sBAAsB,CAAC;YAC/C;;;QAGJ,UAAU;OACX;AAED,YAAM,MAAM,QAAO;IACrB;;;;uCA/WW,YAAS,4BAAA,MAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,GAAA,GAAA,4BAAA,UAAA,GAAA,4BAAA,eAAA,GAAA,4BAAA,QAAA,GAAA,4BAAA,UAAA,GAAA,4BAAA,qBAAA,GAAA,4BAAA,cAAA,CAAA;IAAA;EAAA;;yEAAT,YAAS,WAAA,CAAA,CAAA,WAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,OAAA,qBAAA,OAAA,YAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,UAAA,GAAA,CAAA,YAAA,UAAA,GAAA,CAAA,QAAA,SAAA,QAAA,SAAA,YAAA,IAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,YAAA,QAAA,YAAA,YAAA,IAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,UAAA,SAAA,QAAA,UAAA,GAAA,WAAA,GAAA,CAAA,GAAA,yBAAA,GAAA,CAAA,UAAA,SAAA,QAAA,WAAA,SAAA,WAAA,GAAA,OAAA,GAAA,CAAA,QAAA,oBAAA,QAAA,OAAA,GAAA,CAAA,UAAA,SAAA,QAAA,SAAA,QAAA,SAAA,GAAA,OAAA,GAAA,CAAA,QAAA,eAAA,QAAA,OAAA,GAAA,CAAA,UAAA,SAAA,QAAA,WAAA,SAAA,aAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,mBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACjBtB,QAAA,yBAAA,GAAA,eAAA,CAAA,EAAiC,GAAA,OAAA,CAAA,EACF,GAAA,kBAAA,EAIP,GAAA,OAAA,CAAA;AAEd,QAAA,oBAAA,GAAA,OAAA,CAAA;AACA,QAAA,yBAAA,GAAA,MAAA,CAAA;AAAwB,QAAA,iBAAA,GAAA,cAAA;AAAY,QAAA,uBAAA;AACpC,QAAA,oBAAA,GAAA,GAAA;AACA,QAAA,yBAAA,GAAA,QAAA,CAAA;AAAM,QAAA,qBAAA,YAAA,SAAA,8CAAA;AAAA,iBAAY,IAAA,QAAA;QAAS,CAAA;AACzB,QAAA,yBAAA,GAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACuB,QAAA,iBAAA,IAAA,QAAA;AAAM,QAAA,uBAAA;AACrC,QAAA,yBAAA,IAAA,aAAA,CAAA;AAAwB,QAAA,2BAAA,iBAAA,SAAA,uDAAA,QAAA;AAAA,UAAA,6BAAA,IAAA,YAAA,OAAA,MAAA,MAAA,IAAA,YAAA,QAAA;AAAA,iBAAA;QAAA,CAAA;AAAsD,QAAA,uBAAA,EAAY;AAE5F,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACuB,QAAA,iBAAA,IAAA,WAAA;AAAS,QAAA,uBAAA;AACxC,QAAA,yBAAA,IAAA,aAAA,CAAA;AAA2B,QAAA,2BAAA,iBAAA,SAAA,uDAAA,QAAA;AAAA,UAAA,6BAAA,IAAA,YAAA,UAAA,MAAA,MAAA,IAAA,YAAA,WAAA;AAAA,iBAAA;QAAA,CAAA;AAA4D,QAAA,uBAAA,EAAY;AAErG,QAAA,oBAAA,IAAA,IAAA,EAAI,IAAA,IAAA;AACJ,QAAA,yBAAA,IAAA,cAAA,CAAA;AAA2D,QAAA,iBAAA,IAAA,QAAA;AAAM,QAAA,uBAAA,EAAa;AAIhF,QAAA,yBAAA,IAAA,OAAA,EAAA,EAAqC,IAAA,cAAA,EAAA;AAKjC,QAAA,qBAAA,SAAA,SAAA,kDAAA;AAAA,iBAAS,IAAA,wBAAA;QAAyB,CAAA;AAClC,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,iBAAA,IAAA,uBAAA;AACF,QAAA,uBAAA;AAEA,QAAA,yBAAA,IAAA,cAAA,EAAA;AAIE,QAAA,qBAAA,SAAA,SAAA,kDAAA;AAAA,iBAAS,IAAA,uBAAA;QAAwB,CAAA;AACjC,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,iBAAA,IAAA,uBAAA;AACF,QAAA,uBAAA,EAAa;AAIf,QAAA,yBAAA,IAAA,cAAA,EAAA;AAA4D,QAAA,qBAAA,SAAA,SAAA,kDAAA;AAAA,iBAAS,IAAA,uBAAA;QAAwB,CAAA;AAC3F,QAAA,iBAAA,IAAA,iCAAA;AACF,QAAA,uBAAA;AAEA,QAAA,yBAAA,IAAA,OAAA,EAAA;AACE,QAAA,iBAAA,IAAA,0BAAA;AACA,QAAA,yBAAA,IAAA,KAAA,EAAA;AAAG,QAAA,qBAAA,SAAA,SAAA,yCAAA;AAAA,iBAAS,IAAA,aAAA;QAAc,CAAA;AAAE,QAAA,yBAAA,IAAA,QAAA,EAAQ,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,SAAA;AAAO,QAAA,uBAAA,EAAI,EAAS,EAAI,EAC3D,EACF,EACW,EAEjB;;;AA3C8B,QAAA,oBAAA,EAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,YAAA,KAAA;AAIG,QAAA,oBAAA,CAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,YAAA,QAAA;;sBDL7B,aAAW,WAAA,gBAAA,YAAA,SAAA,UAAA,SAAA,UAAA,4BAAE,aAAW,oBAAA,iBAAA,sBAAA,mBAAA,SAAA,MAAA,GAAA,QAAA,CAAA,mjFAAA,EAAA,CAAA;EAAA;;;sEAKvB,WAAS,CAAA;UAPrB;yBACa,MAAI,SACP,CAAC,aAAa,WAAW,GAAC,UACzB,aAAW,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iBAAA,QAAA,CAAA,s5DAAA,EAAA,CAAA;;;;6EAIV,WAAS,EAAA,WAAA,aAAA,UAAA,qCAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
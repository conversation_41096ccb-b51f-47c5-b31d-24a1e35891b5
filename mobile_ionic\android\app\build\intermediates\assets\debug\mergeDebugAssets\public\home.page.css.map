{"version": 3, "sources": ["src/app/pages/home/<USER>"], "sourcesContent": [".status-text {\r\n  margin-left: 8px;\r\n}\r\n\r\nion-header, ion-title {\r\n  text-align: center;\r\n  font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  letter-spacing: 1px;\r\n  text-shadow: 1px 2px 4px #ccc;\r\n}\r\n\r\n.disaster-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 24px;\r\n  margin: 32px 0 0 0;\r\n}\r\n\r\n.disaster {\r\n  margin: 0;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n  }\r\n\r\n  ion-card-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    text-align: center;\r\n    padding: 16px;\r\n  }\r\n\r\n  img {\r\n    width: 60px;\r\n    height: 60px;\r\n    margin-bottom: 8px;\r\n  }\r\n}\r\n\r\n.earthquake {\r\n  --background: #ffcc80;\r\n}\r\n\r\n.typhoon {\r\n  --background: #c5e1a5;\r\n  size: 100px;\r\n  width: 105px;\r\n  height: 120px;\r\n}\r\n\r\n.flood {\r\n  --background: #81d4fa;\r\n}\r\n\r\n.view-map {\r\n  margin-top: 24px;\r\n  --background: #00bfff;\r\n\r\n  &:hover {\r\n    --background: #0090cc;\r\n  }\r\n\r\n  &[disabled] {\r\n    --background: #999;\r\n  }\r\n}\r\n\r\n.top-disaster {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 16px;\r\n  align-items: center;\r\n\r\n\r\n}\r\n  .home-logo {\r\n    width: 150px;\r\n    height: 150px;\r\n\r\n\r\n  }\r\n    .home-title {\r\n      padding-top: 105px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 30px;\r\n      font-weight: 700;\r\n      letter-spacing: 1px;\r\n      text-shadow: 1px 2px 4px #ccc;\r\n    }\r\n\r\n.notifications-section {\r\n  margin-top: 20px;\r\n  border-top: 1px solid var(--ion-color-light);\r\n  padding-top: 10px;\r\n}\r\n\r\nion-item-divider {\r\n  --background: transparent;\r\n  --color: var(--ion-color-primary);\r\n  font-weight: bold;\r\n  font-size: 1.1rem;\r\n  letter-spacing: 0.5px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n// Notification button styles\r\n.notification-button {\r\n  position: relative;\r\n}\r\n\r\n.notification-badge {\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 8px;\r\n  background: #e41e3f;\r\n  color: white;\r\n  font-size: 10px;\r\n  font-weight: 600;\r\n  min-width: 16px;\r\n  height: 16px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 10;\r\n}"], "mappings": ";AAAA,CAAA;AACE,eAAA;;AAGF;AAAA;AACE,cAAA;AACA;IAAA,SAAA;IAAA,KAAA;IAAA;AACA,aAAA;AACA,eAAA;AACA,kBAAA;AACA,eAAA,IAAA,IAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA,KAAA,EAAA,EAAA;;AAGF,CAAA;AACE,UAAA;AACA,UAAA;AACA,cAAA,UAAA;;AAEA,CALF,QAKE;AACE,aAAA,MAAA;;AAGF,CATF,SASE;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,cAAA;AACA,WAAA;;AAGF,CAjBF,SAiBE;AACE,SAAA;AACA,UAAA;AACA,iBAAA;;AAIJ,CAAA;AACE,gBAAA;;AAGF,CAAA;AACE,gBAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAAA;AACE,gBAAA;;AAGF,CAAA;AACE,cAAA;AACA,gBAAA;;AAEA,CAJF,QAIE;AACE,gBAAA;;AAGF,CARF,QAQE,CAAA;AACE,gBAAA;;AAIJ,CAAA;AACE,WAAA;AACA,mBAAA;AACA,OAAA;AACA,eAAA;;AAIA,CAAA;AACE,SAAA;AACA,UAAA;;AAIA,CAAA;AACE,eAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,kBAAA;AACA,eAAA,IAAA,IAAA,IAAA;;AAGN,CAAA;AACE,cAAA;AACA,cAAA,IAAA,MAAA,IAAA;AACA,eAAA;;AAGF;AACE,gBAAA;AACA,WAAA,IAAA;AACA,eAAA;AACA,aAAA;AACA,kBAAA;AACA,iBAAA;;AAIF,CAAA;AACE,YAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,cAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;AACA,aAAA;AACA,UAAA;AACA,iBAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;;", "names": []}
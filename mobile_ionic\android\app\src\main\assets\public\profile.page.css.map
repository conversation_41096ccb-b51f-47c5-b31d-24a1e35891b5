{"version": 3, "sources": ["src/app/pages/profile/profile.page.scss"], "sourcesContent": ["ion-header {\r\n  background: var(--ion-color-primary);\r\n  \r\n  ion-toolbar {\r\n    --background: transparent;\r\n    \r\n    ion-title {\r\n      color: white;\r\n    }\r\n\r\n    ion-back-button {\r\n      --color: white;\r\n    }\r\n  }\r\n}\r\n\r\n.profile-header {\r\n  position: relative;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.profile-background {\r\n  background: linear-gradient(135deg, #03b2dd 0%, #0891b2 100%);\r\n  height: 200px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  // Add subtle pattern overlay\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: url('data:image/svg+xml,<svg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"><g fill=\"none\" fill-rule=\"evenodd\"><g fill=\"%23ffffff\" fill-opacity=\"0.05\"><circle cx=\"30\" cy=\"30\" r=\"2\"/></g></svg>');\r\n    pointer-events: none;\r\n  }\r\n}\r\n\r\n.profile-avatar {\r\n  width: 80px;\r\n  height: 80px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 16px;\r\n  backdrop-filter: blur(10px);\r\n  border: 3px solid rgba(255, 255, 255, 0.3);\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n\r\n  .avatar-icon {\r\n    font-size: 40px;\r\n    color: white;\r\n  }\r\n}\r\n\r\n.profile-info {\r\n  text-align: center;\r\n  color: white;\r\n\r\n  h2 {\r\n    margin: 0 0 8px 0;\r\n    font-size: 24px;\r\n    font-weight: 600;\r\n    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  p {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    opacity: 0.9;\r\n    font-weight: 400;\r\n  }\r\n}\r\n\r\n.menu-list {\r\n  background: transparent;\r\n  margin-top: 20px;\r\n  padding: 0 16px;\r\n\r\n  ion-item {\r\n    --padding-start: 16px;\r\n    --padding-end: 16px;\r\n    --min-height: 60px;\r\n    margin-bottom: 8px;\r\n    border-radius: 12px;\r\n    --background: white;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\r\n    }\r\n\r\n    ion-icon {\r\n      font-size: 20px;\r\n      margin-right: 16px;\r\n      color: var(--ion-color-primary);\r\n    }\r\n\r\n    ion-label {\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n      color: var(--ion-color-dark);\r\n    }\r\n\r\n    img {\r\n      filter: brightness(0) saturate(100%) invert(34%) sepia(77%) saturate(2476%) hue-rotate(203deg) brightness(99%) contrast(92%);\r\n    }\r\n  }\r\n}\r\n\r\n// Modal Styles\r\n.terms-modal {\r\n  --height: 90%;\r\n  --border-radius: 16px;\r\n\r\n  ion-header {\r\n    ion-toolbar {\r\n      --background: var(--ion-color-light);\r\n\r\n      ion-title {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n      }\r\n    }\r\n  }\r\n\r\n  ion-content {\r\n    h2 {\r\n      color: var(--ion-color-dark);\r\n      font-size: 24px;\r\n      font-weight: 700;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .effective-date {\r\n      color: var(--ion-color-medium);\r\n      font-size: 14px;\r\n      margin-bottom: 24px;\r\n    }\r\n\r\n    h3 {\r\n      color: var(--ion-color-dark);\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      margin: 24px 0 12px;\r\n    }\r\n\r\n    p {\r\n      color: var(--ion-color-medium);\r\n      font-size: 16px;\r\n      line-height: 1.5;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    ul {\r\n      list-style: none;\r\n      padding: 0;\r\n      margin: 0;\r\n\r\n      li {\r\n        color: var(--ion-color-medium);\r\n        font-size: 16px;\r\n        line-height: 1.5;\r\n        margin-bottom: 8px;\r\n        padding-left: 24px;\r\n        position: relative;\r\n\r\n        &:before {\r\n          content: \"•\";\r\n          position: absolute;\r\n          left: 8px;\r\n          color: var(--ion-color-primary);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .legend-title {\r\n    color: var(--ion-color-dark);\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    margin-bottom: 24px;\r\n  }\r\n\r\n  .legend-container {\r\n    margin-top: 30px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n\r\n    .legend-items {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 16px;\r\n      background: var(--ion-color-light);\r\n      border-radius: 8px;\r\n\r\n      .icon-wrapper {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        width: 32px;\r\n        height: 32px;\r\n\r\n        .legend-icon {\r\n          font-size: 20px;\r\n        }\r\n      }\r\n\r\n      .legend-label {\r\n        color: var(--ion-color-dark);\r\n        font-size: 16px;\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA;AACE,cAAA,IAAA;;AAEA,WAAA;AACE,gBAAA;;AAEA,WAAA,YAAA;AACE,SAAA;;AAGF,WAAA,YAAA;AACE,WAAA;;AAKN,CAAA;AACE,YAAA;AACA,iBAAA;;AAGF,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACA,UAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,YAAA;AACA,YAAA;;AAGA,CAXF,kBAWE;AACE,WAAA;AACA,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA;AACA,kBAAA;;AAIJ,CAAA;AACE,SAAA;AACA,UAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,iBAAA;AACA,2BAAA,KAAA;AAAA,mBAAA,KAAA;AACA,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CAbF,eAaE,CAAA;AACE,aAAA;AACA,SAAA;;AAIJ,CAAA;AACE,cAAA;AACA,SAAA;;AAEA,CAJF,aAIE;AACE,UAAA,EAAA,EAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAXF,aAWE;AACE,UAAA;AACA,aAAA;AACA,WAAA;AACA,eAAA;;AAIJ,CAAA;AACE,cAAA;AACA,cAAA;AACA,WAAA,EAAA;;AAEA,CALF,UAKE;AACE,mBAAA;AACA,iBAAA;AACA,gBAAA;AACA,iBAAA;AACA,iBAAA;AACA,gBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CAfJ,UAeI,QAAA;AACE,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CApBJ,UAoBI,SAAA;AACE,aAAA;AACA,gBAAA;AACA,SAAA,IAAA;;AAGF,CA1BJ,UA0BI,SAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA,IAAA;;AAGF,CAhCJ,UAgCI,SAAA;AACE,UAAA,WAAA,GAAA,SAAA,MAAA,OAAA,KAAA,MAAA,KAAA,SAAA,OAAA,WAAA,QAAA,WAAA,KAAA,SAAA;;AAMN,CAAA;AACE,YAAA;AACA,mBAAA;;AAGE,CALJ,YAKI,WAAA;AACE,gBAAA,IAAA;;AAEA,CARN,YAQM,WAAA,YAAA;AACE,aAAA;AACA,eAAA;;AAMJ,CAhBJ,YAgBI,YAAA;AACE,SAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CAvBJ,YAuBI,YAAA,CAAA;AACE,SAAA,IAAA;AACA,aAAA;AACA,iBAAA;;AAGF,CA7BJ,YA6BI,YAAA;AACE,SAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,UAAA,KAAA,EAAA;;AAGF,CApCJ,YAoCI,YAAA;AACE,SAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CA3CJ,YA2CI,YAAA;AACE,cAAA;AACA,WAAA;AACA,UAAA;;AAEA,CAhDN,YAgDM,YAAA,GAAA;AACE,SAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA;AACA,gBAAA;AACA,YAAA;;AAEA,CAxDR,YAwDQ,YAAA,GAAA,EAAA;AACE,WAAA;AACA,YAAA;AACA,QAAA;AACA,SAAA,IAAA;;AAMR,CAlEF,YAkEE,CAAA;AACE,SAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CAzEF,YAyEE,CAAA;AACE,cAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;;AAEA,CA/EJ,YA+EI,CANF,iBAME,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,cAAA,IAAA;AACA,iBAAA;;AAEA,CAtFN,YAsFM,CAbJ,iBAaI,CAPF,aAOE,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,SAAA;AACA,UAAA;;AAEA,CA7FR,YA6FQ,CApBN,iBAoBM,CAdJ,aAcI,CAPF,aAOE,CAAA;AACE,aAAA;;AAIJ,CAlGN,YAkGM,CAzBJ,iBAyBI,CAnBF,aAmBE,CAAA;AACE,SAAA,IAAA;AACA,aAAA;;", "names": []}
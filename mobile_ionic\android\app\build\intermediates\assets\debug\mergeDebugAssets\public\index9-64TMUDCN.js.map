{"version": 3, "sources": ["node_modules/@ionic/core/components/index9.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index6.js';\nimport { q as pointerCoord } from './helpers.js';\nconst startTapClick = config => {\n  if (doc === undefined) {\n    return;\n  }\n  let lastActivated = 0;\n  let activatableEle;\n  let activeRipple;\n  let activeDefer;\n  const useRippleEffect = config.getBoolean('animated', true) && config.getBoolean('rippleEffect', true);\n  const clearDefers = new WeakMap();\n  const cancelActive = () => {\n    if (activeDefer) clearTimeout(activeDefer);\n    activeDefer = undefined;\n    if (activatableEle) {\n      removeActivated(false);\n      activatableEle = undefined;\n    }\n  };\n  const pointerDown = ev => {\n    // Ignore right clicks\n    if (activatableEle || ev.button === 2) {\n      return;\n    }\n    setActivatedElement(getActivatableTarget(ev), ev);\n  };\n  const pointerUp = ev => {\n    setActivatedElement(undefined, ev);\n  };\n  const setActivatedElement = (el, ev) => {\n    // do nothing\n    if (el && el === activatableEle) {\n      return;\n    }\n    if (activeDefer) clearTimeout(activeDefer);\n    activeDefer = undefined;\n    const {\n      x,\n      y\n    } = pointerCoord(ev);\n    // deactivate selected\n    if (activatableEle) {\n      if (clearDefers.has(activatableEle)) {\n        throw new Error('internal error');\n      }\n      if (!activatableEle.classList.contains(ACTIVATED)) {\n        addActivated(activatableEle, x, y);\n      }\n      removeActivated(true);\n    }\n    // activate\n    if (el) {\n      const deferId = clearDefers.get(el);\n      if (deferId) {\n        clearTimeout(deferId);\n        clearDefers.delete(el);\n      }\n      el.classList.remove(ACTIVATED);\n      const callback = () => {\n        addActivated(el, x, y);\n        activeDefer = undefined;\n      };\n      if (isInstant(el)) {\n        callback();\n      } else {\n        activeDefer = setTimeout(callback, ADD_ACTIVATED_DEFERS);\n      }\n    }\n    activatableEle = el;\n  };\n  const addActivated = (el, x, y) => {\n    lastActivated = Date.now();\n    el.classList.add(ACTIVATED);\n    if (!useRippleEffect) return;\n    const rippleEffect = getRippleEffect(el);\n    if (rippleEffect !== null) {\n      removeRipple();\n      activeRipple = rippleEffect.addRipple(x, y);\n    }\n  };\n  const removeRipple = () => {\n    if (activeRipple !== undefined) {\n      activeRipple.then(remove => remove());\n      activeRipple = undefined;\n    }\n  };\n  const removeActivated = smooth => {\n    removeRipple();\n    const active = activatableEle;\n    if (!active) {\n      return;\n    }\n    const time = CLEAR_STATE_DEFERS - Date.now() + lastActivated;\n    if (smooth && time > 0 && !isInstant(active)) {\n      const deferId = setTimeout(() => {\n        active.classList.remove(ACTIVATED);\n        clearDefers.delete(active);\n      }, CLEAR_STATE_DEFERS);\n      clearDefers.set(active, deferId);\n    } else {\n      active.classList.remove(ACTIVATED);\n    }\n  };\n  doc.addEventListener('ionGestureCaptured', cancelActive);\n  doc.addEventListener('pointerdown', pointerDown, true);\n  doc.addEventListener('pointerup', pointerUp, true);\n  /**\n   * Tap click effects such as the ripple effect should\n   * not happen when scrolling. For example, if a user scrolls\n   * the page but also happens to do a touchstart on a button\n   * as part of the scroll, the ripple effect should not\n   * be dispatched. The ripple effect should only happen\n   * if the button is activated and the page is not scrolling.\n   *\n   * pointercancel is dispatched on a gesture when scrolling\n   * starts, so this lets us avoid having to listen for\n   * ion-content's scroll events.\n   */\n  doc.addEventListener('pointercancel', cancelActive, true);\n};\n// TODO(FW-2832): type\nconst getActivatableTarget = ev => {\n  if (ev.composedPath !== undefined) {\n    /**\n     * composedPath returns EventTarget[]. However,\n     * objects other than Element can be targets too.\n     * For example, AudioContext can be a target. In this\n     * case, we know that the event is a UIEvent so we\n     * can assume that the path will contain either Element\n     * or ShadowRoot.\n     */\n    const path = ev.composedPath();\n    for (let i = 0; i < path.length - 2; i++) {\n      const el = path[i];\n      if (!(el instanceof ShadowRoot) && el.classList.contains('ion-activatable')) {\n        return el;\n      }\n    }\n  } else {\n    return ev.target.closest('.ion-activatable');\n  }\n};\nconst isInstant = el => {\n  return el.classList.contains('ion-activatable-instant');\n};\nconst getRippleEffect = el => {\n  if (el.shadowRoot) {\n    const ripple = el.shadowRoot.querySelector('ion-ripple-effect');\n    if (ripple) {\n      return ripple;\n    }\n  }\n  return el.querySelector('ion-ripple-effect');\n};\nconst ACTIVATED = 'ion-activated';\nconst ADD_ACTIVATED_DEFERS = 100;\nconst CLEAR_STATE_DEFERS = 150;\nexport { startTapClick };"], "mappings": ";;;;;;;;;;AAKA,IAAM,gBAAgB,YAAU;AAC9B,MAAI,QAAQ,QAAW;AACrB;AAAA,EACF;AACA,MAAI,gBAAgB;AACpB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,kBAAkB,OAAO,WAAW,YAAY,IAAI,KAAK,OAAO,WAAW,gBAAgB,IAAI;AACrG,QAAM,cAAc,oBAAI,QAAQ;AAChC,QAAM,eAAe,MAAM;AACzB,QAAI,YAAa,cAAa,WAAW;AACzC,kBAAc;AACd,QAAI,gBAAgB;AAClB,sBAAgB,KAAK;AACrB,uBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,cAAc,QAAM;AAExB,QAAI,kBAAkB,GAAG,WAAW,GAAG;AACrC;AAAA,IACF;AACA,wBAAoB,qBAAqB,EAAE,GAAG,EAAE;AAAA,EAClD;AACA,QAAM,YAAY,QAAM;AACtB,wBAAoB,QAAW,EAAE;AAAA,EACnC;AACA,QAAM,sBAAsB,CAAC,IAAI,OAAO;AAEtC,QAAI,MAAM,OAAO,gBAAgB;AAC/B;AAAA,IACF;AACA,QAAI,YAAa,cAAa,WAAW;AACzC,kBAAc;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,aAAa,EAAE;AAEnB,QAAI,gBAAgB;AAClB,UAAI,YAAY,IAAI,cAAc,GAAG;AACnC,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAClC;AACA,UAAI,CAAC,eAAe,UAAU,SAAS,SAAS,GAAG;AACjD,qBAAa,gBAAgB,GAAG,CAAC;AAAA,MACnC;AACA,sBAAgB,IAAI;AAAA,IACtB;AAEA,QAAI,IAAI;AACN,YAAM,UAAU,YAAY,IAAI,EAAE;AAClC,UAAI,SAAS;AACX,qBAAa,OAAO;AACpB,oBAAY,OAAO,EAAE;AAAA,MACvB;AACA,SAAG,UAAU,OAAO,SAAS;AAC7B,YAAM,WAAW,MAAM;AACrB,qBAAa,IAAI,GAAG,CAAC;AACrB,sBAAc;AAAA,MAChB;AACA,UAAI,UAAU,EAAE,GAAG;AACjB,iBAAS;AAAA,MACX,OAAO;AACL,sBAAc,WAAW,UAAU,oBAAoB;AAAA,MACzD;AAAA,IACF;AACA,qBAAiB;AAAA,EACnB;AACA,QAAM,eAAe,CAAC,IAAI,GAAG,MAAM;AACjC,oBAAgB,KAAK,IAAI;AACzB,OAAG,UAAU,IAAI,SAAS;AAC1B,QAAI,CAAC,gBAAiB;AACtB,UAAM,eAAe,gBAAgB,EAAE;AACvC,QAAI,iBAAiB,MAAM;AACzB,mBAAa;AACb,qBAAe,aAAa,UAAU,GAAG,CAAC;AAAA,IAC5C;AAAA,EACF;AACA,QAAM,eAAe,MAAM;AACzB,QAAI,iBAAiB,QAAW;AAC9B,mBAAa,KAAK,YAAU,OAAO,CAAC;AACpC,qBAAe;AAAA,IACjB;AAAA,EACF;AACA,QAAM,kBAAkB,YAAU;AAChC,iBAAa;AACb,UAAM,SAAS;AACf,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,UAAM,OAAO,qBAAqB,KAAK,IAAI,IAAI;AAC/C,QAAI,UAAU,OAAO,KAAK,CAAC,UAAU,MAAM,GAAG;AAC5C,YAAM,UAAU,WAAW,MAAM;AAC/B,eAAO,UAAU,OAAO,SAAS;AACjC,oBAAY,OAAO,MAAM;AAAA,MAC3B,GAAG,kBAAkB;AACrB,kBAAY,IAAI,QAAQ,OAAO;AAAA,IACjC,OAAO;AACL,aAAO,UAAU,OAAO,SAAS;AAAA,IACnC;AAAA,EACF;AACA,MAAI,iBAAiB,sBAAsB,YAAY;AACvD,MAAI,iBAAiB,eAAe,aAAa,IAAI;AACrD,MAAI,iBAAiB,aAAa,WAAW,IAAI;AAajD,MAAI,iBAAiB,iBAAiB,cAAc,IAAI;AAC1D;AAEA,IAAM,uBAAuB,QAAM;AACjC,MAAI,GAAG,iBAAiB,QAAW;AASjC,UAAM,OAAO,GAAG,aAAa;AAC7B,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACxC,YAAM,KAAK,KAAK,CAAC;AACjB,UAAI,EAAE,cAAc,eAAe,GAAG,UAAU,SAAS,iBAAiB,GAAG;AAC3E,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO,GAAG,OAAO,QAAQ,kBAAkB;AAAA,EAC7C;AACF;AACA,IAAM,YAAY,QAAM;AACtB,SAAO,GAAG,UAAU,SAAS,yBAAyB;AACxD;AACA,IAAM,kBAAkB,QAAM;AAC5B,MAAI,GAAG,YAAY;AACjB,UAAM,SAAS,GAAG,WAAW,cAAc,mBAAmB;AAC9D,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,GAAG,cAAc,mBAAmB;AAC7C;AACA,IAAM,YAAY;AAClB,IAAM,uBAAuB;AAC7B,IAAM,qBAAqB;", "names": [], "x_google_ignoreList": [0]}
{"version": 3, "sources": ["src/app/pages/map/map.page.scss"], "sourcesContent": ["/* Offline status banner */\r\n.offline-status-banner {\r\n  position: absolute;\r\n  top: 10px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  background: linear-gradient(135deg, #ff6b35, #f7931e);\r\n  color: white;\r\n  border-radius: 20px;\r\n  padding: 8px 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  z-index: 1001;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  animation: slideDown 0.3s ease-out;\r\n\r\n  ion-icon {\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n@keyframes slideDown {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(-50%) translateY(-20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(-50%) translateY(0);\r\n  }\r\n}\r\n\r\n/* Map container */\r\n#map {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* Travel mode segment */\r\n.mode-segment {\r\n  position: absolute;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  top: 10px;\r\n  z-index: 1000;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 20px;\r\n  padding: 4px;\r\n  width: 90%;\r\n  max-width: 400px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  ion-segment-button {\r\n    --background: transparent;\r\n    --background-checked: var(--ion-color-light);\r\n    --indicator-color: transparent;\r\n    --border-radius: 16px;\r\n    min-height: 40px;\r\n\r\n    .segment-icon {\r\n      width: 24px;\r\n      height: 24px;\r\n      display: block;\r\n      margin: 0 auto 4px;\r\n    }\r\n\r\n    .segment-label {\r\n      font-size: 12px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n/* Route summary card */\r\n.route-summary-card {\r\n  position: absolute;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  top: 70px;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 16px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 12px 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  z-index: 1000;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n\r\n  &:hover {\r\n    background: rgba(255, 255, 255, 1);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    transform: translateX(-50%) translateY(-2px);\r\n  }\r\n\r\n  &:active {\r\n    transform: translateX(-50%) translateY(0);\r\n  }\r\n\r\n  ion-icon {\r\n    font-size: 24px;\r\n  }\r\n\r\n  .summary-text {\r\n    line-height: 1.3;\r\n\r\n    strong {\r\n      font-size: 16px;\r\n    }\r\n\r\n    .travel-mode {\r\n      font-size: 12px;\r\n      opacity: 0.8;\r\n      margin-top: 2px;\r\n    }\r\n  }\r\n\r\n  .expand-icon {\r\n    font-size: 18px;\r\n    margin-left: 8px;\r\n    color: var(--ion-color-medium);\r\n  }\r\n}\r\n\r\n/* FAB button label */\r\n.fab-label {\r\n  position: absolute;\r\n  right: 80px;\r\n  bottom: 30px;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  padding: 8px 16px;\r\n  border-radius: 20px;\r\n  font-size: 14px;\r\n  color: var(--ion-color-primary);\r\n  z-index: 1000;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  font-weight: 500;\r\n}\r\n\r\n/* GPS toggle button */\r\nion-fab-button[activated] {\r\n  --background: var(--ion-color-primary);\r\n  --color: white;\r\n}\r\n\r\n/* GPS status indicator */\r\n.gps-status {\r\n  position: absolute;\r\n  top: 10px;\r\n  left: 70px; /* Moved to the right to make room for the download button */\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 20px;\r\n  padding: 8px 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  z-index: 1000;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  font-size: 14px;\r\n  color: var(--ion-color-medium);\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n\r\n  &:hover {\r\n    background: rgba(255, 255, 255, 1);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    transform: translateY(-2px);\r\n  }\r\n\r\n  &:active {\r\n    transform: translateY(0);\r\n  }\r\n\r\n  &.active {\r\n    color: var(--ion-color-primary);\r\n    background: rgba(var(--ion-color-primary-rgb), 0.1);\r\n\r\n    &:hover {\r\n      background: rgba(var(--ion-color-primary-rgb), 0.2);\r\n    }\r\n\r\n    ion-icon {\r\n      animation: pulse 1.5s infinite;\r\n    }\r\n  }\r\n\r\n  ion-icon {\r\n    font-size: 18px;\r\n  }\r\n\r\n  &::after {\r\n    content: \"?\";\r\n    display: inline-block;\r\n    width: 16px;\r\n    height: 16px;\r\n    line-height: 16px;\r\n    text-align: center;\r\n    background: var(--ion-color-medium);\r\n    color: white;\r\n    border-radius: 50%;\r\n    font-size: 12px;\r\n    margin-left: 6px;\r\n    opacity: 0.7;\r\n  }\r\n}\r\n\r\n/* Disaster type indicator */\r\n.disaster-type-indicator {\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 80px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 20px;\r\n  padding: 8px 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  z-index: 1000;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  color: var(--ion-color-dark);\r\n\r\n  ion-icon {\r\n    font-size: 18px;\r\n    color: var(--ion-color-primary);\r\n  }\r\n}\r\n\r\n/* Location request container */\r\n.location-request-container {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 16px;\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\r\n  padding: 20px;\r\n  text-align: center;\r\n  max-width: 300px;\r\n  width: 90%;\r\n  z-index: 1001; /* Higher than other elements */\r\n  animation: fadeIn 0.5s ease-out;\r\n\r\n  ion-button {\r\n    margin: 10px 0;\r\n    --border-radius: 10px;\r\n    --box-shadow: 0 4px 8px rgba(var(--ion-color-primary-rgb), 0.3);\r\n    font-weight: 600;\r\n    height: 48px;\r\n\r\n    &:active {\r\n      --box-shadow: 0 2px 4px rgba(var(--ion-color-primary-rgb), 0.2);\r\n      transform: translateY(2px);\r\n    }\r\n  }\r\n\r\n  .location-help-text {\r\n    margin: 10px 0 0;\r\n    font-size: 14px;\r\n    color: var(--ion-color-medium);\r\n    line-height: 1.4;\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translate(-50%, -40%);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translate(-50%, -50%);\r\n  }\r\n}\r\n\r\n/* Default map message */\r\n.map-default-message {\r\n  position: absolute;\r\n  bottom: 30px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 16px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 12px 16px;\r\n  text-align: center;\r\n  max-width: 300px;\r\n  z-index: 1000;\r\n\r\n  ion-icon {\r\n    font-size: 24px;\r\n    color: var(--ion-color-primary);\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  p {\r\n    margin: 0 0 5px;\r\n    font-weight: 500;\r\n    font-size: 16px;\r\n    color: var(--ion-color-dark);\r\n  }\r\n\r\n  small {\r\n    color: var(--ion-color-medium);\r\n    font-size: 13px;\r\n    display: block;\r\n    line-height: 1.4;\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    opacity: 0.6;\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    opacity: 0.6;\r\n  }\r\n}\r\n\r\n/* Pulsating marker animation */\r\n@keyframes pulsate {\r\n  0% {\r\n    transform: scale(0.8);\r\n    opacity: 0.8;\r\n  }\r\n  50% {\r\n    transform: scale(1.5);\r\n    opacity: 0.4;\r\n  }\r\n  100% {\r\n    transform: scale(0.8);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n.marker-pulse-container {\r\n  position: relative;\r\n}\r\n\r\n.marker-pulse {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 50px;\r\n  height: 50px;\r\n  margin-top: -25px;\r\n  margin-left: -25px;\r\n  border-radius: 50%;\r\n  z-index: 100;\r\n  pointer-events: none;\r\n  animation: pulsate 1.5s ease-out infinite;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n/* Style for popup button */\r\n:host ::ng-deep .popup-button {\r\n  background-color: var(--ion-color-primary);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding: 6px 12px;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  margin-top: 8px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n:host ::ng-deep .popup-button:hover {\r\n  background-color: var(--ion-color-primary-shade);\r\n}\r\n\r\n:host ::ng-deep .evacuation-popup {\r\n  h3 {\r\n    margin: 0 0 8px 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n  }\r\n\r\n  p {\r\n    margin: 4px 0;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n/* Modal styles */\r\n.evacuation-details-modal {\r\n  --border-radius: 16px 16px 0 0;\r\n  --backdrop-opacity: 0.4;\r\n}"], "mappings": ";AACA,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,aAAA,WAAA;AACA;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,SAAA;AACA,iBAAA;AACA,WAAA,IAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,aAAA;AACA,eAAA;AACA,aAAA,UAAA,KAAA;;AAEA,CAlBF,sBAkBE;AACE,aAAA;;AAIJ,WAPE;AAQA;AACE,aAAA;AACA,eAAA,WAAA,MAAA,WAAA;;AAEF;AACE,aAAA;AACA,eAAA,WAAA,MAAA,WAAA;;;AAKJ,CAAA;AACE,SAAA;AACA,UAAA;;AAIF,CAAA;AACE,YAAA;AACA,QAAA;AACA,aAAA,WAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,WAAA;AACA,SAAA;AACA,aAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CAbF,aAaE;AACE,gBAAA;AACA,wBAAA,IAAA;AACA,qBAAA;AACA,mBAAA;AACA,cAAA;;AAEA,CApBJ,aAoBI,mBAAA,CAAA;AACE,SAAA;AACA,UAAA;AACA,WAAA;AACA,UAAA,EAAA,KAAA;;AAGF,CA3BJ,aA2BI,mBAAA,CAAA;AACE,aAAA;AACA,eAAA;;AAMN,CAAA;AACE,YAAA;AACA,QAAA;AACA,aAAA,WAAA;AACA,OAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA,KAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,UAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CAhBF,kBAgBE;AACE,cAAA,IAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,aAAA,WAAA,MAAA,WAAA;;AAGF,CAtBF,kBAsBE;AACE,aAAA,WAAA,MAAA,WAAA;;AAGF,CA1BF,mBA0BE;AACE,aAAA;;AAGF,CA9BF,mBA8BE,CAAA;AACE,eAAA;;AAEA,CAjCJ,mBAiCI,CAHF,aAGE;AACE,aAAA;;AAGF,CArCJ,mBAqCI,CAPF,aAOE,CAAA;AACE,aAAA;AACA,WAAA;AACA,cAAA;;AAIJ,CA5CF,mBA4CE,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA,IAAA;;AAKJ,CAAA;AACE,YAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;AACA,SAAA,IAAA;AACA,WAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,eAAA;;AAIF,cAAA,CAAA;AACE,gBAAA,IAAA;AACA,WAAA;;AAIF,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,WAAA,IAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,aAAA;AACA,SAAA,IAAA;AACA,UAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CAjBF,UAiBE;AACE,cAAA,IAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,aAAA,WAAA;;AAGF,CAvBF,UAuBE;AACE,aAAA,WAAA;;AAGF,CA3BF,UA2BE,CAAA;AACE,SAAA,IAAA;AACA,cAAA,KAAA,IAAA,wBAAA,EAAA;;AAEA,CA/BJ,UA+BI,CAJF,MAIE;AACE,cAAA,KAAA,IAAA,wBAAA,EAAA;;AAGF,CAnCJ,UAmCI,CARF,OAQE;AACE,aAAA,MAAA,KAAA;;AAIJ,CAxCF,WAwCE;AACE,aAAA;;AAGF,CA5CF,UA4CE;AACE,WAAA;AACA,WAAA;AACA,SAAA;AACA,UAAA;AACA,eAAA;AACA,cAAA;AACA,cAAA,IAAA;AACA,SAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;AACA,WAAA;;AAKJ,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,WAAA,IAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA,IAAA;;AAEA,CAhBF,wBAgBE;AACE,aAAA;AACA,SAAA,IAAA;;AAKJ,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,aAAA,UAAA,IAAA,EAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA;AACA,cAAA;AACA,aAAA;AACA,SAAA;AACA,WAAA;AACA,aAAA,OAAA,KAAA;;AAEA,CAfF,2BAeE;AACE,UAAA,KAAA;AACA,mBAAA;AACA,gBAAA,EAAA,IAAA,IAAA,KAAA,IAAA,wBAAA,EAAA;AACA,eAAA;AACA,UAAA;;AAEA,CAtBJ,2BAsBI,UAAA;AACE,gBAAA,EAAA,IAAA,IAAA,KAAA,IAAA,wBAAA,EAAA;AACA,aAAA,WAAA;;AAIJ,CA5BF,2BA4BE,CAAA;AACE,UAAA,KAAA,EAAA;AACA,aAAA;AACA,SAAA,IAAA;AACA,eAAA;;AAIJ,WAvBE;AAwBA;AACE,aAAA;AACA,eAAA,UAAA,IAAA,EAAA;;AAEF;AACE,aAAA;AACA,eAAA,UAAA,IAAA,EAAA;;;AAKJ,CAAA;AACE,YAAA;AACA,UAAA;AACA,QAAA;AACA,aAAA,WAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA,KAAA;AACA,cAAA;AACA,aAAA;AACA,WAAA;;AAEA,CAbF,oBAaE;AACE,aAAA;AACA,SAAA,IAAA;AACA,iBAAA;;AAGF,CAnBF,oBAmBE;AACE,UAAA,EAAA,EAAA;AACA,eAAA;AACA,aAAA;AACA,SAAA,IAAA;;AAGF,CA1BF,oBA0BE;AACE,SAAA,IAAA;AACA,aAAA;AACA,WAAA;AACA,eAAA;;AAIJ,WAlIM;AAmIJ;AACE,aAAA;;AAEF;AACE,aAAA;;AAEF;AACE,aAAA;;;AAKJ,WAAA;AACE;AACE,eAAA,MAAA;AACA,aAAA;;AAEF;AACE,eAAA,MAAA;AACA,aAAA;;AAEF;AACE,eAAA,MAAA;AACA,aAAA;;;AAIJ,CAAA;AACE,YAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA;AACA,eAAA;AACA,iBAAA;AACA,WAAA;AACA,kBAAA;AACA,aAAA,QAAA,KAAA,SAAA;AACA,cAAA,EAAA,EAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAIF,MAAA,UAAA,CAAA;AACE,oBAAA,IAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,WAAA,IAAA;AACA,aAAA;AACA,UAAA;AACA,cAAA;AACA,cAAA,iBAAA;;AAGF,MAAA,UAAA,CAZA,YAYA;AACE,oBAAA,IAAA;;AAIA,MAAA,UAAA,CAAA,iBAAA;AACE,UAAA,EAAA,EAAA,IAAA;AACA,aAAA;AACA,eAAA;;AAGF,MAAA,UAAA,CANA,iBAMA;AACE,UAAA,IAAA;AACA,aAAA;;AAKJ,CAAA;AACE,mBAAA,KAAA,KAAA,EAAA;AACA,sBAAA;;", "names": []}
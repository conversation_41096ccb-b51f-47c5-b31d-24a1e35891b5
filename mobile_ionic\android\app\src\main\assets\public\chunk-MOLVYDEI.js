import{Cb as d,E as t,F as e,G as i,L as n,W as s,eb as r,fa as l,qb as p,rb as b,wb as c,z as m}from"./chunk-PBKSAHK2.js";import"./chunk-MBKQLJTW.js";import"./chunk-F3654E4N.js";import"./chunk-FHR3DP7J.js";import"./chunk-A4FGPDGZ.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-AUB5HKS7.js";import"./chunk-RS5W3JWO.js";import"./chunk-LOLLZ3RS.js";import"./chunk-XZOVPSKP.js";import"./chunk-7LH2AG5T.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-SPZFNIGG.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-KY4M3ZA2.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-4EI7TLDT.js";import"./chunk-FED6QSGK.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-2R6CW7ES.js";var M=(()=>{class a{constructor(){}static{this.\u0275fac=function(o){return new(o||a)}}static{this.\u0275cmp=m({type:a,selectors:[["app-tabs"]],decls:18,vars:0,consts:[["slot","bottom"],["tab","home"],["src","assets/homePage.png",2,"width","24px","height","24px","display","block","margin","auto"],["tab","search"],["src","assets/searchPlace.png",2,"width","24px","height","24px","display","block","margin","auto"],["tab","map"],["src","assets/map.png",2,"width","24px","height","24px","display","block","margin","auto"],["tab","profile"],["src","assets/setting.png",2,"width","24px","height","24px","display","block","margin","auto"]],template:function(o,u){o&1&&(t(0,"ion-tabs")(1,"ion-tab-bar",0)(2,"ion-tab-button",1),i(3,"img",2),t(4,"ion-label"),n(5,"Home"),e()(),t(6,"ion-tab-button",3),i(7,"img",4),t(8,"ion-label"),n(9,"Search"),e()(),t(10,"ion-tab-button",5),i(11,"img",6),t(12,"ion-label"),n(13,"Map"),e()(),t(14,"ion-tab-button",7),i(15,"img",8),t(16,"ion-label"),n(17,"Settings"),e()()()())},dependencies:[d,r,p,b,c,s,l],encapsulation:2})}}return a})();export{M as TabsPage};

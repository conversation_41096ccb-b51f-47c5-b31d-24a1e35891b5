{"version": 3, "sources": ["src/app/pages/disaster-maps/typhoon-map.page.scss"], "sourcesContent": ["#typhoon-map {\r\n  height: 100%;\r\n  width: 100%;\r\n  z-index: 1;\r\n}\r\n\r\n.floating-info {\r\n  position: absolute;\r\n  top: 20px;\r\n  right: 20px;\r\n  z-index: 1000;\r\n  max-width: 250px;\r\n\r\n  ion-card {\r\n    margin: 0;\r\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\r\n    border-radius: 12px;\r\n    background: rgba(255, 255, 255, 0.95);\r\n    backdrop-filter: blur(10px);\r\n  }\r\n\r\n  ion-card-content {\r\n    padding: 12px;\r\n  }\r\n\r\n  .info-row {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-weight: 600;\r\n    color: var(--ion-color-success);\r\n    margin-bottom: 4px;\r\n\r\n    ion-icon {\r\n      font-size: 18px;\r\n    }\r\n  }\r\n\r\n  .info-text {\r\n    font-size: 12px;\r\n    color: var(--ion-color-medium);\r\n    line-height: 1.3;\r\n  }\r\n}\r\n\r\n// Typhoon-specific styling\r\nion-toolbar {\r\n  --background: var(--ion-color-success);\r\n  --color: white;\r\n}\r\n\r\nion-title {\r\n  font-weight: 600;\r\n}\r\n\r\n// Popup styling for typhoon centers\r\n:global(.leaflet-popup-content) {\r\n  .evacuation-popup {\r\n    text-align: center;\r\n    min-width: 200px;\r\n\r\n    h3 {\r\n      margin: 0 0 8px 0;\r\n      color: var(--ion-color-success);\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    p {\r\n      margin: 4px 0;\r\n      font-size: 14px;\r\n      \r\n      strong {\r\n        color: var(--ion-color-dark);\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,UAAA;AACA,SAAA;AACA,WAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,WAAA;AACA,aAAA;;AAEA,CAPF,cAOE;AACE,UAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,2BAAA,KAAA;AAAA,mBAAA,KAAA;;AAGF,CAfF,cAeE;AACE,WAAA;;AAGF,CAnBF,cAmBE,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,iBAAA;;AAEA,CA3BJ,cA2BI,CARF,SAQE;AACE,aAAA;;AAIJ,CAhCF,cAgCE,CAAA;AACE,aAAA;AACA,SAAA,IAAA;AACA,eAAA;;AAKJ;AACE,gBAAA,IAAA;AACA,WAAA;;AAGF;AACE,eAAA;;AAKA,QAAA,CAAA,uBAAA,CAAA;AACE,cAAA;AACA,aAAA;;AAEA,QAAA,CAJF,uBAIE,CAJF,iBAIE;AACE,UAAA,EAAA,EAAA,IAAA;AACA,SAAA,IAAA;AACA,aAAA;AACA,eAAA;;AAGF,QAAA,CAXF,uBAWE,CAXF,iBAWE;AACE,UAAA,IAAA;AACA,aAAA;;AAEA,QAAA,CAfJ,uBAeI,CAfJ,iBAeI,EAAA;AACE,SAAA,IAAA;;", "names": []}
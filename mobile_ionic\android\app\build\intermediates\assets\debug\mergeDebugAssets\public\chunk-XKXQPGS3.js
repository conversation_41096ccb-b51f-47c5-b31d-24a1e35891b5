import {
  EnvironmentSwitcherService
} from "./chunk-FKALCVFZ.js";
import {
  environment
} from "./chunk-I7MI46CM.js";
import {
  HttpClient,
  HttpHeaders,
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-NS3G4TP7.js";
import {
  __spreadProps,
  __spreadValues
} from "./chunk-UL2P3LPA.js";

// src/app/services/auth.service.ts
var AuthService = class _AuthService {
  get apiUrl() {
    const dynamicUrl = this.envSwitcher?.getCurrentApiUrl();
    const baseUrl = dynamicUrl || environment.apiUrl;
    return `${baseUrl}/auth`;
  }
  constructor(http, envSwitcher) {
    this.http = http;
    this.envSwitcher = envSwitcher;
    console.log("Auth Service initialized");
    console.log("Environment API URL:", environment.apiUrl);
    console.log("Dynamic API URL:", this.envSwitcher.getCurrentApiUrl());
    console.log("Final API URL:", this.apiUrl);
  }
  getHeaders() {
    return new HttpHeaders({
      "Content-Type": "application/json",
      "Accept": "application/json",
      "X-Requested-With": "XMLHttpRequest"
    });
  }
  login(credentials) {
    console.log("\u{1F510} Making login request to:", `${this.apiUrl}/auth/login`);
    console.log("\u{1F4E7} Credentials:", { email: credentials.email, password: "***" });
    return this.http.post(`${this.apiUrl}/auth/login`, credentials, {
      headers: this.getHeaders()
    });
  }
  register(data) {
    console.log("\u{1F4DD} Making registration request to:", `${this.apiUrl}/auth/signup`);
    console.log("\u{1F464} Registration data:", __spreadProps(__spreadValues({}, data), { password: "***", password_confirmation: "***" }));
    return this.http.post(`${this.apiUrl}/auth/signup`, data, {
      headers: this.getHeaders()
    });
  }
  setToken(token) {
    localStorage.setItem("token", token);
    console.log("\u{1F511} Token stored successfully");
  }
  static {
    this.\u0275fac = function AuthService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AuthService)(\u0275\u0275inject(HttpClient), \u0275\u0275inject(EnvironmentSwitcherService));
    };
  }
  static {
    this.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AuthService, factory: _AuthService.\u0275fac, providedIn: "root" });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthService, [{
    type: Injectable,
    args: [{ providedIn: "root" }]
  }], () => [{ type: HttpClient }, { type: EnvironmentSwitcherService }], null);
})();

export {
  AuthService
};
//# sourceMappingURL=chunk-XKXQPGS3.js.map

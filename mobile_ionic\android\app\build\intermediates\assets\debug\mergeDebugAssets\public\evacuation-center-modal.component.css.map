{"version": 3, "sources": ["src/app/pages/search/evacuation-center-modal.component.scss"], "sourcesContent": [".modal-container {\r\n  background-color: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  width: 100%;\r\n  max-width: 350px;\r\n  margin: 0 auto;\r\n  position: relative;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.close-button {\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 10px;\r\n  z-index: 10;\r\n\r\n  ion-icon {\r\n    font-size: 24px;\r\n    background: white;\r\n    border-radius: 50%;\r\n  }\r\n}\r\n\r\n.center-name {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  text-align: center;\r\n  margin: 15px 15px 10px;\r\n  color: #000;\r\n}\r\n\r\n.center-image {\r\n  width: 100%;\r\n  height: 160px;\r\n  overflow: hidden;\r\n\r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n}\r\n\r\n.info-section {\r\n  padding: 10px 15px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n\r\n  &:last-of-type {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.info-label {\r\n  font-size: 14px;\r\n  color: #0099ff;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.info-value {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 15px;\r\n  color: #333;\r\n\r\n  ion-icon {\r\n    margin-right: 8px;\r\n    font-size: 18px;\r\n    min-width: 18px;\r\n  }\r\n\r\n  &.contact {\r\n    ion-icon {\r\n      color: #333;\r\n    }\r\n  }\r\n\r\n  &.address {\r\n    ion-icon {\r\n      color: #ff4961;\r\n    }\r\n  }\r\n}\r\n\r\n.directions-button {\r\n  padding: 10px 15px 15px;\r\n\r\n  ion-button {\r\n    --border-radius: 8px;\r\n    --background: #0099ff;\r\n    font-weight: 500;\r\n    margin: 0;\r\n\r\n    ion-icon {\r\n      margin-right: 5px;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,oBAAA;AACA,iBAAA;AACA,YAAA;AACA,SAAA;AACA,aAAA;AACA,UAAA,EAAA;AACA,YAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,WAAA;;AAEA,CANF,aAME;AACE,aAAA;AACA,cAAA;AACA,iBAAA;;AAIJ,CAAA;AACE,aAAA;AACA,eAAA;AACA,cAAA;AACA,UAAA,KAAA,KAAA;AACA,SAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,YAAA;;AAEA,CALF,aAKE;AACE,SAAA;AACA,UAAA;AACA,cAAA;;AAIJ,CAAA;AACE,WAAA,KAAA;AACA,iBAAA,IAAA,MAAA;;AAEA,CAJF,YAIE;AACE,iBAAA;;AAIJ,CAAA;AACE,aAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,aAAA;AACA,SAAA;;AAEA,CANF,WAME;AACE,gBAAA;AACA,aAAA;AACA,aAAA;;AAIA,CAbJ,UAaI,CAAA,QAAA;AACE,SAAA;;AAKF,CAnBJ,UAmBI,CAAA,QAAA;AACE,SAAA;;AAKN,CAAA;AACE,WAAA,KAAA,KAAA;;AAEA,CAHF,kBAGE;AACE,mBAAA;AACA,gBAAA;AACA,eAAA;AACA,UAAA;;AAEA,CATJ,kBASI,WAAA;AACE,gBAAA;;", "names": []}
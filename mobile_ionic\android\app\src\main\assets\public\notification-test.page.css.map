{"version": 3, "sources": ["src/app/pages/notification-test/notification-test.page.scss"], "sourcesContent": [".test-container {\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.notification-item {\r\n  margin-bottom: 8px;\r\n  border-radius: 8px;\r\n  --background: var(--ion-color-light);\r\n}\r\n\r\n.category-badge {\r\n  margin-top: 8px;\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\nion-badge {\r\n  font-size: 0.7rem;\r\n  padding: 4px 8px;\r\n}\r\n\r\nion-card {\r\n  margin-bottom: 16px;\r\n}\r\n\r\nion-card-title {\r\n  color: var(--ion-color-primary);\r\n}\r\n\r\n.test-button {\r\n  margin: 8px 0;\r\n}\r\n\r\nion-list {\r\n  background: transparent;\r\n}\r\n\r\nion-item {\r\n  --padding-start: 16px;\r\n  --padding-end: 16px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\nh3 {\r\n  margin: 0 0 4px 0;\r\n  font-weight: 600;\r\n}\r\n\r\np {\r\n  margin: 0;\r\n  color: var(--ion-color-medium);\r\n  font-size: 0.9rem;\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,iBAAA;AACA,iBAAA;AACA,gBAAA,IAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,OAAA;;AAGF;AACE,aAAA;AACA,WAAA,IAAA;;AAGF;AACE,iBAAA;;AAGF;AACE,SAAA,IAAA;;AAGF,CAAA;AACE,UAAA,IAAA;;AAGF;AACE,cAAA;;AAGF;AACE,mBAAA;AACA,iBAAA;AACA,iBAAA;;AAGF;AACE,UAAA,EAAA,EAAA,IAAA;AACA,eAAA;;AAGF;AACE,UAAA;AACA,SAAA,IAAA;AACA,aAAA;;", "names": []}
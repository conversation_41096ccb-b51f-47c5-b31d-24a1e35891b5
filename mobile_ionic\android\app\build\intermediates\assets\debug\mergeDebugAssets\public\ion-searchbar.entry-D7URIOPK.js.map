{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-searchbar.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, i as forceUpdate, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { e as debounceEvent, h as inheritAttributes, c as componentOnReady, r as raf } from './helpers-d94bc8ad.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { a as arrowBackSharp, b as closeCircle, d as closeSharp, s as searchOutline, e as searchSharp } from './index-e2cf2ceb.js';\nimport { c as config } from './index-cfd9c1f2.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nconst searchbarIosCss = \".sc-ion-searchbar-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-searchbar-ios-h{color:var(--ion-color-contrast)}.ion-color.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{background:var(--ion-color-base)}.ion-color.sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios,.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.ion-color.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{color:inherit}.searchbar-search-icon.sc-ion-searchbar-ios{color:var(--icon-color);pointer-events:none}.searchbar-input-container.sc-ion-searchbar-ios{display:block;position:relative;-ms-flex-negative:1;flex-shrink:1;width:100%}.searchbar-input.sc-ion-searchbar-ios{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;border-radius:var(--border-radius);display:block;width:100%;min-height:inherit;border:0;outline:none;background:var(--background);font-family:inherit;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-input.sc-ion-searchbar-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-ios::-webkit-search-cancel-button,.searchbar-input.sc-ion-searchbar-ios::-ms-clear{display:none}.searchbar-cancel-button.sc-ion-searchbar-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:none;height:100%;border:0;outline:none;color:var(--cancel-button-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-cancel-button.sc-ion-searchbar-ios>div.sc-ion-searchbar-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.searchbar-clear-button.sc-ion-searchbar-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:none;min-height:0;outline:none;color:var(--clear-button-color);-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-clear-button.sc-ion-searchbar-ios:focus{opacity:0.5}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-ios-h .searchbar-clear-button.sc-ion-searchbar-ios{display:block}.searchbar-disabled.sc-ion-searchbar-ios-h{cursor:default;opacity:0.4;pointer-events:none}.sc-ion-searchbar-ios-h{--background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.07);--border-radius:10px;--box-shadow:none;--cancel-button-color:var(--ion-color-primary, #0054e9);--clear-button-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));--color:var(--ion-text-color, #000);--icon-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:12px;padding-bottom:12px;min-height:60px;contain:content}.searchbar-input-container.sc-ion-searchbar-ios{min-height:36px}.searchbar-search-icon.sc-ion-searchbar-ios{-webkit-margin-start:calc(50% - 60px);margin-inline-start:calc(50% - 60px);top:0;position:absolute;width:1.375rem;height:100%;contain:strict}.searchbar-search-icon.sc-ion-searchbar-ios{inset-inline-start:5px}.searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:6px;padding-bottom:6px;height:100%;font-size:1.0625rem;font-weight:400;contain:strict}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:1.75rem;padding-inline-start:1.75rem;-webkit-padding-end:1.75rem;padding-inline-end:1.75rem}.searchbar-clear-button.sc-ion-searchbar-ios{top:0;background-position:center;position:absolute;width:1.875rem;height:100%;border:0;background-color:transparent}.searchbar-clear-button.sc-ion-searchbar-ios{inset-inline-end:0}.searchbar-clear-icon.sc-ion-searchbar-ios{width:1.125rem;height:100%}.searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0;background-color:transparent;font-size:17px}.searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{-webkit-margin-start:0;margin-inline-start:0}.searchbar-left-aligned.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-padding-start:1.875rem;padding-inline-start:1.875rem}.searchbar-has-focus.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-should-show-cancel.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{display:block}.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios{-webkit-transition:all 300ms ease;transition:all 300ms ease}.searchbar-animated.searchbar-has-focus.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios,.searchbar-animated.searchbar-should-show-cancel.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{opacity:1;pointer-events:auto}.searchbar-animated.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-margin-end:-100%;margin-inline-end:-100%;-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);-webkit-transition:all 300ms ease;transition:all 300ms ease;opacity:0;pointer-events:none}.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-input.sc-ion-searchbar-ios,.searchbar-no-animate.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{-webkit-transition-duration:0ms;transition-duration:0ms}.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios{color:var(--ion-color-base)}@media (any-hover: hover){.ion-color.sc-ion-searchbar-ios-h .searchbar-cancel-button.sc-ion-searchbar-ios:hover{color:var(--ion-color-tint)}}ion-toolbar.sc-ion-searchbar-ios-h,ion-toolbar .sc-ion-searchbar-ios-h{padding-top:1px;padding-bottom:15px;min-height:52px}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color),ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color){color:inherit}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-cancel-button.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-cancel-button.sc-ion-searchbar-ios{color:currentColor}ion-toolbar.ion-color.sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h .searchbar-search-icon.sc-ion-searchbar-ios{color:currentColor;opacity:0.5}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-input.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-input.sc-ion-searchbar-ios{background:rgba(var(--ion-color-contrast-rgb), 0.07);color:currentColor}ion-toolbar.ion-color.sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-clear-button.sc-ion-searchbar-ios,ion-toolbar.ion-color .sc-ion-searchbar-ios-h:not(.ion-color) .searchbar-clear-button.sc-ion-searchbar-ios{color:currentColor;opacity:0.5}\";\nconst IonSearchbarIosStyle0 = searchbarIosCss;\nconst searchbarMdCss = \".sc-ion-searchbar-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-searchbar-md-h{color:var(--ion-color-contrast)}.ion-color.sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md{background:var(--ion-color-base)}.ion-color.sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md,.ion-color.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,.ion-color.sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{color:inherit}.searchbar-search-icon.sc-ion-searchbar-md{color:var(--icon-color);pointer-events:none}.searchbar-input-container.sc-ion-searchbar-md{display:block;position:relative;-ms-flex-negative:1;flex-shrink:1;width:100%}.searchbar-input.sc-ion-searchbar-md{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;border-radius:var(--border-radius);display:block;width:100%;min-height:inherit;border:0;outline:none;background:var(--background);font-family:inherit;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-input.sc-ion-searchbar-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.searchbar-input.sc-ion-searchbar-md::-webkit-search-cancel-button,.searchbar-input.sc-ion-searchbar-md::-ms-clear{display:none}.searchbar-cancel-button.sc-ion-searchbar-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:none;height:100%;border:0;outline:none;color:var(--cancel-button-color);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-cancel-button.sc-ion-searchbar-md>div.sc-ion-searchbar-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.searchbar-clear-button.sc-ion-searchbar-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:none;min-height:0;outline:none;color:var(--clear-button-color);-webkit-appearance:none;-moz-appearance:none;appearance:none}.searchbar-clear-button.sc-ion-searchbar-md:focus{opacity:0.5}.searchbar-has-value.searchbar-should-show-clear.sc-ion-searchbar-md-h .searchbar-clear-button.sc-ion-searchbar-md{display:block}.searchbar-disabled.sc-ion-searchbar-md-h{cursor:default;opacity:0.4;pointer-events:none}.sc-ion-searchbar-md-h{--background:var(--ion-background-color, #fff);--border-radius:2px;--box-shadow:0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);--cancel-button-color:var(--ion-color-step-900, var(--ion-text-color-step-100, #1a1a1a));--clear-button-color:initial;--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--icon-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;background:inherit}.searchbar-search-icon.sc-ion-searchbar-md{top:11px;width:1.3125rem;height:1.3125rem}.searchbar-search-icon.sc-ion-searchbar-md{inset-inline-start:16px}.searchbar-cancel-button.sc-ion-searchbar-md{top:0;background-color:transparent;font-size:1.5em}.searchbar-cancel-button.sc-ion-searchbar-md{inset-inline-start:9px}.searchbar-search-icon.sc-ion-searchbar-md,.searchbar-cancel-button.sc-ion-searchbar-md{position:absolute}.searchbar-search-icon.ion-activated.sc-ion-searchbar-md,.searchbar-cancel-button.ion-activated.sc-ion-searchbar-md{background-color:transparent}.searchbar-input.sc-ion-searchbar-md{-webkit-padding-start:3.4375rem;padding-inline-start:3.4375rem;-webkit-padding-end:3.4375rem;padding-inline-end:3.4375rem;padding-top:0.375rem;padding-bottom:0.375rem;background-position:left 8px center;height:auto;font-size:1rem;font-weight:400;line-height:30px}[dir=rtl].sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md,[dir=rtl] .sc-ion-searchbar-md-h .searchbar-input.sc-ion-searchbar-md{background-position:right 8px center}[dir=rtl].sc-ion-searchbar-md .searchbar-input.sc-ion-searchbar-md{background-position:right 8px center}@supports selector(:dir(rtl)){.searchbar-input.sc-ion-searchbar-md:dir(rtl){background-position:right 8px center}}.searchbar-clear-button.sc-ion-searchbar-md{top:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;position:absolute;height:100%;border:0;background-color:transparent}.searchbar-clear-button.sc-ion-searchbar-md{inset-inline-end:13px}.searchbar-clear-button.ion-activated.sc-ion-searchbar-md{background-color:transparent}.searchbar-clear-icon.sc-ion-searchbar-md{width:1.375rem;height:100%}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-search-icon.sc-ion-searchbar-md{display:block}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md,.searchbar-should-show-cancel.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md{display:block}.searchbar-has-focus.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md+.searchbar-search-icon.sc-ion-searchbar-md,.searchbar-should-show-cancel.sc-ion-searchbar-md-h .searchbar-cancel-button.sc-ion-searchbar-md+.searchbar-search-icon.sc-ion-searchbar-md{display:none}ion-toolbar.sc-ion-searchbar-md-h,ion-toolbar .sc-ion-searchbar-md-h{-webkit-padding-start:7px;padding-inline-start:7px;-webkit-padding-end:7px;padding-inline-end:7px;padding-top:3px;padding-bottom:3px}\";\nconst IonSearchbarMdStyle0 = searchbarMdCss;\nconst Searchbar = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionInput = createEvent(this, \"ionInput\", 7);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionCancel = createEvent(this, \"ionCancel\", 7);\n    this.ionClear = createEvent(this, \"ionClear\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.isCancelVisible = false;\n    this.shouldAlignLeft = true;\n    this.inputId = `ion-searchbar-${searchbarIds++}`;\n    this.inheritedAttributes = {};\n    /**\n     * Clears the input field and triggers the control change.\n     */\n    this.onClearInput = async shouldFocus => {\n      this.ionClear.emit();\n      return new Promise(resolve => {\n        // setTimeout() fixes https://github.com/ionic-team/ionic-framework/issues/7527\n        // wait for 4 frames\n        setTimeout(() => {\n          const value = this.getValue();\n          if (value !== '') {\n            this.value = '';\n            this.emitInputChange();\n            /**\n             * When tapping clear button\n             * ensure input is focused after\n             * clearing input so users\n             * can quickly start typing.\n             */\n            if (shouldFocus && !this.focused) {\n              this.setFocus();\n              /**\n               * The setFocus call above will clear focusedValue,\n               * but ionChange will never have gotten a chance to\n               * fire. Manually revert focusedValue so onBlur can\n               * compare against what was in the box before the clear.\n               */\n              this.focusedValue = value;\n            }\n          }\n          resolve();\n        }, 16 * 4);\n      });\n    };\n    /**\n     * Clears the input field and tells the input to blur since\n     * the clearInput function doesn't want the input to blur\n     * then calls the custom cancel function if the user passed one in.\n     */\n    this.onCancelSearchbar = async ev => {\n      if (ev) {\n        ev.preventDefault();\n        ev.stopPropagation();\n      }\n      this.ionCancel.emit();\n      // get cached values before clearing the input\n      const value = this.getValue();\n      const focused = this.focused;\n      await this.onClearInput();\n      /**\n       * If there used to be something in the box, and we weren't focused\n       * beforehand (meaning no blur fired that would already handle this),\n       * manually fire ionChange.\n       */\n      if (value && !focused) {\n        this.emitValueChange(ev);\n      }\n      if (this.nativeInput) {\n        this.nativeInput.blur();\n      }\n    };\n    /**\n     * Update the Searchbar input value when the input changes\n     */\n    this.onInput = ev => {\n      const input = ev.target;\n      if (input) {\n        this.value = input.value;\n      }\n      this.emitInputChange(ev);\n    };\n    this.onChange = ev => {\n      this.emitValueChange(ev);\n    };\n    /**\n     * Sets the Searchbar to not focused and checks if it should align left\n     * based on whether there is a value in the searchbar or not.\n     */\n    this.onBlur = ev => {\n      this.focused = false;\n      this.ionBlur.emit();\n      this.positionElements();\n      if (this.focusedValue !== this.value) {\n        this.emitValueChange(ev);\n      }\n      this.focusedValue = undefined;\n    };\n    /**\n     * Sets the Searchbar to focused and active on input focus.\n     */\n    this.onFocus = () => {\n      this.focused = true;\n      this.focusedValue = this.value;\n      this.ionFocus.emit();\n      this.positionElements();\n    };\n    this.focused = false;\n    this.noAnimate = true;\n    this.color = undefined;\n    this.animated = false;\n    this.autocapitalize = 'off';\n    this.autocomplete = 'off';\n    this.autocorrect = 'off';\n    this.cancelButtonIcon = config.get('backButtonIcon', arrowBackSharp);\n    this.cancelButtonText = 'Cancel';\n    this.clearIcon = undefined;\n    this.debounce = undefined;\n    this.disabled = false;\n    this.inputmode = undefined;\n    this.enterkeyhint = undefined;\n    this.maxlength = undefined;\n    this.minlength = undefined;\n    this.name = this.inputId;\n    this.placeholder = 'Search';\n    this.searchIcon = undefined;\n    this.showCancelButton = 'never';\n    this.showClearButton = 'always';\n    this.spellcheck = false;\n    this.type = 'search';\n    this.value = '';\n  }\n  /**\n   * lang and dir are globally enumerated attributes.\n   * As a result, creating these as properties\n   * can have unintended side effects. Instead, we\n   * listen for attribute changes and inherit them\n   * to the inner `<input>` element.\n   */\n  onLangChanged(newValue) {\n    this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), {\n      lang: newValue\n    });\n    forceUpdate(this);\n  }\n  onDirChanged(newValue) {\n    this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), {\n      dir: newValue\n    });\n    forceUpdate(this);\n  }\n  debounceChanged() {\n    const {\n      ionInput,\n      debounce,\n      originalIonInput\n    } = this;\n    /**\n     * If debounce is undefined, we have to manually revert the ionInput emitter in case\n     * debounce used to be set to a number. Otherwise, the event would stay debounced.\n     */\n    this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n  }\n  valueChanged() {\n    const inputEl = this.nativeInput;\n    const value = this.getValue();\n    if (inputEl && inputEl.value !== value) {\n      inputEl.value = value;\n    }\n  }\n  showCancelButtonChanged() {\n    requestAnimationFrame(() => {\n      this.positionElements();\n      forceUpdate(this);\n    });\n  }\n  connectedCallback() {\n    this.emitStyle();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign({}, inheritAttributes(this.el, ['lang', 'dir']));\n  }\n  componentDidLoad() {\n    this.originalIonInput = this.ionInput;\n    this.positionElements();\n    this.debounceChanged();\n    setTimeout(() => {\n      this.noAnimate = false;\n    }, 300);\n  }\n  emitStyle() {\n    this.ionStyle.emit({\n      searchbar: true\n    });\n  }\n  /**\n   * Sets focus on the native `input` in `ion-searchbar`. Use this method instead of the global\n   * `input.focus()`.\n   *\n   * Developers who wish to focus an input when a page enters\n   * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n   *\n   * Developers who wish to focus an input when an overlay is presented\n   * should call `setFocus` after `didPresent` has resolved.\n   *\n   * See [managing focus](/docs/developing/managing-focus) for more information.\n   */\n  async setFocus() {\n    if (this.nativeInput) {\n      this.nativeInput.focus();\n    }\n  }\n  /**\n   * Returns the native `<input>` element used under the hood.\n   */\n  async getInputElement() {\n    /**\n     * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n     * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n     */\n    if (!this.nativeInput) {\n      await new Promise(resolve => componentOnReady(this.el, resolve));\n    }\n    return Promise.resolve(this.nativeInput);\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    // Emitting a value change should update the internal state for tracking the focused value\n    this.focusedValue = newValue;\n    this.ionChange.emit({\n      value: newValue,\n      event\n    });\n  }\n  /**\n   * Emits an `ionInput` event.\n   */\n  emitInputChange(event) {\n    const {\n      value\n    } = this;\n    this.ionInput.emit({\n      value,\n      event\n    });\n  }\n  /**\n   * Positions the input search icon, placeholder, and the cancel button\n   * based on the input value and if it is focused. (ios only)\n   */\n  positionElements() {\n    const value = this.getValue();\n    const prevAlignLeft = this.shouldAlignLeft;\n    const mode = getIonMode(this);\n    const shouldAlignLeft = !this.animated || value.trim() !== '' || !!this.focused;\n    this.shouldAlignLeft = shouldAlignLeft;\n    if (mode !== 'ios') {\n      return;\n    }\n    if (prevAlignLeft !== shouldAlignLeft) {\n      this.positionPlaceholder();\n    }\n    if (this.animated) {\n      this.positionCancelButton();\n    }\n  }\n  /**\n   * Positions the input placeholder\n   */\n  positionPlaceholder() {\n    const inputEl = this.nativeInput;\n    if (!inputEl) {\n      return;\n    }\n    const rtl = isRTL(this.el);\n    const iconEl = (this.el.shadowRoot || this.el).querySelector('.searchbar-search-icon');\n    if (this.shouldAlignLeft) {\n      inputEl.removeAttribute('style');\n      iconEl.removeAttribute('style');\n    } else {\n      // Create a dummy span to get the placeholder width\n      const doc = document;\n      const tempSpan = doc.createElement('span');\n      tempSpan.innerText = this.placeholder || '';\n      doc.body.appendChild(tempSpan);\n      // Get the width of the span then remove it\n      raf(() => {\n        const textWidth = tempSpan.offsetWidth;\n        tempSpan.remove();\n        // Calculate the input padding\n        const inputLeft = 'calc(50% - ' + textWidth / 2 + 'px)';\n        // Calculate the icon margin\n        /**\n         * We take the icon width to account\n         * for any text scales applied to the icon\n         * such as Dynamic Type on iOS as well as 8px\n         * of padding.\n         */\n        const iconLeft = 'calc(50% - ' + (textWidth / 2 + iconEl.clientWidth + 8) + 'px)';\n        // Set the input padding start and icon margin start\n        if (rtl) {\n          inputEl.style.paddingRight = inputLeft;\n          iconEl.style.marginRight = iconLeft;\n        } else {\n          inputEl.style.paddingLeft = inputLeft;\n          iconEl.style.marginLeft = iconLeft;\n        }\n      });\n    }\n  }\n  /**\n   * Show the iOS Cancel button on focus, hide it offscreen otherwise\n   */\n  positionCancelButton() {\n    const rtl = isRTL(this.el);\n    const cancelButton = (this.el.shadowRoot || this.el).querySelector('.searchbar-cancel-button');\n    const shouldShowCancel = this.shouldShowCancelButton();\n    if (cancelButton !== null && shouldShowCancel !== this.isCancelVisible) {\n      const cancelStyle = cancelButton.style;\n      this.isCancelVisible = shouldShowCancel;\n      if (shouldShowCancel) {\n        if (rtl) {\n          cancelStyle.marginLeft = '0';\n        } else {\n          cancelStyle.marginRight = '0';\n        }\n      } else {\n        const offset = cancelButton.offsetWidth;\n        if (offset > 0) {\n          if (rtl) {\n            cancelStyle.marginLeft = -offset + 'px';\n          } else {\n            cancelStyle.marginRight = -offset + 'px';\n          }\n        }\n      }\n    }\n  }\n  getValue() {\n    return this.value || '';\n  }\n  hasValue() {\n    return this.getValue() !== '';\n  }\n  /**\n   * Determines whether or not the cancel button should be visible onscreen.\n   * Cancel button should be shown if one of two conditions applies:\n   * 1. `showCancelButton` is set to `always`.\n   * 2. `showCancelButton` is set to `focus`, and the searchbar has been focused.\n   */\n  shouldShowCancelButton() {\n    if (this.showCancelButton === 'never' || this.showCancelButton === 'focus' && !this.focused) {\n      return false;\n    }\n    return true;\n  }\n  /**\n   * Determines whether or not the clear button should be visible onscreen.\n   * Clear button should be shown if one of two conditions applies:\n   * 1. `showClearButton` is set to `always`.\n   * 2. `showClearButton` is set to `focus`, and the searchbar has been focused.\n   */\n  shouldShowClearButton() {\n    if (this.showClearButton === 'never' || this.showClearButton === 'focus' && !this.focused) {\n      return false;\n    }\n    return true;\n  }\n  render() {\n    const {\n      cancelButtonText,\n      autocapitalize\n    } = this;\n    const animated = this.animated && config.getBoolean('animated', true);\n    const mode = getIonMode(this);\n    const clearIcon = this.clearIcon || (mode === 'ios' ? closeCircle : closeSharp);\n    const searchIcon = this.searchIcon || (mode === 'ios' ? searchOutline : searchSharp);\n    const shouldShowCancelButton = this.shouldShowCancelButton();\n    const cancelButton = this.showCancelButton !== 'never' && h(\"button\", {\n      key: '989f3e84c472ada6e66dd9b249d0d268bf17ce26',\n      \"aria-label\": cancelButtonText,\n      \"aria-hidden\": shouldShowCancelButton ? undefined : 'true',\n      type: \"button\",\n      tabIndex: mode === 'ios' && !shouldShowCancelButton ? -1 : undefined,\n      onMouseDown: this.onCancelSearchbar,\n      onTouchStart: this.onCancelSearchbar,\n      class: \"searchbar-cancel-button\"\n    }, h(\"div\", {\n      key: '7d335d4fde33822dc79d26b748ba2e98db7494bb',\n      \"aria-hidden\": \"true\"\n    }, mode === 'md' ? h(\"ion-icon\", {\n      \"aria-hidden\": \"true\",\n      mode: mode,\n      icon: this.cancelButtonIcon,\n      lazy: false\n    }) : cancelButtonText));\n    return h(Host, {\n      key: 'd1a1972725e949fb102c91487aaa7b9d10c2d718',\n      role: \"search\",\n      \"aria-disabled\": this.disabled ? 'true' : null,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'searchbar-animated': animated,\n        'searchbar-disabled': this.disabled,\n        'searchbar-no-animate': animated && this.noAnimate,\n        'searchbar-has-value': this.hasValue(),\n        'searchbar-left-aligned': this.shouldAlignLeft,\n        'searchbar-has-focus': this.focused,\n        'searchbar-should-show-clear': this.shouldShowClearButton(),\n        'searchbar-should-show-cancel': this.shouldShowCancelButton()\n      })\n    }, h(\"div\", {\n      key: 'add53640b2994cb6b2bf02792dafe51aba6b1684',\n      class: \"searchbar-input-container\"\n    }, h(\"input\", Object.assign({\n      key: '160cc36459a4a652e7f41ccd14dcdc782278779e',\n      \"aria-label\": \"search text\",\n      disabled: this.disabled,\n      ref: el => this.nativeInput = el,\n      class: \"searchbar-input\",\n      inputMode: this.inputmode,\n      enterKeyHint: this.enterkeyhint,\n      name: this.name,\n      onInput: this.onInput,\n      onChange: this.onChange,\n      onBlur: this.onBlur,\n      onFocus: this.onFocus,\n      minLength: this.minlength,\n      maxLength: this.maxlength,\n      placeholder: this.placeholder,\n      type: this.type,\n      value: this.getValue(),\n      autoCapitalize: autocapitalize === 'default' ? undefined : autocapitalize,\n      autoComplete: this.autocomplete,\n      autoCorrect: this.autocorrect,\n      spellcheck: this.spellcheck\n    }, this.inheritedAttributes)), mode === 'md' && cancelButton, h(\"ion-icon\", {\n      key: '8825fd13af0d2dea451ccc0e00ae7b5021dc01c4',\n      \"aria-hidden\": \"true\",\n      mode: mode,\n      icon: searchIcon,\n      lazy: false,\n      class: \"searchbar-search-icon\"\n    }), h(\"button\", {\n      key: '8a7b56da278b9ca5c4f5a4ee9c01924fd5ae29d8',\n      \"aria-label\": \"reset\",\n      type: \"button\",\n      \"no-blur\": true,\n      class: \"searchbar-clear-button\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the clear\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onClick: () => this.onClearInput(true)\n    }, h(\"ion-icon\", {\n      key: '24c55274516ab012d8c25f03525c6cdb9409e52f',\n      \"aria-hidden\": \"true\",\n      mode: mode,\n      icon: clearIcon,\n      lazy: false,\n      class: \"searchbar-clear-icon\"\n    }))), mode === 'ios' && cancelButton);\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"lang\": [\"onLangChanged\"],\n      \"dir\": [\"onDirChanged\"],\n      \"debounce\": [\"debounceChanged\"],\n      \"value\": [\"valueChanged\"],\n      \"showCancelButton\": [\"showCancelButtonChanged\"]\n    };\n  }\n};\nlet searchbarIds = 0;\nSearchbar.style = {\n  ios: IonSearchbarIosStyle0,\n  md: IonSearchbarMdStyle0\n};\nexport { Searchbar as ion_searchbar };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,YAAY,MAAM;AAAA,EACtB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,UAAU,iBAAiB,cAAc;AAC9C,SAAK,sBAAsB,CAAC;AAI5B,SAAK,eAAe,CAAM,gBAAe;AACvC,WAAK,SAAS,KAAK;AACnB,aAAO,IAAI,QAAQ,aAAW;AAG5B,mBAAW,MAAM;AACf,gBAAM,QAAQ,KAAK,SAAS;AAC5B,cAAI,UAAU,IAAI;AAChB,iBAAK,QAAQ;AACb,iBAAK,gBAAgB;AAOrB,gBAAI,eAAe,CAAC,KAAK,SAAS;AAChC,mBAAK,SAAS;AAOd,mBAAK,eAAe;AAAA,YACtB;AAAA,UACF;AACA,kBAAQ;AAAA,QACV,GAAG,KAAK,CAAC;AAAA,MACX,CAAC;AAAA,IACH;AAMA,SAAK,oBAAoB,CAAM,OAAM;AACnC,UAAI,IAAI;AACN,WAAG,eAAe;AAClB,WAAG,gBAAgB;AAAA,MACrB;AACA,WAAK,UAAU,KAAK;AAEpB,YAAM,QAAQ,KAAK,SAAS;AAC5B,YAAM,UAAU,KAAK;AACrB,YAAM,KAAK,aAAa;AAMxB,UAAI,SAAS,CAAC,SAAS;AACrB,aAAK,gBAAgB,EAAE;AAAA,MACzB;AACA,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,KAAK;AAAA,MACxB;AAAA,IACF;AAIA,SAAK,UAAU,QAAM;AACnB,YAAM,QAAQ,GAAG;AACjB,UAAI,OAAO;AACT,aAAK,QAAQ,MAAM;AAAA,MACrB;AACA,WAAK,gBAAgB,EAAE;AAAA,IACzB;AACA,SAAK,WAAW,QAAM;AACpB,WAAK,gBAAgB,EAAE;AAAA,IACzB;AAKA,SAAK,SAAS,QAAM;AAClB,WAAK,UAAU;AACf,WAAK,QAAQ,KAAK;AAClB,WAAK,iBAAiB;AACtB,UAAI,KAAK,iBAAiB,KAAK,OAAO;AACpC,aAAK,gBAAgB,EAAE;AAAA,MACzB;AACA,WAAK,eAAe;AAAA,IACtB;AAIA,SAAK,UAAU,MAAM;AACnB,WAAK,UAAU;AACf,WAAK,eAAe,KAAK;AACzB,WAAK,SAAS,KAAK;AACnB,WAAK,iBAAiB;AAAA,IACxB;AACA,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,mBAAmB,OAAO,IAAI,kBAAkB,cAAc;AACnE,SAAK,mBAAmB;AACxB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK;AACjB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,UAAU;AACtB,SAAK,sBAAsB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,mBAAmB,GAAG;AAAA,MACpF,MAAM;AAAA,IACR,CAAC;AACD,gBAAY,IAAI;AAAA,EAClB;AAAA,EACA,aAAa,UAAU;AACrB,SAAK,sBAAsB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,mBAAmB,GAAG;AAAA,MACpF,KAAK;AAAA,IACP,CAAC;AACD,gBAAY,IAAI;AAAA,EAClB;AAAA,EACA,kBAAkB;AAChB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAKJ,SAAK,WAAW,aAAa,SAAY,qBAAqB,QAAQ,qBAAqB,SAAS,mBAAmB,WAAW,cAAc,UAAU,QAAQ;AAAA,EACpK;AAAA,EACA,eAAe;AACb,UAAM,UAAU,KAAK;AACrB,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,WAAW,QAAQ,UAAU,OAAO;AACtC,cAAQ,QAAQ;AAAA,IAClB;AAAA,EACF;AAAA,EACA,0BAA0B;AACxB,0BAAsB,MAAM;AAC1B,WAAK,iBAAiB;AACtB,kBAAY,IAAI;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,oBAAoB;AAClB,SAAK,sBAAsB,OAAO,OAAO,CAAC,GAAG,kBAAkB,KAAK,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;AAAA,EAC1F;AAAA,EACA,mBAAmB;AACjB,SAAK,mBAAmB,KAAK;AAC7B,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,eAAW,MAAM;AACf,WAAK,YAAY;AAAA,IACnB,GAAG,GAAG;AAAA,EACR;AAAA,EACA,YAAY;AACV,SAAK,SAAS,KAAK;AAAA,MACjB,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaM,WAAW;AAAA;AACf,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,MAAM;AAAA,MACzB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,kBAAkB;AAAA;AAKtB,UAAI,CAAC,KAAK,aAAa;AACrB,cAAM,IAAI,QAAQ,aAAW,iBAAiB,KAAK,IAAI,OAAO,CAAC;AAAA,MACjE;AACA,aAAO,QAAQ,QAAQ,KAAK,WAAW;AAAA,IACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,OAAO;AACrB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAEJ,UAAM,WAAW,SAAS,OAAO,QAAQ,MAAM,SAAS;AAExD,SAAK,eAAe;AACpB,SAAK,UAAU,KAAK;AAAA,MAClB,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,OAAO;AACrB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,SAAK,SAAS,KAAK;AAAA,MACjB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,gBAAgB,KAAK;AAC3B,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,kBAAkB,CAAC,KAAK,YAAY,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK;AACxE,SAAK,kBAAkB;AACvB,QAAI,SAAS,OAAO;AAClB;AAAA,IACF;AACA,QAAI,kBAAkB,iBAAiB;AACrC,WAAK,oBAAoB;AAAA,IAC3B;AACA,QAAI,KAAK,UAAU;AACjB,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,UAAM,UAAU,KAAK;AACrB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,UAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAM,UAAU,KAAK,GAAG,cAAc,KAAK,IAAI,cAAc,wBAAwB;AACrF,QAAI,KAAK,iBAAiB;AACxB,cAAQ,gBAAgB,OAAO;AAC/B,aAAO,gBAAgB,OAAO;AAAA,IAChC,OAAO;AAEL,YAAM,MAAM;AACZ,YAAM,WAAW,IAAI,cAAc,MAAM;AACzC,eAAS,YAAY,KAAK,eAAe;AACzC,UAAI,KAAK,YAAY,QAAQ;AAE7B,UAAI,MAAM;AACR,cAAM,YAAY,SAAS;AAC3B,iBAAS,OAAO;AAEhB,cAAM,YAAY,gBAAgB,YAAY,IAAI;AAQlD,cAAM,WAAW,iBAAiB,YAAY,IAAI,OAAO,cAAc,KAAK;AAE5E,YAAI,KAAK;AACP,kBAAQ,MAAM,eAAe;AAC7B,iBAAO,MAAM,cAAc;AAAA,QAC7B,OAAO;AACL,kBAAQ,MAAM,cAAc;AAC5B,iBAAO,MAAM,aAAa;AAAA,QAC5B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB;AACrB,UAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAM,gBAAgB,KAAK,GAAG,cAAc,KAAK,IAAI,cAAc,0BAA0B;AAC7F,UAAM,mBAAmB,KAAK,uBAAuB;AACrD,QAAI,iBAAiB,QAAQ,qBAAqB,KAAK,iBAAiB;AACtE,YAAM,cAAc,aAAa;AACjC,WAAK,kBAAkB;AACvB,UAAI,kBAAkB;AACpB,YAAI,KAAK;AACP,sBAAY,aAAa;AAAA,QAC3B,OAAO;AACL,sBAAY,cAAc;AAAA,QAC5B;AAAA,MACF,OAAO;AACL,cAAM,SAAS,aAAa;AAC5B,YAAI,SAAS,GAAG;AACd,cAAI,KAAK;AACP,wBAAY,aAAa,CAAC,SAAS;AAAA,UACrC,OAAO;AACL,wBAAY,cAAc,CAAC,SAAS;AAAA,UACtC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,WAAW;AACT,WAAO,KAAK,SAAS,MAAM;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,yBAAyB;AACvB,QAAI,KAAK,qBAAqB,WAAW,KAAK,qBAAqB,WAAW,CAAC,KAAK,SAAS;AAC3F,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB;AACtB,QAAI,KAAK,oBAAoB,WAAW,KAAK,oBAAoB,WAAW,CAAC,KAAK,SAAS;AACzF,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,KAAK,YAAY,OAAO,WAAW,YAAY,IAAI;AACpE,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,YAAY,KAAK,cAAc,SAAS,QAAQ,cAAc;AACpE,UAAM,aAAa,KAAK,eAAe,SAAS,QAAQ,gBAAgB;AACxE,UAAM,yBAAyB,KAAK,uBAAuB;AAC3D,UAAM,eAAe,KAAK,qBAAqB,WAAW,EAAE,UAAU;AAAA,MACpE,KAAK;AAAA,MACL,cAAc;AAAA,MACd,eAAe,yBAAyB,SAAY;AAAA,MACpD,MAAM;AAAA,MACN,UAAU,SAAS,SAAS,CAAC,yBAAyB,KAAK;AAAA,MAC3D,aAAa,KAAK;AAAA,MAClB,cAAc,KAAK;AAAA,MACnB,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,eAAe;AAAA,IACjB,GAAG,SAAS,OAAO,EAAE,YAAY;AAAA,MAC/B,eAAe;AAAA,MACf;AAAA,MACA,MAAM,KAAK;AAAA,MACX,MAAM;AAAA,IACR,CAAC,IAAI,gBAAgB,CAAC;AACtB,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,MAAM;AAAA,MACN,iBAAiB,KAAK,WAAW,SAAS;AAAA,MAC1C,OAAO,mBAAmB,KAAK,OAAO;AAAA,QACpC,CAAC,IAAI,GAAG;AAAA,QACR,sBAAsB;AAAA,QACtB,sBAAsB,KAAK;AAAA,QAC3B,wBAAwB,YAAY,KAAK;AAAA,QACzC,uBAAuB,KAAK,SAAS;AAAA,QACrC,0BAA0B,KAAK;AAAA,QAC/B,uBAAuB,KAAK;AAAA,QAC5B,+BAA+B,KAAK,sBAAsB;AAAA,QAC1D,gCAAgC,KAAK,uBAAuB;AAAA,MAC9D,CAAC;AAAA,IACH,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,SAAS,OAAO,OAAO;AAAA,MAC1B,KAAK;AAAA,MACL,cAAc;AAAA,MACd,UAAU,KAAK;AAAA,MACf,KAAK,QAAM,KAAK,cAAc;AAAA,MAC9B,OAAO;AAAA,MACP,WAAW,KAAK;AAAA,MAChB,cAAc,KAAK;AAAA,MACnB,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,MACd,WAAW,KAAK;AAAA,MAChB,WAAW,KAAK;AAAA,MAChB,aAAa,KAAK;AAAA,MAClB,MAAM,KAAK;AAAA,MACX,OAAO,KAAK,SAAS;AAAA,MACrB,gBAAgB,mBAAmB,YAAY,SAAY;AAAA,MAC3D,cAAc,KAAK;AAAA,MACnB,aAAa,KAAK;AAAA,MAClB,YAAY,KAAK;AAAA,IACnB,GAAG,KAAK,mBAAmB,CAAC,GAAG,SAAS,QAAQ,cAAc,EAAE,YAAY;AAAA,MAC1E,KAAK;AAAA,MACL,eAAe;AAAA,MACf;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC,GAAG,EAAE,UAAU;AAAA,MACd,KAAK;AAAA,MACL,cAAc;AAAA,MACd,MAAM;AAAA,MACN,WAAW;AAAA,MACX,OAAO;AAAA,MACP,eAAe,QAAM;AAMnB,WAAG,eAAe;AAAA,MACpB;AAAA,MACA,SAAS,MAAM,KAAK,aAAa,IAAI;AAAA,IACvC,GAAG,EAAE,YAAY;AAAA,MACf,KAAK;AAAA,MACL,eAAe;AAAA,MACf;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC,CAAC,CAAC,GAAG,SAAS,SAAS,YAAY;AAAA,EACtC;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,QAAQ,CAAC,eAAe;AAAA,MACxB,OAAO,CAAC,cAAc;AAAA,MACtB,YAAY,CAAC,iBAAiB;AAAA,MAC9B,SAAS,CAAC,cAAc;AAAA,MACxB,oBAAoB,CAAC,yBAAyB;AAAA,IAChD;AAAA,EACF;AACF;AACA,IAAI,eAAe;AACnB,UAAU,QAAQ;AAAA,EAChB,KAAK;AAAA,EACL,IAAI;AACN;", "names": [], "x_google_ignoreList": [0]}
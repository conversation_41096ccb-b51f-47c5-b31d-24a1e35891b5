{"version": 3, "sources": ["src/environments/environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\r\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\r\n// The list of file replacements can be found in `angular.json`.\r\n\r\nexport const environment = {\r\n  production: false,\r\n\r\n  // ===== CURRENT LOCATION CONFIGURATION =====\r\n  // 🏠 HOME IP: *************** (current location)\r\n  // 🏫 SCHOOL IP: ************* (when at school)\r\n  // For mobile device testing, use your computer's actual IP address\r\n  apiUrl: 'http://***************:8000/api', // Current home IP - change this to your computer's IP\r\n\r\n  // ===== ALTERNATIVE URLS =====\r\n  // Switch between these based on your location:\r\n  // apiUrl: 'http://*************:8000/api', // School IP\r\n  // apiUrl: 'https://2xGdwnDAvorrvfTYkPJwFdH0bl8_7EG3njhPc7viiNrH5QWVN.ngrok-free.app/api', // ngrok URL\r\n  // apiUrl: 'http://localhost:8000/api', // Only works for web development\r\n\r\n  // API endpoints for testing\r\n  healthCheckUrl: 'http://***************:8000/up',\r\n  testApiUrl: 'http://***************:8000/api/test',\r\n\r\n  // External APIs\r\n  orsApiKey: '5b3ce3597851110001cf6248d05f92e32cab4d1da9db6036a3a53fe7', // Keep for fallback\r\n  mapboxAccessToken: 'pk.eyJ1IjoianVucmVsMDcwNDA1IiwiYSI6ImNtYjNocGs1YjBxc2cydnB5OG14NmNzYTIifQ.FGsozY9ibdn28Rg91_msIg',\r\n\r\n  // Firebase configuration - synchronized with Laravel backend\r\n  firebase: {\r\n    projectId: 'last-5acaf',\r\n    messagingSenderId: '660101685392',\r\n    appId: '1:660101685392:android:c7c81cb0ccca4f30cb7815',\r\n    apiKey: 'AIzaSyA5H6_NGbhDlVZ4l67qEC_JNRmcXPQ-GAo',\r\n    databaseURL: 'https://last-5acaf-default-rtdb.firebaseio.com',\r\n    storageBucket: 'last-5acaf.appspot.com'\r\n  },\r\n\r\n  // Communication settings\r\n  communication: {\r\n    retryAttempts: 3,\r\n    timeoutMs: 10000,\r\n    enableOfflineMode: true,\r\n    enableRealTimeUpdates: true\r\n  }\r\n};\r\n\r\n"], "mappings": ";AAIO,IAAM,cAAc;EACzB,YAAY;;;;;EAMZ,QAAQ;;;;;;;;EASR,gBAAgB;EAChB,YAAY;;EAGZ,WAAW;;EACX,mBAAmB;;EAGnB,UAAU;IACR,WAAW;IACX,mBAAmB;IACnB,OAAO;IACP,QAAQ;IACR,aAAa;IACb,eAAe;;;EAIjB,eAAe;IACb,eAAe;IACf,WAAW;IACX,mBAAmB;IACnB,uBAAuB;;;", "names": []}
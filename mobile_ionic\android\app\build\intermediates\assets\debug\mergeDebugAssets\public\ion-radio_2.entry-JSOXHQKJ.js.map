{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-radio_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { a as addEventListener, b as removeEventListener, d as renderHiddenInput } from './helpers-d94bc8ad.js';\nimport { i as isOptionSelected } from './compare-with-utils-a96ff2ea.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport './index-cfd9c1f2.js';\nconst radioIosCss = \":host{--inner-border-radius:50%;display:inline-block;position:relative;max-width:100%;min-height:inherit;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.radio-disabled){pointer-events:none}.radio-icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;contain:layout size style}.radio-icon,.radio-inner{-webkit-box-sizing:border-box;box-sizing:border-box}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host(:focus){outline:none}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.radio-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.radio-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.radio-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between) .radio-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.radio-justify-start) .radio-wrapper{-ms-flex-pack:start;justify-content:start}:host(.radio-justify-end) .radio-wrapper{-ms-flex-pack:end;justify-content:end}:host(.radio-alignment-start) .radio-wrapper{-ms-flex-align:start;align-items:start}:host(.radio-alignment-center) .radio-wrapper{-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between),:host(.radio-justify-start),:host(.radio-justify-end),:host(.radio-alignment-start),:host(.radio-alignment-center){display:block}:host(.radio-label-placement-start) .radio-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.radio-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-end) .radio-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.radio-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.radio-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px}:host(.radio-label-placement-stacked) .radio-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.radio-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host{--color-checked:var(--ion-color-primary, #0054e9)}:host(.ion-color.radio-checked) .radio-inner{border-color:var(--ion-color-base)}.item-radio.item-ios ion-label{-webkit-margin-start:0;margin-inline-start:0}.radio-inner{width:33%;height:50%}:host(.radio-checked) .radio-inner{-webkit-transform:rotate(45deg);transform:rotate(45deg);border-width:0.125rem;border-top-width:0;border-left-width:0;border-style:solid;border-color:var(--color-checked)}:host(.radio-disabled){opacity:0.3}:host(.ion-focused) .radio-icon::after{border-radius:var(--inner-border-radius);top:-8px;display:block;position:absolute;width:36px;height:36px;background:var(--ion-color-primary-tint, #1a65eb);content:\\\"\\\";opacity:0.2}:host(.ion-focused) .radio-icon::after{inset-inline-start:-9px}.native-wrapper .radio-icon{width:0.9375rem;height:1.5rem}\";\nconst IonRadioIosStyle0 = radioIosCss;\nconst radioMdCss = \":host{--inner-border-radius:50%;display:inline-block;position:relative;max-width:100%;min-height:inherit;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.radio-disabled){pointer-events:none}.radio-icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;contain:layout size style}.radio-icon,.radio-inner{-webkit-box-sizing:border-box;box-sizing:border-box}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host(:focus){outline:none}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.radio-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.radio-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.radio-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between) .radio-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.radio-justify-start) .radio-wrapper{-ms-flex-pack:start;justify-content:start}:host(.radio-justify-end) .radio-wrapper{-ms-flex-pack:end;justify-content:end}:host(.radio-alignment-start) .radio-wrapper{-ms-flex-align:start;align-items:start}:host(.radio-alignment-center) .radio-wrapper{-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between),:host(.radio-justify-start),:host(.radio-justify-end),:host(.radio-alignment-start),:host(.radio-alignment-center){display:block}:host(.radio-label-placement-start) .radio-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.radio-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-end) .radio-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.radio-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.radio-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px}:host(.radio-label-placement-stacked) .radio-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.radio-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host{--color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--color-checked:var(--ion-color-primary, #0054e9);--border-width:0.125rem;--border-style:solid;--border-radius:50%}:host(.ion-color) .radio-inner{background:var(--ion-color-base)}:host(.ion-color.radio-checked) .radio-icon{border-color:var(--ion-color-base)}.radio-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--color)}.radio-inner{border-radius:var(--inner-border-radius);width:calc(50% + var(--border-width));height:calc(50% + var(--border-width));-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0);-webkit-transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);background:var(--color-checked)}:host(.radio-checked) .radio-icon{border-color:var(--color-checked)}:host(.radio-checked) .radio-inner{-webkit-transform:scale3d(1, 1, 1);transform:scale3d(1, 1, 1)}:host(.radio-disabled) .label-text-wrapper{opacity:0.38}:host(.radio-disabled) .native-wrapper{opacity:0.63}:host(.ion-focused) .radio-icon::after{border-radius:var(--inner-border-radius);display:block;position:absolute;width:36px;height:36px;background:var(--ion-color-primary-tint, #1a65eb);content:\\\"\\\";opacity:0.2}.native-wrapper .radio-icon{width:1.25rem;height:1.25rem}\";\nconst IonRadioMdStyle0 = radioMdCss;\nconst Radio = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.inputId = `ion-rb-${radioButtonIds++}`;\n    this.radioGroup = null;\n    this.updateState = () => {\n      if (this.radioGroup) {\n        const {\n          compareWith,\n          value: radioGroupValue\n        } = this.radioGroup;\n        this.checked = isOptionSelected(radioGroupValue, this.value, compareWith);\n      }\n    };\n    this.onClick = () => {\n      const {\n        radioGroup,\n        checked,\n        disabled\n      } = this;\n      if (disabled) {\n        return;\n      }\n      /**\n       * The modern control does not use a native input\n       * inside of the radio host, so we cannot rely on the\n       * ev.preventDefault() behavior above. If the radio\n       * is checked and the parent radio group allows for empty\n       * selection, then we can set the checked state to false.\n       * Otherwise, the checked state should always be set\n       * to true because the checked state cannot be toggled.\n       */\n      if (checked && (radioGroup === null || radioGroup === void 0 ? void 0 : radioGroup.allowEmptySelection)) {\n        this.checked = false;\n      } else {\n        this.checked = true;\n      }\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.checked = false;\n    this.buttonTabindex = -1;\n    this.color = undefined;\n    this.name = this.inputId;\n    this.disabled = false;\n    this.value = undefined;\n    this.labelPlacement = 'start';\n    this.justify = undefined;\n    this.alignment = undefined;\n  }\n  valueChanged() {\n    /**\n     * The new value of the radio may\n     * match the radio group's value,\n     * so we see if it should be checked.\n     */\n    this.updateState();\n  }\n  componentDidLoad() {\n    /**\n     * The value may be `undefined` if it\n     * gets set before the radio is\n     * rendered. This ensures that the radio\n     * is checked if the value matches. This\n     * happens most often when Angular is\n     * rendering the radio.\n     */\n    this.updateState();\n  }\n  /** @internal */\n  async setFocus(ev) {\n    if (ev !== undefined) {\n      ev.stopPropagation();\n      ev.preventDefault();\n    }\n    this.el.focus();\n  }\n  /** @internal */\n  async setButtonTabindex(value) {\n    this.buttonTabindex = value;\n  }\n  connectedCallback() {\n    if (this.value === undefined) {\n      this.value = this.inputId;\n    }\n    const radioGroup = this.radioGroup = this.el.closest('ion-radio-group');\n    if (radioGroup) {\n      this.updateState();\n      addEventListener(radioGroup, 'ionValueChange', this.updateState);\n    }\n  }\n  disconnectedCallback() {\n    const radioGroup = this.radioGroup;\n    if (radioGroup) {\n      removeEventListener(radioGroup, 'ionValueChange', this.updateState);\n      this.radioGroup = null;\n    }\n  }\n  get hasLabel() {\n    return this.el.textContent !== '';\n  }\n  renderRadioControl() {\n    return h(\"div\", {\n      class: \"radio-icon\",\n      part: \"container\"\n    }, h(\"div\", {\n      class: \"radio-inner\",\n      part: \"mark\"\n    }), h(\"div\", {\n      class: \"radio-ripple\"\n    }));\n  }\n  render() {\n    const {\n      checked,\n      disabled,\n      color,\n      el,\n      justify,\n      labelPlacement,\n      hasLabel,\n      buttonTabindex,\n      alignment\n    } = this;\n    const mode = getIonMode(this);\n    const inItem = hostContext('ion-item', el);\n    return h(Host, {\n      key: '8badd4aec277addc0793e14df21f73bb345e99b7',\n      onFocus: this.onFocus,\n      onBlur: this.onBlur,\n      onClick: this.onClick,\n      class: createColorClasses(color, {\n        [mode]: true,\n        'in-item': inItem,\n        'radio-checked': checked,\n        'radio-disabled': disabled,\n        [`radio-justify-${justify}`]: justify !== undefined,\n        [`radio-alignment-${alignment}`]: alignment !== undefined,\n        [`radio-label-placement-${labelPlacement}`]: true,\n        // Focus and active styling should not apply when the radio is in an item\n        'ion-activatable': !inItem,\n        'ion-focusable': !inItem\n      }),\n      role: \"radio\",\n      \"aria-checked\": checked ? 'true' : 'false',\n      \"aria-disabled\": disabled ? 'true' : null,\n      tabindex: buttonTabindex\n    }, h(\"label\", {\n      key: '8765b847edc93a1b5a16506e155ed03da807bb10',\n      class: \"radio-wrapper\"\n    }, h(\"div\", {\n      key: '3d568a0192a32d4f0b8a920019c79ff02639b5c9',\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !hasLabel\n      },\n      part: \"label\"\n    }, h(\"slot\", {\n      key: '331f3dc2ce5f6ed8f124fc4560f92e0f7c668a85'\n    })), h(\"div\", {\n      key: '473bd4aaf448753e385f2dda3fddc9f56379aa19',\n      class: \"native-wrapper\"\n    }, this.renderRadioControl())));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nlet radioButtonIds = 0;\nRadio.style = {\n  ios: IonRadioIosStyle0,\n  md: IonRadioMdStyle0\n};\nconst radioGroupIosCss = \"ion-radio-group{vertical-align:top}.radio-group-wrapper{display:inline}.radio-group-top{line-height:1.5}.radio-group-top .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.radio-group-top .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid .radio-group-top .error-text{display:block}.ion-touched.ion-invalid .radio-group-top .helper-text{display:none}ion-list .radio-group-top{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px}\";\nconst IonRadioGroupIosStyle0 = radioGroupIosCss;\nconst radioGroupMdCss = \"ion-radio-group{vertical-align:top}.radio-group-wrapper{display:inline}.radio-group-top{line-height:1.5}.radio-group-top .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.radio-group-top .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid .radio-group-top .error-text{display:block}.ion-touched.ion-invalid .radio-group-top .helper-text{display:none}ion-list .radio-group-top{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px}\";\nconst IonRadioGroupMdStyle0 = radioGroupMdCss;\nconst RadioGroup = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n    this.inputId = `ion-rg-${radioGroupIds++}`;\n    this.helperTextId = `${this.inputId}-helper-text`;\n    this.errorTextId = `${this.inputId}-error-text`;\n    this.labelId = `${this.inputId}-lbl`;\n    this.setRadioTabindex = value => {\n      const radios = this.getRadios();\n      // Get the first radio that is not disabled and the checked one\n      const first = radios.find(radio => !radio.disabled);\n      const checked = radios.find(radio => radio.value === value && !radio.disabled);\n      if (!first && !checked) {\n        return;\n      }\n      // If an enabled checked radio exists, set it to be the focusable radio\n      // otherwise we default to focus the first radio\n      const focusable = checked || first;\n      for (const radio of radios) {\n        const tabindex = radio === focusable ? 0 : -1;\n        radio.setButtonTabindex(tabindex);\n      }\n    };\n    this.onClick = ev => {\n      ev.preventDefault();\n      /**\n       * The Radio Group component mandates that only one radio button\n       * within the group can be selected at any given time. Since `ion-radio`\n       * is a shadow DOM component, it cannot natively perform this behavior\n       * using the `name` attribute.\n       */\n      const selectedRadio = ev.target && ev.target.closest('ion-radio');\n      /**\n       * Our current disabled prop definition causes Stencil to mark it\n       * as optional. While this is not desired, fixing this behavior\n       * in Stencil is a significant breaking change, so this effort is\n       * being de-risked in STENCIL-917. Until then, we compromise\n       * here by checking for falsy `disabled` values instead of strictly\n       * checking `disabled === false`.\n       */\n      if (selectedRadio && !selectedRadio.disabled) {\n        const currentValue = this.value;\n        const newValue = selectedRadio.value;\n        if (newValue !== currentValue) {\n          this.value = newValue;\n          this.emitValueChange(ev);\n        } else if (this.allowEmptySelection) {\n          this.value = undefined;\n          this.emitValueChange(ev);\n        }\n      }\n    };\n    this.allowEmptySelection = false;\n    this.compareWith = undefined;\n    this.name = this.inputId;\n    this.value = undefined;\n    this.helperText = undefined;\n    this.errorText = undefined;\n  }\n  valueChanged(value) {\n    this.setRadioTabindex(value);\n    this.ionValueChange.emit({\n      value\n    });\n  }\n  componentDidLoad() {\n    /**\n     * There's an issue when assigning a value to the radio group\n     * within the Angular primary content (rendering within the\n     * app component template). When the template is isolated to a route,\n     * the value is assigned correctly.\n     * To address this issue, we need to ensure that the watcher is\n     * called after the component has finished loading,\n     * allowing the emit to be dispatched correctly.\n     */\n    this.valueChanged(this.value);\n  }\n  async connectedCallback() {\n    // Get the list header if it exists and set the id\n    // this is used to set aria-labelledby\n    const header = this.el.querySelector('ion-list-header') || this.el.querySelector('ion-item-divider');\n    if (header) {\n      const label = this.label = header.querySelector('ion-label');\n      if (label) {\n        this.labelId = label.id = this.name + '-lbl';\n      }\n    }\n  }\n  getRadios() {\n    return Array.from(this.el.querySelectorAll('ion-radio'));\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange(event) {\n    const {\n      value\n    } = this;\n    this.ionChange.emit({\n      value,\n      event\n    });\n  }\n  onKeydown(ev) {\n    // We don't want the value to automatically change/emit when the radio group is part of a select interface\n    // as this will cause the interface to close when navigating through the radio group options\n    const inSelectInterface = !!this.el.closest('ion-select-popover') || !!this.el.closest('ion-select-modal');\n    if (ev.target && !this.el.contains(ev.target)) {\n      return;\n    }\n    // Get all radios inside of the radio group and then\n    // filter out disabled radios since we need to skip those\n    const radios = this.getRadios().filter(radio => !radio.disabled);\n    // Only move the radio if the current focus is in the radio group\n    if (ev.target && radios.includes(ev.target)) {\n      const index = radios.findIndex(radio => radio === ev.target);\n      const current = radios[index];\n      let next;\n      // If hitting arrow down or arrow right, move to the next radio\n      // If we're on the last radio, move to the first radio\n      if (['ArrowDown', 'ArrowRight'].includes(ev.key)) {\n        next = index === radios.length - 1 ? radios[0] : radios[index + 1];\n      }\n      // If hitting arrow up or arrow left, move to the previous radio\n      // If we're on the first radio, move to the last radio\n      if (['ArrowUp', 'ArrowLeft'].includes(ev.key)) {\n        next = index === 0 ? radios[radios.length - 1] : radios[index - 1];\n      }\n      if (next && radios.includes(next)) {\n        next.setFocus(ev);\n        if (!inSelectInterface) {\n          this.value = next.value;\n          this.emitValueChange(ev);\n        }\n      }\n      // Update the radio group value when a user presses the\n      // space bar on top of a selected radio\n      if ([' '].includes(ev.key)) {\n        const previousValue = this.value;\n        this.value = this.allowEmptySelection && this.value !== undefined ? undefined : current.value;\n        if (previousValue !== this.value || this.allowEmptySelection) {\n          /**\n           * Value change should only be emitted if the value is different,\n           * such as selecting a new radio with the space bar or if\n           * the radio group allows for empty selection and the user\n           * is deselecting a checked radio.\n           */\n          this.emitValueChange(ev);\n        }\n        // Prevent browsers from jumping\n        // to the bottom of the screen\n        ev.preventDefault();\n      }\n    }\n  }\n  /** @internal */\n  async setFocus() {\n    const radioToFocus = this.getRadios().find(r => r.tabIndex !== -1);\n    radioToFocus === null || radioToFocus === void 0 ? void 0 : radioToFocus.setFocus();\n  }\n  /**\n   * Renders the helper text or error text values\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    const hasHintText = !!helperText || !!errorText;\n    if (!hasHintText) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"radio-group-top\"\n    }, h(\"div\", {\n      id: helperTextId,\n      class: \"helper-text\"\n    }, helperText), h(\"div\", {\n      id: errorTextId,\n      class: \"error-text\"\n    }, errorText));\n  }\n  getHintTextID() {\n    const {\n      el,\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n      return errorTextId;\n    }\n    if (helperText) {\n      return helperTextId;\n    }\n    return undefined;\n  }\n  render() {\n    const {\n      label,\n      labelId,\n      el,\n      name,\n      value\n    } = this;\n    const mode = getIonMode(this);\n    renderHiddenInput(true, el, name, value, false);\n    return h(Host, {\n      key: 'cac92777297029d7fd1b6af264d92850e35dfbba',\n      role: \"radiogroup\",\n      \"aria-labelledby\": label ? labelId : null,\n      \"aria-describedby\": this.getHintTextID(),\n      \"aria-invalid\": this.getHintTextID() === this.errorTextId,\n      onClick: this.onClick,\n      class: mode\n    }, this.renderHintText(), h(\"div\", {\n      key: '6b5c634dba30d54eedc031b077863f3d6a9d9e9b',\n      class: \"radio-group-wrapper\"\n    }, h(\"slot\", {\n      key: '443edb3ff6f4c59d4c4324c8a19f2d6def47a322'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nlet radioGroupIds = 0;\nRadioGroup.style = {\n  ios: IonRadioGroupIosStyle0,\n  md: IonRadioGroupMdStyle0\n};\nexport { Radio as ion_radio, RadioGroup as ion_radio_group };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,QAAQ,MAAM;AAAA,EAClB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,UAAU,UAAU,gBAAgB;AACzC,SAAK,aAAa;AAClB,SAAK,cAAc,MAAM;AACvB,UAAI,KAAK,YAAY;AACnB,cAAM;AAAA,UACJ;AAAA,UACA,OAAO;AAAA,QACT,IAAI,KAAK;AACT,aAAK,UAAU,iBAAiB,iBAAiB,KAAK,OAAO,WAAW;AAAA,MAC1E;AAAA,IACF;AACA,SAAK,UAAU,MAAM;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,UAAU;AACZ;AAAA,MACF;AAUA,UAAI,YAAY,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,sBAAsB;AACvG,aAAK,UAAU;AAAA,MACjB,OAAO;AACL,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AACA,SAAK,UAAU,MAAM;AACnB,WAAK,SAAS,KAAK;AAAA,IACrB;AACA,SAAK,SAAS,MAAM;AAClB,WAAK,QAAQ,KAAK;AAAA,IACpB;AACA,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,QAAQ;AACb,SAAK,OAAO,KAAK;AACjB,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,iBAAiB;AACtB,SAAK,UAAU;AACf,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,eAAe;AAMb,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,mBAAmB;AASjB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEM,SAAS,IAAI;AAAA;AACjB,UAAI,OAAO,QAAW;AACpB,WAAG,gBAAgB;AACnB,WAAG,eAAe;AAAA,MACpB;AACA,WAAK,GAAG,MAAM;AAAA,IAChB;AAAA;AAAA;AAAA,EAEM,kBAAkB,OAAO;AAAA;AAC7B,WAAK,iBAAiB;AAAA,IACxB;AAAA;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,UAAU,QAAW;AAC5B,WAAK,QAAQ,KAAK;AAAA,IACpB;AACA,UAAM,aAAa,KAAK,aAAa,KAAK,GAAG,QAAQ,iBAAiB;AACtE,QAAI,YAAY;AACd,WAAK,YAAY;AACjB,uBAAiB,YAAY,kBAAkB,KAAK,WAAW;AAAA,IACjE;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,UAAM,aAAa,KAAK;AACxB,QAAI,YAAY;AACd,0BAAoB,YAAY,kBAAkB,KAAK,WAAW;AAClE,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,GAAG,gBAAgB;AAAA,EACjC;AAAA,EACA,qBAAqB;AACnB,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,MACP,MAAM;AAAA,IACR,GAAG,EAAE,OAAO;AAAA,MACV,OAAO;AAAA,MACP,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,OAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,SAAS,YAAY,YAAY,EAAE;AACzC,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,MACd,OAAO,mBAAmB,OAAO;AAAA,QAC/B,CAAC,IAAI,GAAG;AAAA,QACR,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,CAAC,iBAAiB,OAAO,EAAE,GAAG,YAAY;AAAA,QAC1C,CAAC,mBAAmB,SAAS,EAAE,GAAG,cAAc;AAAA,QAChD,CAAC,yBAAyB,cAAc,EAAE,GAAG;AAAA;AAAA,QAE7C,mBAAmB,CAAC;AAAA,QACpB,iBAAiB,CAAC;AAAA,MACpB,CAAC;AAAA,MACD,MAAM;AAAA,MACN,gBAAgB,UAAU,SAAS;AAAA,MACnC,iBAAiB,WAAW,SAAS;AAAA,MACrC,UAAU;AAAA,IACZ,GAAG,EAAE,SAAS;AAAA,MACZ,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,QACL,sBAAsB;AAAA,QACtB,6BAA6B,CAAC;AAAA,MAChC;AAAA,MACA,MAAM;AAAA,IACR,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MACZ,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,KAAK,mBAAmB,CAAC,CAAC,CAAC;AAAA,EAChC;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,SAAS,CAAC,cAAc;AAAA,IAC1B;AAAA,EACF;AACF;AACA,IAAI,iBAAiB;AACrB,MAAM,QAAQ;AAAA,EACZ,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,mBAAmB;AACzB,IAAM,yBAAyB;AAC/B,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,aAAa,MAAM;AAAA,EACvB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,UAAU,UAAU,eAAe;AACxC,SAAK,eAAe,GAAG,KAAK,OAAO;AACnC,SAAK,cAAc,GAAG,KAAK,OAAO;AAClC,SAAK,UAAU,GAAG,KAAK,OAAO;AAC9B,SAAK,mBAAmB,WAAS;AAC/B,YAAM,SAAS,KAAK,UAAU;AAE9B,YAAM,QAAQ,OAAO,KAAK,WAAS,CAAC,MAAM,QAAQ;AAClD,YAAM,UAAU,OAAO,KAAK,WAAS,MAAM,UAAU,SAAS,CAAC,MAAM,QAAQ;AAC7E,UAAI,CAAC,SAAS,CAAC,SAAS;AACtB;AAAA,MACF;AAGA,YAAM,YAAY,WAAW;AAC7B,iBAAW,SAAS,QAAQ;AAC1B,cAAM,WAAW,UAAU,YAAY,IAAI;AAC3C,cAAM,kBAAkB,QAAQ;AAAA,MAClC;AAAA,IACF;AACA,SAAK,UAAU,QAAM;AACnB,SAAG,eAAe;AAOlB,YAAM,gBAAgB,GAAG,UAAU,GAAG,OAAO,QAAQ,WAAW;AAShE,UAAI,iBAAiB,CAAC,cAAc,UAAU;AAC5C,cAAM,eAAe,KAAK;AAC1B,cAAM,WAAW,cAAc;AAC/B,YAAI,aAAa,cAAc;AAC7B,eAAK,QAAQ;AACb,eAAK,gBAAgB,EAAE;AAAA,QACzB,WAAW,KAAK,qBAAqB;AACnC,eAAK,QAAQ;AACb,eAAK,gBAAgB,EAAE;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,SAAK,sBAAsB;AAC3B,SAAK,cAAc;AACnB,SAAK,OAAO,KAAK;AACjB,SAAK,QAAQ;AACb,SAAK,aAAa;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,iBAAiB,KAAK;AAC3B,SAAK,eAAe,KAAK;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AAUjB,SAAK,aAAa,KAAK,KAAK;AAAA,EAC9B;AAAA,EACM,oBAAoB;AAAA;AAGxB,YAAM,SAAS,KAAK,GAAG,cAAc,iBAAiB,KAAK,KAAK,GAAG,cAAc,kBAAkB;AACnG,UAAI,QAAQ;AACV,cAAM,QAAQ,KAAK,QAAQ,OAAO,cAAc,WAAW;AAC3D,YAAI,OAAO;AACT,eAAK,UAAU,MAAM,KAAK,KAAK,OAAO;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EACA,YAAY;AACV,WAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,WAAW,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,OAAO;AACrB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,SAAK,UAAU,KAAK;AAAA,MAClB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,UAAU,IAAI;AAGZ,UAAM,oBAAoB,CAAC,CAAC,KAAK,GAAG,QAAQ,oBAAoB,KAAK,CAAC,CAAC,KAAK,GAAG,QAAQ,kBAAkB;AACzG,QAAI,GAAG,UAAU,CAAC,KAAK,GAAG,SAAS,GAAG,MAAM,GAAG;AAC7C;AAAA,IACF;AAGA,UAAM,SAAS,KAAK,UAAU,EAAE,OAAO,WAAS,CAAC,MAAM,QAAQ;AAE/D,QAAI,GAAG,UAAU,OAAO,SAAS,GAAG,MAAM,GAAG;AAC3C,YAAM,QAAQ,OAAO,UAAU,WAAS,UAAU,GAAG,MAAM;AAC3D,YAAM,UAAU,OAAO,KAAK;AAC5B,UAAI;AAGJ,UAAI,CAAC,aAAa,YAAY,EAAE,SAAS,GAAG,GAAG,GAAG;AAChD,eAAO,UAAU,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI,OAAO,QAAQ,CAAC;AAAA,MACnE;AAGA,UAAI,CAAC,WAAW,WAAW,EAAE,SAAS,GAAG,GAAG,GAAG;AAC7C,eAAO,UAAU,IAAI,OAAO,OAAO,SAAS,CAAC,IAAI,OAAO,QAAQ,CAAC;AAAA,MACnE;AACA,UAAI,QAAQ,OAAO,SAAS,IAAI,GAAG;AACjC,aAAK,SAAS,EAAE;AAChB,YAAI,CAAC,mBAAmB;AACtB,eAAK,QAAQ,KAAK;AAClB,eAAK,gBAAgB,EAAE;AAAA,QACzB;AAAA,MACF;AAGA,UAAI,CAAC,GAAG,EAAE,SAAS,GAAG,GAAG,GAAG;AAC1B,cAAM,gBAAgB,KAAK;AAC3B,aAAK,QAAQ,KAAK,uBAAuB,KAAK,UAAU,SAAY,SAAY,QAAQ;AACxF,YAAI,kBAAkB,KAAK,SAAS,KAAK,qBAAqB;AAO5D,eAAK,gBAAgB,EAAE;AAAA,QACzB;AAGA,WAAG,eAAe;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEM,WAAW;AAAA;AACf,YAAM,eAAe,KAAK,UAAU,EAAE,KAAK,OAAK,EAAE,aAAa,EAAE;AACjE,uBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,SAAS;AAAA,IACpF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,IAAI;AAAA,MACJ,OAAO;AAAA,IACT,GAAG,UAAU,GAAG,EAAE,OAAO;AAAA,MACvB,IAAI;AAAA,MACJ,OAAO;AAAA,IACT,GAAG,SAAS,CAAC;AAAA,EACf;AAAA,EACA,gBAAgB;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,GAAG,UAAU,SAAS,aAAa,KAAK,GAAG,UAAU,SAAS,aAAa,KAAK,WAAW;AAC7F,aAAO;AAAA,IACT;AACA,QAAI,YAAY;AACd,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,sBAAkB,MAAM,IAAI,MAAM,OAAO,KAAK;AAC9C,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,MAAM;AAAA,MACN,mBAAmB,QAAQ,UAAU;AAAA,MACrC,oBAAoB,KAAK,cAAc;AAAA,MACvC,gBAAgB,KAAK,cAAc,MAAM,KAAK;AAAA,MAC9C,SAAS,KAAK;AAAA,MACd,OAAO;AAAA,IACT,GAAG,KAAK,eAAe,GAAG,EAAE,OAAO;AAAA,MACjC,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,SAAS,CAAC,cAAc;AAAA,IAC1B;AAAA,EACF;AACF;AACA,IAAI,gBAAgB;AACpB,WAAW,QAAQ;AAAA,EACjB,KAAK;AAAA,EACL,IAAI;AACN;", "names": [], "x_google_ignoreList": [0]}
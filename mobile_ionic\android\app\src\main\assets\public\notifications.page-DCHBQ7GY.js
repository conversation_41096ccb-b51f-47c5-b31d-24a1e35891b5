import {
  FcmService
} from "./chunk-RDFT5QPW.js";
import {
  environment
} from "./chunk-I7MI46CM.js";
import "./chunk-TAZAZ6IP.js";
import {
  CommonModule,
  Component,
  FormsModule,
  HttpClient,
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonIcon,
  IonSpinner,
  IonTitle,
  IonToolbar,
  IonicModule,
  NgClass,
  NgForOf,
  NgIf,
  Router,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import {
  __async
} from "./chunk-UL2P3LPA.js";

// src/app/pages/notifications/notifications.page.ts
function NotificationsPage_span_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 17);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.unreadCount);
  }
}
function NotificationsPage_div_18_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 18)(1, "span", 19);
    \u0275\u0275text(2, "Earlier");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "button", 20);
    \u0275\u0275listener("click", function NotificationsPage_div_18_Template_button_click_3_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.seeAllNotifications());
    });
    \u0275\u0275text(4, "See all");
    \u0275\u0275elementEnd()();
  }
}
function NotificationsPage_div_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 21);
    \u0275\u0275element(1, "ion-icon", 22);
    \u0275\u0275elementStart(2, "h3");
    \u0275\u0275text(3, "No notifications yet");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5, "When you receive notifications, they'll appear here.");
    \u0275\u0275elementEnd()();
  }
}
function NotificationsPage_div_21_span_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 36);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const notification_r4 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", notification_r4.reactions, " Reactions ");
  }
}
function NotificationsPage_div_21_div_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "div", 37);
  }
}
function NotificationsPage_div_21_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 23);
    \u0275\u0275listener("click", function NotificationsPage_div_21_Template_div_click_0_listener() {
      const notification_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onNotificationClick(notification_r4));
    });
    \u0275\u0275elementStart(1, "div", 24);
    \u0275\u0275element(2, "img", 25);
    \u0275\u0275elementStart(3, "div", 26);
    \u0275\u0275element(4, "ion-icon", 27);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "div", 28)(6, "div", 29)(7, "span", 30);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "span", 31);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(11, "div", 32)(12, "span", 33);
    \u0275\u0275text(13);
    \u0275\u0275elementEnd();
    \u0275\u0275template(14, NotificationsPage_div_21_span_14_Template, 2, 1, "span", 34);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(15, NotificationsPage_div_21_div_15_Template, 1, 0, "div", 35);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const notification_r4 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275classProp("unread", !notification_r4.read);
    \u0275\u0275advance(2);
    \u0275\u0275property("src", ctx_r0.getNotificationIcon(notification_r4), \u0275\u0275sanitizeUrl)("alt", notification_r4.type);
    \u0275\u0275advance();
    \u0275\u0275property("ngClass", ctx_r0.getIconBadgeClass(notification_r4));
    \u0275\u0275advance();
    \u0275\u0275property("name", ctx_r0.getBadgeIcon(notification_r4));
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r0.getNotificationTitle(notification_r4));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r0.getNotificationDescription(notification_r4));
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r0.getTimeAgo(notification_r4.created_at));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", notification_r4.reactions);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !notification_r4.read);
  }
}
function NotificationsPage_div_22_ion_spinner_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "ion-spinner", 42);
  }
}
function NotificationsPage_div_22_span_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Load More");
    \u0275\u0275elementEnd();
  }
}
function NotificationsPage_div_22_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 38)(1, "ion-button", 39);
    \u0275\u0275listener("click", function NotificationsPage_div_22_Template_ion_button_click_1_listener() {
      \u0275\u0275restoreView(_r5);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.loadMoreNotifications());
    });
    \u0275\u0275template(2, NotificationsPage_div_22_ion_spinner_2_Template, 1, 0, "ion-spinner", 40)(3, NotificationsPage_div_22_span_3_Template, 2, 0, "span", 41);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("disabled", ctx_r0.isLoading);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isLoading);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isLoading);
  }
}
var NotificationsPage = class _NotificationsPage {
  constructor(router, http, fcmService) {
    this.router = router;
    this.http = http;
    this.fcmService = fcmService;
    this.notifications = [];
    this.filteredNotifications = [];
    this.activeTab = "all";
    this.unreadCount = 0;
    this.isLoading = false;
    this.hasMoreNotifications = false;
    this.currentPage = 1;
    this.notificationSubscription = null;
  }
  ngOnInit() {
    this.loadNotifications();
    this.subscribeToNewNotifications();
  }
  ngOnDestroy() {
    if (this.notificationSubscription) {
      this.notificationSubscription.unsubscribe();
    }
  }
  loadNotifications() {
    return __async(this, null, function* () {
      this.isLoading = true;
      try {
        const response = yield this.http.get(`${environment.apiUrl}/notifications?page=${this.currentPage}`).toPromise();
        if (response) {
          if (this.currentPage === 1) {
            this.notifications = response.notifications;
          } else {
            this.notifications.push(...response.notifications);
          }
          this.unreadCount = response.unread_count;
          this.hasMoreNotifications = response.has_more;
          this.filterNotifications();
        }
      } catch (error) {
        console.error("Error loading notifications:", error);
      } finally {
        this.isLoading = false;
      }
    });
  }
  subscribeToNewNotifications() {
    this.notificationSubscription = this.fcmService.notifications$.subscribe((fcmNotification) => {
      const appNotification = {
        id: Date.now(),
        // Temporary ID
        type: this.mapFCMTypeToAppType(fcmNotification.category),
        title: fcmNotification.title,
        message: fcmNotification.body,
        data: fcmNotification,
        read: false,
        created_at: (/* @__PURE__ */ new Date()).toISOString(),
        updated_at: (/* @__PURE__ */ new Date()).toISOString()
      };
      this.notifications.unshift(appNotification);
      this.unreadCount++;
      this.filterNotifications();
    });
  }
  mapFCMTypeToAppType(category) {
    switch (category?.toLowerCase()) {
      case "evacuation":
      case "evacuation_center":
        return "evacuation_center_added";
      case "emergency":
      case "earthquake":
      case "typhoon":
      case "flood":
        return "emergency_alert";
      case "system":
        return "system_update";
      default:
        return "general";
    }
  }
  setActiveTab(tab) {
    this.activeTab = tab;
    this.filterNotifications();
  }
  filterNotifications() {
    if (this.activeTab === "unread") {
      this.filteredNotifications = this.notifications.filter((n) => !n.read);
    } else {
      this.filteredNotifications = this.notifications;
    }
  }
  onNotificationClick(notification) {
    return __async(this, null, function* () {
      if (!notification.read) {
        yield this.markAsRead(notification);
      }
      switch (notification.type) {
        case "evacuation_center_added":
          this.router.navigate(["/tabs/map"], {
            queryParams: {
              disasterType: "all",
              showNewCenters: true
            }
          });
          break;
        case "emergency_alert":
          const disasterType = this.extractDisasterType(notification);
          this.router.navigate(["/tabs/map"], {
            queryParams: {
              disasterType
            }
          });
          break;
        default:
          break;
      }
    });
  }
  extractDisasterType(notification) {
    const message = notification.message.toLowerCase();
    if (message.includes("earthquake"))
      return "Earthquake";
    if (message.includes("typhoon"))
      return "Typhoon";
    if (message.includes("flood"))
      return "Flood";
    return "all";
  }
  markAsRead(notification) {
    return __async(this, null, function* () {
      try {
        yield this.http.put(`${environment.apiUrl}/notifications/${notification.id}/read`, {}).toPromise();
        notification.read = true;
        this.unreadCount = Math.max(0, this.unreadCount - 1);
        this.filterNotifications();
      } catch (error) {
        console.error("Error marking notification as read:", error);
      }
    });
  }
  markAllAsRead() {
    return __async(this, null, function* () {
      try {
        yield this.http.put(`${environment.apiUrl}/notifications/mark-all-read`, {}).toPromise();
        this.notifications.forEach((n) => n.read = true);
        this.unreadCount = 0;
        this.filterNotifications();
      } catch (error) {
        console.error("Error marking all notifications as read:", error);
      }
    });
  }
  loadMoreNotifications() {
    if (!this.isLoading && this.hasMoreNotifications) {
      this.currentPage++;
      this.loadNotifications();
    }
  }
  seeAllNotifications() {
    this.setActiveTab("all");
  }
  goBack() {
    this.router.navigate(["/tabs/home"]);
  }
  trackByNotificationId(index, notification) {
    return notification.id;
  }
  getNotificationIcon(notification) {
    switch (notification.type) {
      case "evacuation_center_added":
        return "assets/evacuation-center-icon.png";
      case "emergency_alert":
        return "assets/emergency-icon.png";
      case "system_update":
        return "assets/system-icon.png";
      default:
        return "assets/alerto_icon.png";
    }
  }
  getBadgeIcon(notification) {
    switch (notification.type) {
      case "evacuation_center_added":
        return "add-circle";
      case "emergency_alert":
        return "warning";
      case "system_update":
        return "settings";
      default:
        return "notifications";
    }
  }
  getIconBadgeClass(notification) {
    switch (notification.type) {
      case "evacuation_center_added":
        return "badge-success";
      case "emergency_alert":
        return "badge-danger";
      case "system_update":
        return "badge-info";
      default:
        return "badge-primary";
    }
  }
  getNotificationTitle(notification) {
    switch (notification.type) {
      case "evacuation_center_added":
        return "New evacuation center added.";
      case "emergency_alert":
        return notification.title;
      default:
        return notification.title;
    }
  }
  getNotificationDescription(notification) {
    return notification.message;
  }
  getTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = /* @__PURE__ */ new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1e3);
    if (diffInSeconds < 60)
      return `${diffInSeconds}s`;
    if (diffInSeconds < 3600)
      return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)}d`;
    return `${Math.floor(diffInSeconds / 604800)}w`;
  }
  static {
    this.\u0275fac = function NotificationsPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _NotificationsPage)(\u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(HttpClient), \u0275\u0275directiveInject(FcmService));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _NotificationsPage, selectors: [["app-notifications"]], decls: 23, vars: 13, consts: [[3, "translucent"], ["slot", "start"], [3, "click"], ["name", "chevron-back-outline"], ["slot", "end"], [3, "click", "disabled"], ["name", "checkmark-done-outline"], [3, "fullscreen"], [1, "notification-header"], [1, "notification-tabs"], [1, "tab-button", 3, "click"], ["class", "unread-badge", 4, "ngIf"], ["class", "section-header", 4, "ngIf"], [1, "notifications-container"], ["class", "no-notifications", 4, "ngIf"], ["class", "notification-item", 3, "unread", "click", 4, "ngFor", "ngForOf", "ngForTrackBy"], ["class", "load-more-container", 4, "ngIf"], [1, "unread-badge"], [1, "section-header"], [1, "section-title"], [1, "see-all-btn", 3, "click"], [1, "no-notifications"], ["name", "notifications-outline", 1, "no-notifications-icon"], [1, "notification-item", 3, "click"], [1, "notification-icon"], [1, "icon-image", 3, "src", "alt"], [1, "icon-badge", 3, "ngClass"], [3, "name"], [1, "notification-content"], [1, "notification-text"], [1, "notification-title"], [1, "notification-description"], [1, "notification-meta"], [1, "notification-time"], ["class", "notification-reactions", 4, "ngIf"], ["class", "unread-indicator", 4, "ngIf"], [1, "notification-reactions"], [1, "unread-indicator"], [1, "load-more-container"], ["fill", "clear", 3, "click", "disabled"], ["name", "crescent", 4, "ngIf"], [4, "ngIf"], ["name", "crescent"]], template: function NotificationsPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header", 0)(1, "ion-toolbar")(2, "ion-buttons", 1)(3, "ion-button", 2);
        \u0275\u0275listener("click", function NotificationsPage_Template_ion_button_click_3_listener() {
          return ctx.goBack();
        });
        \u0275\u0275element(4, "ion-icon", 3);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(5, "ion-title");
        \u0275\u0275text(6, "Notifications");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(7, "ion-buttons", 4)(8, "ion-button", 5);
        \u0275\u0275listener("click", function NotificationsPage_Template_ion_button_click_8_listener() {
          return ctx.markAllAsRead();
        });
        \u0275\u0275element(9, "ion-icon", 6);
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(10, "ion-content", 7)(11, "div", 8)(12, "div", 9)(13, "button", 10);
        \u0275\u0275listener("click", function NotificationsPage_Template_button_click_13_listener() {
          return ctx.setActiveTab("all");
        });
        \u0275\u0275text(14, " All ");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(15, "button", 10);
        \u0275\u0275listener("click", function NotificationsPage_Template_button_click_15_listener() {
          return ctx.setActiveTab("unread");
        });
        \u0275\u0275text(16, " Unread ");
        \u0275\u0275template(17, NotificationsPage_span_17_Template, 2, 1, "span", 11);
        \u0275\u0275elementEnd()();
        \u0275\u0275template(18, NotificationsPage_div_18_Template, 5, 0, "div", 12);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(19, "div", 13);
        \u0275\u0275template(20, NotificationsPage_div_20_Template, 6, 0, "div", 14)(21, NotificationsPage_div_21_Template, 16, 11, "div", 15);
        \u0275\u0275elementEnd();
        \u0275\u0275template(22, NotificationsPage_div_22_Template, 4, 3, "div", 16);
        \u0275\u0275elementEnd();
      }
      if (rf & 2) {
        \u0275\u0275property("translucent", true);
        \u0275\u0275advance(8);
        \u0275\u0275property("disabled", ctx.unreadCount === 0);
        \u0275\u0275advance(2);
        \u0275\u0275property("fullscreen", true);
        \u0275\u0275advance(3);
        \u0275\u0275classProp("active", ctx.activeTab === "all");
        \u0275\u0275advance(2);
        \u0275\u0275classProp("active", ctx.activeTab === "unread");
        \u0275\u0275advance(2);
        \u0275\u0275property("ngIf", ctx.unreadCount > 0);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.filteredNotifications.length > 0);
        \u0275\u0275advance(2);
        \u0275\u0275property("ngIf", ctx.filteredNotifications.length === 0);
        \u0275\u0275advance();
        \u0275\u0275property("ngForOf", ctx.filteredNotifications)("ngForTrackBy", ctx.trackByNotificationId);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.hasMoreNotifications);
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonContent, IonHeader, IonIcon, IonSpinner, IonTitle, IonToolbar, CommonModule, NgClass, NgForOf, NgIf, FormsModule], styles: ['@charset "UTF-8";\n\n\n\nion-content[_ngcontent-%COMP%] {\n  --background: #f0f2f5;\n}\n.notification-header[_ngcontent-%COMP%] {\n  background: white;\n  padding: 16px;\n  border-bottom: 1px solid #e4e6ea;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n.notification-tabs[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 16px;\n  margin-bottom: 16px;\n}\n.tab-button[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  font-size: 16px;\n  font-weight: 600;\n  color: #65676b;\n  padding: 8px 12px;\n  border-radius: 20px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n}\n.tab-button.active[_ngcontent-%COMP%] {\n  color: #1877f2;\n  background: #e7f3ff;\n}\n.tab-button[_ngcontent-%COMP%]:hover {\n  background: #f2f3f4;\n}\n.unread-badge[_ngcontent-%COMP%] {\n  background: #e41e3f;\n  color: white;\n  font-size: 12px;\n  font-weight: 600;\n  padding: 2px 6px;\n  border-radius: 10px;\n  margin-left: 6px;\n  min-width: 18px;\n  text-align: center;\n}\n.section-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.section-title[_ngcontent-%COMP%] {\n  font-size: 17px;\n  font-weight: 600;\n  color: #050505;\n}\n.see-all-btn[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  color: #1877f2;\n  font-size: 15px;\n  font-weight: 500;\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 6px;\n}\n.see-all-btn[_ngcontent-%COMP%]:hover {\n  background: #f2f3f4;\n}\n.notifications-container[_ngcontent-%COMP%] {\n  padding: 0;\n}\n.no-notifications[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 60px 20px;\n  color: #65676b;\n}\n.no-notifications[_ngcontent-%COMP%]   .no-notifications-icon[_ngcontent-%COMP%] {\n  font-size: 64px;\n  color: #bcc0c4;\n  margin-bottom: 16px;\n}\n.no-notifications[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 20px;\n  font-weight: 600;\n  margin: 0 0 8px 0;\n  color: #050505;\n}\n.no-notifications[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 15px;\n  margin: 0;\n  line-height: 1.4;\n}\n.notification-item[_ngcontent-%COMP%] {\n  background: white;\n  padding: 12px 16px;\n  border-bottom: 1px solid #e4e6ea;\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n  position: relative;\n}\n.notification-item[_ngcontent-%COMP%]:hover {\n  background: #f7f8fa;\n}\n.notification-item.unread[_ngcontent-%COMP%] {\n  background: #f0f8ff;\n}\n.notification-item.unread[_ngcontent-%COMP%]:hover {\n  background: #e7f3ff;\n}\n.notification-item[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n.notification-icon[_ngcontent-%COMP%] {\n  position: relative;\n  flex-shrink: 0;\n}\n.icon-image[_ngcontent-%COMP%] {\n  width: 56px;\n  height: 56px;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 2px solid #e4e6ea;\n}\n.icon-badge[_ngcontent-%COMP%] {\n  position: absolute;\n  bottom: -2px;\n  right: -2px;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid white;\n  font-size: 12px;\n}\n.icon-badge.badge-success[_ngcontent-%COMP%] {\n  background: #42b883;\n  color: white;\n}\n.icon-badge.badge-danger[_ngcontent-%COMP%] {\n  background: #e41e3f;\n  color: white;\n}\n.icon-badge.badge-info[_ngcontent-%COMP%] {\n  background: #1877f2;\n  color: white;\n}\n.icon-badge.badge-primary[_ngcontent-%COMP%] {\n  background: #03b2dd;\n  color: white;\n}\n.icon-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 14px;\n}\n.notification-content[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n.notification-text[_ngcontent-%COMP%] {\n  margin-bottom: 4px;\n  line-height: 1.3;\n}\n.notification-title[_ngcontent-%COMP%] {\n  color: #050505;\n  font-size: 15px;\n  font-weight: 400;\n  display: block;\n  margin-bottom: 2px;\n}\n.notification-description[_ngcontent-%COMP%] {\n  color: #65676b;\n  font-size: 13px;\n  display: block;\n  line-height: 1.4;\n}\n.notification-meta[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 13px;\n  color: #65676b;\n}\n.notification-time[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n.notification-reactions[_ngcontent-%COMP%]::before {\n  content: "\\2022";\n  margin-right: 8px;\n}\n.unread-indicator[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 50%;\n  right: 16px;\n  transform: translateY(-50%);\n  width: 8px;\n  height: 8px;\n  background: #1877f2;\n  border-radius: 50%;\n}\n.load-more-container[_ngcontent-%COMP%] {\n  padding: 20px;\n  text-align: center;\n  background: white;\n  border-top: 1px solid #e4e6ea;\n}\n.load-more-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  --color: #1877f2;\n  font-weight: 600;\n}\n@media (prefers-color-scheme: dark) {\n  ion-content[_ngcontent-%COMP%] {\n    --background: #18191a;\n  }\n  .notification-header[_ngcontent-%COMP%] {\n    background: #242526;\n    border-bottom-color: #3a3b3c;\n  }\n  .tab-button[_ngcontent-%COMP%] {\n    color: #b0b3b8;\n  }\n  .tab-button.active[_ngcontent-%COMP%] {\n    color: #2d88ff;\n    background: #263951;\n  }\n  .tab-button[_ngcontent-%COMP%]:hover {\n    background: #3a3b3c;\n  }\n  .section-title[_ngcontent-%COMP%] {\n    color: #e4e6ea;\n  }\n  .see-all-btn[_ngcontent-%COMP%] {\n    color: #2d88ff;\n  }\n  .see-all-btn[_ngcontent-%COMP%]:hover {\n    background: #3a3b3c;\n  }\n  .notification-item[_ngcontent-%COMP%] {\n    background: #242526;\n    border-bottom-color: #3a3b3c;\n  }\n  .notification-item[_ngcontent-%COMP%]:hover {\n    background: #3a3b3c;\n  }\n  .notification-item.unread[_ngcontent-%COMP%] {\n    background: #263951;\n  }\n  .notification-item.unread[_ngcontent-%COMP%]:hover {\n    background: #2d4373;\n  }\n  .notification-title[_ngcontent-%COMP%] {\n    color: #e4e6ea;\n  }\n  .notification-description[_ngcontent-%COMP%], \n   .notification-meta[_ngcontent-%COMP%] {\n    color: #b0b3b8;\n  }\n  .no-notifications[_ngcontent-%COMP%] {\n    color: #b0b3b8;\n  }\n  .no-notifications[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n    color: #e4e6ea;\n  }\n  .load-more-container[_ngcontent-%COMP%] {\n    background: #242526;\n    border-top-color: #3a3b3c;\n  }\n}\n/*# sourceMappingURL=notifications.page.css.map */'] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NotificationsPage, [{
    type: Component,
    args: [{ selector: "app-notifications", standalone: true, imports: [IonicModule, CommonModule, FormsModule], template: `<ion-header [translucent]="true">\r
  <ion-toolbar>\r
    <ion-buttons slot="start">\r
      <ion-button (click)="goBack()">\r
        <ion-icon name="chevron-back-outline"></ion-icon>\r
      </ion-button>\r
    </ion-buttons>\r
    <ion-title>Notifications</ion-title>\r
    <ion-buttons slot="end">\r
      <ion-button (click)="markAllAsRead()" [disabled]="unreadCount === 0">\r
        <ion-icon name="checkmark-done-outline"></ion-icon>\r
      </ion-button>\r
    </ion-buttons>\r
  </ion-toolbar>\r
</ion-header>\r
\r
<ion-content [fullscreen]="true">\r
  <!-- Header with tabs -->\r
  <div class="notification-header">\r
    <div class="notification-tabs">\r
      <button \r
        class="tab-button" \r
        [class.active]="activeTab === 'all'"\r
        (click)="setActiveTab('all')">\r
        All\r
      </button>\r
      <button \r
        class="tab-button" \r
        [class.active]="activeTab === 'unread'"\r
        (click)="setActiveTab('unread')">\r
        Unread\r
        <span class="unread-badge" *ngIf="unreadCount > 0">{{ unreadCount }}</span>\r
      </button>\r
    </div>\r
    \r
    <div class="section-header" *ngIf="filteredNotifications.length > 0">\r
      <span class="section-title">Earlier</span>\r
      <button class="see-all-btn" (click)="seeAllNotifications()">See all</button>\r
    </div>\r
  </div>\r
\r
  <!-- Notifications List -->\r
  <div class="notifications-container">\r
    <!-- No notifications message -->\r
    <div class="no-notifications" *ngIf="filteredNotifications.length === 0">\r
      <ion-icon name="notifications-outline" class="no-notifications-icon"></ion-icon>\r
      <h3>No notifications yet</h3>\r
      <p>When you receive notifications, they'll appear here.</p>\r
    </div>\r
\r
    <!-- Notifications -->\r
    <div class="notification-item" \r
         *ngFor="let notification of filteredNotifications; trackBy: trackByNotificationId"\r
         [class.unread]="!notification.read"\r
         (click)="onNotificationClick(notification)">\r
      \r
      <!-- Notification Icon -->\r
      <div class="notification-icon">\r
        <img [src]="getNotificationIcon(notification)" [alt]="notification.type" class="icon-image">\r
        <div class="icon-badge" [ngClass]="getIconBadgeClass(notification)">\r
          <ion-icon [name]="getBadgeIcon(notification)"></ion-icon>\r
        </div>\r
      </div>\r
\r
      <!-- Notification Content -->\r
      <div class="notification-content">\r
        <div class="notification-text">\r
          <span class="notification-title">{{ getNotificationTitle(notification) }}</span>\r
          <span class="notification-description">{{ getNotificationDescription(notification) }}</span>\r
        </div>\r
        <div class="notification-meta">\r
          <span class="notification-time">{{ getTimeAgo(notification.created_at) }}</span>\r
          <span class="notification-reactions" *ngIf="notification.reactions">\r
            {{ notification.reactions }} Reactions\r
          </span>\r
        </div>\r
      </div>\r
\r
      <!-- Unread indicator -->\r
      <div class="unread-indicator" *ngIf="!notification.read"></div>\r
    </div>\r
  </div>\r
\r
  <!-- Load more button -->\r
  <div class="load-more-container" *ngIf="hasMoreNotifications">\r
    <ion-button fill="clear" (click)="loadMoreNotifications()" [disabled]="isLoading">\r
      <ion-spinner *ngIf="isLoading" name="crescent"></ion-spinner>\r
      <span *ngIf="!isLoading">Load More</span>\r
    </ion-button>\r
  </div>\r
</ion-content>\r
`, styles: ['@charset "UTF-8";\n\n/* src/app/pages/notifications/notifications.page.scss */\nion-content {\n  --background: #f0f2f5;\n}\n.notification-header {\n  background: white;\n  padding: 16px;\n  border-bottom: 1px solid #e4e6ea;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n.notification-tabs {\n  display: flex;\n  gap: 16px;\n  margin-bottom: 16px;\n}\n.tab-button {\n  background: none;\n  border: none;\n  font-size: 16px;\n  font-weight: 600;\n  color: #65676b;\n  padding: 8px 12px;\n  border-radius: 20px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n}\n.tab-button.active {\n  color: #1877f2;\n  background: #e7f3ff;\n}\n.tab-button:hover {\n  background: #f2f3f4;\n}\n.unread-badge {\n  background: #e41e3f;\n  color: white;\n  font-size: 12px;\n  font-weight: 600;\n  padding: 2px 6px;\n  border-radius: 10px;\n  margin-left: 6px;\n  min-width: 18px;\n  text-align: center;\n}\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.section-title {\n  font-size: 17px;\n  font-weight: 600;\n  color: #050505;\n}\n.see-all-btn {\n  background: none;\n  border: none;\n  color: #1877f2;\n  font-size: 15px;\n  font-weight: 500;\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 6px;\n}\n.see-all-btn:hover {\n  background: #f2f3f4;\n}\n.notifications-container {\n  padding: 0;\n}\n.no-notifications {\n  text-align: center;\n  padding: 60px 20px;\n  color: #65676b;\n}\n.no-notifications .no-notifications-icon {\n  font-size: 64px;\n  color: #bcc0c4;\n  margin-bottom: 16px;\n}\n.no-notifications h3 {\n  font-size: 20px;\n  font-weight: 600;\n  margin: 0 0 8px 0;\n  color: #050505;\n}\n.no-notifications p {\n  font-size: 15px;\n  margin: 0;\n  line-height: 1.4;\n}\n.notification-item {\n  background: white;\n  padding: 12px 16px;\n  border-bottom: 1px solid #e4e6ea;\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n  position: relative;\n}\n.notification-item:hover {\n  background: #f7f8fa;\n}\n.notification-item.unread {\n  background: #f0f8ff;\n}\n.notification-item.unread:hover {\n  background: #e7f3ff;\n}\n.notification-item:last-child {\n  border-bottom: none;\n}\n.notification-icon {\n  position: relative;\n  flex-shrink: 0;\n}\n.icon-image {\n  width: 56px;\n  height: 56px;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 2px solid #e4e6ea;\n}\n.icon-badge {\n  position: absolute;\n  bottom: -2px;\n  right: -2px;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid white;\n  font-size: 12px;\n}\n.icon-badge.badge-success {\n  background: #42b883;\n  color: white;\n}\n.icon-badge.badge-danger {\n  background: #e41e3f;\n  color: white;\n}\n.icon-badge.badge-info {\n  background: #1877f2;\n  color: white;\n}\n.icon-badge.badge-primary {\n  background: #03b2dd;\n  color: white;\n}\n.icon-badge ion-icon {\n  font-size: 14px;\n}\n.notification-content {\n  flex: 1;\n  min-width: 0;\n}\n.notification-text {\n  margin-bottom: 4px;\n  line-height: 1.3;\n}\n.notification-title {\n  color: #050505;\n  font-size: 15px;\n  font-weight: 400;\n  display: block;\n  margin-bottom: 2px;\n}\n.notification-description {\n  color: #65676b;\n  font-size: 13px;\n  display: block;\n  line-height: 1.4;\n}\n.notification-meta {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 13px;\n  color: #65676b;\n}\n.notification-time {\n  font-weight: 500;\n}\n.notification-reactions::before {\n  content: "\\2022";\n  margin-right: 8px;\n}\n.unread-indicator {\n  position: absolute;\n  top: 50%;\n  right: 16px;\n  transform: translateY(-50%);\n  width: 8px;\n  height: 8px;\n  background: #1877f2;\n  border-radius: 50%;\n}\n.load-more-container {\n  padding: 20px;\n  text-align: center;\n  background: white;\n  border-top: 1px solid #e4e6ea;\n}\n.load-more-container ion-button {\n  --color: #1877f2;\n  font-weight: 600;\n}\n@media (prefers-color-scheme: dark) {\n  ion-content {\n    --background: #18191a;\n  }\n  .notification-header {\n    background: #242526;\n    border-bottom-color: #3a3b3c;\n  }\n  .tab-button {\n    color: #b0b3b8;\n  }\n  .tab-button.active {\n    color: #2d88ff;\n    background: #263951;\n  }\n  .tab-button:hover {\n    background: #3a3b3c;\n  }\n  .section-title {\n    color: #e4e6ea;\n  }\n  .see-all-btn {\n    color: #2d88ff;\n  }\n  .see-all-btn:hover {\n    background: #3a3b3c;\n  }\n  .notification-item {\n    background: #242526;\n    border-bottom-color: #3a3b3c;\n  }\n  .notification-item:hover {\n    background: #3a3b3c;\n  }\n  .notification-item.unread {\n    background: #263951;\n  }\n  .notification-item.unread:hover {\n    background: #2d4373;\n  }\n  .notification-title {\n    color: #e4e6ea;\n  }\n  .notification-description,\n  .notification-meta {\n    color: #b0b3b8;\n  }\n  .no-notifications {\n    color: #b0b3b8;\n  }\n  .no-notifications h3 {\n    color: #e4e6ea;\n  }\n  .load-more-container {\n    background: #242526;\n    border-top-color: #3a3b3c;\n  }\n}\n/*# sourceMappingURL=notifications.page.css.map */\n'] }]
  }], () => [{ type: Router }, { type: HttpClient }, { type: FcmService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(NotificationsPage, { className: "NotificationsPage", filePath: "src/app/pages/notifications/notifications.page.ts", lineNumber: 31 });
})();
export {
  NotificationsPage
};
//# sourceMappingURL=notifications.page-DCHBQ7GY.js.map

import{f as r,g as i}from"./chunk-4XHS6T7V.js";import{c as a,d as m}from"./chunk-FED6QSGK.js";import{b as s}from"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import{g as n}from"./chunk-2R6CW7ES.js";var y=()=>{let o=window;o.addEventListener("statusTap",()=>{a(()=>{let c=o.innerWidth,d=o.innerHeight,e=document.elementFromPoint(c/2,d/2);if(!e)return;let t=r(e);t&&new Promise(p=>s(t,p)).then(()=>{m(()=>n(null,null,function*(){t.style.setProperty("--overflow","hidden"),yield i(t,300),t.style.removeProperty("--overflow")}))})})})};export{y as startStatusTap};

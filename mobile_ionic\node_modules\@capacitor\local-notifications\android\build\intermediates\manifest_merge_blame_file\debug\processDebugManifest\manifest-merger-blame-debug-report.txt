1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.capacitorjs.plugins.localnotifications" >
4
5    <uses-sdk android:minSdkVersion="23" />
6
7    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
7-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:17:5-80
7-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:17:22-78
8    <uses-permission android:name="android.permission.WAKE_LOCK" />
8-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:18:5-67
8-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:18:22-65
9    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
9-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:19:5-76
9-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:19:22-74
10
11    <application>
11-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:3:5-16:19
12        <receiver android:name="com.capacitorjs.plugins.localnotifications.TimedNotificationPublisher" />
12-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:4:9-106
12-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:4:19-103
13        <receiver android:name="com.capacitorjs.plugins.localnotifications.NotificationDismissReceiver" />
13-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:5:9-107
13-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:5:19-104
14        <receiver
14-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:6:9-15:20
15            android:name="com.capacitorjs.plugins.localnotifications.LocalNotificationRestoreReceiver"
15-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:7:13-103
16            android:directBootAware="true"
16-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:8:13-43
17            android:exported="false" >
17-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:9:13-37
18            <intent-filter>
18-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:10:13-14:29
19                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
19-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:11:17-86
19-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:11:25-83
20                <action android:name="android.intent.action.BOOT_COMPLETED" />
20-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:12:17-79
20-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:12:25-76
21                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
21-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:13:17-82
21-->C:\Users\<USER>\Lastna\LastProject.1\mobile_ionic\node_modules\@capacitor\local-notifications\android\src\main\AndroidManifest.xml:13:25-79
22            </intent-filter>
23        </receiver>
24    </application>
25
26</manifest>

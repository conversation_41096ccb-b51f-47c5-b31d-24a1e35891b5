import {
  Geolocation,
  MapboxRoutingService,
  require_leaflet_src
} from "./chunk-WDZAZAAD.js";
import {
  environment
} from "./chunk-I7MI46CM.js";
import "./chunk-TAZAZ6IP.js";
import {
  AlertController,
  CommonModule,
  Component,
  FormsModule,
  HttpClient,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonContent,
  IonFab,
  IonFabButton,
  IonHeader,
  IonIcon,
  IonLabel,
  IonSegment,
  IonSegmentButton,
  IonTitle,
  IonToolbar,
  IonicModule,
  LoadingController,
  NgControlStatus,
  NgIf,
  NgModel,
  Router,
  SelectValueAccessorDirective,
  ToastController,
  firstValueFrom,
  inject,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import {
  __async,
  __spreadProps,
  __spreadValues,
  __toESM
} from "./chunk-UL2P3LPA.js";

// src/app/pages/disaster-maps/all-maps.page.ts
var L = __toESM(require_leaflet_src());
function AllMapsPage_div_60_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 31)(1, "ion-card")(2, "ion-card-content")(3, "div", 32);
    \u0275\u0275element(4, "ion-icon", 33);
    \u0275\u0275elementStart(5, "span");
    \u0275\u0275text(6, "Route to Nearest Center");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 34)(8, "div", 35);
    \u0275\u0275element(9, "ion-icon", 36);
    \u0275\u0275elementStart(10, "span");
    \u0275\u0275text(11);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(12, "div", 35);
    \u0275\u0275element(13, "ion-icon", 37);
    \u0275\u0275elementStart(14, "span");
    \u0275\u0275text(15);
    \u0275\u0275elementEnd()()()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(9);
    \u0275\u0275property("name", ctx_r0.travelMode === "walking" ? "walk-outline" : ctx_r0.travelMode === "cycling" ? "bicycle-outline" : "car-outline");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("", (ctx_r0.routeTime / 60).toFixed(0), " min");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1("", (ctx_r0.routeDistance / 1e3).toFixed(2), " km");
  }
}
var AllMapsPage = class _AllMapsPage {
  constructor() {
    this.userMarker = null;
    this.routeLayer = null;
    this.nearestMarkers = [];
    this.evacuationCenters = [];
    this.centerCounts = {
      earthquake: 0,
      typhoon: 0,
      flood: 0,
      total: 0
    };
    this.travelMode = "walking";
    this.routeTime = 0;
    this.routeDistance = 0;
    this.userLocation = null;
    this.loadingCtrl = inject(LoadingController);
    this.toastCtrl = inject(ToastController);
    this.alertCtrl = inject(AlertController);
    this.http = inject(HttpClient);
    this.router = inject(Router);
    this.mapboxRouting = inject(MapboxRoutingService);
  }
  ngOnInit() {
    return __async(this, null, function* () {
      console.log("\u{1F5FA}\uFE0F ALL MAPS: Initializing...");
      yield this.loadAllMaps();
    });
  }
  loadAllMaps() {
    return __async(this, null, function* () {
      const loading = yield this.loadingCtrl.create({
        message: "Loading all evacuation centers...",
        spinner: "crescent"
      });
      yield loading.present();
      try {
        const position = yield Geolocation.getCurrentPosition({
          enableHighAccuracy: true,
          timeout: 2e4
        });
        const userLat = position.coords.latitude;
        const userLng = position.coords.longitude;
        this.userLocation = { lat: userLat, lng: userLng };
        console.log(`\u{1F5FA}\uFE0F ALL MAPS: User location [${userLat}, ${userLng}]`);
        this.initializeMap(userLat, userLng);
        yield this.loadAllCenters(userLat, userLng);
        yield loading.dismiss();
        const toast = yield this.toastCtrl.create({
          message: `\u{1F5FA}\uFE0F Showing all ${this.centerCounts.total} evacuation centers`,
          duration: 3e3,
          color: "secondary",
          position: "top"
        });
        yield toast.present();
      } catch (error) {
        yield loading.dismiss();
        console.error("\u{1F5FA}\uFE0F ALL MAPS: Error loading map", error);
        const alert = yield this.alertCtrl.create({
          header: "Location Error",
          message: "Unable to get your location. Please enable GPS and try again.",
          buttons: [
            {
              text: "Retry",
              handler: () => this.loadAllMaps()
            },
            {
              text: "Go Back",
              handler: () => this.router.navigate(["/tabs/home"])
            }
          ]
        });
        yield alert.present();
      }
    });
  }
  initializeMap(lat, lng) {
    console.log(`\u{1F5FA}\uFE0F ALL MAPS: Initializing map at [${lat}, ${lng}]`);
    if (this.map) {
      this.map.remove();
    }
    this.map = L.map("all-maps").setView([lat, lng], 12);
    L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: "OpenStreetMap contributors"
    }).addTo(this.map);
    this.userMarker = L.marker([lat, lng], {
      icon: L.icon({
        iconUrl: "assets/icons/user-location.png",
        iconSize: [30, 30],
        iconAnchor: [15, 30]
      })
    }).addTo(this.map);
    this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup();
  }
  loadAllCenters(userLat, userLng) {
    return __async(this, null, function* () {
      try {
        console.log("\u{1F5FA}\uFE0F ALL MAPS: Fetching all evacuation centers...");
        const allCenters = yield firstValueFrom(this.http.get(`${environment.apiUrl}/evacuation-centers`));
        console.log("\u{1F5FA}\uFE0F ALL MAPS: Total centers received:", allCenters?.length || 0);
        this.evacuationCenters = allCenters || [];
        this.centerCounts.earthquake = this.evacuationCenters.filter((c) => c.disaster_type === "Earthquake").length;
        this.centerCounts.typhoon = this.evacuationCenters.filter((c) => c.disaster_type === "Typhoon").length;
        this.centerCounts.flood = this.evacuationCenters.filter((c) => c.disaster_type === "Flash Flood").length;
        this.centerCounts.total = this.evacuationCenters.length;
        console.log("\u{1F5FA}\uFE0F ALL MAPS: Center counts:", this.centerCounts);
        if (this.evacuationCenters.length === 0) {
          const alert = yield this.alertCtrl.create({
            header: "No Evacuation Centers",
            message: "No evacuation centers found in the database.",
            buttons: ["OK"]
          });
          yield alert.present();
          return;
        }
        this.evacuationCenters.forEach((center) => {
          const lat = Number(center.latitude);
          const lng = Number(center.longitude);
          if (!isNaN(lat) && !isNaN(lng)) {
            let iconUrl = "assets/Location.png";
            let colorEmoji = "\u26AA";
            switch (center.disaster_type) {
              case "Earthquake":
                iconUrl = "assets/forEarthquake.png";
                colorEmoji = "\u{1F7E0}";
                break;
              case "Typhoon":
                iconUrl = "assets/forTyphoon.png";
                colorEmoji = "\u{1F7E2}";
                break;
              case "Flood":
                iconUrl = "assets/forFlood.png";
                colorEmoji = "\u{1F535}";
                break;
            }
            const marker2 = L.marker([lat, lng], {
              icon: L.icon({
                iconUrl,
                iconSize: [40, 40],
                iconAnchor: [20, 40],
                popupAnchor: [0, -40]
              })
            });
            const distance = this.calculateDistance(userLat, userLng, lat, lng);
            marker2.on("click", () => {
              this.showTransportationOptions(center);
            });
            marker2.bindPopup(`
            <div class="evacuation-popup">
              <h3>${colorEmoji} ${center.name}</h3>
              <p><strong>Type:</strong> ${center.disaster_type || "General"}</p>
              <p><strong>Distance:</strong> ${(distance / 1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${center.capacity || "N/A"}</p>
              <p><em>Click marker for route options</em></p>
            </div>
          `);
            marker2.addTo(this.map);
            console.log(`\u{1F5FA}\uFE0F Added ${center.disaster_type} marker: ${center.name}`);
          }
        });
        if (this.evacuationCenters.length > 0) {
          const bounds = L.latLngBounds([]);
          bounds.extend([userLat, userLng]);
          this.evacuationCenters.forEach((center) => {
            bounds.extend([Number(center.latitude), Number(center.longitude)]);
          });
          this.map.fitBounds(bounds, { padding: [50, 50] });
        }
      } catch (error) {
        console.error("\u{1F5FA}\uFE0F ALL MAPS: Error loading centers", error);
        const toast = yield this.toastCtrl.create({
          message: "Error loading evacuation centers. Please check your connection.",
          duration: 3e3,
          color: "danger"
        });
        yield toast.present();
      }
    });
  }
  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371e3;
    const \u03C61 = lat1 * Math.PI / 180;
    const \u03C62 = lat2 * Math.PI / 180;
    const \u0394\u03C6 = (lat2 - lat1) * Math.PI / 180;
    const \u0394\u03BB = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(\u0394\u03C6 / 2) * Math.sin(\u0394\u03C6 / 2) + Math.cos(\u03C61) * Math.cos(\u03C62) * Math.sin(\u0394\u03BB / 2) * Math.sin(\u0394\u03BB / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }
  // Auto-route to 2 nearest evacuation centers
  routeToTwoNearestCenters() {
    return __async(this, null, function* () {
      if (!this.userLocation || this.evacuationCenters.length === 0) {
        console.log("\u{1F5FA}\uFE0F ALL MAPS: No user location or evacuation centers available");
        return;
      }
      try {
        console.log("\u{1F5FA}\uFE0F ALL MAPS: Finding 2 nearest centers...");
        const nearestCenters = this.getTwoNearestCenters(this.userLocation.lat, this.userLocation.lng);
        if (nearestCenters.length === 0) {
          const toast2 = yield this.toastCtrl.create({
            message: "No evacuation centers found nearby",
            duration: 3e3,
            color: "warning"
          });
          yield toast2.present();
          return;
        }
        this.clearRoutes();
        this.addPulsingMarkers(nearestCenters);
        yield this.calculateRoutes(nearestCenters);
        const toast = yield this.toastCtrl.create({
          message: `\u{1F5FA}\uFE0F Showing routes to ${nearestCenters.length} nearest centers via ${this.travelMode}`,
          duration: 3e3,
          color: "success",
          position: "top"
        });
        yield toast.present();
      } catch (error) {
        console.error("\u{1F5FA}\uFE0F ALL MAPS: Error calculating routes", error);
        const toast = yield this.toastCtrl.create({
          message: "Error calculating routes. Please try again.",
          duration: 3e3,
          color: "danger"
        });
        yield toast.present();
      }
    });
  }
  // Get 2 nearest evacuation centers
  getTwoNearestCenters(userLat, userLng) {
    const centersWithDistance = this.evacuationCenters.map((center) => __spreadProps(__spreadValues({}, center), {
      distance: this.calculateDistance(userLat, userLng, Number(center.latitude), Number(center.longitude))
    }));
    return centersWithDistance.sort((a, b) => a.distance - b.distance).slice(0, 2);
  }
  // Add pulsing markers for nearest centers
  addPulsingMarkers(centers) {
    centers.forEach((center, index) => {
      const lat = Number(center.latitude);
      const lng = Number(center.longitude);
      if (!isNaN(lat) && !isNaN(lng)) {
        let iconUrl = "assets/Location.png";
        let pulseColor = "#3880ff";
        if (center.disaster_type === "Earthquake") {
          iconUrl = "assets/forEarthquake.png";
          pulseColor = "#ff9500";
        } else if (center.disaster_type === "Typhoon") {
          iconUrl = "assets/forTyphoon.png";
          pulseColor = "#2dd36f";
        } else if (center.disaster_type === "Flood") {
          iconUrl = "assets/forFlood.png";
          pulseColor = "#3dc2ff";
        }
        const pulsingIcon = L.divIcon({
          className: "pulsing-marker",
          html: `
            <div class="pulse-container">
              <div class="pulse" style="background-color: ${pulseColor}"></div>
              <img src="${iconUrl}" class="marker-icon" />
              <div class="marker-label">${index + 1}</div>
            </div>
          `,
          iconSize: [50, 50],
          iconAnchor: [25, 50]
        });
        const marker2 = L.marker([lat, lng], { icon: pulsingIcon });
        marker2.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${index + 1}</h3>
            <h4>${center.name}</h4>
            <p><strong>Type:</strong> ${center.disaster_type}</p>
            <p><strong>Distance:</strong> ${(center.distance / 1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${center.capacity || "N/A"}</p>
          </div>
        `);
        marker2.addTo(this.map);
        this.nearestMarkers.push(marker2);
      }
    });
  }
  // Calculate routes to nearest centers
  calculateRoutes(centers) {
    return __async(this, null, function* () {
      if (!this.userLocation)
        return;
      this.routeLayer = L.layerGroup().addTo(this.map);
      for (let i = 0; i < centers.length; i++) {
        const center = centers[i];
        const lat = Number(center.latitude);
        const lng = Number(center.longitude);
        if (!isNaN(lat) && !isNaN(lng)) {
          try {
            const mapboxProfile = this.mapboxRouting.convertTravelModeToProfile(this.travelMode);
            const routeData = yield this.mapboxRouting.getDirections(this.userLocation.lng, this.userLocation.lat, lng, lat, mapboxProfile, {
              geometries: "geojson",
              overview: "simplified",
              steps: false
            });
            if (routeData && routeData.routes && routeData.routes.length > 0) {
              const route = routeData.routes[0];
              let routeColor = "#3880ff";
              if (center.disaster_type === "Earthquake")
                routeColor = "#ff9500";
              else if (center.disaster_type === "Typhoon")
                routeColor = "#2dd36f";
              else if (center.disaster_type === "Flash Flood")
                routeColor = "#3dc2ff";
              const routeLine = L.polyline(route.geometry.coordinates.map((coord) => [coord[1], coord[0]]), {
                color: routeColor,
                weight: 4,
                opacity: 0.8,
                dashArray: i === 0 ? void 0 : "10, 10"
                // Solid for first, dashed for second
              });
              routeLine.addTo(this.routeLayer);
              if (i === 0) {
                this.routeTime = route.duration;
                this.routeDistance = route.distance;
              }
              console.log(`\u{1F5FA}\uFE0F Route ${i + 1}: ${(route.distance / 1e3).toFixed(2)}km, ${(route.duration / 60).toFixed(0)}min`);
            }
          } catch (error) {
            console.error(`\u{1F5FA}\uFE0F Error calculating route to center ${i + 1}:`, error);
          }
        }
      }
    });
  }
  // Clear previous routes and markers
  clearRoutes() {
    if (this.routeLayer) {
      this.map.removeLayer(this.routeLayer);
      this.routeLayer = null;
    }
    this.nearestMarkers.forEach((marker2) => {
      this.map.removeLayer(marker2);
    });
    this.nearestMarkers = [];
    this.routeTime = 0;
    this.routeDistance = 0;
  }
  // Handle travel mode change from ion-segment
  onTravelModeChange(event) {
    const value = event.detail.value;
    if (value === "walking" || value === "cycling" || value === "driving") {
      this.changeTravelMode(value);
    }
  }
  // Change travel mode
  changeTravelMode(mode) {
    return __async(this, null, function* () {
      this.travelMode = mode;
      const toast = yield this.toastCtrl.create({
        message: `\u{1F6B6}\u200D\u2642\uFE0F Travel mode changed to ${mode}`,
        duration: 2e3,
        color: "primary"
      });
      yield toast.present();
      if (this.userLocation && this.evacuationCenters.length > 0) {
        yield this.routeToTwoNearestCenters();
      }
    });
  }
  // Show transportation options when marker is clicked
  showTransportationOptions(center) {
    return __async(this, null, function* () {
      const alert = yield this.alertCtrl.create({
        header: `Route to ${center.name}`,
        message: "Choose your transportation mode:",
        buttons: [
          {
            text: "\u{1F6B6}\u200D\u2642\uFE0F Walk",
            handler: () => {
              this.routeToCenter(center, "walking");
            }
          },
          {
            text: "\u{1F6B4}\u200D\u2642\uFE0F Cycle",
            handler: () => {
              this.routeToCenter(center, "cycling");
            }
          },
          {
            text: "\u{1F697} Drive",
            handler: () => {
              this.routeToCenter(center, "driving");
            }
          },
          {
            text: "Cancel",
            role: "cancel"
          }
        ]
      });
      yield alert.present();
    });
  }
  // Route to specific center with chosen transportation mode
  routeToCenter(center, travelMode) {
    return __async(this, null, function* () {
      if (!this.userLocation)
        return;
      try {
        this.clearRoutes();
        const lat = Number(center.latitude);
        const lng = Number(center.longitude);
        if (!isNaN(lat) && !isNaN(lng)) {
          const mapboxProfile = this.mapboxRouting.convertTravelModeToProfile(travelMode);
          const routeData = yield this.mapboxRouting.getDirections(this.userLocation.lng, this.userLocation.lat, lng, lat, mapboxProfile, {
            geometries: "geojson",
            overview: "full",
            steps: false
          });
          if (routeData && routeData.routes && routeData.routes.length > 0) {
            const route = routeData.routes[0];
            let routeColor = "#3880ff";
            let colorEmoji = "\u{1F535}";
            if (center.disaster_type === "Earthquake") {
              routeColor = "#ff9500";
              colorEmoji = "\u{1F7E0}";
            } else if (center.disaster_type === "Typhoon") {
              routeColor = "#2dd36f";
              colorEmoji = "\u{1F7E2}";
            } else if (center.disaster_type === "Flash Flood") {
              routeColor = "#3dc2ff";
              colorEmoji = "\u{1F535}";
            }
            this.routeLayer = L.layerGroup().addTo(this.map);
            const routeLine = L.polyline(route.geometry.coordinates.map((coord) => [coord[1], coord[0]]), {
              color: routeColor,
              weight: 5,
              opacity: 0.8
            });
            routeLine.addTo(this.routeLayer);
            const toast = yield this.toastCtrl.create({
              message: `${colorEmoji} Route: ${(route.distance / 1e3).toFixed(2)}km, ${(route.duration / 60).toFixed(0)}min via ${travelMode}`,
              duration: 4e3,
              color: "primary"
            });
            yield toast.present();
            this.map.fitBounds(routeLine.getBounds(), { padding: [50, 50] });
          }
        }
      } catch (error) {
        console.error("\u{1F5FA}\uFE0F Error routing to center:", error);
        const toast = yield this.toastCtrl.create({
          message: "Error calculating route. Please try again.",
          duration: 3e3,
          color: "danger"
        });
        yield toast.present();
      }
    });
  }
  goBack() {
    this.router.navigate(["/tabs/home"]);
  }
  ionViewWillLeave() {
    this.clearRoutes();
    if (this.map) {
      this.map.remove();
    }
  }
  static {
    this.\u0275fac = function AllMapsPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AllMapsPage)();
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AllMapsPage, selectors: [["app-all-maps"]], decls: 66, vars: 8, consts: [[3, "translucent"], ["color", "secondary"], ["slot", "start"], [3, "click"], ["name", "chevron-back-outline"], [3, "fullscreen"], ["id", "all-maps", 2, "height", "100%", "width", "100%"], [1, "floating-info"], [1, "info-header"], ["name", "map", "color", "secondary"], [1, "disaster-counts"], [1, "count-row"], [1, "disaster-icon"], [1, "disaster-label"], [1, "disaster-count"], [1, "info-text"], [1, "transport-controls"], [1, "transport-header"], ["name", "navigate-outline", "color", "primary"], [3, "ngModelChange", "ionChange", "ngModel"], ["value", "walking"], ["name", "walk-outline"], ["value", "cycling"], ["name", "bicycle-outline"], ["value", "driving"], ["name", "car-outline"], ["class", "route-info", 4, "ngIf"], ["vertical", "bottom", "horizontal", "end", "slot", "fixed"], ["color", "primary", 3, "click"], ["name", "navigate-outline"], [1, "fab-label"], [1, "route-info"], [1, "route-header"], ["name", "time-outline", "color", "success"], [1, "route-details"], [1, "route-item"], [3, "name"], ["name", "location-outline"]], template: function AllMapsPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header", 0)(1, "ion-toolbar", 1)(2, "ion-buttons", 2)(3, "ion-button", 3);
        \u0275\u0275listener("click", function AllMapsPage_Template_ion_button_click_3_listener() {
          return ctx.goBack();
        });
        \u0275\u0275element(4, "ion-icon", 4);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(5, "ion-title");
        \u0275\u0275text(6, "\u{1F5FA}\uFE0F All Evacuation Centers");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(7, "ion-content", 5);
        \u0275\u0275element(8, "div", 6);
        \u0275\u0275elementStart(9, "div", 7)(10, "ion-card")(11, "ion-card-content")(12, "div", 8);
        \u0275\u0275element(13, "ion-icon", 9);
        \u0275\u0275elementStart(14, "span");
        \u0275\u0275text(15);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(16, "div", 10)(17, "div", 11)(18, "span", 12);
        \u0275\u0275text(19, "\u{1F7E0}");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(20, "span", 13);
        \u0275\u0275text(21, "Earthquake:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(22, "span", 14);
        \u0275\u0275text(23);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(24, "div", 11)(25, "span", 12);
        \u0275\u0275text(26, "\u{1F7E2}");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(27, "span", 13);
        \u0275\u0275text(28, "Typhoon:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(29, "span", 14);
        \u0275\u0275text(30);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(31, "div", 11)(32, "span", 12);
        \u0275\u0275text(33, "\u{1F535}");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(34, "span", 13);
        \u0275\u0275text(35, "Flood:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(36, "span", 14);
        \u0275\u0275text(37);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(38, "div", 15);
        \u0275\u0275text(39, " Complete overview of all evacuation centers by disaster type ");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(40, "div", 16)(41, "ion-card")(42, "ion-card-content")(43, "div", 17);
        \u0275\u0275element(44, "ion-icon", 18);
        \u0275\u0275elementStart(45, "span");
        \u0275\u0275text(46, "Travel Mode");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(47, "ion-segment", 19);
        \u0275\u0275twoWayListener("ngModelChange", function AllMapsPage_Template_ion_segment_ngModelChange_47_listener($event) {
          \u0275\u0275twoWayBindingSet(ctx.travelMode, $event) || (ctx.travelMode = $event);
          return $event;
        });
        \u0275\u0275listener("ionChange", function AllMapsPage_Template_ion_segment_ionChange_47_listener($event) {
          return ctx.onTravelModeChange($event);
        });
        \u0275\u0275elementStart(48, "ion-segment-button", 20);
        \u0275\u0275element(49, "ion-icon", 21);
        \u0275\u0275elementStart(50, "ion-label");
        \u0275\u0275text(51, "Walk");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(52, "ion-segment-button", 22);
        \u0275\u0275element(53, "ion-icon", 23);
        \u0275\u0275elementStart(54, "ion-label");
        \u0275\u0275text(55, "Bike");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(56, "ion-segment-button", 24);
        \u0275\u0275element(57, "ion-icon", 25);
        \u0275\u0275elementStart(58, "ion-label");
        \u0275\u0275text(59, "Drive");
        \u0275\u0275elementEnd()()()()()();
        \u0275\u0275template(60, AllMapsPage_div_60_Template, 16, 3, "div", 26);
        \u0275\u0275elementStart(61, "ion-fab", 27)(62, "ion-fab-button", 28);
        \u0275\u0275listener("click", function AllMapsPage_Template_ion_fab_button_click_62_listener() {
          return ctx.routeToTwoNearestCenters();
        });
        \u0275\u0275element(63, "ion-icon", 29);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(64, "ion-label", 30);
        \u0275\u0275text(65, "Route to 2 Nearest Centers");
        \u0275\u0275elementEnd()()();
      }
      if (rf & 2) {
        \u0275\u0275property("translucent", true);
        \u0275\u0275advance(7);
        \u0275\u0275property("fullscreen", true);
        \u0275\u0275advance(8);
        \u0275\u0275textInterpolate1("All Centers: ", ctx.centerCounts.total, "");
        \u0275\u0275advance(8);
        \u0275\u0275textInterpolate(ctx.centerCounts.earthquake);
        \u0275\u0275advance(7);
        \u0275\u0275textInterpolate(ctx.centerCounts.typhoon);
        \u0275\u0275advance(7);
        \u0275\u0275textInterpolate(ctx.centerCounts.flood);
        \u0275\u0275advance(10);
        \u0275\u0275twoWayProperty("ngModel", ctx.travelMode);
        \u0275\u0275advance(13);
        \u0275\u0275property("ngIf", ctx.routeTime && ctx.routeDistance);
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonCard, IonCardContent, IonContent, IonFab, IonFabButton, IonHeader, IonIcon, IonLabel, IonSegment, IonSegmentButton, IonTitle, IonToolbar, SelectValueAccessorDirective, CommonModule, NgIf, FormsModule, NgControlStatus, NgModel], styles: ["\n\n#all-maps[_ngcontent-%COMP%] {\n  height: 100%;\n  width: 100%;\n  z-index: 1;\n}\n.floating-info[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  z-index: 1000;\n  max-width: 280px;\n}\n.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%] {\n  margin: 0;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  padding: 12px;\n}\n.floating-info[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: var(--ion-color-secondary);\n  margin-bottom: 8px;\n}\n.floating-info[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 18px;\n}\n.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%] {\n  margin: 8px 0;\n}\n.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin: 4px 0;\n  font-size: 13px;\n}\n.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-icon[_ngcontent-%COMP%] {\n  font-size: 14px;\n  width: 16px;\n  text-align: center;\n}\n.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-label[_ngcontent-%COMP%] {\n  flex: 1;\n  color: var(--ion-color-dark);\n}\n.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-count[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: var(--ion-color-secondary);\n  min-width: 20px;\n  text-align: right;\n}\n.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%] {\n  font-size: 11px;\n  color: var(--ion-color-medium);\n  line-height: 1.3;\n  margin-top: 8px;\n  padding-top: 8px;\n  border-top: 1px solid var(--ion-color-light);\n}\nion-toolbar[_ngcontent-%COMP%] {\n  --background: var(--ion-color-secondary);\n  --color: white;\n}\nion-title[_ngcontent-%COMP%] {\n  font-weight: 600;\n}\n.transport-controls[_ngcontent-%COMP%] {\n  position: absolute;\n  bottom: 120px;\n  left: 20px;\n  z-index: 1000;\n  max-width: 280px;\n}\n.transport-controls[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%] {\n  margin: 0;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.transport-controls[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  padding: 12px;\n}\n.transport-controls[_ngcontent-%COMP%]   .transport-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: var(--ion-color-primary);\n  margin-bottom: 8px;\n}\n.transport-controls[_ngcontent-%COMP%]   .transport-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 18px;\n}\n.transport-controls[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%] {\n  --background: rgba(var(--ion-color-light-rgb), 0.3);\n  border-radius: 8px;\n}\n.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%] {\n  --color: var(--ion-color-medium);\n  --color-checked: var(--ion-color-primary);\n  --indicator-color: var(--ion-color-primary);\n  min-height: 40px;\n}\n.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 16px;\n  margin-bottom: 2px;\n}\n.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%] {\n  font-size: 12px;\n  font-weight: 500;\n}\n.route-info[_ngcontent-%COMP%] {\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n  z-index: 1000;\n  max-width: 200px;\n}\n.route-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%] {\n  margin: 0;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.route-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  padding: 12px;\n}\n.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: var(--ion-color-success);\n  margin-bottom: 8px;\n}\n.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 18px;\n}\n.route-info[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  color: var(--ion-color-dark);\n}\n.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 16px;\n  color: var(--ion-color-primary);\n}\n.fab-label[_ngcontent-%COMP%] {\n  position: absolute;\n  right: 60px;\n  top: 50%;\n  transform: translateY(-50%);\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  white-space: nowrap;\n  pointer-events: none;\n}\n[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse-container[_ngcontent-%COMP%] {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse[_ngcontent-%COMP%] {\n  position: absolute;\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  opacity: 0.6;\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n  z-index: 1;\n}\n[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-icon[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  z-index: 2;\n  position: relative;\n}\n[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-label[_ngcontent-%COMP%] {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  background: var(--ion-color-primary);\n  color: white;\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: bold;\n  z-index: 3;\n  border: 2px solid white;\n}\n@keyframes _ngcontent-%COMP%_pulse {\n  0% {\n    transform: scale(0.8);\n    opacity: 0.8;\n  }\n  50% {\n    transform: scale(1.2);\n    opacity: 0.4;\n  }\n  100% {\n    transform: scale(0.8);\n    opacity: 0.8;\n  }\n}\n[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%] {\n  text-align: center;\n  min-width: 200px;\n}\n[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0 0 8px 0;\n  color: var(--ion-color-secondary);\n  font-size: 16px;\n  font-weight: 600;\n}\n[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 4px 0;\n  color: var(--ion-color-dark);\n  font-size: 14px;\n  font-weight: 500;\n}\n[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 4px 0;\n  font-size: 14px;\n}\n[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  color: var(--ion-color-dark);\n}\n[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup.nearest-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: var(--ion-color-success);\n  font-size: 18px;\n}\n/*# sourceMappingURL=all-maps.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AllMapsPage, [{
    type: Component,
    args: [{ selector: "app-all-maps", standalone: true, imports: [IonicModule, CommonModule, FormsModule], template: `<ion-header [translucent]="true">\r
  <ion-toolbar color="secondary">\r
    <ion-buttons slot="start">\r
      <ion-button (click)="goBack()">\r
        <ion-icon name="chevron-back-outline"></ion-icon>\r
      </ion-button>\r
    </ion-buttons>\r
    <ion-title>\u{1F5FA}\uFE0F All Evacuation Centers</ion-title>\r
  </ion-toolbar>\r
</ion-header>\r
\r
<ion-content [fullscreen]="true">\r
  <div id="all-maps" style="height: 100%; width: 100%;"></div>\r
\r
  <!-- Floating info card -->\r
  <div class="floating-info">\r
    <ion-card>\r
      <ion-card-content>\r
        <div class="info-header">\r
          <ion-icon name="map" color="secondary"></ion-icon>\r
          <span>All Centers: {{ centerCounts.total }}</span>\r
        </div>\r
\r
        <div class="disaster-counts">\r
          <div class="count-row">\r
            <span class="disaster-icon">\u{1F7E0}</span>\r
            <span class="disaster-label">Earthquake:</span>\r
            <span class="disaster-count">{{ centerCounts.earthquake }}</span>\r
          </div>\r
\r
          <div class="count-row">\r
            <span class="disaster-icon">\u{1F7E2}</span>\r
            <span class="disaster-label">Typhoon:</span>\r
            <span class="disaster-count">{{ centerCounts.typhoon }}</span>\r
          </div>\r
\r
          <div class="count-row">\r
            <span class="disaster-icon">\u{1F535}</span>\r
            <span class="disaster-label">Flood:</span>\r
            <span class="disaster-count">{{ centerCounts.flood }}</span>\r
          </div>\r
        </div>\r
\r
        <div class="info-text">\r
          Complete overview of all evacuation centers by disaster type\r
        </div>\r
      </ion-card-content>\r
    </ion-card>\r
  </div>\r
\r
  <!-- Transportation Mode Controls -->\r
  <div class="transport-controls">\r
    <ion-card>\r
      <ion-card-content>\r
        <div class="transport-header">\r
          <ion-icon name="navigate-outline" color="primary"></ion-icon>\r
          <span>Travel Mode</span>\r
        </div>\r
        <ion-segment [(ngModel)]="travelMode" (ionChange)="onTravelModeChange($event)">\r
          <ion-segment-button value="walking">\r
            <ion-icon name="walk-outline"></ion-icon>\r
            <ion-label>Walk</ion-label>\r
          </ion-segment-button>\r
          <ion-segment-button value="cycling">\r
            <ion-icon name="bicycle-outline"></ion-icon>\r
            <ion-label>Bike</ion-label>\r
          </ion-segment-button>\r
          <ion-segment-button value="driving">\r
            <ion-icon name="car-outline"></ion-icon>\r
            <ion-label>Drive</ion-label>\r
          </ion-segment-button>\r
        </ion-segment>\r
      </ion-card-content>\r
    </ion-card>\r
  </div>\r
\r
  <!-- Route Information -->\r
  <div *ngIf="routeTime && routeDistance" class="route-info">\r
    <ion-card>\r
      <ion-card-content>\r
        <div class="route-header">\r
          <ion-icon name="time-outline" color="success"></ion-icon>\r
          <span>Route to Nearest Center</span>\r
        </div>\r
        <div class="route-details">\r
          <div class="route-item">\r
            <ion-icon [name]="travelMode === 'walking' ? 'walk-outline' : travelMode === 'cycling' ? 'bicycle-outline' : 'car-outline'"></ion-icon>\r
            <span>{{ (routeTime/60).toFixed(0) }} min</span>\r
          </div>\r
          <div class="route-item">\r
            <ion-icon name="location-outline"></ion-icon>\r
            <span>{{ (routeDistance/1000).toFixed(2) }} km</span>\r
          </div>\r
        </div>\r
      </ion-card-content>\r
    </ion-card>\r
  </div>\r
\r
  <!-- Route Button -->\r
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">\r
    <ion-fab-button color="primary" (click)="routeToTwoNearestCenters()">\r
      <ion-icon name="navigate-outline"></ion-icon>\r
    </ion-fab-button>\r
    <ion-label class="fab-label">Route to 2 Nearest Centers</ion-label>\r
  </ion-fab>\r
</ion-content>\r
`, styles: ["/* src/app/pages/disaster-maps/all-maps.page.scss */\n#all-maps {\n  height: 100%;\n  width: 100%;\n  z-index: 1;\n}\n.floating-info {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  z-index: 1000;\n  max-width: 280px;\n}\n.floating-info ion-card {\n  margin: 0;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.floating-info ion-card-content {\n  padding: 12px;\n}\n.floating-info .info-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: var(--ion-color-secondary);\n  margin-bottom: 8px;\n}\n.floating-info .info-header ion-icon {\n  font-size: 18px;\n}\n.floating-info .disaster-counts {\n  margin: 8px 0;\n}\n.floating-info .disaster-counts .count-row {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin: 4px 0;\n  font-size: 13px;\n}\n.floating-info .disaster-counts .count-row .disaster-icon {\n  font-size: 14px;\n  width: 16px;\n  text-align: center;\n}\n.floating-info .disaster-counts .count-row .disaster-label {\n  flex: 1;\n  color: var(--ion-color-dark);\n}\n.floating-info .disaster-counts .count-row .disaster-count {\n  font-weight: 600;\n  color: var(--ion-color-secondary);\n  min-width: 20px;\n  text-align: right;\n}\n.floating-info .info-text {\n  font-size: 11px;\n  color: var(--ion-color-medium);\n  line-height: 1.3;\n  margin-top: 8px;\n  padding-top: 8px;\n  border-top: 1px solid var(--ion-color-light);\n}\nion-toolbar {\n  --background: var(--ion-color-secondary);\n  --color: white;\n}\nion-title {\n  font-weight: 600;\n}\n.transport-controls {\n  position: absolute;\n  bottom: 120px;\n  left: 20px;\n  z-index: 1000;\n  max-width: 280px;\n}\n.transport-controls ion-card {\n  margin: 0;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.transport-controls ion-card-content {\n  padding: 12px;\n}\n.transport-controls .transport-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: var(--ion-color-primary);\n  margin-bottom: 8px;\n}\n.transport-controls .transport-header ion-icon {\n  font-size: 18px;\n}\n.transport-controls ion-segment {\n  --background: rgba(var(--ion-color-light-rgb), 0.3);\n  border-radius: 8px;\n}\n.transport-controls ion-segment-button {\n  --color: var(--ion-color-medium);\n  --color-checked: var(--ion-color-primary);\n  --indicator-color: var(--ion-color-primary);\n  min-height: 40px;\n}\n.transport-controls ion-segment-button ion-icon {\n  font-size: 16px;\n  margin-bottom: 2px;\n}\n.transport-controls ion-segment-button ion-label {\n  font-size: 12px;\n  font-weight: 500;\n}\n.route-info {\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n  z-index: 1000;\n  max-width: 200px;\n}\n.route-info ion-card {\n  margin: 0;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.route-info ion-card-content {\n  padding: 12px;\n}\n.route-info .route-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: var(--ion-color-success);\n  margin-bottom: 8px;\n}\n.route-info .route-header ion-icon {\n  font-size: 18px;\n}\n.route-info .route-details {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n.route-info .route-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  color: var(--ion-color-dark);\n}\n.route-info .route-item ion-icon {\n  font-size: 16px;\n  color: var(--ion-color-primary);\n}\n.fab-label {\n  position: absolute;\n  right: 60px;\n  top: 50%;\n  transform: translateY(-50%);\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  white-space: nowrap;\n  pointer-events: none;\n}\n:global(.pulsing-marker) .pulse-container {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n:global(.pulsing-marker) .pulse {\n  position: absolute;\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  opacity: 0.6;\n  animation: pulse 2s infinite;\n  z-index: 1;\n}\n:global(.pulsing-marker) .marker-icon {\n  width: 40px;\n  height: 40px;\n  z-index: 2;\n  position: relative;\n}\n:global(.pulsing-marker) .marker-label {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  background: var(--ion-color-primary);\n  color: white;\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: bold;\n  z-index: 3;\n  border: 2px solid white;\n}\n@keyframes pulse {\n  0% {\n    transform: scale(0.8);\n    opacity: 0.8;\n  }\n  50% {\n    transform: scale(1.2);\n    opacity: 0.4;\n  }\n  100% {\n    transform: scale(0.8);\n    opacity: 0.8;\n  }\n}\n:global(.leaflet-popup-content) .evacuation-popup {\n  text-align: center;\n  min-width: 200px;\n}\n:global(.leaflet-popup-content) .evacuation-popup h3 {\n  margin: 0 0 8px 0;\n  color: var(--ion-color-secondary);\n  font-size: 16px;\n  font-weight: 600;\n}\n:global(.leaflet-popup-content) .evacuation-popup h4 {\n  margin: 4px 0;\n  color: var(--ion-color-dark);\n  font-size: 14px;\n  font-weight: 500;\n}\n:global(.leaflet-popup-content) .evacuation-popup p {\n  margin: 4px 0;\n  font-size: 14px;\n}\n:global(.leaflet-popup-content) .evacuation-popup p strong {\n  color: var(--ion-color-dark);\n}\n:global(.leaflet-popup-content) .evacuation-popup.nearest-popup h3 {\n  color: var(--ion-color-success);\n  font-size: 18px;\n}\n/*# sourceMappingURL=all-maps.page.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AllMapsPage, { className: "AllMapsPage", filePath: "src/app/pages/disaster-maps/all-maps.page.ts", lineNumber: 32 });
})();
export {
  AllMapsPage
};
//# sourceMappingURL=all-maps.page-ITUJN653.js.map

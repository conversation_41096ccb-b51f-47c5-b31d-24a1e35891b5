import {
  OfflineStorageService
} from "./chunk-73VFBDTI.js";
import {
  AuthService
} from "./chunk-XKXQPGS3.js";
import "./chunk-FKALCVFZ.js";
import {
  FCM,
  FcmService
} from "./chunk-RDFT5QPW.js";
import {
  environment
} from "./chunk-I7MI46CM.js";
import "./chunk-TAZAZ6IP.js";
import {
  AlertController,
  Component,
  FormsModule,
  HttpClient,
  Injectable,
  IonButton,
  IonCardContent,
  IonContent,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonicModule,
  NgControlStatus,
  NgControlStatusGroup,
  NgForm,
  NgModel,
  Platform,
  RequiredValidator,
  Router,
  TextValueAccessorDirective,
  ToastController,
  firstValueFrom,
  setClassMetadata,
  timeout,
  ɵNgNoValidate,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵinject,
  ɵɵlistener,
  ɵɵtext,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import {
  __async
} from "./chunk-UL2P3LPA.js";

// src/app/services/network.service.ts
var NetworkService = class _NetworkService {
  constructor(http, platform, toastCtrl) {
    this.http = http;
    this.platform = platform;
    this.toastCtrl = toastCtrl;
  }
  /**
   * Check if the backend API is reachable
   */
  checkBackendConnectivity() {
    return __async(this, null, function* () {
      try {
        console.log("Checking backend connectivity to:", environment.apiUrl);
        const testResponse = yield firstValueFrom(this.http.get(`${environment.apiUrl.replace("/api", "")}/api/test`).pipe(timeout(1e4)));
        console.log("Backend test endpoint successful:", testResponse);
        const response = yield firstValueFrom(this.http.get(`${environment.apiUrl}/evacuation-centers`).pipe(timeout(1e4)));
        console.log("Backend connectivity check successful");
        return true;
      } catch (error) {
        console.error("Backend connectivity check failed:", error);
        console.error("Error details:", {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });
        if (error.status === 0) {
          console.log("Backend server not reachable - offline mode available");
        } else {
          console.log("Backend is reachable but returned an error:", error.status);
          return true;
        }
        return false;
      }
    });
  }
  /**
   * Check if Mapbox API is reachable
   */
  checkMapboxConnectivity() {
    return __async(this, null, function* () {
      try {
        console.log("Checking Mapbox connectivity...");
        const testUrl = `https://api.mapbox.com/directions/v5/mapbox/walking/121.7740,12.8797;121.7750,12.8807?access_token=${environment.mapboxAccessToken}&overview=simplified`;
        const response = yield firstValueFrom(this.http.get(testUrl));
        console.log("Mapbox connectivity check successful");
        return true;
      } catch (error) {
        console.error("Mapbox connectivity check failed:", error);
        if (error.status === 0) {
          this.showConnectivityError("Mapbox", "Cannot connect to Mapbox API. Please check your internet connection.");
        } else if (error.status === 401) {
          this.showConnectivityError("Mapbox", "Invalid Mapbox access token. Please check your token.");
        } else if (error.status === 403) {
          this.showConnectivityError("Mapbox", "Mapbox access denied. Please check your token permissions.");
        } else if (error.status === 429) {
          this.showConnectivityError("Mapbox", "Too many requests to Mapbox. Please wait and try again.");
        }
        return false;
      }
    });
  }
  /**
   * Check overall network connectivity
   */
  checkNetworkConnectivity() {
    return __async(this, null, function* () {
      console.log("Starting comprehensive network connectivity check...");
      const results = {
        backend: false,
        routing: false
      };
      results.backend = yield this.checkBackendConnectivity();
      results.routing = yield this.checkMapboxConnectivity();
      console.log("Network connectivity check results:", results);
      return results;
    });
  }
  /**
   * Show connectivity error message
   */
  showConnectivityError(service, message) {
    return __async(this, null, function* () {
      const toast = yield this.toastCtrl.create({
        header: `${service} Connection Error`,
        message,
        duration: 5e3,
        color: "danger",
        buttons: [
          {
            text: "Dismiss",
            role: "cancel"
          }
        ]
      });
      yield toast.present();
    });
  }
  /**
   * Test network connectivity with a simple ping
   */
  pingTest() {
    return __async(this, null, function* () {
      try {
        const response = yield firstValueFrom(this.http.get("https://httpbin.org/get"));
        return true;
      } catch (error) {
        console.error("Ping test failed:", error);
        return false;
      }
    });
  }
  /**
   * Get network status information
   */
  getNetworkInfo() {
    if (this.platform.is("capacitor")) {
      return {
        platform: "mobile",
        userAgent: navigator.userAgent
      };
    } else {
      return {
        platform: "web",
        online: navigator.onLine,
        userAgent: navigator.userAgent
      };
    }
  }
  static {
    this.\u0275fac = function NetworkService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _NetworkService)(\u0275\u0275inject(HttpClient), \u0275\u0275inject(Platform), \u0275\u0275inject(ToastController));
    };
  }
  static {
    this.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _NetworkService, factory: _NetworkService.\u0275fac, providedIn: "root" });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NetworkService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }, { type: Platform }, { type: ToastController }], null);
})();

// src/app/pages/login/login.page.ts
var LoginPage = class _LoginPage {
  // Add the goToRegister method
  goToRegister() {
    this.router.navigate(["/register"]);
  }
  openNetworkDiagnostics() {
    this.router.navigate(["/network-diagnostics"]);
  }
  openEnvironmentSwitcher() {
    this.router.navigate(["/environment-switcher"]);
  }
  constructor(router, authService, fcm, http, alertController, platform, fcmService, offlineStorage, networkService) {
    this.router = router;
    this.authService = authService;
    this.fcm = fcm;
    this.http = http;
    this.alertController = alertController;
    this.platform = platform;
    this.fcmService = fcmService;
    this.offlineStorage = offlineStorage;
    this.networkService = networkService;
    this.credentials = {
      email: "",
      password: ""
    };
    this.errorMessage = "";
    this.fcmToken = "";
    this.fcmTokenReady = false;
  }
  ngOnInit() {
    return __async(this, null, function* () {
      console.log("\u{1F525} Login page initializing...");
      yield this.initializeFCM();
    });
  }
  /**
   * Initialize FCM service and get token
   */
  initializeFCM() {
    return __async(this, null, function* () {
      try {
        console.log("\u{1F525} Initializing FCM for login...");
        yield this.fcmService.initPush();
        yield this.getFCMToken();
        console.log("\u2705 FCM initialization complete, token ready:", !!this.fcmToken);
        this.fcmTokenReady = true;
      } catch (error) {
        console.error("\u274C FCM initialization failed:", error);
        this.fcmTokenReady = false;
      }
    });
  }
  getFCMToken() {
    return __async(this, null, function* () {
      try {
        if (!this.platform.is("cordova") && !this.platform.is("capacitor")) {
          console.log("Running in browser, using mock FCM token");
          this.fcmToken = "browser-mock-token-" + Math.random().toString(36).substring(2, 15);
          console.log("Mock FCM Token:", this.fcmToken);
          return;
        }
        console.log("Getting FCM token from service...");
        this.fcmToken = yield this.fcmService.getToken();
        console.log("\u2705 FCM Token obtained:", this.fcmToken.substring(0, 20) + "...");
      } catch (error) {
        console.error("\u274C Error getting FCM token from service:", error);
        try {
          console.log("Trying direct FCM plugin as fallback...");
          this.fcmToken = yield this.fcm.getToken();
          console.log("\u2705 FCM Token from direct plugin:", this.fcmToken.substring(0, 20) + "...");
        } catch (fallbackError) {
          console.error("\u274C All FCM token methods failed:", fallbackError);
          this.fcmToken = "";
        }
      }
    });
  }
  /**
   * Helper method to register a token with a specific endpoint
   * @param endpoint The API endpoint to use
   * @param payload The token payload to send
   * @param onSuccess Callback for successful registration
   * @param onError Callback for failed registration
   */
  registerTokenWithEndpoint(endpoint, payload, onSuccess, onError) {
    const storedToken = localStorage.getItem("fcm_token");
    if (storedToken === this.fcmToken) {
      console.log("Token already registered, skipping registration");
      if (onSuccess)
        onSuccess();
      return;
    }
    if (localStorage.getItem("fcm_token_registering") === "true") {
      console.log("Token registration already in progress, skipping");
      if (onSuccess)
        onSuccess();
      return;
    }
    localStorage.setItem("fcm_token_registering", "true");
    this.http.post(endpoint, payload).subscribe({
      next: (res) => {
        console.log(`FCM token registered with ${endpoint}:`, res);
        localStorage.setItem("fcm_token", this.fcmToken);
        localStorage.removeItem("fcm_token_registering");
        if (onSuccess)
          onSuccess();
      },
      error: (err) => {
        console.error(`Error registering token with ${endpoint}:`, err);
        localStorage.removeItem("fcm_token_registering");
        if (onError)
          onError();
      }
    });
  }
  onLogin() {
    return __async(this, null, function* () {
      if (!this.credentials.email || !this.credentials.password) {
        yield this.presentAlert("Login Failed", "Please enter both email and password.");
        return;
      }
      console.log("\u{1F510} Login attempt started");
      console.log("\u{1F4E7} Email:", this.credentials.email);
      console.log("\u{1F310} API URL:", environment.apiUrl);
      console.log("\u{1F4F1} Platform:", this.platform.is("android") ? "Android" : this.platform.is("ios") ? "iOS" : "Browser");
      console.log("\u{1F30D} Network status:", navigator.onLine ? "Online" : "Offline");
      console.log("\uFFFD Offline mode:", this.offlineStorage.isOfflineMode());
      console.log("\uFFFD\u{1F525} FCM Token ready:", this.fcmTokenReady, "Token:", this.fcmToken ? this.fcmToken.substring(0, 20) + "..." : "None");
      const isOfflineMode = this.offlineStorage.isOfflineMode();
      const isNetworkAvailable = navigator.onLine;
      if (!isNetworkAvailable || isOfflineMode) {
        console.log("\u{1F504} Attempting offline authentication...");
        const offlineLoginSuccess = yield this.attemptOfflineLogin();
        if (offlineLoginSuccess) {
          return;
        }
        if (!isNetworkAvailable) {
          yield this.presentOfflineAlert();
          return;
        }
      }
      if (isNetworkAvailable && !isOfflineMode && !this.fcmTokenReady && !this.fcmToken) {
        console.log("\u{1F525} FCM token not ready, attempting to get it now...");
        try {
          yield this.getFCMToken();
        } catch (error) {
          console.warn("\u26A0\uFE0F Could not get FCM token, continuing without it:", error);
        }
      }
      if (isNetworkAvailable && !isOfflineMode) {
        console.log("\u{1F9EA} Testing API connectivity...");
        const backendConnected = yield this.networkService.checkBackendConnectivity();
        if (!backendConnected) {
          yield this.presentConnectionErrorAlert();
          return;
        }
      }
      this.authService.login(this.credentials).subscribe({
        next: (response) => __async(this, null, function* () {
          console.log("\u2705 Login successful:", response);
          yield this.presentSuccessAlert("Login Successful", "Welcome, " + response.user.full_name);
          this.authService.setToken(response.token);
          if (this.fcmToken) {
            console.log("Registering FCM token with backend:", this.fcmToken);
            const payload = {
              token: this.fcmToken,
              device_type: this.platform.is("ios") ? "ios" : "android",
              project_id: environment.firebase.projectId
            };
            if (response.user && response.user.id) {
              payload.user_id = response.user.id;
            }
            console.log("Token registration payload:", payload);
            console.log("API URL:", `${environment.apiUrl}/device-token`);
            this.fcmService.registerTokenWithBackend(this.fcmToken, response.user.id);
            this.router.navigate(["/welcome"]);
          } else {
            console.warn("No FCM token available to register");
            this.router.navigate(["/welcome"]);
          }
        }),
        error: (error) => {
          console.error("\u274C Login error:", error);
          console.error("\u{1F4CA} Error details:", {
            status: error.status,
            statusText: error.statusText,
            message: error.message,
            url: error.url,
            error: error.error
          });
          this.errorMessage = error.error?.message || "Login failed";
          if (error.status === 0) {
            this.presentAlert("Network Error", `Cannot connect to the server. Please check:
\u2022 Your internet connection
\u2022 If you're on the same WiFi network as the server
\u2022 If the backend server is running

Server URL: ${environment.apiUrl}`);
          } else if (error.status === 401) {
            this.presentAlert("Login Failed", "Invalid email or password. Please try again.");
          } else if (error.status === 404) {
            this.presentAlert("Server Error", "Login endpoint not found. Please check server configuration.");
          } else if (error.status >= 500) {
            this.presentAlert("Server Error", "The server encountered an error. Please try again later.");
          } else {
            this.presentAlert("Login Error", `An error occurred during login (${error.status}).

Please check the console for more details or try again later.`);
          }
        }
      });
    });
  }
  presentAlert(header, message) {
    return __async(this, null, function* () {
      const alert = yield this.alertController.create({
        header,
        message,
        buttons: ["OK"],
        cssClass: "login-alert"
      });
      yield alert.present();
    });
  }
  presentSuccessAlert(header, message) {
    return __async(this, null, function* () {
      const alert = yield this.alertController.create({
        header,
        message,
        buttons: ["OK"],
        cssClass: "login-success-alert"
      });
      yield alert.present();
    });
  }
  attemptOfflineLogin() {
    return __async(this, null, function* () {
      try {
        const storedCredentials = localStorage.getItem("offline_credentials");
        if (!storedCredentials) {
          console.log("\u274C No offline credentials stored");
          return false;
        }
        const credentials = JSON.parse(storedCredentials);
        if (credentials.email === this.credentials.email && credentials.password === this.credentials.password) {
          console.log("\u2705 Offline login successful");
          this.authService.setToken("offline_token_" + Date.now());
          yield this.presentSuccessAlert("Offline Login", "Logged in using cached credentials");
          this.router.navigate(["/welcome"]);
          return true;
        }
        console.log("\u274C Offline credentials do not match");
        return false;
      } catch (error) {
        console.error("\u274C Error during offline login:", error);
        return false;
      }
    });
  }
  presentOfflineAlert() {
    return __async(this, null, function* () {
      const alert = yield this.alertController.create({
        header: "No Internet Connection",
        message: "You are currently offline. Please check your internet connection and try again, or continue in offline mode if you have previously logged in.",
        buttons: [
          {
            text: "Retry",
            handler: () => {
              this.onLogin();
            }
          },
          {
            text: "Continue Offline",
            handler: () => {
              this.attemptOfflineLogin();
            }
          }
        ],
        cssClass: "offline-alert"
      });
      yield alert.present();
    });
  }
  presentConnectionErrorAlert() {
    return __async(this, null, function* () {
      const alert = yield this.alertController.create({
        header: "Connection Error",
        message: `Cannot connect to the server at ${environment.apiUrl}.

Please check:
\u2022 Your internet connection
\u2022 If the backend server is running
\u2022 If you're on the same network as the server`,
        buttons: [
          {
            text: "Retry",
            handler: () => {
              this.onLogin();
            }
          },
          {
            text: "Network Diagnostics",
            handler: () => {
              this.router.navigate(["/network-diagnostics"]);
            }
          }
        ],
        cssClass: "connection-error-alert"
      });
      yield alert.present();
    });
  }
  static {
    this.\u0275fac = function LoginPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _LoginPage)(\u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(FCM), \u0275\u0275directiveInject(HttpClient), \u0275\u0275directiveInject(AlertController), \u0275\u0275directiveInject(Platform), \u0275\u0275directiveInject(FcmService), \u0275\u0275directiveInject(OfflineStorageService), \u0275\u0275directiveInject(NetworkService));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _LoginPage, selectors: [["app-login"]], decls: 36, vars: 2, consts: [[1, "ion-padding"], [1, "login-container"], [1, "login-wrapper"], ["src", "assets/ALERTO.png", "alt", "App Logo", 1, "login-logo"], [1, "login-title"], [1, "login-form", 3, "ngSubmit"], ["position", "floating"], ["type", "email", "name", "email", "required", "", 3, "ngModelChange", "ngModel"], ["type", "password", "name", "password", "required", "", 3, "ngModelChange", "ngModel"], ["expand", "block", "type", "submit", 1, "login-btn"], [1, "troubleshooting-section"], ["expand", "block", "fill", "outline", "color", "warning", 3, "click"], ["name", "settings-outline", "slot", "start"], ["expand", "block", "fill", "clear", "size", "small", 3, "click"], ["name", "bug-outline", "slot", "start"], ["expand", "block", "fill", "outline", "color", "secondary", 3, "click"], [1, "login-link"], [3, "click"]], template: function LoginPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-content", 0)(1, "div", 1)(2, "ion-card-content")(3, "div", 2);
        \u0275\u0275element(4, "img", 3);
        \u0275\u0275elementStart(5, "h1", 4);
        \u0275\u0275text(6, "Log In Here!");
        \u0275\u0275elementEnd();
        \u0275\u0275element(7, "p");
        \u0275\u0275elementStart(8, "form", 5);
        \u0275\u0275listener("ngSubmit", function LoginPage_Template_form_ngSubmit_8_listener() {
          return ctx.onLogin();
        });
        \u0275\u0275elementStart(9, "ion-item")(10, "ion-label", 6);
        \u0275\u0275text(11, "Email:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(12, "ion-input", 7);
        \u0275\u0275twoWayListener("ngModelChange", function LoginPage_Template_ion_input_ngModelChange_12_listener($event) {
          \u0275\u0275twoWayBindingSet(ctx.credentials.email, $event) || (ctx.credentials.email = $event);
          return $event;
        });
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(13, "ion-item")(14, "ion-label", 6);
        \u0275\u0275text(15, "Password:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(16, "ion-input", 8);
        \u0275\u0275twoWayListener("ngModelChange", function LoginPage_Template_ion_input_ngModelChange_16_listener($event) {
          \u0275\u0275twoWayBindingSet(ctx.credentials.password, $event) || (ctx.credentials.password = $event);
          return $event;
        });
        \u0275\u0275elementEnd()();
        \u0275\u0275element(17, "br")(18, "br");
        \u0275\u0275elementStart(19, "ion-button", 9);
        \u0275\u0275text(20, "Log In");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(21, "div", 10)(22, "ion-button", 11);
        \u0275\u0275listener("click", function LoginPage_Template_ion_button_click_22_listener() {
          return ctx.openEnvironmentSwitcher();
        });
        \u0275\u0275element(23, "ion-icon", 12);
        \u0275\u0275text(24, " Switch API Endpoint ");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(25, "ion-button", 13);
        \u0275\u0275listener("click", function LoginPage_Template_ion_button_click_25_listener() {
          return ctx.openNetworkDiagnostics();
        });
        \u0275\u0275element(26, "ion-icon", 14);
        \u0275\u0275text(27, " Network Diagnostics ");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(28, "ion-button", 15);
        \u0275\u0275listener("click", function LoginPage_Template_ion_button_click_28_listener() {
          return ctx.openNetworkDiagnostics();
        });
        \u0275\u0275text(29, " \u{1F527} Network Diagnostics ");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(30, "div", 16);
        \u0275\u0275text(31, " Don't have an account? ");
        \u0275\u0275elementStart(32, "a", 17);
        \u0275\u0275listener("click", function LoginPage_Template_a_click_32_listener() {
          return ctx.goToRegister();
        });
        \u0275\u0275elementStart(33, "strong")(34, "u");
        \u0275\u0275text(35, "Sign Up");
        \u0275\u0275elementEnd()()()()()()()();
      }
      if (rf & 2) {
        \u0275\u0275advance(12);
        \u0275\u0275twoWayProperty("ngModel", ctx.credentials.email);
        \u0275\u0275advance(4);
        \u0275\u0275twoWayProperty("ngModel", ctx.credentials.password);
      }
    }, dependencies: [IonicModule, IonButton, IonCardContent, IonContent, IonIcon, IonInput, IonItem, IonLabel, TextValueAccessorDirective, FormsModule, \u0275NgNoValidate, NgControlStatus, NgControlStatusGroup, RequiredValidator, NgModel, NgForm], styles: ["\n\n.login-container[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 80vh;\n}\n.login-wrapper[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 420px;\n  padding: 32px 28px;\n  margin: 0 auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n.login-logo[_ngcontent-%COMP%] {\n  width: 300px;\n  height: 300px;\n}\n.login-title[_ngcontent-%COMP%] {\n  font-size: 2.2rem;\n  font-weight: 700;\n}\n.login-desc[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  color: #888;\n}\n.login-form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  border-radius: 16px;\n}\n.login-btn[_ngcontent-%COMP%] {\n  --border-radius: 25px;\n  font-size: 1.2rem;\n  height: 48px;\n}\n.login-link[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  font-size: 1.1rem;\n}\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  font-weight: 700;\n}\n.troubleshooting-section[_ngcontent-%COMP%] {\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid var(--ion-color-light);\n}\n.troubleshooting-section[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  margin-bottom: 8px;\n}\n.login-alert[_ngcontent-%COMP%] {\n  --backdrop-opacity: 0.8;\n}\n.login-alert[_ngcontent-%COMP%]   .alert-wrapper[_ngcontent-%COMP%] {\n  border-radius: 15px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n}\n.login-alert[_ngcontent-%COMP%]   .alert-head[_ngcontent-%COMP%] {\n  padding-bottom: 10px;\n}\n.login-alert[_ngcontent-%COMP%]   .alert-title[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #d9534f;\n}\n.login-alert[_ngcontent-%COMP%]   .alert-message[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: #333;\n}\n.login-alert[_ngcontent-%COMP%]   .alert-button[_ngcontent-%COMP%] {\n  color: #3880ff;\n  font-weight: 500;\n}\n.login-success-alert[_ngcontent-%COMP%] {\n  --backdrop-opacity: 0.8;\n}\n.login-success-alert[_ngcontent-%COMP%]   .alert-wrapper[_ngcontent-%COMP%] {\n  border-radius: 15px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n}\n.login-success-alert[_ngcontent-%COMP%]   .alert-head[_ngcontent-%COMP%] {\n  padding-bottom: 10px;\n}\n.login-success-alert[_ngcontent-%COMP%]   .alert-title[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #5cb85c;\n}\n.login-success-alert[_ngcontent-%COMP%]   .alert-message[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: #333;\n}\n.login-success-alert[_ngcontent-%COMP%]   .alert-button[_ngcontent-%COMP%] {\n  color: #3880ff;\n  font-weight: 500;\n}\n/*# sourceMappingURL=login.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoginPage, [{
    type: Component,
    args: [{ standalone: true, imports: [IonicModule, FormsModule], selector: "app-login", template: `\r
\r
<ion-content class="ion-padding">\r
  <div class="login-container">\r
\r
\r
\r
      <ion-card-content>\r
        <div class="login-wrapper">\r
          <img src="assets/ALERTO.png" alt="App Logo" class="login-logo" />\r
          <h1 class="login-title">Log In Here!</h1>\r
          <p></p>\r
          <form (ngSubmit)="onLogin()" class="login-form">\r
            <ion-item>\r
              <ion-label position="floating">Email:</ion-label>\r
              <ion-input type="email" [(ngModel)]="credentials.email" name="email" required></ion-input>\r
            </ion-item>\r
            <ion-item>\r
              <ion-label position="floating">Password:</ion-label>\r
              <ion-input type="password" [(ngModel)]="credentials.password" name="password" required></ion-input>\r
            </ion-item>\r
            <br><br>\r
            <ion-button expand="block" type="submit" class="login-btn">Log In</ion-button>\r
          </form>\r
\r
          <!-- Connection Troubleshooting -->\r
          <div class="troubleshooting-section">\r
            <ion-button\r
              expand="block"\r
              fill="outline"\r
              color="warning"\r
              (click)="openEnvironmentSwitcher()">\r
              <ion-icon name="settings-outline" slot="start"></ion-icon>\r
              Switch API Endpoint\r
            </ion-button>\r
\r
            <ion-button\r
              expand="block"\r
              fill="clear"\r
              size="small"\r
              (click)="openNetworkDiagnostics()">\r
              <ion-icon name="bug-outline" slot="start"></ion-icon>\r
              Network Diagnostics\r
            </ion-button>\r
          </div>\r
\r
          <!-- Debug button for troubleshooting -->\r
          <ion-button expand="block" fill="outline" color="secondary" (click)="openNetworkDiagnostics()">\r
            \u{1F527} Network Diagnostics\r
          </ion-button>\r
\r
          <div class="login-link">\r
            Don't have an account?\r
            <a (click)="goToRegister()"><strong><u>Sign Up</u></strong></a>\r
          </div>\r
        </div>\r
      </ion-card-content>\r
\r
  </div>\r
</ion-content>`, styles: ["/* src/app/pages/login/login.page.scss */\n.login-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 80vh;\n}\n.login-wrapper {\n  width: 100%;\n  max-width: 420px;\n  padding: 32px 28px;\n  margin: 0 auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n.login-logo {\n  width: 300px;\n  height: 300px;\n}\n.login-title {\n  font-size: 2.2rem;\n  font-weight: 700;\n}\n.login-desc {\n  font-size: 1.1rem;\n  color: #888;\n}\n.login-form ion-item {\n  font-size: 1.1rem;\n  border-radius: 16px;\n}\n.login-btn {\n  --border-radius: 25px;\n  font-size: 1.2rem;\n  height: 48px;\n}\n.login-link {\n  margin-top: 20px;\n  font-size: 1.1rem;\n}\n.login-link a {\n  font-size: 1.1rem;\n  font-weight: 700;\n}\n.troubleshooting-section {\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid var(--ion-color-light);\n}\n.troubleshooting-section ion-button {\n  margin-bottom: 8px;\n}\n.login-alert {\n  --backdrop-opacity: 0.8;\n}\n.login-alert .alert-wrapper {\n  border-radius: 15px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n}\n.login-alert .alert-head {\n  padding-bottom: 10px;\n}\n.login-alert .alert-title {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #d9534f;\n}\n.login-alert .alert-message {\n  font-size: 1rem;\n  color: #333;\n}\n.login-alert .alert-button {\n  color: #3880ff;\n  font-weight: 500;\n}\n.login-success-alert {\n  --backdrop-opacity: 0.8;\n}\n.login-success-alert .alert-wrapper {\n  border-radius: 15px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n}\n.login-success-alert .alert-head {\n  padding-bottom: 10px;\n}\n.login-success-alert .alert-title {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #5cb85c;\n}\n.login-success-alert .alert-message {\n  font-size: 1rem;\n  color: #333;\n}\n.login-success-alert .alert-button {\n  color: #3880ff;\n  font-weight: 500;\n}\n/*# sourceMappingURL=login.page.css.map */\n"] }]
  }], () => [{ type: Router }, { type: AuthService }, { type: FCM }, { type: HttpClient }, { type: AlertController }, { type: Platform }, { type: FcmService }, { type: OfflineStorageService }, { type: NetworkService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(LoginPage, { className: "LoginPage", filePath: "src/app/pages/login/login.page.ts", lineNumber: 20 });
})();
export {
  LoginPage
};
//# sourceMappingURL=login.page-CUJWPRDK.js.map

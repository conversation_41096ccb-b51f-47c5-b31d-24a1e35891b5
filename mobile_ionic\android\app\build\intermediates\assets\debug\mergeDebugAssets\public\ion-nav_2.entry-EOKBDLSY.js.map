{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-nav_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, f as getElement, e as Host } from './index-527b9e34.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { n as assert, s as shallowEqualStringMap } from './helpers-d94bc8ad.js';\nimport { c as config, p as printIonWarning } from './index-cfd9c1f2.js';\nimport { l as lifecycle, t as transition, s as setPageHidden, d as LIFECYCLE_WILL_UNLOAD, b as LIFECYCLE_WILL_LEAVE, c as LIFECYCLE_DID_LEAVE } from './index-68c0d151.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport { a as attachComponent } from './framework-delegate-56b467ad.js';\nconst VIEW_STATE_NEW = 1;\nconst VIEW_STATE_ATTACHED = 2;\nconst VIEW_STATE_DESTROYED = 3;\n// TODO(FW-2832): types\nclass ViewController {\n  constructor(component, params) {\n    this.component = component;\n    this.params = params;\n    this.state = VIEW_STATE_NEW;\n  }\n  async init(container) {\n    this.state = VIEW_STATE_ATTACHED;\n    if (!this.element) {\n      const component = this.component;\n      this.element = await attachComponent(this.delegate, container, component, ['ion-page', 'ion-page-invisible'], this.params);\n    }\n  }\n  /**\n   * DOM WRITE\n   */\n  _destroy() {\n    assert(this.state !== VIEW_STATE_DESTROYED, 'view state must be ATTACHED');\n    const element = this.element;\n    if (element) {\n      if (this.delegate) {\n        this.delegate.removeViewFromDom(element.parentElement, element);\n      } else {\n        element.remove();\n      }\n    }\n    this.nav = undefined;\n    this.state = VIEW_STATE_DESTROYED;\n  }\n}\nconst matches = (view, id, params) => {\n  if (!view) {\n    return false;\n  }\n  if (view.component !== id) {\n    return false;\n  }\n  return shallowEqualStringMap(view.params, params);\n};\nconst convertToView = (page, params) => {\n  if (!page) {\n    return null;\n  }\n  if (page instanceof ViewController) {\n    return page;\n  }\n  return new ViewController(page, params);\n};\nconst convertToViews = pages => {\n  return pages.map(page => {\n    if (page instanceof ViewController) {\n      return page;\n    }\n    if ('component' in page) {\n      return convertToView(page.component, page.componentProps === null ? undefined : page.componentProps);\n    }\n    return convertToView(page, undefined);\n  }).filter(v => v !== null);\n};\nconst navCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}\";\nconst IonNavStyle0 = navCss;\nconst Nav = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n    this.ionNavWillChange = createEvent(this, \"ionNavWillChange\", 3);\n    this.ionNavDidChange = createEvent(this, \"ionNavDidChange\", 3);\n    this.transInstr = [];\n    this.gestureOrAnimationInProgress = false;\n    this.useRouter = false;\n    this.isTransitioning = false;\n    this.destroyed = false;\n    this.views = [];\n    this.didLoad = false;\n    this.delegate = undefined;\n    this.swipeGesture = undefined;\n    this.animated = true;\n    this.animation = undefined;\n    this.rootParams = undefined;\n    this.root = undefined;\n  }\n  swipeGestureChanged() {\n    if (this.gesture) {\n      this.gesture.enable(this.swipeGesture === true);\n    }\n  }\n  rootChanged() {\n    if (this.root === undefined) {\n      return;\n    }\n    if (this.didLoad === false) {\n      /**\n       * If the component has not loaded yet, we can skip setting up the root component.\n       * It will be called when `componentDidLoad` fires.\n       */\n      return;\n    }\n    if (!this.useRouter) {\n      if (this.root !== undefined) {\n        this.setRoot(this.root, this.rootParams);\n      }\n    }\n  }\n  componentWillLoad() {\n    this.useRouter = document.querySelector('ion-router') !== null && this.el.closest('[no-router]') === null;\n    if (this.swipeGesture === undefined) {\n      const mode = getIonMode(this);\n      this.swipeGesture = config.getBoolean('swipeBackEnabled', mode === 'ios');\n    }\n    this.ionNavWillLoad.emit();\n  }\n  async componentDidLoad() {\n    // We want to set this flag before any watch callbacks are manually called\n    this.didLoad = true;\n    this.rootChanged();\n    this.gesture = (await import('./swipe-back-0184f6b3.js')).createSwipeBackGesture(this.el, this.canStart.bind(this), this.onStart.bind(this), this.onMove.bind(this), this.onEnd.bind(this));\n    this.swipeGestureChanged();\n  }\n  connectedCallback() {\n    this.destroyed = false;\n  }\n  disconnectedCallback() {\n    for (const view of this.views) {\n      lifecycle(view.element, LIFECYCLE_WILL_UNLOAD);\n      view._destroy();\n    }\n    // Release swipe back gesture and transition.\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n    this.transInstr.length = 0;\n    this.views.length = 0;\n    this.destroyed = true;\n  }\n  /**\n   * Push a new component onto the current navigation stack. Pass any additional\n   * information along as an object. This additional information is accessible\n   * through NavParams.\n   *\n   * @param component The component to push onto the navigation stack.\n   * @param componentProps Any properties of the component.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  push(component, componentProps, opts, done) {\n    return this.insert(-1, component, componentProps, opts, done);\n  }\n  /**\n   * Inserts a component into the navigation stack at the specified index.\n   * This is useful to add a component at any point in the navigation stack.\n   *\n   * @param insertIndex The index to insert the component at in the stack.\n   * @param component The component to insert into the navigation stack.\n   * @param componentProps Any properties of the component.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  insert(insertIndex, component, componentProps, opts, done) {\n    return this.insertPages(insertIndex, [{\n      component,\n      componentProps\n    }], opts, done);\n  }\n  /**\n   * Inserts an array of components into the navigation stack at the specified index.\n   * The last component in the array will become instantiated as a view, and animate\n   * in to become the active view.\n   *\n   * @param insertIndex The index to insert the components at in the stack.\n   * @param insertComponents The components to insert into the navigation stack.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  insertPages(insertIndex, insertComponents, opts, done) {\n    return this.queueTrns({\n      insertStart: insertIndex,\n      insertViews: insertComponents,\n      opts\n    }, done);\n  }\n  /**\n   * Pop a component off of the navigation stack. Navigates back from the current\n   * component.\n   *\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  pop(opts, done) {\n    return this.removeIndex(-1, 1, opts, done);\n  }\n  /**\n   * Pop to a specific index in the navigation stack.\n   *\n   * @param indexOrViewCtrl The index or view controller to pop to.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  popTo(indexOrViewCtrl, opts, done) {\n    const ti = {\n      removeStart: -1,\n      removeCount: -1,\n      opts\n    };\n    if (typeof indexOrViewCtrl === 'object' && indexOrViewCtrl.component) {\n      ti.removeView = indexOrViewCtrl;\n      ti.removeStart = 1;\n    } else if (typeof indexOrViewCtrl === 'number') {\n      ti.removeStart = indexOrViewCtrl + 1;\n    }\n    return this.queueTrns(ti, done);\n  }\n  /**\n   * Navigate back to the root of the stack, no matter how far back that is.\n   *\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  popToRoot(opts, done) {\n    return this.removeIndex(1, -1, opts, done);\n  }\n  /**\n   * Removes a component from the navigation stack at the specified index.\n   *\n   * @param startIndex The number to begin removal at.\n   * @param removeCount The number of components to remove.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  removeIndex(startIndex, removeCount = 1, opts, done) {\n    return this.queueTrns({\n      removeStart: startIndex,\n      removeCount,\n      opts\n    }, done);\n  }\n  /**\n   * Set the root for the current navigation stack to a component.\n   *\n   * @param component The component to set as the root of the navigation stack.\n   * @param componentProps Any properties of the component.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  setRoot(component, componentProps, opts, done) {\n    return this.setPages([{\n      component,\n      componentProps\n    }], opts, done);\n  }\n  /**\n   * Set the views of the current navigation stack and navigate to the last view.\n   * By default animations are disabled, but they can be enabled by passing options\n   * to the navigation controller. Navigation parameters can also be passed to the\n   * individual pages in the array.\n   *\n   * @param views The list of views to set as the navigation stack.\n   * @param opts The navigation options.\n   * @param done The transition complete function.\n   */\n  setPages(views, opts, done) {\n    opts !== null && opts !== void 0 ? opts : opts = {};\n    // if animation wasn't set to true then default it to NOT animate\n    if (opts.animated !== true) {\n      opts.animated = false;\n    }\n    return this.queueTrns({\n      insertStart: 0,\n      insertViews: views,\n      removeStart: 0,\n      removeCount: -1,\n      opts\n    }, done);\n  }\n  /**\n   * Called by the router to update the view.\n   *\n   * @param id The component tag.\n   * @param params The component params.\n   * @param direction A direction hint.\n   * @param animation an AnimationBuilder.\n   *\n   * @return the status.\n   * @internal\n   */\n  setRouteId(id, params, direction, animation) {\n    const active = this.getActiveSync();\n    if (matches(active, id, params)) {\n      return Promise.resolve({\n        changed: false,\n        element: active.element\n      });\n    }\n    let resolve;\n    const promise = new Promise(r => resolve = r);\n    let finish;\n    const commonOpts = {\n      updateURL: false,\n      viewIsReady: enteringEl => {\n        let mark;\n        const p = new Promise(r => mark = r);\n        resolve({\n          changed: true,\n          element: enteringEl,\n          markVisible: async () => {\n            mark();\n            await finish;\n          }\n        });\n        return p;\n      }\n    };\n    if (direction === 'root') {\n      finish = this.setRoot(id, params, commonOpts);\n    } else {\n      // Look for a view matching the target in the view stack.\n      const viewController = this.views.find(v => matches(v, id, params));\n      if (viewController) {\n        finish = this.popTo(viewController, Object.assign(Object.assign({}, commonOpts), {\n          direction: 'back',\n          animationBuilder: animation\n        }));\n      } else if (direction === 'forward') {\n        finish = this.push(id, params, Object.assign(Object.assign({}, commonOpts), {\n          animationBuilder: animation\n        }));\n      } else if (direction === 'back') {\n        finish = this.setRoot(id, params, Object.assign(Object.assign({}, commonOpts), {\n          direction: 'back',\n          animated: true,\n          animationBuilder: animation\n        }));\n      }\n    }\n    return promise;\n  }\n  /**\n   * Called by <ion-router> to retrieve the current component.\n   *\n   * @internal\n   */\n  async getRouteId() {\n    const active = this.getActiveSync();\n    if (active) {\n      return {\n        id: active.element.tagName,\n        params: active.params,\n        element: active.element\n      };\n    }\n    return undefined;\n  }\n  /**\n   * Get the active view.\n   */\n  async getActive() {\n    return this.getActiveSync();\n  }\n  /**\n   * Get the view at the specified index.\n   *\n   * @param index The index of the view.\n   */\n  async getByIndex(index) {\n    return this.views[index];\n  }\n  /**\n   * Returns `true` if the current view can go back.\n   *\n   * @param view The view to check.\n   */\n  async canGoBack(view) {\n    return this.canGoBackSync(view);\n  }\n  /**\n   * Get the previous view.\n   *\n   * @param view The view to get.\n   */\n  async getPrevious(view) {\n    return this.getPreviousSync(view);\n  }\n  /**\n   * Returns the number of views in the stack.\n   */\n  async getLength() {\n    return Promise.resolve(this.views.length);\n  }\n  getActiveSync() {\n    return this.views[this.views.length - 1];\n  }\n  canGoBackSync(view = this.getActiveSync()) {\n    return !!(view && this.getPreviousSync(view));\n  }\n  getPreviousSync(view = this.getActiveSync()) {\n    if (!view) {\n      return undefined;\n    }\n    const views = this.views;\n    const index = views.indexOf(view);\n    return index > 0 ? views[index - 1] : undefined;\n  }\n  /**\n   * Adds a navigation stack change to the queue and schedules it to run.\n   *\n   * @returns Whether the transition succeeds.\n   */\n  async queueTrns(ti, done) {\n    var _a, _b;\n    if (this.isTransitioning && ((_a = ti.opts) === null || _a === void 0 ? void 0 : _a.skipIfBusy)) {\n      return false;\n    }\n    const promise = new Promise((resolve, reject) => {\n      ti.resolve = resolve;\n      ti.reject = reject;\n    });\n    ti.done = done;\n    /**\n     * If using router, check to see if navigation hooks\n     * will allow us to perform this transition. This\n     * is required in order for hooks to work with\n     * the ion-back-button or swipe to go back.\n     */\n    if (ti.opts && ti.opts.updateURL !== false && this.useRouter) {\n      const router = document.querySelector('ion-router');\n      if (router) {\n        const canTransition = await router.canTransition();\n        if (canTransition === false) {\n          return false;\n        }\n        if (typeof canTransition === 'string') {\n          router.push(canTransition, ti.opts.direction || 'back');\n          return false;\n        }\n      }\n    }\n    // Normalize empty\n    if (((_b = ti.insertViews) === null || _b === void 0 ? void 0 : _b.length) === 0) {\n      ti.insertViews = undefined;\n    }\n    // Enqueue transition instruction\n    this.transInstr.push(ti);\n    // if there isn't a transition already happening\n    // then this will kick off this transition\n    this.nextTrns();\n    return promise;\n  }\n  success(result, ti) {\n    if (this.destroyed) {\n      this.fireError('nav controller was destroyed', ti);\n      return;\n    }\n    if (ti.done) {\n      ti.done(result.hasCompleted, result.requiresTransition, result.enteringView, result.leavingView, result.direction);\n    }\n    ti.resolve(result.hasCompleted);\n    if (ti.opts.updateURL !== false && this.useRouter) {\n      const router = document.querySelector('ion-router');\n      if (router) {\n        const direction = result.direction === 'back' ? 'back' : 'forward';\n        router.navChanged(direction);\n      }\n    }\n  }\n  failed(rejectReason, ti) {\n    if (this.destroyed) {\n      this.fireError('nav controller was destroyed', ti);\n      return;\n    }\n    this.transInstr.length = 0;\n    this.fireError(rejectReason, ti);\n  }\n  fireError(rejectReason, ti) {\n    if (ti.done) {\n      ti.done(false, false, rejectReason);\n    }\n    if (ti.reject && !this.destroyed) {\n      ti.reject(rejectReason);\n    } else {\n      ti.resolve(false);\n    }\n  }\n  /**\n   * Consumes the next transition in the queue.\n   *\n   * @returns whether the transition is executed.\n   */\n  nextTrns() {\n    // this is the framework's bread 'n butta function\n    // only one transition is allowed at any given time\n    if (this.isTransitioning) {\n      return false;\n    }\n    // there is no transition happening right now, executes the next instructions.\n    const ti = this.transInstr.shift();\n    if (!ti) {\n      return false;\n    }\n    this.runTransition(ti);\n    return true;\n  }\n  /** Executes all the transition instruction from the queue. */\n  async runTransition(ti) {\n    try {\n      // set that this nav is actively transitioning\n      this.ionNavWillChange.emit();\n      this.isTransitioning = true;\n      this.prepareTI(ti);\n      const leavingView = this.getActiveSync();\n      const enteringView = this.getEnteringView(ti, leavingView);\n      if (!leavingView && !enteringView) {\n        throw new Error('no views in the stack to be removed');\n      }\n      if (enteringView && enteringView.state === VIEW_STATE_NEW) {\n        await enteringView.init(this.el);\n      }\n      this.postViewInit(enteringView, leavingView, ti);\n      // Needs transition?\n      const requiresTransition = (ti.enteringRequiresTransition || ti.leavingRequiresTransition) && enteringView !== leavingView;\n      if (requiresTransition && ti.opts && leavingView) {\n        const isBackDirection = ti.opts.direction === 'back';\n        /**\n         * If heading back, use the entering page's animation\n         * unless otherwise specified by the developer.\n         */\n        if (isBackDirection) {\n          ti.opts.animationBuilder = ti.opts.animationBuilder || (enteringView === null || enteringView === void 0 ? void 0 : enteringView.animationBuilder);\n        }\n        leavingView.animationBuilder = ti.opts.animationBuilder;\n      }\n      let result;\n      if (requiresTransition) {\n        result = await this.transition(enteringView, leavingView, ti);\n      } else {\n        // transition is not required, so we are already done!\n        // they're inserting/removing the views somewhere in the middle or\n        // beginning, so visually nothing needs to animate/transition\n        // resolve immediately because there's no animation that's happening\n        result = {\n          hasCompleted: true,\n          requiresTransition: false\n        };\n      }\n      this.success(result, ti);\n      this.ionNavDidChange.emit();\n    } catch (rejectReason) {\n      this.failed(rejectReason, ti);\n    }\n    this.isTransitioning = false;\n    this.nextTrns();\n  }\n  prepareTI(ti) {\n    var _a, _b;\n    var _c;\n    const viewsLength = this.views.length;\n    (_a = ti.opts) !== null && _a !== void 0 ? _a : ti.opts = {};\n    (_b = (_c = ti.opts).delegate) !== null && _b !== void 0 ? _b : _c.delegate = this.delegate;\n    if (ti.removeView !== undefined) {\n      assert(ti.removeStart !== undefined, 'removeView needs removeStart');\n      assert(ti.removeCount !== undefined, 'removeView needs removeCount');\n      const index = this.views.indexOf(ti.removeView);\n      if (index < 0) {\n        throw new Error('removeView was not found');\n      }\n      ti.removeStart += index;\n    }\n    if (ti.removeStart !== undefined) {\n      if (ti.removeStart < 0) {\n        ti.removeStart = viewsLength - 1;\n      }\n      if (ti.removeCount < 0) {\n        ti.removeCount = viewsLength - ti.removeStart;\n      }\n      ti.leavingRequiresTransition = ti.removeCount > 0 && ti.removeStart + ti.removeCount === viewsLength;\n    }\n    if (ti.insertViews) {\n      // allow -1 to be passed in to auto push it on the end\n      // and clean up the index if it's larger then the size of the stack\n      if (ti.insertStart < 0 || ti.insertStart > viewsLength) {\n        ti.insertStart = viewsLength;\n      }\n      ti.enteringRequiresTransition = ti.insertStart === viewsLength;\n    }\n    const insertViews = ti.insertViews;\n    if (!insertViews) {\n      return;\n    }\n    assert(insertViews.length > 0, 'length can not be zero');\n    const viewControllers = convertToViews(insertViews);\n    if (viewControllers.length === 0) {\n      throw new Error('invalid views to insert');\n    }\n    // Check all the inserted view are correct\n    for (const view of viewControllers) {\n      view.delegate = ti.opts.delegate;\n      const nav = view.nav;\n      if (nav && nav !== this) {\n        throw new Error('inserted view was already inserted');\n      }\n      if (view.state === VIEW_STATE_DESTROYED) {\n        throw new Error('inserted view was already destroyed');\n      }\n    }\n    ti.insertViews = viewControllers;\n  }\n  /**\n   * Returns the view that will be entered considering the transition instructions.\n   *\n   * @param ti The instructions.\n   * @param leavingView The view being left or undefined if none.\n   *\n   * @returns The view that will be entered, undefined if none.\n   */\n  getEnteringView(ti, leavingView) {\n    // The last inserted view will be entered when view are inserted.\n    const insertViews = ti.insertViews;\n    if (insertViews !== undefined) {\n      return insertViews[insertViews.length - 1];\n    }\n    // When views are deleted, we will enter the last view that is not removed and not the view being left.\n    const removeStart = ti.removeStart;\n    if (removeStart !== undefined) {\n      const views = this.views;\n      const removeEnd = removeStart + ti.removeCount;\n      for (let i = views.length - 1; i >= 0; i--) {\n        const view = views[i];\n        if ((i < removeStart || i >= removeEnd) && view !== leavingView) {\n          return view;\n        }\n      }\n    }\n    return undefined;\n  }\n  /**\n   * Adds and Removes the views from the navigation stack.\n   *\n   * @param enteringView The view being entered.\n   * @param leavingView The view being left.\n   * @param ti The instructions.\n   */\n  postViewInit(enteringView, leavingView, ti) {\n    var _a, _b, _c;\n    assert(leavingView || enteringView, 'Both leavingView and enteringView are null');\n    assert(ti.resolve, 'resolve must be valid');\n    assert(ti.reject, 'reject must be valid');\n    // Compute the views to remove.\n    const opts = ti.opts;\n    const {\n      insertViews,\n      removeStart,\n      removeCount\n    } = ti;\n    /** Records the view to destroy */\n    let destroyQueue;\n    // there are views to remove\n    if (removeStart !== undefined && removeCount !== undefined) {\n      assert(removeStart >= 0, 'removeStart can not be negative');\n      assert(removeCount >= 0, 'removeCount can not be negative');\n      destroyQueue = [];\n      for (let i = removeStart; i < removeStart + removeCount; i++) {\n        const view = this.views[i];\n        if (view !== undefined && view !== enteringView && view !== leavingView) {\n          destroyQueue.push(view);\n        }\n      }\n      // default the direction to \"back\"\n      (_a = opts.direction) !== null && _a !== void 0 ? _a : opts.direction = 'back';\n    }\n    const finalNumViews = this.views.length + ((_b = insertViews === null || insertViews === void 0 ? void 0 : insertViews.length) !== null && _b !== void 0 ? _b : 0) - (removeCount !== null && removeCount !== void 0 ? removeCount : 0);\n    assert(finalNumViews >= 0, 'final balance can not be negative');\n    if (finalNumViews === 0) {\n      printIonWarning(`[ion-nav] - You can't remove all the pages in the navigation stack. nav.pop() is probably called too many times.`, this, this.el);\n      throw new Error('navigation stack needs at least one root page');\n    }\n    // At this point the transition can not be rejected, any throw should be an error\n    // Insert the new views in the stack.\n    if (insertViews) {\n      // add the views to the\n      let insertIndex = ti.insertStart;\n      for (const view of insertViews) {\n        this.insertViewAt(view, insertIndex);\n        insertIndex++;\n      }\n      if (ti.enteringRequiresTransition) {\n        // default to forward if not already set\n        (_c = opts.direction) !== null && _c !== void 0 ? _c : opts.direction = 'forward';\n      }\n    }\n    // if the views to be removed are in the beginning or middle\n    // and there is not a view that needs to visually transition out\n    // then just destroy them and don't transition anything\n    // batch all of lifecycles together\n    // let's make sure, callbacks are zoned\n    if (destroyQueue && destroyQueue.length > 0) {\n      for (const view of destroyQueue) {\n        lifecycle(view.element, LIFECYCLE_WILL_LEAVE);\n        lifecycle(view.element, LIFECYCLE_DID_LEAVE);\n        lifecycle(view.element, LIFECYCLE_WILL_UNLOAD);\n      }\n      // once all lifecycle events has been delivered, we can safely detroy the views\n      for (const view of destroyQueue) {\n        this.destroyView(view);\n      }\n    }\n  }\n  async transition(enteringView, leavingView, ti) {\n    // we should animate (duration > 0) if the pushed page is not the first one (startup)\n    // or if it is a portal (modal, actionsheet, etc.)\n    const opts = ti.opts;\n    const progressCallback = opts.progressAnimation ? ani => {\n      /**\n       * Because this progress callback is called asynchronously\n       * it is possible for the gesture to start and end before\n       * the animation is ever set. In that scenario, we should\n       * immediately call progressEnd so that the transition promise\n       * resolves and the gesture does not get locked up.\n       */\n      if (ani !== undefined && !this.gestureOrAnimationInProgress) {\n        this.gestureOrAnimationInProgress = true;\n        ani.onFinish(() => {\n          this.gestureOrAnimationInProgress = false;\n        }, {\n          oneTimeCallback: true\n        });\n        /**\n         * Playing animation to beginning\n         * with a duration of 0 prevents\n         * any flickering when the animation\n         * is later cleaned up.\n         */\n        ani.progressEnd(0, 0, 0);\n      } else {\n        this.sbAni = ani;\n      }\n    } : undefined;\n    const mode = getIonMode(this);\n    const enteringEl = enteringView.element;\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    const leavingEl = leavingView && leavingView.element;\n    const animationOpts = Object.assign(Object.assign({\n      mode,\n      showGoBack: this.canGoBackSync(enteringView),\n      baseEl: this.el,\n      progressCallback,\n      animated: this.animated && config.getBoolean('animated', true),\n      enteringEl,\n      leavingEl\n    }, opts), {\n      animationBuilder: opts.animationBuilder || this.animation || config.get('navAnimation')\n    });\n    const {\n      hasCompleted\n    } = await transition(animationOpts);\n    return this.transitionFinish(hasCompleted, enteringView, leavingView, opts);\n  }\n  transitionFinish(hasCompleted, enteringView, leavingView, opts) {\n    /**\n     * If the transition did not complete, the leavingView will still be the active\n     * view on the stack. Otherwise unmount all the views after the enteringView.\n     */\n    const activeView = hasCompleted ? enteringView : leavingView;\n    if (activeView) {\n      this.unmountInactiveViews(activeView);\n    }\n    return {\n      hasCompleted,\n      requiresTransition: true,\n      enteringView,\n      leavingView,\n      direction: opts.direction\n    };\n  }\n  /**\n   * Inserts a view at the specified index.\n   *\n   * When the view already is in the stack it will be moved to the new position.\n   *\n   * @param view The view to insert.\n   * @param index The index where to insert the view.\n   */\n  insertViewAt(view, index) {\n    const views = this.views;\n    const existingIndex = views.indexOf(view);\n    if (existingIndex > -1) {\n      assert(view.nav === this, 'view is not part of the nav');\n      // The view already in the stack, removes it.\n      views.splice(existingIndex, 1);\n      // and add it back at the requested index.\n      views.splice(index, 0, view);\n    } else {\n      assert(!view.nav, 'nav is used');\n      // this is a new view to add to the stack\n      // create the new entering view\n      view.nav = this;\n      views.splice(index, 0, view);\n    }\n  }\n  /**\n   * Removes a view from the stack.\n   *\n   * @param view The view to remove.\n   */\n  removeView(view) {\n    assert(view.state === VIEW_STATE_ATTACHED || view.state === VIEW_STATE_DESTROYED, 'view state should be loaded or destroyed');\n    const views = this.views;\n    const index = views.indexOf(view);\n    assert(index > -1, 'view must be part of the stack');\n    if (index >= 0) {\n      views.splice(index, 1);\n    }\n  }\n  destroyView(view) {\n    view._destroy();\n    this.removeView(view);\n  }\n  /**\n   * Unmounts all inactive views after the specified active view.\n   *\n   * DOM WRITE\n   *\n   * @param activeView The view that is actively visible in the stack. Used to calculate which views to unmount.\n   */\n  unmountInactiveViews(activeView) {\n    // ok, cleanup time!! Destroy all of the views that are\n    // INACTIVE and come after the active view\n    // only do this if the views exist, though\n    if (this.destroyed) {\n      return;\n    }\n    const views = this.views;\n    const activeViewIndex = views.indexOf(activeView);\n    for (let i = views.length - 1; i >= 0; i--) {\n      const view = views[i];\n      /**\n       * When inserting multiple views via insertPages\n       * the last page will be transitioned to, but the\n       * others will not be. As a result, a DOM element\n       * will only be created for the last page inserted.\n       * As a result, it is possible to have views in the\n       * stack that do not have `view.element` yet.\n       */\n      const element = view.element;\n      if (element) {\n        if (i > activeViewIndex) {\n          // this view comes after the active view\n          // let's unload it\n          lifecycle(element, LIFECYCLE_WILL_UNLOAD);\n          this.destroyView(view);\n        } else if (i < activeViewIndex) {\n          // this view comes before the active view\n          // and it is not a portal then ensure it is hidden\n          setPageHidden(element, true);\n        }\n      }\n    }\n  }\n  canStart() {\n    return !this.gestureOrAnimationInProgress && !!this.swipeGesture && !this.isTransitioning && this.transInstr.length === 0 && this.canGoBackSync();\n  }\n  onStart() {\n    this.gestureOrAnimationInProgress = true;\n    this.pop({\n      direction: 'back',\n      progressAnimation: true\n    });\n  }\n  onMove(stepValue) {\n    if (this.sbAni) {\n      this.sbAni.progressStep(stepValue);\n    }\n  }\n  onEnd(shouldComplete, stepValue, dur) {\n    if (this.sbAni) {\n      this.sbAni.onFinish(() => {\n        this.gestureOrAnimationInProgress = false;\n      }, {\n        oneTimeCallback: true\n      });\n      // Account for rounding errors in JS\n      let newStepValue = shouldComplete ? -0.001 : 0.001;\n      /**\n       * Animation will be reversed here, so need to\n       * reverse the easing curve as well\n       *\n       * Additionally, we need to account for the time relative\n       * to the new easing curve, as `stepValue` is going to be given\n       * in terms of a linear curve.\n       */\n      if (!shouldComplete) {\n        this.sbAni.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n        newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], stepValue)[0];\n      } else {\n        newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], stepValue)[0];\n      }\n      this.sbAni.progressEnd(shouldComplete ? 1 : 0, newStepValue, dur);\n    } else {\n      this.gestureOrAnimationInProgress = false;\n    }\n  }\n  render() {\n    return h(\"slot\", {\n      key: '188d0abd6c047d235380f07aac81223b757010e8'\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"swipeGesture\": [\"swipeGestureChanged\"],\n      \"root\": [\"rootChanged\"]\n    };\n  }\n};\nNav.style = IonNavStyle0;\nconst navLink = (el, routerDirection, component, componentProps, routerAnimation) => {\n  const nav = el.closest('ion-nav');\n  if (nav) {\n    if (routerDirection === 'forward') {\n      if (component !== undefined) {\n        return nav.push(component, componentProps, {\n          skipIfBusy: true,\n          animationBuilder: routerAnimation\n        });\n      }\n    } else if (routerDirection === 'root') {\n      if (component !== undefined) {\n        return nav.setRoot(component, componentProps, {\n          skipIfBusy: true,\n          animationBuilder: routerAnimation\n        });\n      }\n    } else if (routerDirection === 'back') {\n      return nav.pop({\n        skipIfBusy: true,\n        animationBuilder: routerAnimation\n      });\n    }\n  }\n  return Promise.resolve(false);\n};\nconst NavLink = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onClick = () => {\n      return navLink(this.el, this.routerDirection, this.component, this.componentProps, this.routerAnimation);\n    };\n    this.component = undefined;\n    this.componentProps = undefined;\n    this.routerDirection = 'forward';\n    this.routerAnimation = undefined;\n  }\n  render() {\n    return h(Host, {\n      key: '9ba170d1b10e08e8a6b5e6a30d363871d455a0a9',\n      onClick: this.onClick\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nexport { Nav as ion_nav, NavLink as ion_nav_link };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,iBAAiB;AACvB,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAE7B,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,WAAW,QAAQ;AAC7B,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AAAA,EACM,KAAK,WAAW;AAAA;AACpB,WAAK,QAAQ;AACb,UAAI,CAAC,KAAK,SAAS;AACjB,cAAM,YAAY,KAAK;AACvB,aAAK,UAAU,MAAM,gBAAgB,KAAK,UAAU,WAAW,WAAW,CAAC,YAAY,oBAAoB,GAAG,KAAK,MAAM;AAAA,MAC3H;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,WAAO,KAAK,UAAU,sBAAsB,6BAA6B;AACzE,UAAM,UAAU,KAAK;AACrB,QAAI,SAAS;AACX,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,kBAAkB,QAAQ,eAAe,OAAO;AAAA,MAChE,OAAO;AACL,gBAAQ,OAAO;AAAA,MACjB;AAAA,IACF;AACA,SAAK,MAAM;AACX,SAAK,QAAQ;AAAA,EACf;AACF;AACA,IAAM,UAAU,CAAC,MAAM,IAAI,WAAW;AACpC,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,MAAI,KAAK,cAAc,IAAI;AACzB,WAAO;AAAA,EACT;AACA,SAAO,sBAAsB,KAAK,QAAQ,MAAM;AAClD;AACA,IAAM,gBAAgB,CAAC,MAAM,WAAW;AACtC,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,gBAAgB;AAClC,WAAO;AAAA,EACT;AACA,SAAO,IAAI,eAAe,MAAM,MAAM;AACxC;AACA,IAAM,iBAAiB,WAAS;AAC9B,SAAO,MAAM,IAAI,UAAQ;AACvB,QAAI,gBAAgB,gBAAgB;AAClC,aAAO;AAAA,IACT;AACA,QAAI,eAAe,MAAM;AACvB,aAAO,cAAc,KAAK,WAAW,KAAK,mBAAmB,OAAO,SAAY,KAAK,cAAc;AAAA,IACrG;AACA,WAAO,cAAc,MAAM,MAAS;AAAA,EACtC,CAAC,EAAE,OAAO,OAAK,MAAM,IAAI;AAC3B;AACA,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,MAAM,MAAM;AAAA,EAChB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,mBAAmB,YAAY,MAAM,oBAAoB,CAAC;AAC/D,SAAK,kBAAkB,YAAY,MAAM,mBAAmB,CAAC;AAC7D,SAAK,aAAa,CAAC;AACnB,SAAK,+BAA+B;AACpC,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,QAAQ,CAAC;AACd,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,OAAO,KAAK,iBAAiB,IAAI;AAAA,IAChD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,SAAS,QAAW;AAC3B;AAAA,IACF;AACA,QAAI,KAAK,YAAY,OAAO;AAK1B;AAAA,IACF;AACA,QAAI,CAAC,KAAK,WAAW;AACnB,UAAI,KAAK,SAAS,QAAW;AAC3B,aAAK,QAAQ,KAAK,MAAM,KAAK,UAAU;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,YAAY,SAAS,cAAc,YAAY,MAAM,QAAQ,KAAK,GAAG,QAAQ,aAAa,MAAM;AACrG,QAAI,KAAK,iBAAiB,QAAW;AACnC,YAAM,OAAO,WAAW,IAAI;AAC5B,WAAK,eAAe,OAAO,WAAW,oBAAoB,SAAS,KAAK;AAAA,IAC1E;AACA,SAAK,eAAe,KAAK;AAAA,EAC3B;AAAA,EACM,mBAAmB;AAAA;AAEvB,WAAK,UAAU;AACf,WAAK,YAAY;AACjB,WAAK,WAAW,MAAM,OAAO,mCAA0B,GAAG,uBAAuB,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,GAAG,KAAK,QAAQ,KAAK,IAAI,GAAG,KAAK,OAAO,KAAK,IAAI,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC;AAC1L,WAAK,oBAAoB;AAAA,IAC3B;AAAA;AAAA,EACA,oBAAoB;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,uBAAuB;AACrB,eAAW,QAAQ,KAAK,OAAO;AAC7B,gBAAU,KAAK,SAAS,qBAAqB;AAC7C,WAAK,SAAS;AAAA,IAChB;AAEA,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACjB;AACA,SAAK,WAAW,SAAS;AACzB,SAAK,MAAM,SAAS;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,KAAK,WAAW,gBAAgB,MAAM,MAAM;AAC1C,WAAO,KAAK,OAAO,IAAI,WAAW,gBAAgB,MAAM,IAAI;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,aAAa,WAAW,gBAAgB,MAAM,MAAM;AACzD,WAAO,KAAK,YAAY,aAAa,CAAC;AAAA,MACpC;AAAA,MACA;AAAA,IACF,CAAC,GAAG,MAAM,IAAI;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,YAAY,aAAa,kBAAkB,MAAM,MAAM;AACrD,WAAO,KAAK,UAAU;AAAA,MACpB,aAAa;AAAA,MACb,aAAa;AAAA,MACb;AAAA,IACF,GAAG,IAAI;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MAAM,MAAM;AACd,WAAO,KAAK,YAAY,IAAI,GAAG,MAAM,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,iBAAiB,MAAM,MAAM;AACjC,UAAM,KAAK;AAAA,MACT,aAAa;AAAA,MACb,aAAa;AAAA,MACb;AAAA,IACF;AACA,QAAI,OAAO,oBAAoB,YAAY,gBAAgB,WAAW;AACpE,SAAG,aAAa;AAChB,SAAG,cAAc;AAAA,IACnB,WAAW,OAAO,oBAAoB,UAAU;AAC9C,SAAG,cAAc,kBAAkB;AAAA,IACrC;AACA,WAAO,KAAK,UAAU,IAAI,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,MAAM,MAAM;AACpB,WAAO,KAAK,YAAY,GAAG,IAAI,MAAM,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,YAAY,cAAc,GAAG,MAAM,MAAM;AACnD,WAAO,KAAK,UAAU;AAAA,MACpB,aAAa;AAAA,MACb;AAAA,MACA;AAAA,IACF,GAAG,IAAI;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,WAAW,gBAAgB,MAAM,MAAM;AAC7C,WAAO,KAAK,SAAS,CAAC;AAAA,MACpB;AAAA,MACA;AAAA,IACF,CAAC,GAAG,MAAM,IAAI;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,OAAO,MAAM,MAAM;AAC1B,aAAS,QAAQ,SAAS,SAAS,OAAO,OAAO,CAAC;AAElD,QAAI,KAAK,aAAa,MAAM;AAC1B,WAAK,WAAW;AAAA,IAClB;AACA,WAAO,KAAK,UAAU;AAAA,MACpB,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,MACb;AAAA,IACF,GAAG,IAAI;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,WAAW,IAAI,QAAQ,WAAW,WAAW;AAC3C,UAAM,SAAS,KAAK,cAAc;AAClC,QAAI,QAAQ,QAAQ,IAAI,MAAM,GAAG;AAC/B,aAAO,QAAQ,QAAQ;AAAA,QACrB,SAAS;AAAA,QACT,SAAS,OAAO;AAAA,MAClB,CAAC;AAAA,IACH;AACA,QAAI;AACJ,UAAM,UAAU,IAAI,QAAQ,OAAK,UAAU,CAAC;AAC5C,QAAI;AACJ,UAAM,aAAa;AAAA,MACjB,WAAW;AAAA,MACX,aAAa,gBAAc;AACzB,YAAI;AACJ,cAAM,IAAI,IAAI,QAAQ,OAAK,OAAO,CAAC;AACnC,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,UACT,aAAa,MAAY;AACvB,iBAAK;AACL,kBAAM;AAAA,UACR;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,cAAc,QAAQ;AACxB,eAAS,KAAK,QAAQ,IAAI,QAAQ,UAAU;AAAA,IAC9C,OAAO;AAEL,YAAM,iBAAiB,KAAK,MAAM,KAAK,OAAK,QAAQ,GAAG,IAAI,MAAM,CAAC;AAClE,UAAI,gBAAgB;AAClB,iBAAS,KAAK,MAAM,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,UAC/E,WAAW;AAAA,UACX,kBAAkB;AAAA,QACpB,CAAC,CAAC;AAAA,MACJ,WAAW,cAAc,WAAW;AAClC,iBAAS,KAAK,KAAK,IAAI,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,UAC1E,kBAAkB;AAAA,QACpB,CAAC,CAAC;AAAA,MACJ,WAAW,cAAc,QAAQ;AAC/B,iBAAS,KAAK,QAAQ,IAAI,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,UAC7E,WAAW;AAAA,UACX,UAAU;AAAA,UACV,kBAAkB;AAAA,QACpB,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,aAAa;AAAA;AACjB,YAAM,SAAS,KAAK,cAAc;AAClC,UAAI,QAAQ;AACV,eAAO;AAAA,UACL,IAAI,OAAO,QAAQ;AAAA,UACnB,QAAQ,OAAO;AAAA,UACf,SAAS,OAAO;AAAA,QAClB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,YAAY;AAAA;AAChB,aAAO,KAAK,cAAc;AAAA,IAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,WAAW,OAAO;AAAA;AACtB,aAAO,KAAK,MAAM,KAAK;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,UAAU,MAAM;AAAA;AACpB,aAAO,KAAK,cAAc,IAAI;AAAA,IAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,YAAY,MAAM;AAAA;AACtB,aAAO,KAAK,gBAAgB,IAAI;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,YAAY;AAAA;AAChB,aAAO,QAAQ,QAAQ,KAAK,MAAM,MAAM;AAAA,IAC1C;AAAA;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAAA,EACzC;AAAA,EACA,cAAc,OAAO,KAAK,cAAc,GAAG;AACzC,WAAO,CAAC,EAAE,QAAQ,KAAK,gBAAgB,IAAI;AAAA,EAC7C;AAAA,EACA,gBAAgB,OAAO,KAAK,cAAc,GAAG;AAC3C,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,KAAK;AACnB,UAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,WAAO,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,UAAU,IAAI,MAAM;AAAA;AACxB,UAAI,IAAI;AACR,UAAI,KAAK,qBAAqB,KAAK,GAAG,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AAC/F,eAAO;AAAA,MACT;AACA,YAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/C,WAAG,UAAU;AACb,WAAG,SAAS;AAAA,MACd,CAAC;AACD,SAAG,OAAO;AAOV,UAAI,GAAG,QAAQ,GAAG,KAAK,cAAc,SAAS,KAAK,WAAW;AAC5D,cAAM,SAAS,SAAS,cAAc,YAAY;AAClD,YAAI,QAAQ;AACV,gBAAM,gBAAgB,MAAM,OAAO,cAAc;AACjD,cAAI,kBAAkB,OAAO;AAC3B,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,kBAAkB,UAAU;AACrC,mBAAO,KAAK,eAAe,GAAG,KAAK,aAAa,MAAM;AACtD,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,YAAM,KAAK,GAAG,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,GAAG;AAChF,WAAG,cAAc;AAAA,MACnB;AAEA,WAAK,WAAW,KAAK,EAAE;AAGvB,WAAK,SAAS;AACd,aAAO;AAAA,IACT;AAAA;AAAA,EACA,QAAQ,QAAQ,IAAI;AAClB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,gCAAgC,EAAE;AACjD;AAAA,IACF;AACA,QAAI,GAAG,MAAM;AACX,SAAG,KAAK,OAAO,cAAc,OAAO,oBAAoB,OAAO,cAAc,OAAO,aAAa,OAAO,SAAS;AAAA,IACnH;AACA,OAAG,QAAQ,OAAO,YAAY;AAC9B,QAAI,GAAG,KAAK,cAAc,SAAS,KAAK,WAAW;AACjD,YAAM,SAAS,SAAS,cAAc,YAAY;AAClD,UAAI,QAAQ;AACV,cAAM,YAAY,OAAO,cAAc,SAAS,SAAS;AACzD,eAAO,WAAW,SAAS;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,cAAc,IAAI;AACvB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,gCAAgC,EAAE;AACjD;AAAA,IACF;AACA,SAAK,WAAW,SAAS;AACzB,SAAK,UAAU,cAAc,EAAE;AAAA,EACjC;AAAA,EACA,UAAU,cAAc,IAAI;AAC1B,QAAI,GAAG,MAAM;AACX,SAAG,KAAK,OAAO,OAAO,YAAY;AAAA,IACpC;AACA,QAAI,GAAG,UAAU,CAAC,KAAK,WAAW;AAChC,SAAG,OAAO,YAAY;AAAA,IACxB,OAAO;AACL,SAAG,QAAQ,KAAK;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AAGT,QAAI,KAAK,iBAAiB;AACxB,aAAO;AAAA,IACT;AAEA,UAAM,KAAK,KAAK,WAAW,MAAM;AACjC,QAAI,CAAC,IAAI;AACP,aAAO;AAAA,IACT;AACA,SAAK,cAAc,EAAE;AACrB,WAAO;AAAA,EACT;AAAA;AAAA,EAEM,cAAc,IAAI;AAAA;AACtB,UAAI;AAEF,aAAK,iBAAiB,KAAK;AAC3B,aAAK,kBAAkB;AACvB,aAAK,UAAU,EAAE;AACjB,cAAM,cAAc,KAAK,cAAc;AACvC,cAAM,eAAe,KAAK,gBAAgB,IAAI,WAAW;AACzD,YAAI,CAAC,eAAe,CAAC,cAAc;AACjC,gBAAM,IAAI,MAAM,qCAAqC;AAAA,QACvD;AACA,YAAI,gBAAgB,aAAa,UAAU,gBAAgB;AACzD,gBAAM,aAAa,KAAK,KAAK,EAAE;AAAA,QACjC;AACA,aAAK,aAAa,cAAc,aAAa,EAAE;AAE/C,cAAM,sBAAsB,GAAG,8BAA8B,GAAG,8BAA8B,iBAAiB;AAC/G,YAAI,sBAAsB,GAAG,QAAQ,aAAa;AAChD,gBAAM,kBAAkB,GAAG,KAAK,cAAc;AAK9C,cAAI,iBAAiB;AACnB,eAAG,KAAK,mBAAmB,GAAG,KAAK,qBAAqB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa;AAAA,UACnI;AACA,sBAAY,mBAAmB,GAAG,KAAK;AAAA,QACzC;AACA,YAAI;AACJ,YAAI,oBAAoB;AACtB,mBAAS,MAAM,KAAK,WAAW,cAAc,aAAa,EAAE;AAAA,QAC9D,OAAO;AAKL,mBAAS;AAAA,YACP,cAAc;AAAA,YACd,oBAAoB;AAAA,UACtB;AAAA,QACF;AACA,aAAK,QAAQ,QAAQ,EAAE;AACvB,aAAK,gBAAgB,KAAK;AAAA,MAC5B,SAAS,cAAc;AACrB,aAAK,OAAO,cAAc,EAAE;AAAA,MAC9B;AACA,WAAK,kBAAkB;AACvB,WAAK,SAAS;AAAA,IAChB;AAAA;AAAA,EACA,UAAU,IAAI;AACZ,QAAI,IAAI;AACR,QAAI;AACJ,UAAM,cAAc,KAAK,MAAM;AAC/B,KAAC,KAAK,GAAG,UAAU,QAAQ,OAAO,SAAS,KAAK,GAAG,OAAO,CAAC;AAC3D,KAAC,MAAM,KAAK,GAAG,MAAM,cAAc,QAAQ,OAAO,SAAS,KAAK,GAAG,WAAW,KAAK;AACnF,QAAI,GAAG,eAAe,QAAW;AAC/B,aAAO,GAAG,gBAAgB,QAAW,8BAA8B;AACnE,aAAO,GAAG,gBAAgB,QAAW,8BAA8B;AACnE,YAAM,QAAQ,KAAK,MAAM,QAAQ,GAAG,UAAU;AAC9C,UAAI,QAAQ,GAAG;AACb,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC5C;AACA,SAAG,eAAe;AAAA,IACpB;AACA,QAAI,GAAG,gBAAgB,QAAW;AAChC,UAAI,GAAG,cAAc,GAAG;AACtB,WAAG,cAAc,cAAc;AAAA,MACjC;AACA,UAAI,GAAG,cAAc,GAAG;AACtB,WAAG,cAAc,cAAc,GAAG;AAAA,MACpC;AACA,SAAG,4BAA4B,GAAG,cAAc,KAAK,GAAG,cAAc,GAAG,gBAAgB;AAAA,IAC3F;AACA,QAAI,GAAG,aAAa;AAGlB,UAAI,GAAG,cAAc,KAAK,GAAG,cAAc,aAAa;AACtD,WAAG,cAAc;AAAA,MACnB;AACA,SAAG,6BAA6B,GAAG,gBAAgB;AAAA,IACrD;AACA,UAAM,cAAc,GAAG;AACvB,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,WAAO,YAAY,SAAS,GAAG,wBAAwB;AACvD,UAAM,kBAAkB,eAAe,WAAW;AAClD,QAAI,gBAAgB,WAAW,GAAG;AAChC,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AAEA,eAAW,QAAQ,iBAAiB;AAClC,WAAK,WAAW,GAAG,KAAK;AACxB,YAAM,MAAM,KAAK;AACjB,UAAI,OAAO,QAAQ,MAAM;AACvB,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACtD;AACA,UAAI,KAAK,UAAU,sBAAsB;AACvC,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACvD;AAAA,IACF;AACA,OAAG,cAAc;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,IAAI,aAAa;AAE/B,UAAM,cAAc,GAAG;AACvB,QAAI,gBAAgB,QAAW;AAC7B,aAAO,YAAY,YAAY,SAAS,CAAC;AAAA,IAC3C;AAEA,UAAM,cAAc,GAAG;AACvB,QAAI,gBAAgB,QAAW;AAC7B,YAAM,QAAQ,KAAK;AACnB,YAAM,YAAY,cAAc,GAAG;AACnC,eAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,cAAM,OAAO,MAAM,CAAC;AACpB,aAAK,IAAI,eAAe,KAAK,cAAc,SAAS,aAAa;AAC/D,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,cAAc,aAAa,IAAI;AAC1C,QAAI,IAAI,IAAI;AACZ,WAAO,eAAe,cAAc,4CAA4C;AAChF,WAAO,GAAG,SAAS,uBAAuB;AAC1C,WAAO,GAAG,QAAQ,sBAAsB;AAExC,UAAM,OAAO,GAAG;AAChB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,QAAI;AAEJ,QAAI,gBAAgB,UAAa,gBAAgB,QAAW;AAC1D,aAAO,eAAe,GAAG,iCAAiC;AAC1D,aAAO,eAAe,GAAG,iCAAiC;AAC1D,qBAAe,CAAC;AAChB,eAAS,IAAI,aAAa,IAAI,cAAc,aAAa,KAAK;AAC5D,cAAM,OAAO,KAAK,MAAM,CAAC;AACzB,YAAI,SAAS,UAAa,SAAS,gBAAgB,SAAS,aAAa;AACvE,uBAAa,KAAK,IAAI;AAAA,QACxB;AAAA,MACF;AAEA,OAAC,KAAK,KAAK,eAAe,QAAQ,OAAO,SAAS,KAAK,KAAK,YAAY;AAAA,IAC1E;AACA,UAAM,gBAAgB,KAAK,MAAM,WAAW,KAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,YAAY,QAAQ,OAAO,SAAS,KAAK,MAAM,gBAAgB,QAAQ,gBAAgB,SAAS,cAAc;AACrO,WAAO,iBAAiB,GAAG,mCAAmC;AAC9D,QAAI,kBAAkB,GAAG;AACvB,sBAAgB,oHAAoH,MAAM,KAAK,EAAE;AACjJ,YAAM,IAAI,MAAM,+CAA+C;AAAA,IACjE;AAGA,QAAI,aAAa;AAEf,UAAI,cAAc,GAAG;AACrB,iBAAW,QAAQ,aAAa;AAC9B,aAAK,aAAa,MAAM,WAAW;AACnC;AAAA,MACF;AACA,UAAI,GAAG,4BAA4B;AAEjC,SAAC,KAAK,KAAK,eAAe,QAAQ,OAAO,SAAS,KAAK,KAAK,YAAY;AAAA,MAC1E;AAAA,IACF;AAMA,QAAI,gBAAgB,aAAa,SAAS,GAAG;AAC3C,iBAAW,QAAQ,cAAc;AAC/B,kBAAU,KAAK,SAAS,oBAAoB;AAC5C,kBAAU,KAAK,SAAS,mBAAmB;AAC3C,kBAAU,KAAK,SAAS,qBAAqB;AAAA,MAC/C;AAEA,iBAAW,QAAQ,cAAc;AAC/B,aAAK,YAAY,IAAI;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA,EACM,WAAW,cAAc,aAAa,IAAI;AAAA;AAG9C,YAAM,OAAO,GAAG;AAChB,YAAM,mBAAmB,KAAK,oBAAoB,SAAO;AAQvD,YAAI,QAAQ,UAAa,CAAC,KAAK,8BAA8B;AAC3D,eAAK,+BAA+B;AACpC,cAAI,SAAS,MAAM;AACjB,iBAAK,+BAA+B;AAAA,UACtC,GAAG;AAAA,YACD,iBAAiB;AAAA,UACnB,CAAC;AAOD,cAAI,YAAY,GAAG,GAAG,CAAC;AAAA,QACzB,OAAO;AACL,eAAK,QAAQ;AAAA,QACf;AAAA,MACF,IAAI;AACJ,YAAM,OAAO,WAAW,IAAI;AAC5B,YAAM,aAAa,aAAa;AAEhC,YAAM,YAAY,eAAe,YAAY;AAC7C,YAAM,gBAAgB,OAAO,OAAO,OAAO,OAAO;AAAA,QAChD;AAAA,QACA,YAAY,KAAK,cAAc,YAAY;AAAA,QAC3C,QAAQ,KAAK;AAAA,QACb;AAAA,QACA,UAAU,KAAK,YAAY,OAAO,WAAW,YAAY,IAAI;AAAA,QAC7D;AAAA,QACA;AAAA,MACF,GAAG,IAAI,GAAG;AAAA,QACR,kBAAkB,KAAK,oBAAoB,KAAK,aAAa,OAAO,IAAI,cAAc;AAAA,MACxF,CAAC;AACD,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,MAAM,WAAW,aAAa;AAClC,aAAO,KAAK,iBAAiB,cAAc,cAAc,aAAa,IAAI;AAAA,IAC5E;AAAA;AAAA,EACA,iBAAiB,cAAc,cAAc,aAAa,MAAM;AAK9D,UAAM,aAAa,eAAe,eAAe;AACjD,QAAI,YAAY;AACd,WAAK,qBAAqB,UAAU;AAAA,IACtC;AACA,WAAO;AAAA,MACL;AAAA,MACA,oBAAoB;AAAA,MACpB;AAAA,MACA;AAAA,MACA,WAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,MAAM,OAAO;AACxB,UAAM,QAAQ,KAAK;AACnB,UAAM,gBAAgB,MAAM,QAAQ,IAAI;AACxC,QAAI,gBAAgB,IAAI;AACtB,aAAO,KAAK,QAAQ,MAAM,6BAA6B;AAEvD,YAAM,OAAO,eAAe,CAAC;AAE7B,YAAM,OAAO,OAAO,GAAG,IAAI;AAAA,IAC7B,OAAO;AACL,aAAO,CAAC,KAAK,KAAK,aAAa;AAG/B,WAAK,MAAM;AACX,YAAM,OAAO,OAAO,GAAG,IAAI;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,MAAM;AACf,WAAO,KAAK,UAAU,uBAAuB,KAAK,UAAU,sBAAsB,0CAA0C;AAC5H,UAAM,QAAQ,KAAK;AACnB,UAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,WAAO,QAAQ,IAAI,gCAAgC;AACnD,QAAI,SAAS,GAAG;AACd,YAAM,OAAO,OAAO,CAAC;AAAA,IACvB;AAAA,EACF;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,SAAS;AACd,SAAK,WAAW,IAAI;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,YAAY;AAI/B,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AACA,UAAM,QAAQ,KAAK;AACnB,UAAM,kBAAkB,MAAM,QAAQ,UAAU;AAChD,aAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,YAAM,OAAO,MAAM,CAAC;AASpB,YAAM,UAAU,KAAK;AACrB,UAAI,SAAS;AACX,YAAI,IAAI,iBAAiB;AAGvB,oBAAU,SAAS,qBAAqB;AACxC,eAAK,YAAY,IAAI;AAAA,QACvB,WAAW,IAAI,iBAAiB;AAG9B,wBAAc,SAAS,IAAI;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,WAAO,CAAC,KAAK,gCAAgC,CAAC,CAAC,KAAK,gBAAgB,CAAC,KAAK,mBAAmB,KAAK,WAAW,WAAW,KAAK,KAAK,cAAc;AAAA,EAClJ;AAAA,EACA,UAAU;AACR,SAAK,+BAA+B;AACpC,SAAK,IAAI;AAAA,MACP,WAAW;AAAA,MACX,mBAAmB;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,OAAO,WAAW;AAChB,QAAI,KAAK,OAAO;AACd,WAAK,MAAM,aAAa,SAAS;AAAA,IACnC;AAAA,EACF;AAAA,EACA,MAAM,gBAAgB,WAAW,KAAK;AACpC,QAAI,KAAK,OAAO;AACd,WAAK,MAAM,SAAS,MAAM;AACxB,aAAK,+BAA+B;AAAA,MACtC,GAAG;AAAA,QACD,iBAAiB;AAAA,MACnB,CAAC;AAED,UAAI,eAAe,iBAAiB,QAAS;AAS7C,UAAI,CAAC,gBAAgB;AACnB,aAAK,MAAM,OAAO,gCAAgC;AAClD,wBAAgB,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS,EAAE,CAAC;AAAA,MAC5F,OAAO;AACL,wBAAgB,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS,EAAE,CAAC;AAAA,MAC5F;AACA,WAAK,MAAM,YAAY,iBAAiB,IAAI,GAAG,cAAc,GAAG;AAAA,IAClE,OAAO;AACL,WAAK,+BAA+B;AAAA,IACtC;AAAA,EACF;AAAA,EACA,SAAS;AACP,WAAO,EAAE,QAAQ;AAAA,MACf,KAAK;AAAA,IACP,CAAC;AAAA,EACH;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,gBAAgB,CAAC,qBAAqB;AAAA,MACtC,QAAQ,CAAC,aAAa;AAAA,IACxB;AAAA,EACF;AACF;AACA,IAAI,QAAQ;AACZ,IAAM,UAAU,CAAC,IAAI,iBAAiB,WAAW,gBAAgB,oBAAoB;AACnF,QAAM,MAAM,GAAG,QAAQ,SAAS;AAChC,MAAI,KAAK;AACP,QAAI,oBAAoB,WAAW;AACjC,UAAI,cAAc,QAAW;AAC3B,eAAO,IAAI,KAAK,WAAW,gBAAgB;AAAA,UACzC,YAAY;AAAA,UACZ,kBAAkB;AAAA,QACpB,CAAC;AAAA,MACH;AAAA,IACF,WAAW,oBAAoB,QAAQ;AACrC,UAAI,cAAc,QAAW;AAC3B,eAAO,IAAI,QAAQ,WAAW,gBAAgB;AAAA,UAC5C,YAAY;AAAA,UACZ,kBAAkB;AAAA,QACpB,CAAC;AAAA,MACH;AAAA,IACF,WAAW,oBAAoB,QAAQ;AACrC,aAAO,IAAI,IAAI;AAAA,QACb,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO,QAAQ,QAAQ,KAAK;AAC9B;AACA,IAAM,UAAU,MAAM;AAAA,EACpB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,UAAU,MAAM;AACnB,aAAO,QAAQ,KAAK,IAAI,KAAK,iBAAiB,KAAK,WAAW,KAAK,gBAAgB,KAAK,eAAe;AAAA,IACzG;AACA,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,SAAS;AACP,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;", "names": [], "x_google_ignoreList": [0]}
import{C as d,Cb as O,E as o,F as a,G as s,Ja as b,L as c,S as p,Va as f,W as u,da as m,na as h,x as r,y as l,z as g}from"./chunk-PBKSAHK2.js";import"./chunk-MBKQLJTW.js";import"./chunk-F3654E4N.js";import"./chunk-FHR3DP7J.js";import"./chunk-A4FGPDGZ.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-AUB5HKS7.js";import"./chunk-RS5W3JWO.js";import"./chunk-LOLLZ3RS.js";import"./chunk-XZOVPSKP.js";import"./chunk-7LH2AG5T.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-SPZFNIGG.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-KY4M3ZA2.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-4EI7TLDT.js";import"./chunk-FED6QSGK.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-2R6CW7ES.js";var v=()=>["OK"],x=(()=>{class t{constructor(n){this.router=n,this.isOnline=!1}ngOnInit(){console.log("LoadingPage ngOnInit");let n=window;n.appDebug&&n.appDebug("LoadingPage ngOnInit"),this.checkInternetConnection()}checkInternetConnection(){this.isOnline=navigator.onLine,console.log("LoadingPage checkInternetConnection, isOnline:",this.isOnline);let n=window;n.appDebug&&n.appDebug("LoadingPage checkInternetConnection, isOnline: "+this.isOnline);let e=localStorage.getItem("token"),i=localStorage.getItem("onboardingComplete");console.log("Auth status - Token:",!!e,"Onboarding complete:",i==="true","Online:",this.isOnline),setTimeout(()=>{e&&i==="true"?(console.log("User is authenticated and onboarding complete - navigating to tabs/home"),n.appDebug&&n.appDebug("LoadingPage navigating to tabs/home (authenticated & onboarded)"),this.router.navigate(["/tabs/home"])):e?(console.log("User is authenticated but onboarding incomplete - navigating to welcome"),n.appDebug&&n.appDebug("LoadingPage navigating to welcome (authenticated but not onboarded)"),this.router.navigate(["/welcome"])):(console.log("User is not authenticated - navigating to login"),n.appDebug&&n.appDebug("LoadingPage navigating to login (not authenticated)"),this.router.navigate(["/login"]))},1e3)}ionViewWillEnter(){window.addEventListener("online",this.updateOnlineStatus.bind(this)),window.addEventListener("offline",this.updateOnlineStatus.bind(this))}ionViewWillLeave(){window.removeEventListener("online",this.updateOnlineStatus.bind(this)),window.removeEventListener("offline",this.updateOnlineStatus.bind(this))}updateOnlineStatus(){this.isOnline=navigator.onLine;let n=window;n.appDebug&&n.appDebug("LoadingPage updateOnlineStatus, isOnline: "+this.isOnline),this.checkInternetConnection()}static{this.\u0275fac=function(e){return new(e||t)(l(m))}}static{this.\u0275cmp=g({type:t,selectors:[["app-loading"]],decls:6,vars:3,consts:[[1,"ion-padding"],[1,"loading-container"],[1,"loader"],["header","No Internet Connection","message","Please check your internet connection and try again.",3,"isOpen","buttons"]],template:function(e,i){e&1&&(o(0,"ion-content",0)(1,"div",1),s(2,"div",2),o(3,"h2"),c(4,"Loading..."),a(),s(5,"ion-alert",3),a()()),e&2&&(r(5),d("isOpen",!i.isOnline)("buttons",p(2,v)))},dependencies:[O,b,f,u,h],styles:[".loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;height:100%;text-align:center}.loader[_ngcontent-%COMP%]{width:48px;height:48px;border:5px solid #FFF;border-bottom-color:#3880ff;border-radius:50%;display:inline-block;box-sizing:border-box;animation:_ngcontent-%COMP%_rotation 1s linear infinite;margin-bottom:20px}@keyframes _ngcontent-%COMP%_rotation{0%{transform:rotate(0)}to{transform:rotate(360deg)}}h2[_ngcontent-%COMP%]{color:#3880ff;font-size:24px;margin:0}"]})}}return t})();export{x as LoadingPage};

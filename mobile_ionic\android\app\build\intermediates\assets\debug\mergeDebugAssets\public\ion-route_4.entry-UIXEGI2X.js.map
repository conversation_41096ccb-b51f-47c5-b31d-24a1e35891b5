{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-route_4.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, f as getElement, h, e as Host } from './index-527b9e34.js';\nimport { c as componentOnReady, p as debounce } from './helpers-d94bc8ad.js';\nimport { d as printIonError, p as printIonWarning } from './index-cfd9c1f2.js';\nimport { o as openURL, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nconst Route = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRouteDataChanged = createEvent(this, \"ionRouteDataChanged\", 7);\n    this.url = '';\n    this.component = undefined;\n    this.componentProps = undefined;\n    this.beforeLeave = undefined;\n    this.beforeEnter = undefined;\n  }\n  onUpdate(newValue) {\n    this.ionRouteDataChanged.emit(newValue);\n  }\n  onComponentProps(newValue, oldValue) {\n    if (newValue === oldValue) {\n      return;\n    }\n    const keys1 = newValue ? Object.keys(newValue) : [];\n    const keys2 = oldValue ? Object.keys(oldValue) : [];\n    if (keys1.length !== keys2.length) {\n      this.onUpdate(newValue);\n      return;\n    }\n    for (const key of keys1) {\n      if (newValue[key] !== oldValue[key]) {\n        this.onUpdate(newValue);\n        return;\n      }\n    }\n  }\n  connectedCallback() {\n    this.ionRouteDataChanged.emit();\n  }\n  static get watchers() {\n    return {\n      \"url\": [\"onUpdate\"],\n      \"component\": [\"onUpdate\"],\n      \"componentProps\": [\"onComponentProps\"]\n    };\n  }\n};\nconst RouteRedirect = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRouteRedirectChanged = createEvent(this, \"ionRouteRedirectChanged\", 7);\n    this.from = undefined;\n    this.to = undefined;\n  }\n  propDidChange() {\n    this.ionRouteRedirectChanged.emit();\n  }\n  connectedCallback() {\n    this.ionRouteRedirectChanged.emit();\n  }\n  static get watchers() {\n    return {\n      \"from\": [\"propDidChange\"],\n      \"to\": [\"propDidChange\"]\n    };\n  }\n};\nconst ROUTER_INTENT_NONE = 'root';\nconst ROUTER_INTENT_FORWARD = 'forward';\nconst ROUTER_INTENT_BACK = 'back';\n\n/** Join the non empty segments with \"/\". */\nconst generatePath = segments => {\n  const path = segments.filter(s => s.length > 0).join('/');\n  return '/' + path;\n};\nconst generateUrl = (segments, useHash, queryString) => {\n  let url = generatePath(segments);\n  if (useHash) {\n    url = '#' + url;\n  }\n  if (queryString !== undefined) {\n    url += '?' + queryString;\n  }\n  return url;\n};\nconst writeSegments = (history, root, useHash, segments, direction, state, queryString) => {\n  const url = generateUrl([...parsePath(root).segments, ...segments], useHash, queryString);\n  if (direction === ROUTER_INTENT_FORWARD) {\n    history.pushState(state, '', url);\n  } else {\n    history.replaceState(state, '', url);\n  }\n};\n/**\n * Transforms a chain to a list of segments.\n *\n * Notes:\n * - parameter segments of the form :param are replaced with their value,\n * - null is returned when a value is missing for any parameter segment.\n */\nconst chainToSegments = chain => {\n  const segments = [];\n  for (const route of chain) {\n    for (const segment of route.segments) {\n      if (segment[0] === ':') {\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        const param = route.params && route.params[segment.slice(1)];\n        if (!param) {\n          return null;\n        }\n        segments.push(param);\n      } else if (segment !== '') {\n        segments.push(segment);\n      }\n    }\n  }\n  return segments;\n};\n/**\n * Removes the prefix segments from the path segments.\n *\n * Return:\n * - null when the path segments do not start with the passed prefix,\n * - the path segments after the prefix otherwise.\n */\nconst removePrefix = (prefix, segments) => {\n  if (prefix.length > segments.length) {\n    return null;\n  }\n  if (prefix.length <= 1 && prefix[0] === '') {\n    return segments;\n  }\n  for (let i = 0; i < prefix.length; i++) {\n    if (prefix[i] !== segments[i]) {\n      return null;\n    }\n  }\n  if (segments.length === prefix.length) {\n    return [''];\n  }\n  return segments.slice(prefix.length);\n};\nconst readSegments = (loc, root, useHash) => {\n  const prefix = parsePath(root).segments;\n  const pathname = useHash ? loc.hash.slice(1) : loc.pathname;\n  const segments = parsePath(pathname).segments;\n  return removePrefix(prefix, segments);\n};\n/**\n * Parses the path to:\n * - segments an array of '/' separated parts,\n * - queryString (undefined when no query string).\n */\nconst parsePath = path => {\n  let segments = [''];\n  let queryString;\n  if (path != null) {\n    const qsStart = path.indexOf('?');\n    if (qsStart > -1) {\n      queryString = path.substring(qsStart + 1);\n      path = path.substring(0, qsStart);\n    }\n    segments = path.split('/').map(s => s.trim()).filter(s => s.length > 0);\n    if (segments.length === 0) {\n      segments = [''];\n    }\n  }\n  return {\n    segments,\n    queryString\n  };\n};\nconst printRoutes = routes => {\n  console.group(`[ion-core] ROUTES[${routes.length}]`);\n  for (const chain of routes) {\n    const segments = [];\n    chain.forEach(r => segments.push(...r.segments));\n    const ids = chain.map(r => r.id);\n    console.debug(`%c ${generatePath(segments)}`, 'font-weight: bold; padding-left: 20px', '=>\\t', `(${ids.join(', ')})`);\n  }\n  console.groupEnd();\n};\nconst printRedirects = redirects => {\n  console.group(`[ion-core] REDIRECTS[${redirects.length}]`);\n  for (const redirect of redirects) {\n    if (redirect.to) {\n      console.debug('FROM: ', `$c ${generatePath(redirect.from)}`, 'font-weight: bold', ' TO: ', `$c ${generatePath(redirect.to.segments)}`, 'font-weight: bold');\n    }\n  }\n  console.groupEnd();\n};\n\n/**\n * Activates the passed route chain.\n *\n * There must be exactly one outlet per route entry in the chain.\n *\n * The methods calls setRouteId on each of the outlet with the corresponding route entry in the chain.\n * setRouteId will create or select the view in the outlet.\n */\nconst writeNavState = async (root, chain, direction, index, changed = false, animation) => {\n  try {\n    // find next navigation outlet in the DOM\n    const outlet = searchNavNode(root);\n    // make sure we can continue interacting the DOM, otherwise abort\n    if (index >= chain.length || !outlet) {\n      return changed;\n    }\n    await new Promise(resolve => componentOnReady(outlet, resolve));\n    const route = chain[index];\n    const result = await outlet.setRouteId(route.id, route.params, direction, animation);\n    // if the outlet changed the page, reset navigation to neutral (no direction)\n    // this means nested outlets will not animate\n    if (result.changed) {\n      direction = ROUTER_INTENT_NONE;\n      changed = true;\n    }\n    // recursively set nested outlets\n    changed = await writeNavState(result.element, chain, direction, index + 1, changed, animation);\n    // once all nested outlets are visible let's make the parent visible too,\n    // using markVisible prevents flickering\n    if (result.markVisible) {\n      await result.markVisible();\n    }\n    return changed;\n  } catch (e) {\n    printIonError('[ion-router] - Exception in writeNavState:', e);\n    return false;\n  }\n};\n/**\n * Recursively walks the outlet in the DOM.\n *\n * The function returns a list of RouteID corresponding to each of the outlet and the last outlet without a RouteID.\n */\nconst readNavState = async root => {\n  const ids = [];\n  let outlet;\n  let node = root;\n  // eslint-disable-next-line no-cond-assign\n  while (outlet = searchNavNode(node)) {\n    const id = await outlet.getRouteId();\n    if (id) {\n      node = id.element;\n      id.element = undefined;\n      ids.push(id);\n    } else {\n      break;\n    }\n  }\n  return {\n    ids,\n    outlet\n  };\n};\nconst waitUntilNavNode = () => {\n  if (searchNavNode(document.body)) {\n    return Promise.resolve();\n  }\n  return new Promise(resolve => {\n    window.addEventListener('ionNavWillLoad', () => resolve(), {\n      once: true\n    });\n  });\n};\n/** Selector for all the outlets supported by the router. */\nconst OUTLET_SELECTOR = ':not([no-router]) ion-nav, :not([no-router]) ion-tabs, :not([no-router]) ion-router-outlet';\nconst searchNavNode = root => {\n  if (!root) {\n    return undefined;\n  }\n  if (root.matches(OUTLET_SELECTOR)) {\n    return root;\n  }\n  const outlet = root.querySelector(OUTLET_SELECTOR);\n  return outlet !== null && outlet !== void 0 ? outlet : undefined;\n};\n\n/**\n * Returns whether the given redirect matches the given path segments.\n *\n * A redirect matches when the segments of the path and redirect.from are equal.\n * Note that segments are only checked until redirect.from contains a '*' which matches any path segment.\n * The path ['some', 'path', 'to', 'page'] matches both ['some', 'path', 'to', 'page'] and ['some', 'path', '*'].\n */\nconst matchesRedirect = (segments, redirect) => {\n  const {\n    from,\n    to\n  } = redirect;\n  if (to === undefined) {\n    return false;\n  }\n  if (from.length > segments.length) {\n    return false;\n  }\n  for (let i = 0; i < from.length; i++) {\n    const expected = from[i];\n    if (expected === '*') {\n      return true;\n    }\n    if (expected !== segments[i]) {\n      return false;\n    }\n  }\n  return from.length === segments.length;\n};\n/** Returns the first redirect matching the path segments or undefined when no match found. */\nconst findRouteRedirect = (segments, redirects) => {\n  return redirects.find(redirect => matchesRedirect(segments, redirect));\n};\nconst matchesIDs = (ids, chain) => {\n  const len = Math.min(ids.length, chain.length);\n  let score = 0;\n  for (let i = 0; i < len; i++) {\n    const routeId = ids[i];\n    const routeChain = chain[i];\n    // Skip results where the route id does not match the chain at the same index\n    if (routeId.id.toLowerCase() !== routeChain.id) {\n      break;\n    }\n    if (routeId.params) {\n      const routeIdParams = Object.keys(routeId.params);\n      // Only compare routes with the chain that have the same number of parameters.\n      if (routeIdParams.length === routeChain.segments.length) {\n        // Maps the route's params into a path based on the path variable names,\n        // to compare against the route chain format.\n        //\n        // Before:\n        // ```ts\n        // {\n        //  params: {\n        //    s1: 'a',\n        //    s2: 'b'\n        //  }\n        // }\n        // ```\n        //\n        // After:\n        // ```ts\n        // [':s1',':s2']\n        // ```\n        //\n        const pathWithParams = routeIdParams.map(key => `:${key}`);\n        for (let j = 0; j < pathWithParams.length; j++) {\n          // Skip results where the path variable is not a match\n          if (pathWithParams[j].toLowerCase() !== routeChain.segments[j]) {\n            break;\n          }\n          // Weight path matches for the same index higher.\n          score++;\n        }\n      }\n    }\n    // Weight id matches\n    score++;\n  }\n  return score;\n};\n/**\n * Matches the segments against the chain.\n *\n * Returns:\n * - null when there is no match,\n * - a chain with the params properties updated with the parameter segments on match.\n */\nconst matchesSegments = (segments, chain) => {\n  const inputSegments = new RouterSegments(segments);\n  let matchesDefault = false;\n  let allparams;\n  for (let i = 0; i < chain.length; i++) {\n    const chainSegments = chain[i].segments;\n    if (chainSegments[0] === '') {\n      matchesDefault = true;\n    } else {\n      for (const segment of chainSegments) {\n        const data = inputSegments.next();\n        // data param\n        if (segment[0] === ':') {\n          if (data === '') {\n            return null;\n          }\n          allparams = allparams || [];\n          const params = allparams[i] || (allparams[i] = {});\n          params[segment.slice(1)] = data;\n        } else if (data !== segment) {\n          return null;\n        }\n      }\n      matchesDefault = false;\n    }\n  }\n  const matches = matchesDefault ? matchesDefault === (inputSegments.next() === '') : true;\n  if (!matches) {\n    return null;\n  }\n  if (allparams) {\n    return chain.map((route, i) => ({\n      id: route.id,\n      segments: route.segments,\n      params: mergeParams(route.params, allparams[i]),\n      beforeEnter: route.beforeEnter,\n      beforeLeave: route.beforeLeave\n    }));\n  }\n  return chain;\n};\n/**\n * Merges the route parameter objects.\n * Returns undefined when both parameters are undefined.\n */\nconst mergeParams = (a, b) => {\n  return a || b ? Object.assign(Object.assign({}, a), b) : undefined;\n};\n/**\n * Finds the best match for the ids in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the RouteIDs.\n * That is they contain both the componentProps of the <ion-route> and the parameter segment.\n */\nconst findChainForIDs = (ids, chains) => {\n  let match = null;\n  let maxMatches = 0;\n  for (const chain of chains) {\n    const score = matchesIDs(ids, chain);\n    if (score > maxMatches) {\n      match = chain;\n      maxMatches = score;\n    }\n  }\n  if (match) {\n    return match.map((route, i) => {\n      var _a;\n      return {\n        id: route.id,\n        segments: route.segments,\n        params: mergeParams(route.params, (_a = ids[i]) === null || _a === void 0 ? void 0 : _a.params)\n      };\n    });\n  }\n  return null;\n};\n/**\n * Finds the best match for the segments in the chains.\n *\n * Returns the best match or null when no match is found.\n * When a chain is returned the parameters are updated from the segments.\n * That is they contain both the componentProps of the <ion-route> and the parameter segments.\n */\nconst findChainForSegments = (segments, chains) => {\n  let match = null;\n  let bestScore = 0;\n  for (const chain of chains) {\n    const matchedChain = matchesSegments(segments, chain);\n    if (matchedChain !== null) {\n      const score = computePriority(matchedChain);\n      if (score > bestScore) {\n        bestScore = score;\n        match = matchedChain;\n      }\n    }\n  }\n  return match;\n};\n/**\n * Computes the priority of a chain.\n *\n * Parameter segments are given a lower priority over fixed segments.\n *\n * Considering the following 2 chains matching the path /path/to/page:\n * - /path/to/:where\n * - /path/to/page\n *\n * The second one will be given a higher priority because \"page\" is a fixed segment (vs \":where\", a parameter segment).\n */\nconst computePriority = chain => {\n  let score = 1;\n  let level = 1;\n  for (const route of chain) {\n    for (const segment of route.segments) {\n      if (segment[0] === ':') {\n        score += Math.pow(1, level);\n      } else if (segment !== '') {\n        score += Math.pow(2, level);\n      }\n      level++;\n    }\n  }\n  return score;\n};\nclass RouterSegments {\n  constructor(segments) {\n    this.segments = segments.slice();\n  }\n  next() {\n    if (this.segments.length > 0) {\n      return this.segments.shift();\n    }\n    return '';\n  }\n}\nconst readProp = (el, prop) => {\n  if (prop in el) {\n    return el[prop];\n  }\n  if (el.hasAttribute(prop)) {\n    return el.getAttribute(prop);\n  }\n  return null;\n};\n/**\n * Extracts the redirects (that is <ion-route-redirect> elements inside the root).\n *\n * The redirects are returned as a list of RouteRedirect.\n */\nconst readRedirects = root => {\n  return Array.from(root.children).filter(el => el.tagName === 'ION-ROUTE-REDIRECT').map(el => {\n    const to = readProp(el, 'to');\n    return {\n      from: parsePath(readProp(el, 'from')).segments,\n      to: to == null ? undefined : parsePath(to)\n    };\n  });\n};\n/**\n * Extracts all the routes (that is <ion-route> elements inside the root).\n *\n * The routes are returned as a list of chains - the flattened tree.\n */\nconst readRoutes = root => {\n  return flattenRouterTree(readRouteNodes(root));\n};\n/**\n * Reads the route nodes as a tree modeled after the DOM tree of <ion-route> elements.\n *\n * Note: routes without a component are ignored together with their children.\n */\nconst readRouteNodes = node => {\n  return Array.from(node.children).filter(el => el.tagName === 'ION-ROUTE' && el.component).map(el => {\n    const component = readProp(el, 'component');\n    return {\n      segments: parsePath(readProp(el, 'url')).segments,\n      id: component.toLowerCase(),\n      params: el.componentProps,\n      beforeLeave: el.beforeLeave,\n      beforeEnter: el.beforeEnter,\n      children: readRouteNodes(el)\n    };\n  });\n};\n/**\n * Flattens a RouterTree in a list of chains.\n *\n * Each chain represents a path from the root node to a terminal node.\n */\nconst flattenRouterTree = nodes => {\n  const chains = [];\n  for (const node of nodes) {\n    flattenNode([], chains, node);\n  }\n  return chains;\n};\n/** Flattens a route node recursively and push each branch to the chains list. */\nconst flattenNode = (chain, chains, node) => {\n  chain = [...chain, {\n    id: node.id,\n    segments: node.segments,\n    params: node.params,\n    beforeLeave: node.beforeLeave,\n    beforeEnter: node.beforeEnter\n  }];\n  if (node.children.length === 0) {\n    chains.push(chain);\n    return;\n  }\n  for (const child of node.children) {\n    flattenNode(chain, chains, child);\n  }\n};\nconst Router = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionRouteWillChange = createEvent(this, \"ionRouteWillChange\", 7);\n    this.ionRouteDidChange = createEvent(this, \"ionRouteDidChange\", 7);\n    this.previousPath = null;\n    this.busy = false;\n    this.state = 0;\n    this.lastState = 0;\n    this.root = '/';\n    this.useHash = true;\n  }\n  async componentWillLoad() {\n    await waitUntilNavNode();\n    const canProceed = await this.runGuards(this.getSegments());\n    if (canProceed !== true) {\n      if (typeof canProceed === 'object') {\n        const {\n          redirect\n        } = canProceed;\n        const path = parsePath(redirect);\n        this.setSegments(path.segments, ROUTER_INTENT_NONE, path.queryString);\n        await this.writeNavStateRoot(path.segments, ROUTER_INTENT_NONE);\n      }\n    } else {\n      await this.onRoutesChanged();\n    }\n  }\n  componentDidLoad() {\n    window.addEventListener('ionRouteRedirectChanged', debounce(this.onRedirectChanged.bind(this), 10));\n    window.addEventListener('ionRouteDataChanged', debounce(this.onRoutesChanged.bind(this), 100));\n  }\n  async onPopState() {\n    const direction = this.historyDirection();\n    let segments = this.getSegments();\n    const canProceed = await this.runGuards(segments);\n    if (canProceed !== true) {\n      if (typeof canProceed === 'object') {\n        segments = parsePath(canProceed.redirect).segments;\n      } else {\n        return false;\n      }\n    }\n    return this.writeNavStateRoot(segments, direction);\n  }\n  onBackButton(ev) {\n    ev.detail.register(0, processNextHandler => {\n      this.back();\n      processNextHandler();\n    });\n  }\n  /** @internal */\n  async canTransition() {\n    const canProceed = await this.runGuards();\n    if (canProceed !== true) {\n      if (typeof canProceed === 'object') {\n        return canProceed.redirect;\n      } else {\n        return false;\n      }\n    }\n    return true;\n  }\n  /**\n   * Navigate to the specified path.\n   *\n   * @param path The path to navigate to.\n   * @param direction The direction of the animation. Defaults to `\"forward\"`.\n   */\n  async push(path, direction = 'forward', animation) {\n    var _a;\n    if (path.startsWith('.')) {\n      const currentPath = (_a = this.previousPath) !== null && _a !== void 0 ? _a : '/';\n      // Convert currentPath to an URL by pre-pending a protocol and a host to resolve the relative path.\n      const url = new URL(path, `https://host/${currentPath}`);\n      path = url.pathname + url.search;\n    }\n    let parsedPath = parsePath(path);\n    const canProceed = await this.runGuards(parsedPath.segments);\n    if (canProceed !== true) {\n      if (typeof canProceed === 'object') {\n        parsedPath = parsePath(canProceed.redirect);\n      } else {\n        return false;\n      }\n    }\n    this.setSegments(parsedPath.segments, direction, parsedPath.queryString);\n    return this.writeNavStateRoot(parsedPath.segments, direction, animation);\n  }\n  /** Go back to previous page in the window.history. */\n  back() {\n    window.history.back();\n    return Promise.resolve(this.waitPromise);\n  }\n  /** @internal */\n  async printDebug() {\n    printRoutes(readRoutes(this.el));\n    printRedirects(readRedirects(this.el));\n  }\n  /** @internal */\n  async navChanged(direction) {\n    if (this.busy) {\n      printIonWarning('[ion-router] - Router is busy, navChanged was cancelled.');\n      return false;\n    }\n    const {\n      ids,\n      outlet\n    } = await readNavState(window.document.body);\n    const routes = readRoutes(this.el);\n    const chain = findChainForIDs(ids, routes);\n    if (!chain) {\n      printIonWarning('[ion-router] - No matching URL for', ids.map(i => i.id));\n      return false;\n    }\n    const segments = chainToSegments(chain);\n    if (!segments) {\n      printIonWarning('[ion-router] - Router could not match path because some required param is missing.');\n      return false;\n    }\n    this.setSegments(segments, direction);\n    await this.safeWriteNavState(outlet, chain, ROUTER_INTENT_NONE, segments, null, ids.length);\n    return true;\n  }\n  /** This handler gets called when a `ion-route-redirect` component is added to the DOM or if the from or to property of such node changes. */\n  onRedirectChanged() {\n    const segments = this.getSegments();\n    if (segments && findRouteRedirect(segments, readRedirects(this.el))) {\n      this.writeNavStateRoot(segments, ROUTER_INTENT_NONE);\n    }\n  }\n  /** This handler gets called when a `ion-route` component is added to the DOM or if the from or to property of such node changes. */\n  onRoutesChanged() {\n    return this.writeNavStateRoot(this.getSegments(), ROUTER_INTENT_NONE);\n  }\n  historyDirection() {\n    var _a;\n    const win = window;\n    if (win.history.state === null) {\n      this.state++;\n      win.history.replaceState(this.state, win.document.title, (_a = win.document.location) === null || _a === void 0 ? void 0 : _a.href);\n    }\n    const state = win.history.state;\n    const lastState = this.lastState;\n    this.lastState = state;\n    if (state > lastState || state >= lastState && lastState > 0) {\n      return ROUTER_INTENT_FORWARD;\n    }\n    if (state < lastState) {\n      return ROUTER_INTENT_BACK;\n    }\n    return ROUTER_INTENT_NONE;\n  }\n  async writeNavStateRoot(segments, direction, animation) {\n    if (!segments) {\n      printIonError('[ion-router] - URL is not part of the routing set.');\n      return false;\n    }\n    // lookup redirect rule\n    const redirects = readRedirects(this.el);\n    const redirect = findRouteRedirect(segments, redirects);\n    let redirectFrom = null;\n    if (redirect) {\n      const {\n        segments: toSegments,\n        queryString\n      } = redirect.to;\n      this.setSegments(toSegments, direction, queryString);\n      redirectFrom = redirect.from;\n      segments = toSegments;\n    }\n    // lookup route chain\n    const routes = readRoutes(this.el);\n    const chain = findChainForSegments(segments, routes);\n    if (!chain) {\n      printIonError('[ion-router] - The path does not match any route.');\n      return false;\n    }\n    // write DOM give\n    return this.safeWriteNavState(document.body, chain, direction, segments, redirectFrom, 0, animation);\n  }\n  async safeWriteNavState(node, chain, direction, segments, redirectFrom, index = 0, animation) {\n    const unlock = await this.lock();\n    let changed = false;\n    try {\n      changed = await this.writeNavState(node, chain, direction, segments, redirectFrom, index, animation);\n    } catch (e) {\n      printIonError('[ion-router] - Exception in safeWriteNavState:', e);\n    }\n    unlock();\n    return changed;\n  }\n  async lock() {\n    const p = this.waitPromise;\n    let resolve;\n    this.waitPromise = new Promise(r => resolve = r);\n    if (p !== undefined) {\n      await p;\n    }\n    return resolve;\n  }\n  /**\n   * Executes the beforeLeave hook of the source route and the beforeEnter hook of the target route if they exist.\n   *\n   * When the beforeLeave hook does not return true (to allow navigating) then that value is returned early and the beforeEnter is executed.\n   * Otherwise the beforeEnterHook hook of the target route is executed.\n   */\n  async runGuards(to = this.getSegments(), from) {\n    if (from === undefined) {\n      from = parsePath(this.previousPath).segments;\n    }\n    if (!to || !from) {\n      return true;\n    }\n    const routes = readRoutes(this.el);\n    const fromChain = findChainForSegments(from, routes);\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    const beforeLeaveHook = fromChain && fromChain[fromChain.length - 1].beforeLeave;\n    const canLeave = beforeLeaveHook ? await beforeLeaveHook() : true;\n    if (canLeave === false || typeof canLeave === 'object') {\n      return canLeave;\n    }\n    const toChain = findChainForSegments(to, routes);\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    const beforeEnterHook = toChain && toChain[toChain.length - 1].beforeEnter;\n    return beforeEnterHook ? beforeEnterHook() : true;\n  }\n  async writeNavState(node, chain, direction, segments, redirectFrom, index = 0, animation) {\n    if (this.busy) {\n      printIonWarning('[ion-router] - Router is busy, transition was cancelled.');\n      return false;\n    }\n    this.busy = true;\n    // generate route event and emit will change\n    const routeEvent = this.routeChangeEvent(segments, redirectFrom);\n    if (routeEvent) {\n      this.ionRouteWillChange.emit(routeEvent);\n    }\n    const changed = await writeNavState(node, chain, direction, index, false, animation);\n    this.busy = false;\n    // emit did change\n    if (routeEvent) {\n      this.ionRouteDidChange.emit(routeEvent);\n    }\n    return changed;\n  }\n  setSegments(segments, direction, queryString) {\n    this.state++;\n    writeSegments(window.history, this.root, this.useHash, segments, direction, this.state, queryString);\n  }\n  getSegments() {\n    return readSegments(window.location, this.root, this.useHash);\n  }\n  routeChangeEvent(toSegments, redirectFromSegments) {\n    const from = this.previousPath;\n    const to = generatePath(toSegments);\n    this.previousPath = to;\n    if (to === from) {\n      return null;\n    }\n    const redirectedFrom = redirectFromSegments ? generatePath(redirectFromSegments) : null;\n    return {\n      from,\n      redirectedFrom,\n      to\n    };\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst routerLinkCss = \":host{--background:transparent;--color:var(--ion-color-primary, #0054e9);background:var(--background);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}a{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit}\";\nconst IonRouterLinkStyle0 = routerLinkCss;\nconst RouterLink = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onClick = ev => {\n      openURL(this.href, ev, this.routerDirection, this.routerAnimation);\n    };\n    this.color = undefined;\n    this.href = undefined;\n    this.rel = undefined;\n    this.routerDirection = 'forward';\n    this.routerAnimation = undefined;\n    this.target = undefined;\n  }\n  render() {\n    const mode = getIonMode(this);\n    const attrs = {\n      href: this.href,\n      rel: this.rel,\n      target: this.target\n    };\n    return h(Host, {\n      key: '11183264fb6ae0db9a7a47c71b6862d60001b834',\n      onClick: this.onClick,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'ion-activatable': true\n      })\n    }, h(\"a\", Object.assign({\n      key: '3e0e5242161cb0df593d6d573e51b8ba750065a1'\n    }, attrs), h(\"slot\", {\n      key: '5bd808e98a4627bb1236f0d955f4b32971355417'\n    })));\n  }\n};\nRouterLink.style = IonRouterLinkStyle0;\nexport { Route as ion_route, RouteRedirect as ion_route_redirect, Router as ion_router, RouterLink as ion_router_link };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,QAAQ,MAAM;AAAA,EAClB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,sBAAsB,YAAY,MAAM,uBAAuB,CAAC;AACrE,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,SAAS,UAAU;AACjB,SAAK,oBAAoB,KAAK,QAAQ;AAAA,EACxC;AAAA,EACA,iBAAiB,UAAU,UAAU;AACnC,QAAI,aAAa,UAAU;AACzB;AAAA,IACF;AACA,UAAM,QAAQ,WAAW,OAAO,KAAK,QAAQ,IAAI,CAAC;AAClD,UAAM,QAAQ,WAAW,OAAO,KAAK,QAAQ,IAAI,CAAC;AAClD,QAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,WAAK,SAAS,QAAQ;AACtB;AAAA,IACF;AACA,eAAW,OAAO,OAAO;AACvB,UAAI,SAAS,GAAG,MAAM,SAAS,GAAG,GAAG;AACnC,aAAK,SAAS,QAAQ;AACtB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,oBAAoB,KAAK;AAAA,EAChC;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,OAAO,CAAC,UAAU;AAAA,MAClB,aAAa,CAAC,UAAU;AAAA,MACxB,kBAAkB,CAAC,kBAAkB;AAAA,IACvC;AAAA,EACF;AACF;AACA,IAAM,gBAAgB,MAAM;AAAA,EAC1B,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,0BAA0B,YAAY,MAAM,2BAA2B,CAAC;AAC7E,SAAK,OAAO;AACZ,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,gBAAgB;AACd,SAAK,wBAAwB,KAAK;AAAA,EACpC;AAAA,EACA,oBAAoB;AAClB,SAAK,wBAAwB,KAAK;AAAA,EACpC;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,QAAQ,CAAC,eAAe;AAAA,MACxB,MAAM,CAAC,eAAe;AAAA,IACxB;AAAA,EACF;AACF;AACA,IAAM,qBAAqB;AAC3B,IAAM,wBAAwB;AAC9B,IAAM,qBAAqB;AAG3B,IAAM,eAAe,cAAY;AAC/B,QAAM,OAAO,SAAS,OAAO,OAAK,EAAE,SAAS,CAAC,EAAE,KAAK,GAAG;AACxD,SAAO,MAAM;AACf;AACA,IAAM,cAAc,CAAC,UAAU,SAAS,gBAAgB;AACtD,MAAI,MAAM,aAAa,QAAQ;AAC/B,MAAI,SAAS;AACX,UAAM,MAAM;AAAA,EACd;AACA,MAAI,gBAAgB,QAAW;AAC7B,WAAO,MAAM;AAAA,EACf;AACA,SAAO;AACT;AACA,IAAM,gBAAgB,CAAC,SAAS,MAAM,SAAS,UAAU,WAAW,OAAO,gBAAgB;AACzF,QAAM,MAAM,YAAY,CAAC,GAAG,UAAU,IAAI,EAAE,UAAU,GAAG,QAAQ,GAAG,SAAS,WAAW;AACxF,MAAI,cAAc,uBAAuB;AACvC,YAAQ,UAAU,OAAO,IAAI,GAAG;AAAA,EAClC,OAAO;AACL,YAAQ,aAAa,OAAO,IAAI,GAAG;AAAA,EACrC;AACF;AAQA,IAAM,kBAAkB,WAAS;AAC/B,QAAM,WAAW,CAAC;AAClB,aAAW,SAAS,OAAO;AACzB,eAAW,WAAW,MAAM,UAAU;AACpC,UAAI,QAAQ,CAAC,MAAM,KAAK;AAEtB,cAAM,QAAQ,MAAM,UAAU,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAC;AAC3D,YAAI,CAAC,OAAO;AACV,iBAAO;AAAA,QACT;AACA,iBAAS,KAAK,KAAK;AAAA,MACrB,WAAW,YAAY,IAAI;AACzB,iBAAS,KAAK,OAAO;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAQA,IAAM,eAAe,CAAC,QAAQ,aAAa;AACzC,MAAI,OAAO,SAAS,SAAS,QAAQ;AACnC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,KAAK,OAAO,CAAC,MAAM,IAAI;AAC1C,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,OAAO,CAAC,MAAM,SAAS,CAAC,GAAG;AAC7B,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,SAAS,WAAW,OAAO,QAAQ;AACrC,WAAO,CAAC,EAAE;AAAA,EACZ;AACA,SAAO,SAAS,MAAM,OAAO,MAAM;AACrC;AACA,IAAM,eAAe,CAAC,KAAK,MAAM,YAAY;AAC3C,QAAM,SAAS,UAAU,IAAI,EAAE;AAC/B,QAAM,WAAW,UAAU,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI;AACnD,QAAM,WAAW,UAAU,QAAQ,EAAE;AACrC,SAAO,aAAa,QAAQ,QAAQ;AACtC;AAMA,IAAM,YAAY,UAAQ;AACxB,MAAI,WAAW,CAAC,EAAE;AAClB,MAAI;AACJ,MAAI,QAAQ,MAAM;AAChB,UAAM,UAAU,KAAK,QAAQ,GAAG;AAChC,QAAI,UAAU,IAAI;AAChB,oBAAc,KAAK,UAAU,UAAU,CAAC;AACxC,aAAO,KAAK,UAAU,GAAG,OAAO;AAAA,IAClC;AACA,eAAW,KAAK,MAAM,GAAG,EAAE,IAAI,OAAK,EAAE,KAAK,CAAC,EAAE,OAAO,OAAK,EAAE,SAAS,CAAC;AACtE,QAAI,SAAS,WAAW,GAAG;AACzB,iBAAW,CAAC,EAAE;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,cAAc,YAAU;AAC5B,UAAQ,MAAM,qBAAqB,OAAO,MAAM,GAAG;AACnD,aAAW,SAAS,QAAQ;AAC1B,UAAM,WAAW,CAAC;AAClB,UAAM,QAAQ,OAAK,SAAS,KAAK,GAAG,EAAE,QAAQ,CAAC;AAC/C,UAAM,MAAM,MAAM,IAAI,OAAK,EAAE,EAAE;AAC/B,YAAQ,MAAM,MAAM,aAAa,QAAQ,CAAC,IAAI,yCAAyC,OAAQ,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG;AAAA,EACtH;AACA,UAAQ,SAAS;AACnB;AACA,IAAM,iBAAiB,eAAa;AAClC,UAAQ,MAAM,wBAAwB,UAAU,MAAM,GAAG;AACzD,aAAW,YAAY,WAAW;AAChC,QAAI,SAAS,IAAI;AACf,cAAQ,MAAM,UAAU,MAAM,aAAa,SAAS,IAAI,CAAC,IAAI,qBAAqB,SAAS,MAAM,aAAa,SAAS,GAAG,QAAQ,CAAC,IAAI,mBAAmB;AAAA,IAC5J;AAAA,EACF;AACA,UAAQ,SAAS;AACnB;AAUA,IAAM,gBAAgB,CAAO,MAAM,OAAO,WAAW,OAAO,UAAU,OAAO,cAAc;AACzF,MAAI;AAEF,UAAM,SAAS,cAAc,IAAI;AAEjC,QAAI,SAAS,MAAM,UAAU,CAAC,QAAQ;AACpC,aAAO;AAAA,IACT;AACA,UAAM,IAAI,QAAQ,aAAW,iBAAiB,QAAQ,OAAO,CAAC;AAC9D,UAAM,QAAQ,MAAM,KAAK;AACzB,UAAM,SAAS,MAAM,OAAO,WAAW,MAAM,IAAI,MAAM,QAAQ,WAAW,SAAS;AAGnF,QAAI,OAAO,SAAS;AAClB,kBAAY;AACZ,gBAAU;AAAA,IACZ;AAEA,cAAU,MAAM,cAAc,OAAO,SAAS,OAAO,WAAW,QAAQ,GAAG,SAAS,SAAS;AAG7F,QAAI,OAAO,aAAa;AACtB,YAAM,OAAO,YAAY;AAAA,IAC3B;AACA,WAAO;AAAA,EACT,SAAS,GAAG;AACV,kBAAc,8CAA8C,CAAC;AAC7D,WAAO;AAAA,EACT;AACF;AAMA,IAAM,eAAe,CAAM,SAAQ;AACjC,QAAM,MAAM,CAAC;AACb,MAAI;AACJ,MAAI,OAAO;AAEX,SAAO,SAAS,cAAc,IAAI,GAAG;AACnC,UAAM,KAAK,MAAM,OAAO,WAAW;AACnC,QAAI,IAAI;AACN,aAAO,GAAG;AACV,SAAG,UAAU;AACb,UAAI,KAAK,EAAE;AAAA,IACb,OAAO;AACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,mBAAmB,MAAM;AAC7B,MAAI,cAAc,SAAS,IAAI,GAAG;AAChC,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,SAAO,IAAI,QAAQ,aAAW;AAC5B,WAAO,iBAAiB,kBAAkB,MAAM,QAAQ,GAAG;AAAA,MACzD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH;AAEA,IAAM,kBAAkB;AACxB,IAAM,gBAAgB,UAAQ;AAC5B,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,MAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,WAAO;AAAA,EACT;AACA,QAAM,SAAS,KAAK,cAAc,eAAe;AACjD,SAAO,WAAW,QAAQ,WAAW,SAAS,SAAS;AACzD;AASA,IAAM,kBAAkB,CAAC,UAAU,aAAa;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,QAAW;AACpB,WAAO;AAAA,EACT;AACA,MAAI,KAAK,SAAS,SAAS,QAAQ;AACjC,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,WAAW,KAAK,CAAC;AACvB,QAAI,aAAa,KAAK;AACpB,aAAO;AAAA,IACT;AACA,QAAI,aAAa,SAAS,CAAC,GAAG;AAC5B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,KAAK,WAAW,SAAS;AAClC;AAEA,IAAM,oBAAoB,CAAC,UAAU,cAAc;AACjD,SAAO,UAAU,KAAK,cAAY,gBAAgB,UAAU,QAAQ,CAAC;AACvE;AACA,IAAM,aAAa,CAAC,KAAK,UAAU;AACjC,QAAM,MAAM,KAAK,IAAI,IAAI,QAAQ,MAAM,MAAM;AAC7C,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,UAAU,IAAI,CAAC;AACrB,UAAM,aAAa,MAAM,CAAC;AAE1B,QAAI,QAAQ,GAAG,YAAY,MAAM,WAAW,IAAI;AAC9C;AAAA,IACF;AACA,QAAI,QAAQ,QAAQ;AAClB,YAAM,gBAAgB,OAAO,KAAK,QAAQ,MAAM;AAEhD,UAAI,cAAc,WAAW,WAAW,SAAS,QAAQ;AAmBvD,cAAM,iBAAiB,cAAc,IAAI,SAAO,IAAI,GAAG,EAAE;AACzD,iBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAE9C,cAAI,eAAe,CAAC,EAAE,YAAY,MAAM,WAAW,SAAS,CAAC,GAAG;AAC9D;AAAA,UACF;AAEA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA;AAAA,EACF;AACA,SAAO;AACT;AAQA,IAAM,kBAAkB,CAAC,UAAU,UAAU;AAC3C,QAAM,gBAAgB,IAAI,eAAe,QAAQ;AACjD,MAAI,iBAAiB;AACrB,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,gBAAgB,MAAM,CAAC,EAAE;AAC/B,QAAI,cAAc,CAAC,MAAM,IAAI;AAC3B,uBAAiB;AAAA,IACnB,OAAO;AACL,iBAAW,WAAW,eAAe;AACnC,cAAM,OAAO,cAAc,KAAK;AAEhC,YAAI,QAAQ,CAAC,MAAM,KAAK;AACtB,cAAI,SAAS,IAAI;AACf,mBAAO;AAAA,UACT;AACA,sBAAY,aAAa,CAAC;AAC1B,gBAAM,SAAS,UAAU,CAAC,MAAM,UAAU,CAAC,IAAI,CAAC;AAChD,iBAAO,QAAQ,MAAM,CAAC,CAAC,IAAI;AAAA,QAC7B,WAAW,SAAS,SAAS;AAC3B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,uBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,iBAAiB,oBAAoB,cAAc,KAAK,MAAM,MAAM;AACpF,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AACA,MAAI,WAAW;AACb,WAAO,MAAM,IAAI,CAAC,OAAO,OAAO;AAAA,MAC9B,IAAI,MAAM;AAAA,MACV,UAAU,MAAM;AAAA,MAChB,QAAQ,YAAY,MAAM,QAAQ,UAAU,CAAC,CAAC;AAAA,MAC9C,aAAa,MAAM;AAAA,MACnB,aAAa,MAAM;AAAA,IACrB,EAAE;AAAA,EACJ;AACA,SAAO;AACT;AAKA,IAAM,cAAc,CAAC,GAAG,MAAM;AAC5B,SAAO,KAAK,IAAI,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;AAC3D;AAQA,IAAM,kBAAkB,CAAC,KAAK,WAAW;AACvC,MAAI,QAAQ;AACZ,MAAI,aAAa;AACjB,aAAW,SAAS,QAAQ;AAC1B,UAAM,QAAQ,WAAW,KAAK,KAAK;AACnC,QAAI,QAAQ,YAAY;AACtB,cAAQ;AACR,mBAAa;AAAA,IACf;AAAA,EACF;AACA,MAAI,OAAO;AACT,WAAO,MAAM,IAAI,CAAC,OAAO,MAAM;AAC7B,UAAI;AACJ,aAAO;AAAA,QACL,IAAI,MAAM;AAAA,QACV,UAAU,MAAM;AAAA,QAChB,QAAQ,YAAY,MAAM,SAAS,KAAK,IAAI,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,MAChG;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAQA,IAAM,uBAAuB,CAAC,UAAU,WAAW;AACjD,MAAI,QAAQ;AACZ,MAAI,YAAY;AAChB,aAAW,SAAS,QAAQ;AAC1B,UAAM,eAAe,gBAAgB,UAAU,KAAK;AACpD,QAAI,iBAAiB,MAAM;AACzB,YAAM,QAAQ,gBAAgB,YAAY;AAC1C,UAAI,QAAQ,WAAW;AACrB,oBAAY;AACZ,gBAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAYA,IAAM,kBAAkB,WAAS;AAC/B,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,aAAW,SAAS,OAAO;AACzB,eAAW,WAAW,MAAM,UAAU;AACpC,UAAI,QAAQ,CAAC,MAAM,KAAK;AACtB,iBAAS,KAAK,IAAI,GAAG,KAAK;AAAA,MAC5B,WAAW,YAAY,IAAI;AACzB,iBAAS,KAAK,IAAI,GAAG,KAAK;AAAA,MAC5B;AACA;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,UAAU;AACpB,SAAK,WAAW,SAAS,MAAM;AAAA,EACjC;AAAA,EACA,OAAO;AACL,QAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,aAAO,KAAK,SAAS,MAAM;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,WAAW,CAAC,IAAI,SAAS;AAC7B,MAAI,QAAQ,IAAI;AACd,WAAO,GAAG,IAAI;AAAA,EAChB;AACA,MAAI,GAAG,aAAa,IAAI,GAAG;AACzB,WAAO,GAAG,aAAa,IAAI;AAAA,EAC7B;AACA,SAAO;AACT;AAMA,IAAM,gBAAgB,UAAQ;AAC5B,SAAO,MAAM,KAAK,KAAK,QAAQ,EAAE,OAAO,QAAM,GAAG,YAAY,oBAAoB,EAAE,IAAI,QAAM;AAC3F,UAAM,KAAK,SAAS,IAAI,IAAI;AAC5B,WAAO;AAAA,MACL,MAAM,UAAU,SAAS,IAAI,MAAM,CAAC,EAAE;AAAA,MACtC,IAAI,MAAM,OAAO,SAAY,UAAU,EAAE;AAAA,IAC3C;AAAA,EACF,CAAC;AACH;AAMA,IAAM,aAAa,UAAQ;AACzB,SAAO,kBAAkB,eAAe,IAAI,CAAC;AAC/C;AAMA,IAAM,iBAAiB,UAAQ;AAC7B,SAAO,MAAM,KAAK,KAAK,QAAQ,EAAE,OAAO,QAAM,GAAG,YAAY,eAAe,GAAG,SAAS,EAAE,IAAI,QAAM;AAClG,UAAM,YAAY,SAAS,IAAI,WAAW;AAC1C,WAAO;AAAA,MACL,UAAU,UAAU,SAAS,IAAI,KAAK,CAAC,EAAE;AAAA,MACzC,IAAI,UAAU,YAAY;AAAA,MAC1B,QAAQ,GAAG;AAAA,MACX,aAAa,GAAG;AAAA,MAChB,aAAa,GAAG;AAAA,MAChB,UAAU,eAAe,EAAE;AAAA,IAC7B;AAAA,EACF,CAAC;AACH;AAMA,IAAM,oBAAoB,WAAS;AACjC,QAAM,SAAS,CAAC;AAChB,aAAW,QAAQ,OAAO;AACxB,gBAAY,CAAC,GAAG,QAAQ,IAAI;AAAA,EAC9B;AACA,SAAO;AACT;AAEA,IAAM,cAAc,CAAC,OAAO,QAAQ,SAAS;AAC3C,UAAQ,CAAC,GAAG,OAAO;AAAA,IACjB,IAAI,KAAK;AAAA,IACT,UAAU,KAAK;AAAA,IACf,QAAQ,KAAK;AAAA,IACb,aAAa,KAAK;AAAA,IAClB,aAAa,KAAK;AAAA,EACpB,CAAC;AACD,MAAI,KAAK,SAAS,WAAW,GAAG;AAC9B,WAAO,KAAK,KAAK;AACjB;AAAA,EACF;AACA,aAAW,SAAS,KAAK,UAAU;AACjC,gBAAY,OAAO,QAAQ,KAAK;AAAA,EAClC;AACF;AACA,IAAM,SAAS,MAAM;AAAA,EACnB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,qBAAqB,YAAY,MAAM,sBAAsB,CAAC;AACnE,SAAK,oBAAoB,YAAY,MAAM,qBAAqB,CAAC;AACjE,SAAK,eAAe;AACpB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACjB;AAAA,EACM,oBAAoB;AAAA;AACxB,YAAM,iBAAiB;AACvB,YAAM,aAAa,MAAM,KAAK,UAAU,KAAK,YAAY,CAAC;AAC1D,UAAI,eAAe,MAAM;AACvB,YAAI,OAAO,eAAe,UAAU;AAClC,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,gBAAM,OAAO,UAAU,QAAQ;AAC/B,eAAK,YAAY,KAAK,UAAU,oBAAoB,KAAK,WAAW;AACpE,gBAAM,KAAK,kBAAkB,KAAK,UAAU,kBAAkB;AAAA,QAChE;AAAA,MACF,OAAO;AACL,cAAM,KAAK,gBAAgB;AAAA,MAC7B;AAAA,IACF;AAAA;AAAA,EACA,mBAAmB;AACjB,WAAO,iBAAiB,2BAA2B,SAAS,KAAK,kBAAkB,KAAK,IAAI,GAAG,EAAE,CAAC;AAClG,WAAO,iBAAiB,uBAAuB,SAAS,KAAK,gBAAgB,KAAK,IAAI,GAAG,GAAG,CAAC;AAAA,EAC/F;AAAA,EACM,aAAa;AAAA;AACjB,YAAM,YAAY,KAAK,iBAAiB;AACxC,UAAI,WAAW,KAAK,YAAY;AAChC,YAAM,aAAa,MAAM,KAAK,UAAU,QAAQ;AAChD,UAAI,eAAe,MAAM;AACvB,YAAI,OAAO,eAAe,UAAU;AAClC,qBAAW,UAAU,WAAW,QAAQ,EAAE;AAAA,QAC5C,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO,KAAK,kBAAkB,UAAU,SAAS;AAAA,IACnD;AAAA;AAAA,EACA,aAAa,IAAI;AACf,OAAG,OAAO,SAAS,GAAG,wBAAsB;AAC1C,WAAK,KAAK;AACV,yBAAmB;AAAA,IACrB,CAAC;AAAA,EACH;AAAA;AAAA,EAEM,gBAAgB;AAAA;AACpB,YAAM,aAAa,MAAM,KAAK,UAAU;AACxC,UAAI,eAAe,MAAM;AACvB,YAAI,OAAO,eAAe,UAAU;AAClC,iBAAO,WAAW;AAAA,QACpB,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,KAAK,MAAM,YAAY,WAAW,WAAW;AAAA;AACjD,UAAI;AACJ,UAAI,KAAK,WAAW,GAAG,GAAG;AACxB,cAAM,eAAe,KAAK,KAAK,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AAE9E,cAAM,MAAM,IAAI,IAAI,MAAM,gBAAgB,WAAW,EAAE;AACvD,eAAO,IAAI,WAAW,IAAI;AAAA,MAC5B;AACA,UAAI,aAAa,UAAU,IAAI;AAC/B,YAAM,aAAa,MAAM,KAAK,UAAU,WAAW,QAAQ;AAC3D,UAAI,eAAe,MAAM;AACvB,YAAI,OAAO,eAAe,UAAU;AAClC,uBAAa,UAAU,WAAW,QAAQ;AAAA,QAC5C,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,WAAK,YAAY,WAAW,UAAU,WAAW,WAAW,WAAW;AACvE,aAAO,KAAK,kBAAkB,WAAW,UAAU,WAAW,SAAS;AAAA,IACzE;AAAA;AAAA;AAAA,EAEA,OAAO;AACL,WAAO,QAAQ,KAAK;AACpB,WAAO,QAAQ,QAAQ,KAAK,WAAW;AAAA,EACzC;AAAA;AAAA,EAEM,aAAa;AAAA;AACjB,kBAAY,WAAW,KAAK,EAAE,CAAC;AAC/B,qBAAe,cAAc,KAAK,EAAE,CAAC;AAAA,IACvC;AAAA;AAAA;AAAA,EAEM,WAAW,WAAW;AAAA;AAC1B,UAAI,KAAK,MAAM;AACb,wBAAgB,0DAA0D;AAC1E,eAAO;AAAA,MACT;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,MAAM,aAAa,OAAO,SAAS,IAAI;AAC3C,YAAM,SAAS,WAAW,KAAK,EAAE;AACjC,YAAM,QAAQ,gBAAgB,KAAK,MAAM;AACzC,UAAI,CAAC,OAAO;AACV,wBAAgB,sCAAsC,IAAI,IAAI,OAAK,EAAE,EAAE,CAAC;AACxE,eAAO;AAAA,MACT;AACA,YAAM,WAAW,gBAAgB,KAAK;AACtC,UAAI,CAAC,UAAU;AACb,wBAAgB,oFAAoF;AACpG,eAAO;AAAA,MACT;AACA,WAAK,YAAY,UAAU,SAAS;AACpC,YAAM,KAAK,kBAAkB,QAAQ,OAAO,oBAAoB,UAAU,MAAM,IAAI,MAAM;AAC1F,aAAO;AAAA,IACT;AAAA;AAAA;AAAA,EAEA,oBAAoB;AAClB,UAAM,WAAW,KAAK,YAAY;AAClC,QAAI,YAAY,kBAAkB,UAAU,cAAc,KAAK,EAAE,CAAC,GAAG;AACnE,WAAK,kBAAkB,UAAU,kBAAkB;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,kBAAkB,KAAK,YAAY,GAAG,kBAAkB;AAAA,EACtE;AAAA,EACA,mBAAmB;AACjB,QAAI;AACJ,UAAM,MAAM;AACZ,QAAI,IAAI,QAAQ,UAAU,MAAM;AAC9B,WAAK;AACL,UAAI,QAAQ,aAAa,KAAK,OAAO,IAAI,SAAS,QAAQ,KAAK,IAAI,SAAS,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI;AAAA,IACpI;AACA,UAAM,QAAQ,IAAI,QAAQ;AAC1B,UAAM,YAAY,KAAK;AACvB,SAAK,YAAY;AACjB,QAAI,QAAQ,aAAa,SAAS,aAAa,YAAY,GAAG;AAC5D,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,WAAW;AACrB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACM,kBAAkB,UAAU,WAAW,WAAW;AAAA;AACtD,UAAI,CAAC,UAAU;AACb,sBAAc,oDAAoD;AAClE,eAAO;AAAA,MACT;AAEA,YAAM,YAAY,cAAc,KAAK,EAAE;AACvC,YAAM,WAAW,kBAAkB,UAAU,SAAS;AACtD,UAAI,eAAe;AACnB,UAAI,UAAU;AACZ,cAAM;AAAA,UACJ,UAAU;AAAA,UACV;AAAA,QACF,IAAI,SAAS;AACb,aAAK,YAAY,YAAY,WAAW,WAAW;AACnD,uBAAe,SAAS;AACxB,mBAAW;AAAA,MACb;AAEA,YAAM,SAAS,WAAW,KAAK,EAAE;AACjC,YAAM,QAAQ,qBAAqB,UAAU,MAAM;AACnD,UAAI,CAAC,OAAO;AACV,sBAAc,mDAAmD;AACjE,eAAO;AAAA,MACT;AAEA,aAAO,KAAK,kBAAkB,SAAS,MAAM,OAAO,WAAW,UAAU,cAAc,GAAG,SAAS;AAAA,IACrG;AAAA;AAAA,EACM,kBAAkB,MAAM,OAAO,WAAW,UAAU,cAAc,QAAQ,GAAG,WAAW;AAAA;AAC5F,YAAM,SAAS,MAAM,KAAK,KAAK;AAC/B,UAAI,UAAU;AACd,UAAI;AACF,kBAAU,MAAM,KAAK,cAAc,MAAM,OAAO,WAAW,UAAU,cAAc,OAAO,SAAS;AAAA,MACrG,SAAS,GAAG;AACV,sBAAc,kDAAkD,CAAC;AAAA,MACnE;AACA,aAAO;AACP,aAAO;AAAA,IACT;AAAA;AAAA,EACM,OAAO;AAAA;AACX,YAAM,IAAI,KAAK;AACf,UAAI;AACJ,WAAK,cAAc,IAAI,QAAQ,OAAK,UAAU,CAAC;AAC/C,UAAI,MAAM,QAAW;AACnB,cAAM;AAAA,MACR;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,YAAyC;AAAA,+CAA/B,KAAK,KAAK,YAAY,GAAG,MAAM;AAC7C,UAAI,SAAS,QAAW;AACtB,eAAO,UAAU,KAAK,YAAY,EAAE;AAAA,MACtC;AACA,UAAI,CAAC,MAAM,CAAC,MAAM;AAChB,eAAO;AAAA,MACT;AACA,YAAM,SAAS,WAAW,KAAK,EAAE;AACjC,YAAM,YAAY,qBAAqB,MAAM,MAAM;AAEnD,YAAM,kBAAkB,aAAa,UAAU,UAAU,SAAS,CAAC,EAAE;AACrE,YAAM,WAAW,kBAAkB,MAAM,gBAAgB,IAAI;AAC7D,UAAI,aAAa,SAAS,OAAO,aAAa,UAAU;AACtD,eAAO;AAAA,MACT;AACA,YAAM,UAAU,qBAAqB,IAAI,MAAM;AAE/C,YAAM,kBAAkB,WAAW,QAAQ,QAAQ,SAAS,CAAC,EAAE;AAC/D,aAAO,kBAAkB,gBAAgB,IAAI;AAAA,IAC/C;AAAA;AAAA,EACM,cAAc,MAAM,OAAO,WAAW,UAAU,cAAc,QAAQ,GAAG,WAAW;AAAA;AACxF,UAAI,KAAK,MAAM;AACb,wBAAgB,0DAA0D;AAC1E,eAAO;AAAA,MACT;AACA,WAAK,OAAO;AAEZ,YAAM,aAAa,KAAK,iBAAiB,UAAU,YAAY;AAC/D,UAAI,YAAY;AACd,aAAK,mBAAmB,KAAK,UAAU;AAAA,MACzC;AACA,YAAM,UAAU,MAAM,cAAc,MAAM,OAAO,WAAW,OAAO,OAAO,SAAS;AACnF,WAAK,OAAO;AAEZ,UAAI,YAAY;AACd,aAAK,kBAAkB,KAAK,UAAU;AAAA,MACxC;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACA,YAAY,UAAU,WAAW,aAAa;AAC5C,SAAK;AACL,kBAAc,OAAO,SAAS,KAAK,MAAM,KAAK,SAAS,UAAU,WAAW,KAAK,OAAO,WAAW;AAAA,EACrG;AAAA,EACA,cAAc;AACZ,WAAO,aAAa,OAAO,UAAU,KAAK,MAAM,KAAK,OAAO;AAAA,EAC9D;AAAA,EACA,iBAAiB,YAAY,sBAAsB;AACjD,UAAM,OAAO,KAAK;AAClB,UAAM,KAAK,aAAa,UAAU;AAClC,SAAK,eAAe;AACpB,QAAI,OAAO,MAAM;AACf,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,uBAAuB,aAAa,oBAAoB,IAAI;AACnF,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;AAC5B,IAAM,aAAa,MAAM;AAAA,EACvB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,UAAU,QAAM;AACnB,cAAQ,KAAK,MAAM,IAAI,KAAK,iBAAiB,KAAK,eAAe;AAAA,IACnE;AACA,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,QAAQ;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,KAAK,KAAK;AAAA,MACV,QAAQ,KAAK;AAAA,IACf;AACA,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,SAAS,KAAK;AAAA,MACd,OAAO,mBAAmB,KAAK,OAAO;AAAA,QACpC,CAAC,IAAI,GAAG;AAAA,QACR,mBAAmB;AAAA,MACrB,CAAC;AAAA,IACH,GAAG,EAAE,KAAK,OAAO,OAAO;AAAA,MACtB,KAAK;AAAA,IACP,GAAG,KAAK,GAAG,EAAE,QAAQ;AAAA,MACnB,KAAK;AAAA,IACP,CAAC,CAAC,CAAC;AAAA,EACL;AACF;AACA,WAAW,QAAQ;", "names": [], "x_google_ignoreList": [0]}
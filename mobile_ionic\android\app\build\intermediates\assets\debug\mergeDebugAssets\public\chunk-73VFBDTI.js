import {
  environment
} from "./chunk-I7MI46CM.js";
import {
  HttpClient,
  Injectable,
  firstValueFrom,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-NS3G4TP7.js";
import {
  __async,
  __spreadProps,
  __spreadValues
} from "./chunk-UL2P3LPA.js";

// src/app/services/offline-storage.service.ts
var OfflineStorageService = class _OfflineStorageService {
  constructor(http) {
    this.http = http;
    this.STORAGE_KEYS = {
      EVACUATION_CENTERS: "offline_evacuation_centers",
      ROUTES: "offline_routes",
      MAP_TILES: "offline_map_tiles",
      LAST_SYNC: "last_data_sync",
      OFFLINE_MODE: "offline_mode_enabled",
      USER_LOCATION: "last_user_location"
    };
    this.MAX_STORAGE_SIZE = 50 * 1024 * 1024;
    this.TILE_CACHE_LIMIT = 1e3;
    this.initializeStorage();
  }
  initializeStorage() {
    if (!localStorage.getItem(this.STORAGE_KEYS.EVACUATION_CENTERS)) {
      localStorage.setItem(this.STORAGE_KEYS.EVACUATION_CENTERS, JSON.stringify([]));
    }
    if (!localStorage.getItem(this.STORAGE_KEYS.ROUTES)) {
      localStorage.setItem(this.STORAGE_KEYS.ROUTES, JSON.stringify([]));
    }
    if (!localStorage.getItem(this.STORAGE_KEYS.MAP_TILES)) {
      localStorage.setItem(this.STORAGE_KEYS.MAP_TILES, JSON.stringify({}));
    }
    console.log("\u2705 Offline storage initialized");
  }
  // ===== EVACUATION CENTERS MANAGEMENT =====
  /**
   * Sync evacuation centers from backend to local storage
   */
  syncEvacuationCenters() {
    return __async(this, null, function* () {
      try {
        console.log("\u{1F504} Syncing evacuation centers from backend...");
        const response = yield firstValueFrom(this.http.get(`${environment.apiUrl}/offline/evacuation-centers`));
        if (response.success && response.data) {
          yield this.saveEvacuationCenters(response.data);
          localStorage.setItem(this.STORAGE_KEYS.LAST_SYNC, response.sync_timestamp);
          console.log(`\u2705 Synced ${response.count} evacuation centers`);
          return true;
        } else {
          console.error("\u274C Invalid response from server");
          return false;
        }
      } catch (error) {
        console.error("\u274C Failed to sync evacuation centers:", error);
        return false;
      }
    });
  }
  /**
   * Save evacuation centers to local storage
   */
  saveEvacuationCenters(centers) {
    return __async(this, null, function* () {
      try {
        const centersWithTimestamp = centers.map((center) => __spreadProps(__spreadValues({}, center), {
          last_updated: (/* @__PURE__ */ new Date()).toISOString()
        }));
        localStorage.setItem(this.STORAGE_KEYS.EVACUATION_CENTERS, JSON.stringify(centersWithTimestamp));
        console.log(`\u{1F4BE} Saved ${centers.length} evacuation centers to local storage`);
      } catch (error) {
        console.error("\u274C Error saving evacuation centers:", error);
        throw error;
      }
    });
  }
  /**
   * Get evacuation centers from local storage
   */
  getEvacuationCenters(disasterType) {
    return __async(this, null, function* () {
      try {
        const stored = localStorage.getItem(this.STORAGE_KEYS.EVACUATION_CENTERS);
        if (!stored)
          return [];
        const centers = JSON.parse(stored);
        if (disasterType) {
          return centers.filter((center) => center.disaster_type === disasterType);
        }
        return centers;
      } catch (error) {
        console.error("\u274C Error fetching evacuation centers:", error);
        return [];
      }
    });
  }
  /**
   * Get nearest evacuation centers
   */
  getNearestCenters(userLat, userLng, disasterType, limit = 2) {
    return __async(this, null, function* () {
      const centers = yield this.getEvacuationCenters(disasterType);
      const centersWithDistance = centers.map((center) => __spreadProps(__spreadValues({}, center), {
        distance: this.calculateDistance(userLat, userLng, center.latitude, center.longitude)
      }));
      return centersWithDistance.sort((a, b) => a.distance - b.distance).slice(0, limit);
    });
  }
  // ===== ROUTE CACHING =====
  /**
   * Save route to local storage
   */
  saveRoute(route) {
    return __async(this, null, function* () {
      try {
        const routes = this.getStoredRoutes();
        const routeWithId = __spreadProps(__spreadValues({}, route), {
          id: `${route.start_lat}_${route.start_lng}_${route.end_lat}_${route.end_lng}_${route.travel_mode}`,
          created_at: (/* @__PURE__ */ new Date()).toISOString()
        });
        const filteredRoutes = routes.filter((r) => r.id !== routeWithId.id);
        filteredRoutes.push(routeWithId);
        const limitedRoutes = filteredRoutes.slice(-100);
        localStorage.setItem(this.STORAGE_KEYS.ROUTES, JSON.stringify(limitedRoutes));
        console.log("\u{1F4BE} Route saved to cache");
      } catch (error) {
        console.error("\u274C Error saving route:", error);
      }
    });
  }
  /**
   * Get cached route
   */
  getRoute(startLat, startLng, endLat, endLng, travelMode) {
    return __async(this, null, function* () {
      try {
        const routes = this.getStoredRoutes();
        const routeId = `${startLat}_${startLng}_${endLat}_${endLng}_${travelMode}`;
        const route = routes.find((r) => r.id === routeId);
        if (route && route.created_at) {
          const routeAge = Date.now() - new Date(route.created_at).getTime();
          const maxAge = 24 * 60 * 60 * 1e3;
          if (routeAge < maxAge) {
            return route;
          }
        }
        return null;
      } catch (error) {
        console.error("\u274C Error fetching route:", error);
        return null;
      }
    });
  }
  getStoredRoutes() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.ROUTES);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error("\u274C Error parsing stored routes:", error);
      return [];
    }
  }
  // ===== MAP TILES CACHING =====
  /**
   * Save map tile to cache
   */
  saveMapTile(z, x, y, tileData) {
    return __async(this, null, function* () {
      try {
        const tiles = this.getStoredTiles();
        const tileKey = `${z}_${x}_${y}`;
        tiles[tileKey] = {
          key: tileKey,
          z,
          x,
          y,
          tile_data: tileData,
          created_at: (/* @__PURE__ */ new Date()).toISOString()
        };
        const tileKeys = Object.keys(tiles);
        if (tileKeys.length > this.TILE_CACHE_LIMIT) {
          const sortedTiles = tileKeys.map((key) => ({ key, created_at: tiles[key].created_at })).sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
          const tilesToRemove = sortedTiles.slice(0, tileKeys.length - this.TILE_CACHE_LIMIT);
          tilesToRemove.forEach((tile) => delete tiles[tile.key]);
        }
        localStorage.setItem(this.STORAGE_KEYS.MAP_TILES, JSON.stringify(tiles));
      } catch (error) {
        console.error("\u274C Error saving map tile:", error);
      }
    });
  }
  /**
   * Get cached map tile
   */
  getMapTile(z, x, y) {
    return __async(this, null, function* () {
      try {
        const tiles = this.getStoredTiles();
        const tileKey = `${z}_${x}_${y}`;
        return tiles[tileKey] || null;
      } catch (error) {
        console.error("\u274C Error getting map tile:", error);
        return null;
      }
    });
  }
  getStoredTiles() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.MAP_TILES);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error("\u274C Error parsing stored tiles:", error);
      return {};
    }
  }
  // ===== OFFLINE MODE MANAGEMENT =====
  /**
   * Enable offline mode
   */
  setOfflineMode(enabled) {
    localStorage.setItem(this.STORAGE_KEYS.OFFLINE_MODE, enabled.toString());
    console.log(`\u{1F504} Offline mode ${enabled ? "enabled" : "disabled"}`);
  }
  /**
   * Check if offline mode is enabled
   */
  isOfflineMode() {
    return localStorage.getItem(this.STORAGE_KEYS.OFFLINE_MODE) === "true";
  }
  /**
   * Save user location for offline use
   */
  saveUserLocation(lat, lng) {
    const location = { lat, lng, timestamp: (/* @__PURE__ */ new Date()).toISOString() };
    localStorage.setItem(this.STORAGE_KEYS.USER_LOCATION, JSON.stringify(location));
  }
  /**
   * Get last known user location
   */
  getLastUserLocation() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.USER_LOCATION);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error("\u274C Error getting user location:", error);
      return null;
    }
  }
  // ===== UTILITY METHODS =====
  /**
   * Check if offline data is available
   */
  isDataAvailable() {
    return __async(this, null, function* () {
      const centers = yield this.getEvacuationCenters();
      return centers.length > 0;
    });
  }
  /**
   * Get last sync time
   */
  getLastSyncTime() {
    return localStorage.getItem(this.STORAGE_KEYS.LAST_SYNC);
  }
  /**
   * Calculate distance between two points using Haversine formula
   */
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371;
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }
  toRadians(degrees) {
    return degrees * (Math.PI / 180);
  }
  /**
   * Clear all offline data
   */
  clearOfflineData() {
    Object.values(this.STORAGE_KEYS).forEach((key) => {
      localStorage.removeItem(key);
    });
    this.initializeStorage();
    console.log("\u{1F5D1}\uFE0F All offline data cleared");
  }
  /**
   * Get storage usage information
   */
  getStorageInfo() {
    let used = 0;
    Object.values(this.STORAGE_KEYS).forEach((key) => {
      const item = localStorage.getItem(key);
      if (item) {
        used += new Blob([item]).size;
      }
    });
    const available = this.MAX_STORAGE_SIZE - used;
    const percentage = used / this.MAX_STORAGE_SIZE * 100;
    return { used, available, percentage };
  }
  /**
   * Check if device is online
   */
  isOnline() {
    return navigator.onLine;
  }
  static {
    this.\u0275fac = function OfflineStorageService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OfflineStorageService)(\u0275\u0275inject(HttpClient));
    };
  }
  static {
    this.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _OfflineStorageService, factory: _OfflineStorageService.\u0275fac, providedIn: "root" });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OfflineStorageService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  OfflineStorageService
};
//# sourceMappingURL=chunk-73VFBDTI.js.map

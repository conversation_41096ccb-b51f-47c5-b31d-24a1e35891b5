{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/notch-controller-1a1f7183.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\nimport { r as raf } from './helpers-d94bc8ad.js';\n\n/**\n * A utility to calculate the size of an outline notch\n * width relative to the content passed. This is used in\n * components such as `ion-select` with `fill=\"outline\"`\n * where we need to pass slotted HTML content. This is not\n * needed when rendering plaintext content because we can\n * render the plaintext again hidden with `opacity: 0` inside\n * of the notch. As a result we can rely on the intrinsic size\n * of the element to correctly compute the notch width. We\n * cannot do this with slotted content because we cannot project\n * it into 2 places at once.\n *\n * @internal\n * @param el: The host element\n * @param getNotchSpacerEl: A function that returns a reference to the notch spacer element inside of the component template.\n * @param getLabelSlot: A function that returns a reference to the slotted content.\n */\nconst createNotchController = (el, getNotchSpacerEl, getLabelSlot) => {\n  let notchVisibilityIO;\n  const needsExplicitNotchWidth = () => {\n    const notchSpacerEl = getNotchSpacerEl();\n    if (\n    /**\n     * If the notch is not being used\n     * then we do not need to set the notch width.\n     */\n    notchSpacerEl === undefined ||\n    /**\n     * If either the label property is being\n     * used or the label slot is not defined,\n     * then we do not need to estimate the notch width.\n     */\n    el.label !== undefined || getLabelSlot() === null) {\n      return false;\n    }\n    return true;\n  };\n  const calculateNotchWidth = () => {\n    if (needsExplicitNotchWidth()) {\n      /**\n       * Run this the frame after\n       * the browser has re-painted the host element.\n       * Otherwise, the label element may have a width\n       * of 0 and the IntersectionObserver will be used.\n       */\n      raf(() => {\n        setNotchWidth();\n      });\n    }\n  };\n  /**\n   * When using a label prop we can render\n   * the label value inside of the notch and\n   * let the browser calculate the size of the notch.\n   * However, we cannot render the label slot in multiple\n   * places so we need to manually calculate the notch dimension\n   * based on the size of the slotted content.\n   *\n   * This function should only be used to set the notch width\n   * on slotted label content. The notch width for label prop\n   * content is automatically calculated based on the\n   * intrinsic size of the label text.\n   */\n  const setNotchWidth = () => {\n    const notchSpacerEl = getNotchSpacerEl();\n    if (notchSpacerEl === undefined) {\n      return;\n    }\n    if (!needsExplicitNotchWidth()) {\n      notchSpacerEl.style.removeProperty('width');\n      return;\n    }\n    const width = getLabelSlot().scrollWidth;\n    if (\n    /**\n     * If the computed width of the label is 0\n     * and notchSpacerEl's offsetParent is null\n     * then that means the element is hidden.\n     * As a result, we need to wait for the element\n     * to become visible before setting the notch width.\n     *\n     * We do not check el.offsetParent because\n     * that can be null if the host element has\n     * position: fixed applied to it.\n     * notchSpacerEl does not have position: fixed.\n     */\n    width === 0 && notchSpacerEl.offsetParent === null && win !== undefined && 'IntersectionObserver' in win) {\n      /**\n       * If there is an IO already attached\n       * then that will update the notch\n       * once the element becomes visible.\n       * As a result, there is no need to create\n       * another one.\n       */\n      if (notchVisibilityIO !== undefined) {\n        return;\n      }\n      const io = notchVisibilityIO = new IntersectionObserver(ev => {\n        /**\n         * If the element is visible then we\n         * can try setting the notch width again.\n         */\n        if (ev[0].intersectionRatio === 1) {\n          setNotchWidth();\n          io.disconnect();\n          notchVisibilityIO = undefined;\n        }\n      },\n      /**\n       * Set the root to be the host element\n       * This causes the IO callback\n       * to be fired in WebKit as soon as the element\n       * is visible. If we used the default root value\n       * then WebKit would only fire the IO callback\n       * after any animations (such as a modal transition)\n       * finished, and there would potentially be a flicker.\n       */\n      {\n        threshold: 0.01,\n        root: el\n      });\n      io.observe(notchSpacerEl);\n      return;\n    }\n    /**\n     * If the element is visible then we can set the notch width.\n     * The notch is only visible when the label is scaled,\n     * which is why we multiply the width by 0.75 as this is\n     * the same amount the label element is scaled by in the host CSS.\n     * (See $form-control-label-stacked-scale in ionic.globals.scss).\n     */\n    notchSpacerEl.style.setProperty('width', `${width * 0.75}px`);\n  };\n  const destroy = () => {\n    if (notchVisibilityIO) {\n      notchVisibilityIO.disconnect();\n      notchVisibilityIO = undefined;\n    }\n  };\n  return {\n    calculateNotchWidth,\n    destroy\n  };\n};\nexport { createNotchController as c };"], "mappings": ";;;;;;;;AAuBA,IAAM,wBAAwB,CAAC,IAAI,kBAAkB,iBAAiB;AACpE,MAAI;AACJ,QAAM,0BAA0B,MAAM;AACpC,UAAM,gBAAgB,iBAAiB;AACvC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMlB,GAAG,UAAU,UAAa,aAAa,MAAM;AAAA,MAAM;AACjD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,sBAAsB,MAAM;AAChC,QAAI,wBAAwB,GAAG;AAO7B,UAAI,MAAM;AACR,sBAAc;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACF;AAcA,QAAM,gBAAgB,MAAM;AAC1B,UAAM,gBAAgB,iBAAiB;AACvC,QAAI,kBAAkB,QAAW;AAC/B;AAAA,IACF;AACA,QAAI,CAAC,wBAAwB,GAAG;AAC9B,oBAAc,MAAM,eAAe,OAAO;AAC1C;AAAA,IACF;AACA,UAAM,QAAQ,aAAa,EAAE;AAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaA,UAAU,KAAK,cAAc,iBAAiB,QAAQ,QAAQ,UAAa,0BAA0B;AAAA,MAAK;AAQxG,UAAI,sBAAsB,QAAW;AACnC;AAAA,MACF;AACA,YAAM,KAAK,oBAAoB,IAAI;AAAA,QAAqB,QAAM;AAK5D,cAAI,GAAG,CAAC,EAAE,sBAAsB,GAAG;AACjC,0BAAc;AACd,eAAG,WAAW;AACd,gCAAoB;AAAA,UACtB;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUA;AAAA,UACE,WAAW;AAAA,UACX,MAAM;AAAA,QACR;AAAA,MAAC;AACD,SAAG,QAAQ,aAAa;AACxB;AAAA,IACF;AAQA,kBAAc,MAAM,YAAY,SAAS,GAAG,QAAQ,IAAI,IAAI;AAAA,EAC9D;AACA,QAAM,UAAU,MAAM;AACpB,QAAI,mBAAmB;AACrB,wBAAkB,WAAW;AAC7B,0BAAoB;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;", "names": [], "x_google_ignoreList": [0]}
{"version": 3, "sources": ["src/app/pages/disaster-maps/all-maps.page.ts", "src/app/pages/disaster-maps/all-maps.page.html"], "sourcesContent": ["import { Component, OnInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule, LoadingController, ToastController, AlertController } from '@ionic/angular';\r\nimport { Router } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Geolocation } from '@capacitor/geolocation';\r\nimport { MapboxRoutingService } from '../../services/mapbox-routing.service';\r\nimport * as L from 'leaflet';\r\n\r\ninterface EvacuationCenter {\r\n  id: number;\r\n  name: string;\r\n  address: string;\r\n  latitude: number;\r\n  longitude: number;\r\n  capacity?: number;\r\n  status?: string;\r\n  disaster_type?: string;\r\n  contact?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-all-maps',\r\n  templateUrl: './all-maps.page.html',\r\n  styleUrls: ['./all-maps.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule]\r\n})\r\nexport class AllMapsPage implements OnInit {\r\n  private map!: L.Map;\r\n  private userMarker: L.Marker<any> | null = null;\r\n  private routeLayer: L.LayerGroup | null = null;\r\n  private nearestMarkers: L.Marker[] = [];\r\n\r\n  public evacuationCenters: EvacuationCenter[] = [];\r\n  public centerCounts = {\r\n    earthquake: 0,\r\n    typhoon: 0,\r\n    flood: 0,\r\n    total: 0\r\n  };\r\n\r\n  public travelMode: 'walking' | 'cycling' | 'driving' = 'walking';\r\n  public routeTime: number = 0;\r\n  public routeDistance: number = 0;\r\n  public userLocation: { lat: number, lng: number } | null = null;\r\n\r\n  private loadingCtrl = inject(LoadingController);\r\n  private toastCtrl = inject(ToastController);\r\n  private alertCtrl = inject(AlertController);\r\n  private http = inject(HttpClient);\r\n  private router = inject(Router);\r\n  private mapboxRouting = inject(MapboxRoutingService);\r\n\r\n  async ngOnInit() {\r\n    console.log('🗺️ ALL MAPS: Initializing...');\r\n    await this.loadAllMaps();\r\n  }\r\n\r\n  async loadAllMaps() {\r\n    const loading = await this.loadingCtrl.create({\r\n      message: 'Loading all evacuation centers...',\r\n      spinner: 'crescent'\r\n    });\r\n    await loading.present();\r\n\r\n    try {\r\n      // Get user location\r\n      const position = await Geolocation.getCurrentPosition({\r\n        enableHighAccuracy: true,\r\n        timeout: 20000\r\n      });\r\n\r\n      const userLat = position.coords.latitude;\r\n      const userLng = position.coords.longitude;\r\n\r\n      this.userLocation = { lat: userLat, lng: userLng };\r\n\r\n      console.log(`🗺️ ALL MAPS: User location [${userLat}, ${userLng}]`);\r\n\r\n      // Initialize map\r\n      this.initializeMap(userLat, userLng);\r\n\r\n      // Load ALL evacuation centers with category-specific markers\r\n      await this.loadAllCenters(userLat, userLng);\r\n\r\n      await loading.dismiss();\r\n\r\n      // Show success message\r\n      const toast = await this.toastCtrl.create({\r\n        message: `🗺️ Showing all ${this.centerCounts.total} evacuation centers`,\r\n        duration: 3000,\r\n        color: 'secondary',\r\n        position: 'top'\r\n      });\r\n      await toast.present();\r\n\r\n    } catch (error) {\r\n      await loading.dismiss();\r\n      console.error('🗺️ ALL MAPS: Error loading map', error);\r\n\r\n      const alert = await this.alertCtrl.create({\r\n        header: 'Location Error',\r\n        message: 'Unable to get your location. Please enable GPS and try again.',\r\n        buttons: [\r\n          {\r\n            text: 'Retry',\r\n            handler: () => this.loadAllMaps()\r\n          },\r\n          {\r\n            text: 'Go Back',\r\n            handler: () => this.router.navigate(['/tabs/home'])\r\n          }\r\n        ]\r\n      });\r\n      await alert.present();\r\n    }\r\n  }\r\n\r\n  initializeMap(lat: number, lng: number) {\r\n    console.log(`🗺️ ALL MAPS: Initializing map at [${lat}, ${lng}]`);\r\n\r\n    if (this.map) {\r\n      this.map.remove();\r\n    }\r\n\r\n    this.map = L.map('all-maps').setView([lat, lng], 12);\r\n\r\n    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {\r\n      attribution: 'OpenStreetMap contributors'\r\n    }).addTo(this.map);\r\n\r\n    // Add user marker\r\n    this.userMarker = L.marker([lat, lng], {\r\n      icon: L.icon({\r\n        iconUrl: 'assets/icons/user-location.png',\r\n        iconSize: [30, 30],\r\n        iconAnchor: [15, 30]\r\n      })\r\n    }).addTo(this.map);\r\n\r\n    this.userMarker.bindPopup('📍 You are here!').openPopup();\r\n  }\r\n\r\n  async loadAllCenters(userLat: number, userLng: number) {\r\n    try {\r\n      console.log('🗺️ ALL MAPS: Fetching all evacuation centers...');\r\n\r\n      // Get all centers from API\r\n      const allCenters = await firstValueFrom(\r\n        this.http.get<EvacuationCenter[]>(`${environment.apiUrl}/evacuation-centers`)\r\n      );\r\n\r\n      console.log('🗺️ ALL MAPS: Total centers received:', allCenters?.length || 0);\r\n\r\n      this.evacuationCenters = allCenters || [];\r\n\r\n      // Count centers by disaster type\r\n      this.centerCounts.earthquake = this.evacuationCenters.filter(c => c.disaster_type === 'Earthquake').length;\r\n      this.centerCounts.typhoon = this.evacuationCenters.filter(c => c.disaster_type === 'Typhoon').length;\r\n      this.centerCounts.flood = this.evacuationCenters.filter(c => c.disaster_type === 'Flash Flood').length;\r\n      this.centerCounts.total = this.evacuationCenters.length;\r\n\r\n      console.log('🗺️ ALL MAPS: Center counts:', this.centerCounts);\r\n\r\n      if (this.evacuationCenters.length === 0) {\r\n        const alert = await this.alertCtrl.create({\r\n          header: 'No Evacuation Centers',\r\n          message: 'No evacuation centers found in the database.',\r\n          buttons: ['OK']\r\n        });\r\n        await alert.present();\r\n        return;\r\n      }\r\n\r\n      // Add all markers with appropriate colors\r\n      this.evacuationCenters.forEach(center => {\r\n        const lat = Number(center.latitude);\r\n        const lng = Number(center.longitude);\r\n\r\n        if (!isNaN(lat) && !isNaN(lng)) {\r\n          // Get icon based on disaster type\r\n          let iconUrl = 'assets/Location.png';\r\n          let colorEmoji = '⚪';\r\n\r\n          switch(center.disaster_type) {\r\n            case 'Earthquake':\r\n              iconUrl = 'assets/forEarthquake.png';\r\n              colorEmoji = '🟠';\r\n              break;\r\n            case 'Typhoon':\r\n              iconUrl = 'assets/forTyphoon.png';\r\n              colorEmoji = '🟢';\r\n              break;\r\n            case 'Flood':\r\n              iconUrl = 'assets/forFlood.png';\r\n              colorEmoji = '🔵';\r\n              break;\r\n          }\r\n\r\n          const marker = L.marker([lat, lng], {\r\n            icon: L.icon({\r\n              iconUrl: iconUrl,\r\n              iconSize: [40, 40],\r\n              iconAnchor: [20, 40],\r\n              popupAnchor: [0, -40]\r\n            })\r\n          });\r\n\r\n          const distance = this.calculateDistance(userLat, userLng, lat, lng);\r\n\r\n          // Make marker clickable with transportation options\r\n          marker.on('click', () => {\r\n            this.showTransportationOptions(center);\r\n          });\r\n\r\n          marker.bindPopup(`\r\n            <div class=\"evacuation-popup\">\r\n              <h3>${colorEmoji} ${center.name}</h3>\r\n              <p><strong>Type:</strong> ${center.disaster_type || 'General'}</p>\r\n              <p><strong>Distance:</strong> ${(distance / 1000).toFixed(2)} km</p>\r\n              <p><strong>Capacity:</strong> ${center.capacity || 'N/A'}</p>\r\n              <p><em>Click marker for route options</em></p>\r\n            </div>\r\n          `);\r\n\r\n          marker.addTo(this.map);\r\n          console.log(`🗺️ Added ${center.disaster_type} marker: ${center.name}`);\r\n        }\r\n      });\r\n\r\n      // No auto-routing for \"See Whole Map\" - only show markers\r\n\r\n      // Fit map to show all centers\r\n      if (this.evacuationCenters.length > 0) {\r\n        const bounds = L.latLngBounds([]);\r\n        bounds.extend([userLat, userLng]);\r\n\r\n        this.evacuationCenters.forEach(center => {\r\n          bounds.extend([Number(center.latitude), Number(center.longitude)]);\r\n        });\r\n\r\n        this.map.fitBounds(bounds, { padding: [50, 50] });\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('🗺️ ALL MAPS: Error loading centers', error);\r\n\r\n      const toast = await this.toastCtrl.create({\r\n        message: 'Error loading evacuation centers. Please check your connection.',\r\n        duration: 3000,\r\n        color: 'danger'\r\n      });\r\n      await toast.present();\r\n    }\r\n  }\r\n\r\n  calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {\r\n    const R = 6371e3; // meters\r\n    const φ1 = lat1 * Math.PI / 180;\r\n    const φ2 = lat2 * Math.PI / 180;\r\n    const Δφ = (lat2 - lat1) * Math.PI / 180;\r\n    const Δλ = (lon2 - lon1) * Math.PI / 180;\r\n\r\n    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +\r\n              Math.cos(φ1) * Math.cos(φ2) *\r\n              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);\r\n\r\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\r\n    return R * c;\r\n  }\r\n\r\n  // Auto-route to 2 nearest evacuation centers\r\n  async routeToTwoNearestCenters() {\r\n    if (!this.userLocation || this.evacuationCenters.length === 0) {\r\n      console.log('🗺️ ALL MAPS: No user location or evacuation centers available');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      console.log('🗺️ ALL MAPS: Finding 2 nearest centers...');\r\n\r\n      // Find 2 nearest centers\r\n      const nearestCenters = this.getTwoNearestCenters(\r\n        this.userLocation.lat,\r\n        this.userLocation.lng\r\n      );\r\n\r\n      if (nearestCenters.length === 0) {\r\n        const toast = await this.toastCtrl.create({\r\n          message: 'No evacuation centers found nearby',\r\n          duration: 3000,\r\n          color: 'warning'\r\n        });\r\n        await toast.present();\r\n        return;\r\n      }\r\n\r\n      // Clear previous routes and markers\r\n      this.clearRoutes();\r\n\r\n      // Add pulsing markers for nearest centers\r\n      this.addPulsingMarkers(nearestCenters);\r\n\r\n      // Calculate and display routes\r\n      await this.calculateRoutes(nearestCenters);\r\n\r\n      // Show success message\r\n      const toast = await this.toastCtrl.create({\r\n        message: `🗺️ Showing routes to ${nearestCenters.length} nearest centers via ${this.travelMode}`,\r\n        duration: 3000,\r\n        color: 'success',\r\n        position: 'top'\r\n      });\r\n      await toast.present();\r\n\r\n    } catch (error) {\r\n      console.error('🗺️ ALL MAPS: Error calculating routes', error);\r\n\r\n      const toast = await this.toastCtrl.create({\r\n        message: 'Error calculating routes. Please try again.',\r\n        duration: 3000,\r\n        color: 'danger'\r\n      });\r\n      await toast.present();\r\n    }\r\n  }\r\n\r\n  // Get 2 nearest evacuation centers\r\n  getTwoNearestCenters(userLat: number, userLng: number): EvacuationCenter[] {\r\n    const centersWithDistance = this.evacuationCenters.map(center => ({\r\n      ...center,\r\n      distance: this.calculateDistance(\r\n        userLat, userLng,\r\n        Number(center.latitude), Number(center.longitude)\r\n      )\r\n    }));\r\n\r\n    // Sort by distance and take first 2\r\n    return centersWithDistance\r\n      .sort((a, b) => a.distance - b.distance)\r\n      .slice(0, 2);\r\n  }\r\n\r\n  // Add pulsing markers for nearest centers\r\n  addPulsingMarkers(centers: EvacuationCenter[]) {\r\n    centers.forEach((center, index) => {\r\n      const lat = Number(center.latitude);\r\n      const lng = Number(center.longitude);\r\n\r\n      if (!isNaN(lat) && !isNaN(lng)) {\r\n        // Get disaster-specific icon and color\r\n        let iconUrl = 'assets/Location.png';\r\n        let pulseColor = '#3880ff';\r\n\r\n        if (center.disaster_type === 'Earthquake') {\r\n          iconUrl = 'assets/forEarthquake.png';\r\n          pulseColor = '#ff9500';\r\n        } else if (center.disaster_type === 'Typhoon') {\r\n          iconUrl = 'assets/forTyphoon.png';\r\n          pulseColor = '#2dd36f';\r\n        } else if (center.disaster_type === 'Flood') {\r\n          iconUrl = 'assets/forFlood.png';\r\n          pulseColor = '#3dc2ff';\r\n        }\r\n\r\n        // Create pulsing marker\r\n        const pulsingIcon = L.divIcon({\r\n          className: 'pulsing-marker',\r\n          html: `\r\n            <div class=\"pulse-container\">\r\n              <div class=\"pulse\" style=\"background-color: ${pulseColor}\"></div>\r\n              <img src=\"${iconUrl}\" class=\"marker-icon\" />\r\n              <div class=\"marker-label\">${index + 1}</div>\r\n            </div>\r\n          `,\r\n          iconSize: [50, 50],\r\n          iconAnchor: [25, 50]\r\n        });\r\n\r\n        const marker = L.marker([lat, lng], { icon: pulsingIcon });\r\n\r\n        marker.bindPopup(`\r\n          <div class=\"evacuation-popup nearest-popup\">\r\n            <h3>🎯 Nearest Center #${index + 1}</h3>\r\n            <h4>${center.name}</h4>\r\n            <p><strong>Type:</strong> ${center.disaster_type}</p>\r\n            <p><strong>Distance:</strong> ${((center as any).distance / 1000).toFixed(2)} km</p>\r\n            <p><strong>Capacity:</strong> ${center.capacity || 'N/A'}</p>\r\n          </div>\r\n        `);\r\n\r\n        marker.addTo(this.map);\r\n        this.nearestMarkers.push(marker);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Calculate routes to nearest centers\r\n  async calculateRoutes(centers: EvacuationCenter[]) {\r\n    if (!this.userLocation) return;\r\n\r\n    this.routeLayer = L.layerGroup().addTo(this.map);\r\n\r\n    for (let i = 0; i < centers.length; i++) {\r\n      const center = centers[i];\r\n      const lat = Number(center.latitude);\r\n      const lng = Number(center.longitude);\r\n\r\n      if (!isNaN(lat) && !isNaN(lng)) {\r\n        try {\r\n          // Convert travel mode to Mapbox profile\r\n          const mapboxProfile = this.mapboxRouting.convertTravelModeToProfile(this.travelMode);\r\n\r\n          const routeData = await this.mapboxRouting.getDirections(\r\n            this.userLocation.lng, this.userLocation.lat,\r\n            lng, lat,\r\n            mapboxProfile,\r\n            {\r\n              geometries: 'geojson',\r\n              overview: 'simplified',\r\n              steps: false\r\n            }\r\n          );\r\n\r\n          if (routeData && routeData.routes && routeData.routes.length > 0) {\r\n            const route = routeData.routes[0];\r\n\r\n            // Get color based on disaster type\r\n            let routeColor = '#3880ff';\r\n            if (center.disaster_type === 'Earthquake') routeColor = '#ff9500';\r\n            else if (center.disaster_type === 'Typhoon') routeColor = '#2dd36f';\r\n            else if (center.disaster_type === 'Flash Flood') routeColor = '#3dc2ff';\r\n\r\n            // Draw route\r\n            const routeLine = L.polyline(\r\n              route.geometry.coordinates.map((coord: number[]) => [coord[1], coord[0]]),\r\n              {\r\n                color: routeColor,\r\n                weight: 4,\r\n                opacity: 0.8,\r\n                dashArray: i === 0 ? undefined : '10, 10' // Solid for first, dashed for second\r\n              }\r\n            );\r\n\r\n            routeLine.addTo(this.routeLayer);\r\n\r\n            // Store route info for first center\r\n            if (i === 0) {\r\n              this.routeTime = route.duration;\r\n              this.routeDistance = route.distance;\r\n            }\r\n\r\n            console.log(`🗺️ Route ${i + 1}: ${(route.distance/1000).toFixed(2)}km, ${(route.duration/60).toFixed(0)}min`);\r\n          }\r\n        } catch (error) {\r\n          console.error(`🗺️ Error calculating route to center ${i + 1}:`, error);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // Clear previous routes and markers\r\n  clearRoutes() {\r\n    if (this.routeLayer) {\r\n      this.map.removeLayer(this.routeLayer);\r\n      this.routeLayer = null;\r\n    }\r\n\r\n    this.nearestMarkers.forEach(marker => {\r\n      this.map.removeLayer(marker);\r\n    });\r\n    this.nearestMarkers = [];\r\n\r\n    this.routeTime = 0;\r\n    this.routeDistance = 0;\r\n  }\r\n\r\n  // Handle travel mode change from ion-segment\r\n  onTravelModeChange(event: any) {\r\n    const value = event.detail.value;\r\n    if (value === 'walking' || value === 'cycling' || value === 'driving') {\r\n      this.changeTravelMode(value);\r\n    }\r\n  }\r\n\r\n  // Change travel mode\r\n  async changeTravelMode(mode: 'walking' | 'cycling' | 'driving') {\r\n    this.travelMode = mode;\r\n\r\n    const toast = await this.toastCtrl.create({\r\n      message: `🚶‍♂️ Travel mode changed to ${mode}`,\r\n      duration: 2000,\r\n      color: 'primary'\r\n    });\r\n    await toast.present();\r\n\r\n    // Recalculate routes with new travel mode\r\n    if (this.userLocation && this.evacuationCenters.length > 0) {\r\n      await this.routeToTwoNearestCenters();\r\n    }\r\n  }\r\n\r\n  // Show transportation options when marker is clicked\r\n  async showTransportationOptions(center: EvacuationCenter) {\r\n    const alert = await this.alertCtrl.create({\r\n      header: `Route to ${center.name}`,\r\n      message: 'Choose your transportation mode:',\r\n      buttons: [\r\n        {\r\n          text: '🚶‍♂️ Walk',\r\n          handler: () => {\r\n            this.routeToCenter(center, 'walking');\r\n          }\r\n        },\r\n        {\r\n          text: '🚴‍♂️ Cycle',\r\n          handler: () => {\r\n            this.routeToCenter(center, 'cycling');\r\n          }\r\n        },\r\n        {\r\n          text: '🚗 Drive',\r\n          handler: () => {\r\n            this.routeToCenter(center, 'driving');\r\n          }\r\n        },\r\n        {\r\n          text: 'Cancel',\r\n          role: 'cancel'\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  // Route to specific center with chosen transportation mode\r\n  async routeToCenter(center: EvacuationCenter, travelMode: 'walking' | 'cycling' | 'driving') {\r\n    if (!this.userLocation) return;\r\n\r\n    try {\r\n      // Clear existing routes\r\n      this.clearRoutes();\r\n\r\n      const lat = Number(center.latitude);\r\n      const lng = Number(center.longitude);\r\n\r\n      if (!isNaN(lat) && !isNaN(lng)) {\r\n        const mapboxProfile = this.mapboxRouting.convertTravelModeToProfile(travelMode);\r\n\r\n        const routeData = await this.mapboxRouting.getDirections(\r\n          this.userLocation.lng, this.userLocation.lat,\r\n          lng, lat,\r\n          mapboxProfile,\r\n          {\r\n            geometries: 'geojson',\r\n            overview: 'full',\r\n            steps: false\r\n          }\r\n        );\r\n\r\n        if (routeData && routeData.routes && routeData.routes.length > 0) {\r\n          const route = routeData.routes[0];\r\n\r\n          // Use disaster-specific color\r\n          let routeColor = '#3880ff'; // Default blue\r\n          let colorEmoji = '🔵';\r\n\r\n          if (center.disaster_type === 'Earthquake') {\r\n            routeColor = '#ff9500'; // Orange\r\n            colorEmoji = '🟠';\r\n          } else if (center.disaster_type === 'Typhoon') {\r\n            routeColor = '#2dd36f'; // Green\r\n            colorEmoji = '🟢';\r\n          } else if (center.disaster_type === 'Flash Flood') {\r\n            routeColor = '#3dc2ff'; // Blue\r\n            colorEmoji = '🔵';\r\n          }\r\n\r\n          this.routeLayer = L.layerGroup().addTo(this.map);\r\n\r\n          // Draw route\r\n          const routeLine = L.polyline(\r\n            route.geometry.coordinates.map((coord: number[]) => [coord[1], coord[0]]),\r\n            {\r\n              color: routeColor,\r\n              weight: 5,\r\n              opacity: 0.8\r\n            }\r\n          );\r\n\r\n          routeLine.addTo(this.routeLayer);\r\n\r\n          // Show route info\r\n          const toast = await this.toastCtrl.create({\r\n            message: `${colorEmoji} Route: ${(route.distance/1000).toFixed(2)}km, ${(route.duration/60).toFixed(0)}min via ${travelMode}`,\r\n            duration: 4000,\r\n            color: 'primary'\r\n          });\r\n          await toast.present();\r\n\r\n          // Fit map to route\r\n          this.map.fitBounds(routeLine.getBounds(), { padding: [50, 50] });\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('🗺️ Error routing to center:', error);\r\n\r\n      const toast = await this.toastCtrl.create({\r\n        message: 'Error calculating route. Please try again.',\r\n        duration: 3000,\r\n        color: 'danger'\r\n      });\r\n      await toast.present();\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigate(['/tabs/home']);\r\n  }\r\n\r\n  ionViewWillLeave() {\r\n    this.clearRoutes();\r\n    if (this.map) {\r\n      this.map.remove();\r\n    }\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-button (click)=\"goBack()\">\r\n        <ion-icon name=\"chevron-back-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n    <ion-title>🗺️ All Evacuation Centers</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div id=\"all-maps\" style=\"height: 100%; width: 100%;\"></div>\r\n\r\n  <!-- Floating info card -->\r\n  <div class=\"floating-info\">\r\n    <ion-card>\r\n      <ion-card-content>\r\n        <div class=\"info-header\">\r\n          <ion-icon name=\"map\" color=\"secondary\"></ion-icon>\r\n          <span>All Centers: {{ centerCounts.total }}</span>\r\n        </div>\r\n\r\n        <div class=\"disaster-counts\">\r\n          <div class=\"count-row\">\r\n            <span class=\"disaster-icon\">🟠</span>\r\n            <span class=\"disaster-label\">Earthquake:</span>\r\n            <span class=\"disaster-count\">{{ centerCounts.earthquake }}</span>\r\n          </div>\r\n\r\n          <div class=\"count-row\">\r\n            <span class=\"disaster-icon\">🟢</span>\r\n            <span class=\"disaster-label\">Typhoon:</span>\r\n            <span class=\"disaster-count\">{{ centerCounts.typhoon }}</span>\r\n          </div>\r\n\r\n          <div class=\"count-row\">\r\n            <span class=\"disaster-icon\">🔵</span>\r\n            <span class=\"disaster-label\">Flood:</span>\r\n            <span class=\"disaster-count\">{{ centerCounts.flood }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"info-text\">\r\n          Complete overview of all evacuation centers by disaster type\r\n        </div>\r\n      </ion-card-content>\r\n    </ion-card>\r\n  </div>\r\n\r\n  <!-- Transportation Mode Controls -->\r\n  <div class=\"transport-controls\">\r\n    <ion-card>\r\n      <ion-card-content>\r\n        <div class=\"transport-header\">\r\n          <ion-icon name=\"navigate-outline\" color=\"primary\"></ion-icon>\r\n          <span>Travel Mode</span>\r\n        </div>\r\n        <ion-segment [(ngModel)]=\"travelMode\" (ionChange)=\"onTravelModeChange($event)\">\r\n          <ion-segment-button value=\"walking\">\r\n            <ion-icon name=\"walk-outline\"></ion-icon>\r\n            <ion-label>Walk</ion-label>\r\n          </ion-segment-button>\r\n          <ion-segment-button value=\"cycling\">\r\n            <ion-icon name=\"bicycle-outline\"></ion-icon>\r\n            <ion-label>Bike</ion-label>\r\n          </ion-segment-button>\r\n          <ion-segment-button value=\"driving\">\r\n            <ion-icon name=\"car-outline\"></ion-icon>\r\n            <ion-label>Drive</ion-label>\r\n          </ion-segment-button>\r\n        </ion-segment>\r\n      </ion-card-content>\r\n    </ion-card>\r\n  </div>\r\n\r\n  <!-- Route Information -->\r\n  <div *ngIf=\"routeTime && routeDistance\" class=\"route-info\">\r\n    <ion-card>\r\n      <ion-card-content>\r\n        <div class=\"route-header\">\r\n          <ion-icon name=\"time-outline\" color=\"success\"></ion-icon>\r\n          <span>Route to Nearest Center</span>\r\n        </div>\r\n        <div class=\"route-details\">\r\n          <div class=\"route-item\">\r\n            <ion-icon [name]=\"travelMode === 'walking' ? 'walk-outline' : travelMode === 'cycling' ? 'bicycle-outline' : 'car-outline'\"></ion-icon>\r\n            <span>{{ (routeTime/60).toFixed(0) }} min</span>\r\n          </div>\r\n          <div class=\"route-item\">\r\n            <ion-icon name=\"location-outline\"></ion-icon>\r\n            <span>{{ (routeDistance/1000).toFixed(2) }} km</span>\r\n          </div>\r\n        </div>\r\n      </ion-card-content>\r\n    </ion-card>\r\n  </div>\r\n\r\n  <!-- Route Button -->\r\n  <ion-fab vertical=\"bottom\" horizontal=\"end\" slot=\"fixed\">\r\n    <ion-fab-button color=\"primary\" (click)=\"routeToTwoNearestCenters()\">\r\n      <ion-icon name=\"navigate-outline\"></ion-icon>\r\n    </ion-fab-button>\r\n    <ion-label class=\"fab-label\">Route to 2 Nearest Centers</ion-label>\r\n  </ion-fab>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,QAAmB;;;ACmEjB,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2D,GAAA,UAAA,EAC/C,GAAA,kBAAA,EACU,GAAA,OAAA,EAAA;AAEd,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,GAAA,yBAAA;AAAuB,IAAA,uBAAA,EAAO;AAEtC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2B,GAAA,OAAA,EAAA;AAEvB,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,EAAA;AAAmC,IAAA,uBAAA,EAAO;AAElD,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,YAAA,EAAA;AACA,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,EAAA;AAAwC,IAAA,uBAAA,EAAO,EACjD,EACF,EACW,EACV;;;;AATO,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,eAAA,YAAA,iBAAA,OAAA,eAAA,YAAA,oBAAA,aAAA;AACJ,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,YAAA,IAAA,QAAA,CAAA,GAAA,MAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,gBAAA,KAAA,QAAA,CAAA,GAAA,KAAA;;;AD5DZ,IAAO,cAAP,MAAO,aAAW;EAPxB,cAAA;AASU,SAAA,aAAmC;AACnC,SAAA,aAAkC;AAClC,SAAA,iBAA6B,CAAA;AAE9B,SAAA,oBAAwC,CAAA;AACxC,SAAA,eAAe;MACpB,YAAY;MACZ,SAAS;MACT,OAAO;MACP,OAAO;;AAGF,SAAA,aAAgD;AAChD,SAAA,YAAoB;AACpB,SAAA,gBAAwB;AACxB,SAAA,eAAoD;AAEnD,SAAA,cAAc,OAAO,iBAAiB;AACtC,SAAA,YAAY,OAAO,eAAe;AAClC,SAAA,YAAY,OAAO,eAAe;AAClC,SAAA,OAAO,OAAO,UAAU;AACxB,SAAA,SAAS,OAAO,MAAM;AACtB,SAAA,gBAAgB,OAAO,oBAAoB;;EAE7C,WAAQ;;AACZ,cAAQ,IAAI,2CAA+B;AAC3C,YAAM,KAAK,YAAW;IACxB;;EAEM,cAAW;;AACf,YAAM,UAAU,MAAM,KAAK,YAAY,OAAO;QAC5C,SAAS;QACT,SAAS;OACV;AACD,YAAM,QAAQ,QAAO;AAErB,UAAI;AAEF,cAAM,WAAW,MAAM,YAAY,mBAAmB;UACpD,oBAAoB;UACpB,SAAS;SACV;AAED,cAAM,UAAU,SAAS,OAAO;AAChC,cAAM,UAAU,SAAS,OAAO;AAEhC,aAAK,eAAe,EAAE,KAAK,SAAS,KAAK,QAAO;AAEhD,gBAAQ,IAAI,4CAAgC,OAAO,KAAK,OAAO,GAAG;AAGlE,aAAK,cAAc,SAAS,OAAO;AAGnC,cAAM,KAAK,eAAe,SAAS,OAAO;AAE1C,cAAM,QAAQ,QAAO;AAGrB,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS,+BAAmB,KAAK,aAAa,KAAK;UACnD,UAAU;UACV,OAAO;UACP,UAAU;SACX;AACD,cAAM,MAAM,QAAO;MAErB,SAAS,OAAO;AACd,cAAM,QAAQ,QAAO;AACrB,gBAAQ,MAAM,+CAAmC,KAAK;AAEtD,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,QAAQ;UACR,SAAS;UACT,SAAS;YACP;cACE,MAAM;cACN,SAAS,MAAM,KAAK,YAAW;;YAEjC;cACE,MAAM;cACN,SAAS,MAAM,KAAK,OAAO,SAAS,CAAC,YAAY,CAAC;;;SAGvD;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;EAEA,cAAc,KAAa,KAAW;AACpC,YAAQ,IAAI,kDAAsC,GAAG,KAAK,GAAG,GAAG;AAEhE,QAAI,KAAK,KAAK;AACZ,WAAK,IAAI,OAAM;IACjB;AAEA,SAAK,MAAQ,MAAI,UAAU,EAAE,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE;AAEnD,IAAE,YAAU,sDAAsD;MAChE,aAAa;KACd,EAAE,MAAM,KAAK,GAAG;AAGjB,SAAK,aAAe,SAAO,CAAC,KAAK,GAAG,GAAG;MACrC,MAAQ,OAAK;QACX,SAAS;QACT,UAAU,CAAC,IAAI,EAAE;QACjB,YAAY,CAAC,IAAI,EAAE;OACpB;KACF,EAAE,MAAM,KAAK,GAAG;AAEjB,SAAK,WAAW,UAAU,yBAAkB,EAAE,UAAS;EACzD;EAEM,eAAe,SAAiB,SAAe;;AACnD,UAAI;AACF,gBAAQ,IAAI,8DAAkD;AAG9D,cAAM,aAAa,MAAM,eACvB,KAAK,KAAK,IAAwB,GAAG,YAAY,MAAM,qBAAqB,CAAC;AAG/E,gBAAQ,IAAI,qDAAyC,YAAY,UAAU,CAAC;AAE5E,aAAK,oBAAoB,cAAc,CAAA;AAGvC,aAAK,aAAa,aAAa,KAAK,kBAAkB,OAAO,OAAK,EAAE,kBAAkB,YAAY,EAAE;AACpG,aAAK,aAAa,UAAU,KAAK,kBAAkB,OAAO,OAAK,EAAE,kBAAkB,SAAS,EAAE;AAC9F,aAAK,aAAa,QAAQ,KAAK,kBAAkB,OAAO,OAAK,EAAE,kBAAkB,aAAa,EAAE;AAChG,aAAK,aAAa,QAAQ,KAAK,kBAAkB;AAEjD,gBAAQ,IAAI,4CAAgC,KAAK,YAAY;AAE7D,YAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,gBAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;YACxC,QAAQ;YACR,SAAS;YACT,SAAS,CAAC,IAAI;WACf;AACD,gBAAM,MAAM,QAAO;AACnB;QACF;AAGA,aAAK,kBAAkB,QAAQ,YAAS;AACtC,gBAAM,MAAM,OAAO,OAAO,QAAQ;AAClC,gBAAM,MAAM,OAAO,OAAO,SAAS;AAEnC,cAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;AAE9B,gBAAI,UAAU;AACd,gBAAI,aAAa;AAEjB,oBAAO,OAAO,eAAe;cAC3B,KAAK;AACH,0BAAU;AACV,6BAAa;AACb;cACF,KAAK;AACH,0BAAU;AACV,6BAAa;AACb;cACF,KAAK;AACH,0BAAU;AACV,6BAAa;AACb;YACJ;AAEA,kBAAMA,UAAW,SAAO,CAAC,KAAK,GAAG,GAAG;cAClC,MAAQ,OAAK;gBACX;gBACA,UAAU,CAAC,IAAI,EAAE;gBACjB,YAAY,CAAC,IAAI,EAAE;gBACnB,aAAa,CAAC,GAAG,GAAG;eACrB;aACF;AAED,kBAAM,WAAW,KAAK,kBAAkB,SAAS,SAAS,KAAK,GAAG;AAGlE,YAAAA,QAAO,GAAG,SAAS,MAAK;AACtB,mBAAK,0BAA0B,MAAM;YACvC,CAAC;AAED,YAAAA,QAAO,UAAU;;oBAEP,UAAU,IAAI,OAAO,IAAI;0CACH,OAAO,iBAAiB,SAAS;+CAC5B,WAAW,KAAM,QAAQ,CAAC,CAAC;8CAC5B,OAAO,YAAY,KAAK;;;WAG3D;AAED,YAAAA,QAAO,MAAM,KAAK,GAAG;AACrB,oBAAQ,IAAI,yBAAa,OAAO,aAAa,YAAY,OAAO,IAAI,EAAE;UACxE;QACF,CAAC;AAKD,YAAI,KAAK,kBAAkB,SAAS,GAAG;AACrC,gBAAM,SAAW,eAAa,CAAA,CAAE;AAChC,iBAAO,OAAO,CAAC,SAAS,OAAO,CAAC;AAEhC,eAAK,kBAAkB,QAAQ,YAAS;AACtC,mBAAO,OAAO,CAAC,OAAO,OAAO,QAAQ,GAAG,OAAO,OAAO,SAAS,CAAC,CAAC;UACnE,CAAC;AAED,eAAK,IAAI,UAAU,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,EAAC,CAAE;QAClD;MAEF,SAAS,OAAO;AACd,gBAAQ,MAAM,mDAAuC,KAAK;AAE1D,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS;UACT,UAAU;UACV,OAAO;SACR;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;EAEA,kBAAkB,MAAc,MAAc,MAAc,MAAY;AACtE,UAAM,IAAI;AACV,UAAM,UAAK,OAAO,KAAK,KAAK;AAC5B,UAAM,UAAK,OAAO,KAAK,KAAK;AAC5B,UAAM,gBAAM,OAAO,QAAQ,KAAK,KAAK;AACrC,UAAM,gBAAM,OAAO,QAAQ,KAAK,KAAK;AAErC,UAAM,IAAI,KAAK,IAAI,eAAK,CAAC,IAAI,KAAK,IAAI,eAAK,CAAC,IAClC,KAAK,IAAI,OAAE,IAAI,KAAK,IAAI,OAAE,IAC1B,KAAK,IAAI,eAAK,CAAC,IAAI,KAAK,IAAI,eAAK,CAAC;AAE5C,UAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AACvD,WAAO,IAAI;EACb;;EAGM,2BAAwB;;AAC5B,UAAI,CAAC,KAAK,gBAAgB,KAAK,kBAAkB,WAAW,GAAG;AAC7D,gBAAQ,IAAI,4EAAgE;AAC5E;MACF;AAEA,UAAI;AACF,gBAAQ,IAAI,wDAA4C;AAGxD,cAAM,iBAAiB,KAAK,qBAC1B,KAAK,aAAa,KAClB,KAAK,aAAa,GAAG;AAGvB,YAAI,eAAe,WAAW,GAAG;AAC/B,gBAAMC,SAAQ,MAAM,KAAK,UAAU,OAAO;YACxC,SAAS;YACT,UAAU;YACV,OAAO;WACR;AACD,gBAAMA,OAAM,QAAO;AACnB;QACF;AAGA,aAAK,YAAW;AAGhB,aAAK,kBAAkB,cAAc;AAGrC,cAAM,KAAK,gBAAgB,cAAc;AAGzC,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS,qCAAyB,eAAe,MAAM,wBAAwB,KAAK,UAAU;UAC9F,UAAU;UACV,OAAO;UACP,UAAU;SACX;AACD,cAAM,MAAM,QAAO;MAErB,SAAS,OAAO;AACd,gBAAQ,MAAM,sDAA0C,KAAK;AAE7D,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS;UACT,UAAU;UACV,OAAO;SACR;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;;EAGA,qBAAqB,SAAiB,SAAe;AACnD,UAAM,sBAAsB,KAAK,kBAAkB,IAAI,YAAW,iCAC7D,SAD6D;MAEhE,UAAU,KAAK,kBACb,SAAS,SACT,OAAO,OAAO,QAAQ,GAAG,OAAO,OAAO,SAAS,CAAC;MAEnD;AAGF,WAAO,oBACJ,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE,QAAQ,EACtC,MAAM,GAAG,CAAC;EACf;;EAGA,kBAAkB,SAA2B;AAC3C,YAAQ,QAAQ,CAAC,QAAQ,UAAS;AAChC,YAAM,MAAM,OAAO,OAAO,QAAQ;AAClC,YAAM,MAAM,OAAO,OAAO,SAAS;AAEnC,UAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;AAE9B,YAAI,UAAU;AACd,YAAI,aAAa;AAEjB,YAAI,OAAO,kBAAkB,cAAc;AACzC,oBAAU;AACV,uBAAa;QACf,WAAW,OAAO,kBAAkB,WAAW;AAC7C,oBAAU;AACV,uBAAa;QACf,WAAW,OAAO,kBAAkB,SAAS;AAC3C,oBAAU;AACV,uBAAa;QACf;AAGA,cAAM,cAAgB,UAAQ;UAC5B,WAAW;UACX,MAAM;;4DAE4C,UAAU;0BAC5C,OAAO;0CACS,QAAQ,CAAC;;;UAGzC,UAAU,CAAC,IAAI,EAAE;UACjB,YAAY,CAAC,IAAI,EAAE;SACpB;AAED,cAAMD,UAAW,SAAO,CAAC,KAAK,GAAG,GAAG,EAAE,MAAM,YAAW,CAAE;AAEzD,QAAAA,QAAO,UAAU;;4CAEY,QAAQ,CAAC;kBAC5B,OAAO,IAAI;wCACW,OAAO,aAAa;6CACd,OAAe,WAAW,KAAM,QAAQ,CAAC,CAAC;4CAC5C,OAAO,YAAY,KAAK;;SAE3D;AAED,QAAAA,QAAO,MAAM,KAAK,GAAG;AACrB,aAAK,eAAe,KAAKA,OAAM;MACjC;IACF,CAAC;EACH;;EAGM,gBAAgB,SAA2B;;AAC/C,UAAI,CAAC,KAAK;AAAc;AAExB,WAAK,aAAe,aAAU,EAAG,MAAM,KAAK,GAAG;AAE/C,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAM,SAAS,QAAQ,CAAC;AACxB,cAAM,MAAM,OAAO,OAAO,QAAQ;AAClC,cAAM,MAAM,OAAO,OAAO,SAAS;AAEnC,YAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;AAC9B,cAAI;AAEF,kBAAM,gBAAgB,KAAK,cAAc,2BAA2B,KAAK,UAAU;AAEnF,kBAAM,YAAY,MAAM,KAAK,cAAc,cACzC,KAAK,aAAa,KAAK,KAAK,aAAa,KACzC,KAAK,KACL,eACA;cACE,YAAY;cACZ,UAAU;cACV,OAAO;aACR;AAGH,gBAAI,aAAa,UAAU,UAAU,UAAU,OAAO,SAAS,GAAG;AAChE,oBAAM,QAAQ,UAAU,OAAO,CAAC;AAGhC,kBAAI,aAAa;AACjB,kBAAI,OAAO,kBAAkB;AAAc,6BAAa;uBAC/C,OAAO,kBAAkB;AAAW,6BAAa;uBACjD,OAAO,kBAAkB;AAAe,6BAAa;AAG9D,oBAAM,YAAc,WAClB,MAAM,SAAS,YAAY,IAAI,CAAC,UAAoB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GACxE;gBACE,OAAO;gBACP,QAAQ;gBACR,SAAS;gBACT,WAAW,MAAM,IAAI,SAAY;;eAClC;AAGH,wBAAU,MAAM,KAAK,UAAU;AAG/B,kBAAI,MAAM,GAAG;AACX,qBAAK,YAAY,MAAM;AACvB,qBAAK,gBAAgB,MAAM;cAC7B;AAEA,sBAAQ,IAAI,yBAAa,IAAI,CAAC,MAAM,MAAM,WAAS,KAAM,QAAQ,CAAC,CAAC,QAAQ,MAAM,WAAS,IAAI,QAAQ,CAAC,CAAC,KAAK;YAC/G;UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,qDAAyC,IAAI,CAAC,KAAK,KAAK;UACxE;QACF;MACF;IACF;;;EAGA,cAAW;AACT,QAAI,KAAK,YAAY;AACnB,WAAK,IAAI,YAAY,KAAK,UAAU;AACpC,WAAK,aAAa;IACpB;AAEA,SAAK,eAAe,QAAQ,CAAAA,YAAS;AACnC,WAAK,IAAI,YAAYA,OAAM;IAC7B,CAAC;AACD,SAAK,iBAAiB,CAAA;AAEtB,SAAK,YAAY;AACjB,SAAK,gBAAgB;EACvB;;EAGA,mBAAmB,OAAU;AAC3B,UAAM,QAAQ,MAAM,OAAO;AAC3B,QAAI,UAAU,aAAa,UAAU,aAAa,UAAU,WAAW;AACrE,WAAK,iBAAiB,KAAK;IAC7B;EACF;;EAGM,iBAAiB,MAAuC;;AAC5D,WAAK,aAAa;AAElB,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC,SAAS,sDAAgC,IAAI;QAC7C,UAAU;QACV,OAAO;OACR;AACD,YAAM,MAAM,QAAO;AAGnB,UAAI,KAAK,gBAAgB,KAAK,kBAAkB,SAAS,GAAG;AAC1D,cAAM,KAAK,yBAAwB;MACrC;IACF;;;EAGM,0BAA0B,QAAwB;;AACtD,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC,QAAQ,YAAY,OAAO,IAAI;QAC/B,SAAS;QACT,SAAS;UACP;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,cAAc,QAAQ,SAAS;YACtC;;UAEF;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,cAAc,QAAQ,SAAS;YACtC;;UAEF;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,cAAc,QAAQ,SAAS;YACtC;;UAEF;YACE,MAAM;YACN,MAAM;;;OAGX;AAED,YAAM,MAAM,QAAO;IACrB;;;EAGM,cAAc,QAA0B,YAA6C;;AACzF,UAAI,CAAC,KAAK;AAAc;AAExB,UAAI;AAEF,aAAK,YAAW;AAEhB,cAAM,MAAM,OAAO,OAAO,QAAQ;AAClC,cAAM,MAAM,OAAO,OAAO,SAAS;AAEnC,YAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;AAC9B,gBAAM,gBAAgB,KAAK,cAAc,2BAA2B,UAAU;AAE9E,gBAAM,YAAY,MAAM,KAAK,cAAc,cACzC,KAAK,aAAa,KAAK,KAAK,aAAa,KACzC,KAAK,KACL,eACA;YACE,YAAY;YACZ,UAAU;YACV,OAAO;WACR;AAGH,cAAI,aAAa,UAAU,UAAU,UAAU,OAAO,SAAS,GAAG;AAChE,kBAAM,QAAQ,UAAU,OAAO,CAAC;AAGhC,gBAAI,aAAa;AACjB,gBAAI,aAAa;AAEjB,gBAAI,OAAO,kBAAkB,cAAc;AACzC,2BAAa;AACb,2BAAa;YACf,WAAW,OAAO,kBAAkB,WAAW;AAC7C,2BAAa;AACb,2BAAa;YACf,WAAW,OAAO,kBAAkB,eAAe;AACjD,2BAAa;AACb,2BAAa;YACf;AAEA,iBAAK,aAAe,aAAU,EAAG,MAAM,KAAK,GAAG;AAG/C,kBAAM,YAAc,WAClB,MAAM,SAAS,YAAY,IAAI,CAAC,UAAoB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GACxE;cACE,OAAO;cACP,QAAQ;cACR,SAAS;aACV;AAGH,sBAAU,MAAM,KAAK,UAAU;AAG/B,kBAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;cACxC,SAAS,GAAG,UAAU,YAAY,MAAM,WAAS,KAAM,QAAQ,CAAC,CAAC,QAAQ,MAAM,WAAS,IAAI,QAAQ,CAAC,CAAC,WAAW,UAAU;cAC3H,UAAU;cACV,OAAO;aACR;AACD,kBAAM,MAAM,QAAO;AAGnB,iBAAK,IAAI,UAAU,UAAU,UAAS,GAAI,EAAE,SAAS,CAAC,IAAI,EAAE,EAAC,CAAE;UACjE;QACF;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,4CAAgC,KAAK;AAEnD,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS;UACT,UAAU;UACV,OAAO;SACR;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;EAEA,SAAM;AACJ,SAAK,OAAO,SAAS,CAAC,YAAY,CAAC;EACrC;EAEA,mBAAgB;AACd,SAAK,YAAW;AAChB,QAAI,KAAK,KAAK;AACZ,WAAK,IAAI,OAAM;IACjB;EACF;;;uCAvlBW,cAAW;IAAA;EAAA;;yEAAX,cAAW,WAAA,CAAA,CAAA,cAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,QAAA,OAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,sBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,MAAA,YAAA,GAAA,UAAA,QAAA,SAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,OAAA,SAAA,WAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,QAAA,oBAAA,SAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,aAAA,SAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,QAAA,cAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,QAAA,iBAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,QAAA,aAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,YAAA,UAAA,cAAA,OAAA,QAAA,OAAA,GAAA,CAAA,SAAA,WAAA,GAAA,OAAA,GAAA,CAAA,QAAA,kBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,QAAA,gBAAA,SAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,kBAAA,CAAA,GAAA,UAAA,SAAA,qBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;AC/BxB,QAAA,yBAAA,GAAA,cAAA,CAAA,EAAiC,GAAA,eAAA,CAAA,EACA,GAAA,eAAA,CAAA,EACH,GAAA,cAAA,CAAA;AACZ,QAAA,qBAAA,SAAA,SAAA,mDAAA;AAAA,iBAAS,IAAA,OAAA;QAAQ,CAAA;AAC3B,QAAA,oBAAA,GAAA,YAAA,CAAA;AACF,QAAA,uBAAA,EAAa;AAEf,QAAA,yBAAA,GAAA,WAAA;AAAW,QAAA,iBAAA,GAAA,wCAAA;AAA0B,QAAA,uBAAA,EAAY,EACrC;AAGhB,QAAA,yBAAA,GAAA,eAAA,CAAA;AACE,QAAA,oBAAA,GAAA,OAAA,CAAA;AAGA,QAAA,yBAAA,GAAA,OAAA,CAAA,EAA2B,IAAA,UAAA,EACf,IAAA,kBAAA,EACU,IAAA,OAAA,CAAA;AAEd,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,yBAAA,IAAA,MAAA;AAAM,QAAA,iBAAA,EAAA;AAAqC,QAAA,uBAAA,EAAO;AAGpD,QAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,OAAA,EAAA,EACJ,IAAA,QAAA,EAAA;AACO,QAAA,iBAAA,IAAA,WAAA;AAAE,QAAA,uBAAA;AAC9B,QAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,QAAA,iBAAA,IAAA,aAAA;AAAW,QAAA,uBAAA;AACxC,QAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,QAAA,iBAAA,EAAA;AAA6B,QAAA,uBAAA,EAAO;AAGnE,QAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,QAAA,EAAA;AACO,QAAA,iBAAA,IAAA,WAAA;AAAE,QAAA,uBAAA;AAC9B,QAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,QAAA,iBAAA,IAAA,UAAA;AAAQ,QAAA,uBAAA;AACrC,QAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,QAAA,iBAAA,EAAA;AAA0B,QAAA,uBAAA,EAAO;AAGhE,QAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,QAAA,EAAA;AACO,QAAA,iBAAA,IAAA,WAAA;AAAE,QAAA,uBAAA;AAC9B,QAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,QAAA,iBAAA,IAAA,QAAA;AAAM,QAAA,uBAAA;AACnC,QAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,QAAA,iBAAA,EAAA;AAAwB,QAAA,uBAAA,EAAO,EACxD;AAGR,QAAA,yBAAA,IAAA,OAAA,EAAA;AACE,QAAA,iBAAA,IAAA,gEAAA;AACF,QAAA,uBAAA,EAAM,EACW,EACV;AAIb,QAAA,yBAAA,IAAA,OAAA,EAAA,EAAgC,IAAA,UAAA,EACpB,IAAA,kBAAA,EACU,IAAA,OAAA,EAAA;AAEd,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,yBAAA,IAAA,MAAA;AAAM,QAAA,iBAAA,IAAA,aAAA;AAAW,QAAA,uBAAA,EAAO;AAE1B,QAAA,yBAAA,IAAA,eAAA,EAAA;AAAa,QAAA,2BAAA,iBAAA,SAAA,2DAAA,QAAA;AAAA,UAAA,6BAAA,IAAA,YAAA,MAAA,MAAA,IAAA,aAAA;AAAA,iBAAA;QAAA,CAAA;AAAyB,QAAA,qBAAA,aAAA,SAAA,uDAAA,QAAA;AAAA,iBAAa,IAAA,mBAAA,MAAA;QAA0B,CAAA;AAC3E,QAAA,yBAAA,IAAA,sBAAA,EAAA;AACE,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,yBAAA,IAAA,WAAA;AAAW,QAAA,iBAAA,IAAA,MAAA;AAAI,QAAA,uBAAA,EAAY;AAE7B,QAAA,yBAAA,IAAA,sBAAA,EAAA;AACE,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,yBAAA,IAAA,WAAA;AAAW,QAAA,iBAAA,IAAA,MAAA;AAAI,QAAA,uBAAA,EAAY;AAE7B,QAAA,yBAAA,IAAA,sBAAA,EAAA;AACE,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,yBAAA,IAAA,WAAA;AAAW,QAAA,iBAAA,IAAA,OAAA;AAAK,QAAA,uBAAA,EAAY,EACT,EACT,EACG,EACV;AAIb,QAAA,qBAAA,IAAA,6BAAA,IAAA,GAAA,OAAA,EAAA;AAsBA,QAAA,yBAAA,IAAA,WAAA,EAAA,EAAyD,IAAA,kBAAA,EAAA;AACvB,QAAA,qBAAA,SAAA,SAAA,wDAAA;AAAA,iBAAS,IAAA,yBAAA;QAA0B,CAAA;AACjE,QAAA,oBAAA,IAAA,YAAA,EAAA;AACF,QAAA,uBAAA;AACA,QAAA,yBAAA,IAAA,aAAA,EAAA;AAA6B,QAAA,iBAAA,IAAA,4BAAA;AAA0B,QAAA,uBAAA,EAAY,EAC3D;;;AAxGA,QAAA,qBAAA,eAAA,IAAA;AAWC,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,cAAA,IAAA;AASG,QAAA,oBAAA,CAAA;AAAA,QAAA,6BAAA,iBAAA,IAAA,aAAA,OAAA,EAAA;AAOyB,QAAA,oBAAA,CAAA;AAAA,QAAA,4BAAA,IAAA,aAAA,UAAA;AAMA,QAAA,oBAAA,CAAA;AAAA,QAAA,4BAAA,IAAA,aAAA,OAAA;AAMA,QAAA,oBAAA,CAAA;AAAA,QAAA,4BAAA,IAAA,aAAA,KAAA;AAmBpB,QAAA,oBAAA,EAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,UAAA;AAmBb,QAAA,oBAAA,EAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,aAAA,IAAA,aAAA;;sBDhDI,aAAW,WAAA,YAAA,SAAA,gBAAA,YAAA,QAAA,cAAA,WAAA,SAAA,UAAA,YAAA,kBAAA,UAAA,YAAA,8BAAE,cAAY,MAAE,aAAW,iBAAA,OAAA,GAAA,QAAA,CAAA,wkPAAA,EAAA,CAAA;EAAA;;;sEAErC,aAAW,CAAA;UAPvB;uBACW,gBAAc,YAGZ,MAAI,SACP,CAAC,aAAa,cAAc,WAAW,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,4sLAAA,EAAA,CAAA;;;;6EAEtC,aAAW,EAAA,WAAA,eAAA,UAAA,gDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": ["marker", "toast"]}
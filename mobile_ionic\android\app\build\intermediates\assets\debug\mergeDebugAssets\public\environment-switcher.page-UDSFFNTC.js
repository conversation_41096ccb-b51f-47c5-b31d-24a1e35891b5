import {
  EnvironmentSwitcherService
} from "./chunk-FKALCVFZ.js";
import {
  AlertController,
  CommonModule,
  Component,
  IonBadge,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardSubtitle,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonTitle,
  IonToolbar,
  IonicModule,
  LoadingController,
  NgForOf,
  Ng<PERSON>f,
  Router,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import {
  __async
} from "./chunk-UL2P3LPA.js";

// src/app/pages/environment-switcher/environment-switcher.page.ts
function EnvironmentSwitcherPage_ion_item_37_p_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const endpoint_r2 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275classProp("success-message", endpoint_r2.testResult.success)("error-message", !endpoint_r2.testResult.success);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", endpoint_r2.testResult.message, " ");
  }
}
function EnvironmentSwitcherPage_ion_item_37_ion_button_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-button", 19);
    \u0275\u0275listener("click", function EnvironmentSwitcherPage_ion_item_37_ion_button_13_Template_ion_button_click_0_listener($event) {
      \u0275\u0275restoreView(_r4);
      const endpoint_r2 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext();
      ctx_r2.selectEndpoint(endpoint_r2);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275element(1, "ion-icon", 23);
    \u0275\u0275elementEnd();
  }
}
function EnvironmentSwitcherPage_ion_item_37_ion_badge_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-badge", 24);
    \u0275\u0275text(1, " Active ");
    \u0275\u0275elementEnd();
  }
}
function EnvironmentSwitcherPage_ion_item_37_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-item", 14);
    \u0275\u0275element(1, "ion-icon", 15);
    \u0275\u0275elementStart(2, "ion-label")(3, "h2");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "p", 16);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd();
    \u0275\u0275template(9, EnvironmentSwitcherPage_ion_item_37_p_9_Template, 2, 5, "p", 17);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "ion-buttons", 18)(11, "ion-button", 19);
    \u0275\u0275listener("click", function EnvironmentSwitcherPage_ion_item_37_Template_ion_button_click_11_listener($event) {
      const endpoint_r2 = \u0275\u0275restoreView(_r1).$implicit;
      const ctx_r2 = \u0275\u0275nextContext();
      ctx_r2.testEndpoint(endpoint_r2);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275element(12, "ion-icon", 20);
    \u0275\u0275elementEnd();
    \u0275\u0275template(13, EnvironmentSwitcherPage_ion_item_37_ion_button_13_Template, 2, 0, "ion-button", 21)(14, EnvironmentSwitcherPage_ion_item_37_ion_badge_14_Template, 2, 0, "ion-badge", 22);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const endpoint_r2 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275classProp("active-endpoint", endpoint_r2.isActive);
    \u0275\u0275advance();
    \u0275\u0275property("name", ctx_r2.getStatusIcon(endpoint_r2))("color", ctx_r2.getStatusColor(endpoint_r2));
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(endpoint_r2.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(endpoint_r2.description);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(endpoint_r2.url);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", endpoint_r2.testResult);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", !endpoint_r2.isActive);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", endpoint_r2.isActive);
  }
}
var EnvironmentSwitcherPage = class _EnvironmentSwitcherPage {
  constructor(envSwitcher, alertController, loadingController, router) {
    this.envSwitcher = envSwitcher;
    this.alertController = alertController;
    this.loadingController = loadingController;
    this.router = router;
    this.endpoints = [];
    this.currentApiUrl = "";
    this.isLoading = false;
  }
  ngOnInit() {
    this.loadEndpoints();
    this.currentApiUrl = this.envSwitcher.getCurrentApiUrl();
  }
  loadEndpoints() {
    this.endpoints = this.envSwitcher.getApiEndpoints();
  }
  selectEndpoint(endpoint) {
    return __async(this, null, function* () {
      const alert = yield this.alertController.create({
        header: "Switch API Endpoint",
        message: `Switch to ${endpoint.name}?

${endpoint.description}`,
        buttons: [
          {
            text: "Cancel",
            role: "cancel"
          },
          {
            text: "Switch",
            handler: () => {
              this.envSwitcher.setApiUrl(endpoint.url);
              this.currentApiUrl = endpoint.url;
              this.loadEndpoints();
              this.presentSuccessAlert("API endpoint switched successfully!");
            }
          }
        ]
      });
      yield alert.present();
    });
  }
  testEndpoint(endpoint) {
    return __async(this, null, function* () {
      const loading = yield this.loadingController.create({
        message: `Testing ${endpoint.name}...`,
        duration: 1e4
      });
      yield loading.present();
      try {
        const result = yield this.envSwitcher.testEndpoint(endpoint.url);
        const index = this.endpoints.findIndex((e) => e.url === endpoint.url);
        if (index !== -1) {
          this.endpoints[index].testResult = result;
        }
        yield loading.dismiss();
        const alert = yield this.alertController.create({
          header: "Connection Test",
          message: `${endpoint.name}

${result.message}`,
          buttons: ["OK"]
        });
        yield alert.present();
      } catch (error) {
        yield loading.dismiss();
        this.presentErrorAlert("Test failed", "Unable to test endpoint");
      }
    });
  }
  testAllEndpoints() {
    return __async(this, null, function* () {
      const loading = yield this.loadingController.create({
        message: "Testing all endpoints...",
        duration: 3e4
      });
      yield loading.present();
      try {
        const results = yield this.envSwitcher.testAllEndpoints();
        this.endpoints = results;
        yield loading.dismiss();
        const workingEndpoints = results.filter((r) => r.testResult.success);
        if (workingEndpoints.length > 0) {
          const message = `Found ${workingEndpoints.length} working endpoint(s):

` + workingEndpoints.map((e) => `\u2705 ${e.name}`).join("\n");
          this.presentSuccessAlert(message);
        } else {
          this.presentErrorAlert("No Working Endpoints", "All endpoints failed connectivity test");
        }
      } catch (error) {
        yield loading.dismiss();
        this.presentErrorAlert("Test Failed", "Unable to test endpoints");
      }
    });
  }
  autoDetect() {
    return __async(this, null, function* () {
      const loading = yield this.loadingController.create({
        message: "Auto-detecting best endpoint...",
        duration: 3e4
      });
      yield loading.present();
      try {
        const bestEndpoint = yield this.envSwitcher.autoDetectBestEndpoint();
        yield loading.dismiss();
        if (bestEndpoint) {
          this.currentApiUrl = bestEndpoint.url;
          this.loadEndpoints();
          this.presentSuccessAlert(`Auto-detected and switched to: ${bestEndpoint.name}`);
        } else {
          this.presentErrorAlert("Auto-Detection Failed", "No working endpoints found");
        }
      } catch (error) {
        yield loading.dismiss();
        this.presentErrorAlert("Auto-Detection Failed", "Unable to detect working endpoint");
      }
    });
  }
  getStatusIcon(endpoint) {
    if (!endpoint.testResult)
      return "help-circle-outline";
    return endpoint.testResult.success ? "checkmark-circle" : "close-circle";
  }
  getStatusColor(endpoint) {
    if (!endpoint.testResult)
      return "medium";
    return endpoint.testResult.success ? "success" : "danger";
  }
  goBack() {
    this.router.navigate(["/login"]);
  }
  presentSuccessAlert(message) {
    return __async(this, null, function* () {
      const alert = yield this.alertController.create({
        header: "Success",
        message,
        buttons: ["OK"]
      });
      yield alert.present();
    });
  }
  presentErrorAlert(header, message) {
    return __async(this, null, function* () {
      const alert = yield this.alertController.create({
        header,
        message,
        buttons: ["OK"]
      });
      yield alert.present();
    });
  }
  static {
    this.\u0275fac = function EnvironmentSwitcherPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _EnvironmentSwitcherPage)(\u0275\u0275directiveInject(EnvironmentSwitcherService), \u0275\u0275directiveInject(AlertController), \u0275\u0275directiveInject(LoadingController), \u0275\u0275directiveInject(Router));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _EnvironmentSwitcherPage, selectors: [["app-environment-switcher"]], decls: 65, vars: 4, consts: [[3, "translucent"], ["slot", "start"], [3, "click"], ["name", "chevron-back-outline"], [1, "ion-padding", 3, "fullscreen"], [1, "switcher-container"], ["expand", "block", "fill", "outline", 3, "click"], ["name", "search-outline", "slot", "start"], ["expand", "block", 3, "click"], ["name", "flash-outline", "slot", "start"], ["button", "", 3, "active-endpoint", 4, "ngFor", "ngForOf"], ["name", "wifi-outline", "slot", "start", "color", "primary"], ["name", "home-outline", "slot", "start", "color", "warning"], ["name", "desktop-outline", "slot", "start", "color", "medium"], ["button", ""], ["slot", "start", 3, "name", "color"], [1, "endpoint-url"], [3, "success-message", "error-message", 4, "ngIf"], ["slot", "end"], ["fill", "clear", 3, "click"], ["name", "refresh-outline"], ["fill", "clear", 3, "click", 4, "ngIf"], ["color", "primary", 4, "ngIf"], ["name", "checkmark-outline"], ["color", "primary"]], template: function EnvironmentSwitcherPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header", 0)(1, "ion-toolbar")(2, "ion-buttons", 1)(3, "ion-button", 2);
        \u0275\u0275listener("click", function EnvironmentSwitcherPage_Template_ion_button_click_3_listener() {
          return ctx.goBack();
        });
        \u0275\u0275element(4, "ion-icon", 3);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(5, "ion-title");
        \u0275\u0275text(6, "API Endpoint Switcher");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(7, "ion-content", 4)(8, "div", 5)(9, "ion-card")(10, "ion-card-header")(11, "ion-card-title");
        \u0275\u0275text(12, "Current API Endpoint");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(13, "ion-card-content")(14, "p")(15, "strong");
        \u0275\u0275text(16, "URL:");
        \u0275\u0275elementEnd();
        \u0275\u0275text(17);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(18, "ion-button", 6);
        \u0275\u0275listener("click", function EnvironmentSwitcherPage_Template_ion_button_click_18_listener() {
          return ctx.autoDetect();
        });
        \u0275\u0275element(19, "ion-icon", 7);
        \u0275\u0275text(20, " Auto-Detect Best Endpoint ");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(21, "ion-card")(22, "ion-card-header")(23, "ion-card-title");
        \u0275\u0275text(24, "Quick Actions");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(25, "ion-card-content")(26, "ion-button", 8);
        \u0275\u0275listener("click", function EnvironmentSwitcherPage_Template_ion_button_click_26_listener() {
          return ctx.testAllEndpoints();
        });
        \u0275\u0275element(27, "ion-icon", 9);
        \u0275\u0275text(28, " Test All Endpoints ");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(29, "ion-card")(30, "ion-card-header")(31, "ion-card-title");
        \u0275\u0275text(32, "Available Endpoints");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(33, "ion-card-subtitle");
        \u0275\u0275text(34, "Select an endpoint to switch to");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(35, "ion-card-content")(36, "ion-list");
        \u0275\u0275template(37, EnvironmentSwitcherPage_ion_item_37_Template, 15, 10, "ion-item", 10);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(38, "ion-card")(39, "ion-card-header")(40, "ion-card-title");
        \u0275\u0275text(41, "Troubleshooting Tips");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(42, "ion-card-content")(43, "ion-list")(44, "ion-item");
        \u0275\u0275element(45, "ion-icon", 11);
        \u0275\u0275elementStart(46, "ion-label")(47, "h3");
        \u0275\u0275text(48, "ngrok (Recommended)");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(49, "p");
        \u0275\u0275text(50, "Most reliable for device testing. Works from anywhere.");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(51, "ion-item");
        \u0275\u0275element(52, "ion-icon", 12);
        \u0275\u0275elementStart(53, "ion-label")(54, "h3");
        \u0275\u0275text(55, "Local IP");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(56, "p");
        \u0275\u0275text(57, "Requires same WiFi network and firewall configuration.");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(58, "ion-item");
        \u0275\u0275element(59, "ion-icon", 13);
        \u0275\u0275elementStart(60, "ion-label")(61, "h3");
        \u0275\u0275text(62, "Localhost");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(63, "p");
        \u0275\u0275text(64, "Only works in web browser, not on mobile devices.");
        \u0275\u0275elementEnd()()()()()()()();
      }
      if (rf & 2) {
        \u0275\u0275property("translucent", true);
        \u0275\u0275advance(7);
        \u0275\u0275property("fullscreen", true);
        \u0275\u0275advance(10);
        \u0275\u0275textInterpolate1(" ", ctx.currentApiUrl, "");
        \u0275\u0275advance(20);
        \u0275\u0275property("ngForOf", ctx.endpoints);
      }
    }, dependencies: [IonicModule, IonBadge, IonButton, IonButtons, IonCard, IonCardContent, IonCardHeader, IonCardSubtitle, IonCardTitle, IonContent, IonHeader, IonIcon, IonItem, IonLabel, IonList, IonTitle, IonToolbar, CommonModule, NgForOf, NgIf], styles: ["\n\n.switcher-container[_ngcontent-%COMP%] {\n  max-width: 600px;\n  margin: 0 auto;\n}\n.active-endpoint[_ngcontent-%COMP%] {\n  --background: var(--ion-color-primary-tint);\n  --border-color: var(--ion-color-primary);\n  border-left: 4px solid var(--ion-color-primary);\n}\n.endpoint-url[_ngcontent-%COMP%] {\n  font-family: monospace;\n  font-size: 0.8em;\n  color: var(--ion-color-medium);\n  word-break: break-all;\n}\n.success-message[_ngcontent-%COMP%] {\n  color: var(--ion-color-success);\n  font-weight: 500;\n}\n.error-message[_ngcontent-%COMP%] {\n  color: var(--ion-color-danger);\n  font-weight: 500;\n}\nion-card[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\nion-card-title[_ngcontent-%COMP%] {\n  color: var(--ion-color-primary);\n}\nion-item[_ngcontent-%COMP%] {\n  --padding-start: 16px;\n  --padding-end: 16px;\n}\nion-button[_ngcontent-%COMP%] {\n  --border-radius: 8px;\n}\n.quick-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 8px;\n  margin-bottom: 16px;\n}\n.quick-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  flex: 1;\n}\n/*# sourceMappingURL=environment-switcher.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(EnvironmentSwitcherPage, [{
    type: Component,
    args: [{ standalone: true, imports: [IonicModule, CommonModule], selector: "app-environment-switcher", template: '<ion-header [translucent]="true">\r\n  <ion-toolbar>\r\n    <ion-buttons slot="start">\r\n      <ion-button (click)="goBack()">\r\n        <ion-icon name="chevron-back-outline"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n    <ion-title>API Endpoint Switcher</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]="true" class="ion-padding">\r\n  <div class="switcher-container">\r\n    \r\n    <!-- Current Status -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Current API Endpoint</ion-card-title>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <p><strong>URL:</strong> {{ currentApiUrl }}</p>\r\n        <ion-button expand="block" fill="outline" (click)="autoDetect()">\r\n          <ion-icon name="search-outline" slot="start"></ion-icon>\r\n          Auto-Detect Best Endpoint\r\n        </ion-button>\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Quick Actions -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Quick Actions</ion-card-title>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <ion-button expand="block" (click)="testAllEndpoints()">\r\n          <ion-icon name="flash-outline" slot="start"></ion-icon>\r\n          Test All Endpoints\r\n        </ion-button>\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Available Endpoints -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Available Endpoints</ion-card-title>\r\n        <ion-card-subtitle>Select an endpoint to switch to</ion-card-subtitle>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        \r\n        <ion-list>\r\n          <ion-item \r\n            *ngFor="let endpoint of endpoints" \r\n            [class.active-endpoint]="endpoint.isActive"\r\n            button\r\n          >\r\n            <ion-icon \r\n              [name]="getStatusIcon(endpoint)" \r\n              [color]="getStatusColor(endpoint)"\r\n              slot="start">\r\n            </ion-icon>\r\n            \r\n            <ion-label>\r\n              <h2>{{ endpoint.name }}</h2>\r\n              <p>{{ endpoint.description }}</p>\r\n              <p class="endpoint-url">{{ endpoint.url }}</p>\r\n              <p *ngIf="endpoint.testResult" \r\n                 [class.success-message]="endpoint.testResult.success"\r\n                 [class.error-message]="!endpoint.testResult.success">\r\n                {{ endpoint.testResult.message }}\r\n              </p>\r\n            </ion-label>\r\n\r\n            <ion-buttons slot="end">\r\n              <ion-button \r\n                fill="clear" \r\n                (click)="testEndpoint(endpoint); $event.stopPropagation()">\r\n                <ion-icon name="refresh-outline"></ion-icon>\r\n              </ion-button>\r\n              \r\n              <ion-button \r\n                *ngIf="!endpoint.isActive"\r\n                fill="clear" \r\n                (click)="selectEndpoint(endpoint); $event.stopPropagation()">\r\n                <ion-icon name="checkmark-outline"></ion-icon>\r\n              </ion-button>\r\n              \r\n              <ion-badge *ngIf="endpoint.isActive" color="primary">\r\n                Active\r\n              </ion-badge>\r\n            </ion-buttons>\r\n          </ion-item>\r\n        </ion-list>\r\n\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Help Section -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Troubleshooting Tips</ion-card-title>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <ion-list>\r\n          <ion-item>\r\n            <ion-icon name="wifi-outline" slot="start" color="primary"></ion-icon>\r\n            <ion-label>\r\n              <h3>ngrok (Recommended)</h3>\r\n              <p>Most reliable for device testing. Works from anywhere.</p>\r\n            </ion-label>\r\n          </ion-item>\r\n          \r\n          <ion-item>\r\n            <ion-icon name="home-outline" slot="start" color="warning"></ion-icon>\r\n            <ion-label>\r\n              <h3>Local IP</h3>\r\n              <p>Requires same WiFi network and firewall configuration.</p>\r\n            </ion-label>\r\n          </ion-item>\r\n          \r\n          <ion-item>\r\n            <ion-icon name="desktop-outline" slot="start" color="medium"></ion-icon>\r\n            <ion-label>\r\n              <h3>Localhost</h3>\r\n              <p>Only works in web browser, not on mobile devices.</p>\r\n            </ion-label>\r\n          </ion-item>\r\n        </ion-list>\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n  </div>\r\n</ion-content>\r\n', styles: ["/* src/app/pages/environment-switcher/environment-switcher.page.scss */\n.switcher-container {\n  max-width: 600px;\n  margin: 0 auto;\n}\n.active-endpoint {\n  --background: var(--ion-color-primary-tint);\n  --border-color: var(--ion-color-primary);\n  border-left: 4px solid var(--ion-color-primary);\n}\n.endpoint-url {\n  font-family: monospace;\n  font-size: 0.8em;\n  color: var(--ion-color-medium);\n  word-break: break-all;\n}\n.success-message {\n  color: var(--ion-color-success);\n  font-weight: 500;\n}\n.error-message {\n  color: var(--ion-color-danger);\n  font-weight: 500;\n}\nion-card {\n  margin-bottom: 16px;\n}\nion-card-title {\n  color: var(--ion-color-primary);\n}\nion-item {\n  --padding-start: 16px;\n  --padding-end: 16px;\n}\nion-button {\n  --border-radius: 8px;\n}\n.quick-actions {\n  display: flex;\n  gap: 8px;\n  margin-bottom: 16px;\n}\n.quick-actions ion-button {\n  flex: 1;\n}\n/*# sourceMappingURL=environment-switcher.page.css.map */\n"] }]
  }], () => [{ type: EnvironmentSwitcherService }, { type: AlertController }, { type: LoadingController }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(EnvironmentSwitcherPage, { className: "EnvironmentSwitcherPage", filePath: "src/app/pages/environment-switcher/environment-switcher.page.ts", lineNumber: 14 });
})();
export {
  EnvironmentSwitcherPage
};
//# sourceMappingURL=environment-switcher.page-UDSFFNTC.js.map

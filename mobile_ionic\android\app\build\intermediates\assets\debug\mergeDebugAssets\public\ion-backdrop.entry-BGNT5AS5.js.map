{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-backdrop.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host } from './index-527b9e34.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport './index-cfd9c1f2.js';\nconst backdropIosCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\nconst IonBackdropIosStyle0 = backdropIosCss;\nconst backdropMdCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\nconst IonBackdropMdStyle0 = backdropMdCss;\nconst Backdrop = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionBackdropTap = createEvent(this, \"ionBackdropTap\", 7);\n    this.visible = true;\n    this.tappable = true;\n    this.stopPropagation = true;\n  }\n  onMouseDown(ev) {\n    this.emitTap(ev);\n  }\n  emitTap(ev) {\n    if (this.stopPropagation) {\n      ev.preventDefault();\n      ev.stopPropagation();\n    }\n    if (this.tappable) {\n      this.ionBackdropTap.emit();\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '7abaf2c310aa399607451b14063265e8a5846938',\n      \"aria-hidden\": \"true\",\n      class: {\n        [mode]: true,\n        'backdrop-hide': !this.visible,\n        'backdrop-no-tappable': !this.tappable\n      }\n    });\n  }\n};\nBackdrop.style = {\n  ios: IonBackdropIosStyle0,\n  md: IonBackdropMdStyle0\n};\nexport { Backdrop as ion_backdrop };"], "mappings": ";;;;;;;;;;;;;AAMA,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;AAC5B,IAAM,WAAW,MAAM;AAAA,EACrB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,YAAY,IAAI;AACd,SAAK,QAAQ,EAAE;AAAA,EACjB;AAAA,EACA,QAAQ,IAAI;AACV,QAAI,KAAK,iBAAiB;AACxB,SAAG,eAAe;AAClB,SAAG,gBAAgB;AAAA,IACrB;AACA,QAAI,KAAK,UAAU;AACjB,WAAK,eAAe,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,eAAe;AAAA,MACf,OAAO;AAAA,QACL,CAAC,IAAI,GAAG;AAAA,QACR,iBAAiB,CAAC,KAAK;AAAA,QACvB,wBAAwB,CAAC,KAAK;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,QAAQ;AAAA,EACf,KAAK;AAAA,EACL,IAAI;AACN;", "names": [], "x_google_ignoreList": [0]}
import {
  require_leaflet_src
} from "./chunk-WDZAZAAD.js";
import {
  OfflineStorageService
} from "./chunk-73VFBDTI.js";
import {
  HttpClient,
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-NS3G4TP7.js";
import {
  __async,
  __toESM
} from "./chunk-UL2P3LPA.js";

// src/app/services/offline-map.service.ts
var L = __toESM(require_leaflet_src());
var OfflineMapService = class _OfflineMapService {
  constructor(http, offlineStorage) {
    this.http = http;
    this.offlineStorage = offlineStorage;
    this.TILE_CACHE_SIZE = 1e3;
    this.CACHE_EXPIRY_DAYS = 30;
    this.PHILIPPINES_BOUNDS = {
      north: 21,
      south: 4.5,
      east: 127,
      west: 116
    };
  }
  /**
   * Create an offline-capable tile layer for Leaflet
   */
  createOfflineTileLayer() {
    const offlineLayer = L.tileLayer("", {
      attribution: "\xA9 OpenStreetMap contributors (Offline Mode)",
      maxZoom: 18,
      minZoom: 8
    });
    offlineLayer.createTile = (coords, done) => {
      const tile = document.createElement("img");
      this.getTileFromCache(coords.z, coords.x, coords.y).then((cachedTile) => {
        if (cachedTile) {
          tile.src = `data:image/png;base64,${cachedTile.tile_data}`;
          done(null, tile);
        } else {
          tile.src = this.createPlaceholderTile(coords);
          done(null, tile);
        }
      }).catch((error) => {
        console.error("Error loading cached tile:", error);
        tile.src = this.createPlaceholderTile(coords);
        done(null, tile);
      });
      return tile;
    };
    return offlineLayer;
  }
  /**
   * Pre-cache map tiles for Philippines region at multiple zoom levels
   */
  preloadMapTiles(centerLat, centerLng, radiusKm = 50, onProgress) {
    return __async(this, null, function* () {
      console.log("\u{1F5FA}\uFE0F Starting map tile preload...");
      const zoomLevels = [10, 11, 12, 13, 14, 15];
      let totalTiles = 0;
      let processedTiles = 0;
      for (const zoom of zoomLevels) {
        const bounds = this.calculateTileBounds(centerLat, centerLng, radiusKm, zoom);
        totalTiles += (bounds.maxX - bounds.minX + 1) * (bounds.maxY - bounds.minY + 1);
      }
      console.log(`\u{1F4CA} Total tiles to download: ${totalTiles}`);
      for (const zoom of zoomLevels) {
        const bounds = this.calculateTileBounds(centerLat, centerLng, radiusKm, zoom);
        for (let x = bounds.minX; x <= bounds.maxX; x++) {
          for (let y = bounds.minY; y <= bounds.maxY; y++) {
            try {
              yield this.downloadAndCacheTile(zoom, x, y);
              processedTiles++;
              if (onProgress) {
                onProgress(processedTiles, totalTiles);
              }
              yield this.delay(100);
            } catch (error) {
              console.warn(`Failed to cache tile ${zoom}/${x}/${y}:`, error);
              processedTiles++;
            }
          }
        }
      }
      console.log("\u2705 Map tile preload completed");
    });
  }
  /**
   * Download and cache a single tile
   */
  downloadAndCacheTile(z, x, y) {
    return __async(this, null, function* () {
      const existingTile = yield this.getTileFromCache(z, x, y);
      if (existingTile && !this.isTileExpired(existingTile.created_at)) {
        return;
      }
      const tileUrl = `https://tile.openstreetmap.org/${z}/${x}/${y}.png`;
      try {
        const response = yield this.http.get(tileUrl, { responseType: "blob" }).toPromise();
        if (response) {
          const base64Data = yield this.blobToBase64(response);
          yield this.saveTileToCache(z, x, y, base64Data);
        }
      } catch (error) {
        throw new Error(`Failed to download tile: ${error}`);
      }
    });
  }
  /**
   * Get tile from cache
   */
  getTileFromCache(z, x, y) {
    return __async(this, null, function* () {
      const cachedTile = yield this.offlineStorage.getMapTile(z, x, y);
      if (cachedTile) {
        return {
          z: cachedTile.z,
          x: cachedTile.x,
          y: cachedTile.y,
          tile_data: cachedTile.tile_data,
          created_at: cachedTile.created_at
        };
      }
      return null;
    });
  }
  /**
   * Save tile to cache
   */
  saveTileToCache(z, x, y, tileData) {
    return __async(this, null, function* () {
      yield this.offlineStorage.saveMapTile(z, x, y, tileData);
    });
  }
  /**
   * Calculate tile bounds for a given center point and radius
   */
  calculateTileBounds(lat, lng, radiusKm, zoom) {
    const latRad = lat * Math.PI / 180;
    const n = Math.pow(2, zoom);
    const latDelta = radiusKm / 111;
    const lngDelta = radiusKm / (111 * Math.cos(latRad));
    const minLat = Math.max(lat - latDelta, this.PHILIPPINES_BOUNDS.south);
    const maxLat = Math.min(lat + latDelta, this.PHILIPPINES_BOUNDS.north);
    const minLng = Math.max(lng - lngDelta, this.PHILIPPINES_BOUNDS.west);
    const maxLng = Math.min(lng + lngDelta, this.PHILIPPINES_BOUNDS.east);
    return {
      minX: Math.floor((minLng + 180) / 360 * n),
      maxX: Math.floor((maxLng + 180) / 360 * n),
      minY: Math.floor((1 - Math.log(Math.tan(maxLat * Math.PI / 180) + 1 / Math.cos(maxLat * Math.PI / 180)) / Math.PI) / 2 * n),
      maxY: Math.floor((1 - Math.log(Math.tan(minLat * Math.PI / 180) + 1 / Math.cos(minLat * Math.PI / 180)) / Math.PI) / 2 * n)
    };
  }
  /**
   * Create a placeholder tile for missing tiles
   */
  createPlaceholderTile(coords) {
    const canvas = document.createElement("canvas");
    canvas.width = 256;
    canvas.height = 256;
    const ctx = canvas.getContext("2d");
    if (ctx) {
      ctx.fillStyle = "#f0f0f0";
      ctx.fillRect(0, 0, 256, 256);
      ctx.strokeStyle = "#ccc";
      ctx.strokeRect(0, 0, 256, 256);
      ctx.fillStyle = "#999";
      ctx.font = "12px Arial";
      ctx.textAlign = "center";
      ctx.fillText("Offline Mode", 128, 120);
      ctx.fillText(`${coords.z}/${coords.x}/${coords.y}`, 128, 140);
    }
    return canvas.toDataURL();
  }
  /**
   * Check if a tile is expired
   */
  isTileExpired(createdAt) {
    const created = new Date(createdAt);
    const now = /* @__PURE__ */ new Date();
    const diffDays = (now.getTime() - created.getTime()) / (1e3 * 60 * 60 * 24);
    return diffDays > this.CACHE_EXPIRY_DAYS;
  }
  /**
   * Convert blob to base64
   */
  blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result;
        resolve(result.split(",")[1]);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }
  /**
   * Utility delay function
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  /**
   * Clean up old tiles to manage storage space
   */
  cleanupOldTiles() {
    return __async(this, null, function* () {
      console.log("\u{1F9F9} Cleaning up old map tiles...");
      console.log("\u2705 Tile cleanup completed");
    });
  }
  /**
   * Get cache statistics
   */
  getCacheStats() {
    return __async(this, null, function* () {
      const storageInfo = this.offlineStorage.getStorageInfo();
      const avgTileSize = 15e3;
      const tileCount = Math.floor(storageInfo.used / avgTileSize);
      const sizeEstimate = this.formatBytes(storageInfo.used);
      return { tileCount, sizeEstimate };
    });
  }
  formatBytes(bytes) {
    if (bytes === 0)
      return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
  static {
    this.\u0275fac = function OfflineMapService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OfflineMapService)(\u0275\u0275inject(HttpClient), \u0275\u0275inject(OfflineStorageService));
    };
  }
  static {
    this.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _OfflineMapService, factory: _OfflineMapService.\u0275fac, providedIn: "root" });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OfflineMapService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }, { type: OfflineStorageService }], null);
})();

export {
  OfflineMapService
};
//# sourceMappingURL=chunk-YBHKUHM7.js.map

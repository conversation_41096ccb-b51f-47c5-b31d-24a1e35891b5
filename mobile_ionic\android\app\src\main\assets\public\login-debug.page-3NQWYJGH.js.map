{"version": 3, "sources": ["src/app/pages/login-debug/login-debug.page.ts", "src/app/pages/login-debug/login-debug.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { IonicModule, Platform, AlertController, ToastController } from '@ionic/angular';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { environment } from '../../../environments/environment';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { FcmService } from '../../services/fcm.service';\r\n\r\ninterface DiagnosticResult {\r\n  test: string;\r\n  status: 'success' | 'error' | 'warning' | 'pending';\r\n  message: string;\r\n  details?: any;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-login-debug',\r\n  templateUrl: './login-debug.page.html',\r\n  styleUrls: ['./login-debug.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule]\r\n})\r\nexport class LoginDebugPage implements OnInit {\r\n\r\n  diagnostics: DiagnosticResult[] = [];\r\n  isRunning = false;\r\n\r\n  testCredentials = {\r\n    email: '<EMAIL>',\r\n    password: 'password123'\r\n  };\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private platform: Platform,\r\n    private alertCtrl: AlertController,\r\n    private toastCtrl: ToastController,\r\n    private authService: AuthService,\r\n    private fcmService: FcmService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.runDiagnostics();\r\n  }\r\n\r\n  async runDiagnostics() {\r\n    this.isRunning = true;\r\n    this.diagnostics = [];\r\n\r\n    // Test 1: Platform Detection\r\n    await this.testPlatform();\r\n\r\n    // Test 2: Network Connectivity\r\n    await this.testNetworkConnectivity();\r\n\r\n    // Test 3: Backend Connectivity\r\n    await this.testBackendConnectivity();\r\n\r\n    // Test 4: API Endpoint Accessibility\r\n    await this.testApiEndpoints();\r\n\r\n    // Test 5: CORS Configuration\r\n    await this.testCorsConfiguration();\r\n\r\n    // Test 6: FCM Token Generation\r\n    await this.testFcmToken();\r\n\r\n    // Test 7: LocalStorage Functionality\r\n    await this.testLocalStorage();\r\n\r\n    // Test 8: Login API Call\r\n    await this.testLoginApi();\r\n\r\n    this.isRunning = false;\r\n\r\n    // Show summary\r\n    await this.showDiagnosticSummary();\r\n  }\r\n\r\n  async testPlatform() {\r\n    const result: DiagnosticResult = {\r\n      test: 'Platform Detection',\r\n      status: 'pending',\r\n      message: 'Detecting platform...'\r\n    };\r\n    this.diagnostics.push(result);\r\n\r\n    try {\r\n      const platformInfo = {\r\n        isMobile: this.platform.is('mobile'),\r\n        isAndroid: this.platform.is('android'),\r\n        isIOS: this.platform.is('ios'),\r\n        isCordova: this.platform.is('cordova'),\r\n        isCapacitor: this.platform.is('capacitor'),\r\n        isBrowser: !this.platform.is('cordova') && !this.platform.is('capacitor'),\r\n        userAgent: navigator.userAgent\r\n      };\r\n\r\n      result.status = 'success';\r\n      result.message = `Platform: ${this.platform.is('android') ? 'Android' : this.platform.is('ios') ? 'iOS' : 'Browser'}`;\r\n      result.details = platformInfo;\r\n    } catch (error) {\r\n      result.status = 'error';\r\n      result.message = `Platform detection failed: ${error}`;\r\n    }\r\n  }\r\n\r\n  async testNetworkConnectivity() {\r\n    const result: DiagnosticResult = {\r\n      test: 'Network Connectivity',\r\n      status: 'pending',\r\n      message: 'Testing network connectivity...'\r\n    };\r\n    this.diagnostics.push(result);\r\n\r\n    try {\r\n      const isOnline = navigator.onLine;\r\n\r\n      if (isOnline) {\r\n        // Test external connectivity\r\n        const response = await firstValueFrom(\r\n          this.http.get('https://httpbin.org/get')\r\n        );\r\n\r\n        result.status = 'success';\r\n        result.message = 'Network connectivity: Online';\r\n        result.details = { online: isOnline, externalTest: 'success' };\r\n      } else {\r\n        result.status = 'error';\r\n        result.message = 'Network connectivity: Offline';\r\n        result.details = { online: isOnline };\r\n      }\r\n    } catch (error) {\r\n      result.status = 'warning';\r\n      result.message = `Network test failed: ${error}`;\r\n      result.details = { online: navigator.onLine, error: error };\r\n    }\r\n  }\r\n\r\n  async testBackendConnectivity() {\r\n    const result: DiagnosticResult = {\r\n      test: 'Backend Connectivity',\r\n      status: 'pending',\r\n      message: 'Testing backend server connectivity...'\r\n    };\r\n    this.diagnostics.push(result);\r\n\r\n    try {\r\n      console.log('Testing backend at:', environment.apiUrl);\r\n\r\n      const testUrl = environment.apiUrl.replace('/api', '/up'); // Laravel health check\r\n      const response = await firstValueFrom(\r\n        this.http.get(testUrl)\r\n      );\r\n\r\n      result.status = 'success';\r\n      result.message = `Backend server accessible at ${environment.apiUrl}`;\r\n      result.details = { url: testUrl, response: response };\r\n    } catch (error: any) {\r\n      result.status = 'error';\r\n      result.message = `Backend server not accessible: ${error.status || 'Network error'}`;\r\n      result.details = {\r\n        url: environment.apiUrl,\r\n        error: error.message || error,\r\n        status: error.status,\r\n        statusText: error.statusText\r\n      };\r\n    }\r\n  }\r\n\r\n  async testApiEndpoints() {\r\n    const result: DiagnosticResult = {\r\n      test: 'API Endpoints',\r\n      status: 'pending',\r\n      message: 'Testing API endpoints...'\r\n    };\r\n    this.diagnostics.push(result);\r\n\r\n    try {\r\n      // Test evacuation centers endpoint (public)\r\n      const centersResponse = await firstValueFrom(\r\n        this.http.get(`${environment.apiUrl}/evacuation-centers`)\r\n      );\r\n\r\n      result.status = 'success';\r\n      result.message = 'API endpoints accessible';\r\n      result.details = {\r\n        evacuationCenters: Array.isArray(centersResponse) ? centersResponse.length : 'Invalid response'\r\n      };\r\n    } catch (error: any) {\r\n      result.status = 'error';\r\n      result.message = `API endpoints not accessible: ${error.status || 'Network error'}`;\r\n      result.details = { error: error.message || error };\r\n    }\r\n  }\r\n\r\n  async testCorsConfiguration() {\r\n    const result: DiagnosticResult = {\r\n      test: 'CORS Configuration',\r\n      status: 'pending',\r\n      message: 'Testing CORS configuration...'\r\n    };\r\n    this.diagnostics.push(result);\r\n\r\n    try {\r\n      // Make a preflight request to test CORS\r\n      const response = await firstValueFrom(\r\n        this.http.options(`${environment.apiUrl}/test`, {\r\n          headers: { 'Content-Type': 'application/json' }\r\n        })\r\n      );\r\n\r\n      result.status = 'success';\r\n      result.message = 'CORS configuration working';\r\n      result.details = { response: response };\r\n    } catch (error: any) {\r\n      if (error.status === 0) {\r\n        result.status = 'error';\r\n        result.message = 'CORS error: Request blocked by browser';\r\n      } else {\r\n        result.status = 'warning';\r\n        result.message = `CORS test inconclusive: ${error.status}`;\r\n      }\r\n      result.details = { error: error };\r\n    }\r\n  }\r\n\r\n  async testFcmToken() {\r\n    const result: DiagnosticResult = {\r\n      test: 'FCM Token Generation',\r\n      status: 'pending',\r\n      message: 'Testing FCM token generation...'\r\n    };\r\n    this.diagnostics.push(result);\r\n\r\n    try {\r\n      const token = await this.fcmService.getToken();\r\n\r\n      if (token) {\r\n        result.status = 'success';\r\n        result.message = 'FCM token generated successfully';\r\n        result.details = { tokenLength: token.length, tokenPreview: token.substring(0, 20) + '...' };\r\n      } else {\r\n        result.status = 'warning';\r\n        result.message = 'FCM token generation returned empty';\r\n        result.details = { token: token };\r\n      }\r\n    } catch (error) {\r\n      result.status = 'error';\r\n      result.message = `FCM token generation failed: ${error}`;\r\n      result.details = { error: error };\r\n    }\r\n  }\r\n\r\n  async testLocalStorage() {\r\n    const result: DiagnosticResult = {\r\n      test: 'LocalStorage Functionality',\r\n      status: 'pending',\r\n      message: 'Testing localStorage functionality...'\r\n    };\r\n    this.diagnostics.push(result);\r\n\r\n    try {\r\n      const testKey = 'diagnostic_test';\r\n      const testValue = 'test_value_' + Date.now();\r\n\r\n      // Test write\r\n      localStorage.setItem(testKey, testValue);\r\n\r\n      // Test read\r\n      const retrievedValue = localStorage.getItem(testKey);\r\n\r\n      // Test delete\r\n      localStorage.removeItem(testKey);\r\n\r\n      if (retrievedValue === testValue) {\r\n        result.status = 'success';\r\n        result.message = 'LocalStorage working correctly';\r\n        result.details = { test: 'passed' };\r\n      } else {\r\n        result.status = 'error';\r\n        result.message = 'LocalStorage read/write failed';\r\n        result.details = { expected: testValue, actual: retrievedValue };\r\n      }\r\n    } catch (error) {\r\n      result.status = 'error';\r\n      result.message = `LocalStorage error: ${error}`;\r\n      result.details = { error: error };\r\n    }\r\n  }\r\n\r\n  async testLoginApi() {\r\n    const result: DiagnosticResult = {\r\n      test: 'Login API Call',\r\n      status: 'pending',\r\n      message: 'Testing login API call...'\r\n    };\r\n    this.diagnostics.push(result);\r\n\r\n    try {\r\n      // Make a test login call (this will likely fail with 401, but we're testing connectivity)\r\n      const response = await firstValueFrom(\r\n        this.http.post(`${environment.apiUrl}/auth/login`, this.testCredentials)\r\n      );\r\n\r\n      result.status = 'success';\r\n      result.message = 'Login API call successful (unexpected!)';\r\n      result.details = { response: response };\r\n    } catch (error: any) {\r\n      if (error.status === 401) {\r\n        result.status = 'success';\r\n        result.message = 'Login API reachable (401 expected for test credentials)';\r\n        result.details = { status: error.status, message: 'API working correctly' };\r\n      } else if (error.status === 0) {\r\n        result.status = 'error';\r\n        result.message = 'Login API not reachable (network error)';\r\n        result.details = { error: 'Network connectivity issue' };\r\n      } else {\r\n        result.status = 'warning';\r\n        result.message = `Login API returned unexpected status: ${error.status}`;\r\n        result.details = { status: error.status, error: error.message };\r\n      }\r\n    }\r\n  }\r\n\r\n  async showDiagnosticSummary() {\r\n    const successCount = this.diagnostics.filter(d => d.status === 'success').length;\r\n    const errorCount = this.diagnostics.filter(d => d.status === 'error').length;\r\n    const warningCount = this.diagnostics.filter(d => d.status === 'warning').length;\r\n\r\n    let message = `Diagnostics Complete:\\n✅ ${successCount} passed\\n⚠️ ${warningCount} warnings\\n❌ ${errorCount} failed`;\r\n\r\n    if (errorCount > 0) {\r\n      message += '\\n\\nCheck the failed tests to identify login issues.';\r\n    }\r\n\r\n    const alert = await this.alertCtrl.create({\r\n      header: 'Diagnostic Results',\r\n      message: message,\r\n      buttons: ['OK']\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  getStatusIcon(status: string): string {\r\n    switch (status) {\r\n      case 'success': return 'checkmark-circle';\r\n      case 'error': return 'close-circle';\r\n      case 'warning': return 'warning';\r\n      case 'pending': return 'time';\r\n      default: return 'help-circle';\r\n    }\r\n  }\r\n\r\n  getStatusColor(status: string): string {\r\n    switch (status) {\r\n      case 'success': return 'success';\r\n      case 'error': return 'danger';\r\n      case 'warning': return 'warning';\r\n      case 'pending': return 'medium';\r\n      default: return 'medium';\r\n    }\r\n  }\r\n\r\n  async showDetails(diagnostic: DiagnosticResult) {\r\n    const alert = await this.alertCtrl.create({\r\n      header: diagnostic.test,\r\n      message: `Status: ${diagnostic.message}\\n\\nDetails: ${JSON.stringify(diagnostic.details, null, 2)}`,\r\n      buttons: ['OK']\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  getApiUrl(): string {\r\n    return environment.apiUrl;\r\n  }\r\n\r\n  isProduction(): boolean {\r\n    return environment.production;\r\n  }\r\n\r\n  getPlatformInfo(): string {\r\n    if (this.platform.is('android')) return 'Android';\r\n    if (this.platform.is('ios')) return 'iOS';\r\n    if (this.platform.is('capacitor')) return 'Capacitor';\r\n    if (this.platform.is('cordova')) return 'Cordova';\r\n    return 'Browser';\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar>\r\n    <ion-title>Login Diagnostics</ion-title>\r\n    <ion-buttons slot=\"end\">\r\n      <ion-button (click)=\"runDiagnostics()\" [disabled]=\"isRunning\">\r\n        <ion-icon name=\"refresh\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <ion-header collapse=\"condense\">\r\n    <ion-toolbar>\r\n      <ion-title size=\"large\">Login Debug</ion-title>\r\n    </ion-toolbar>\r\n  </ion-header>\r\n\r\n  <div class=\"diagnostic-container\">\r\n    \r\n    <!-- Running State -->\r\n    <div *ngIf=\"isRunning\" class=\"loading-container\">\r\n      <ion-spinner></ion-spinner>\r\n      <p>Running diagnostics...</p>\r\n    </div>\r\n\r\n    <!-- Diagnostic Results -->\r\n    <ion-card *ngIf=\"!isRunning && diagnostics.length > 0\">\r\n      <ion-card-header>\r\n        <ion-card-title>Diagnostic Results</ion-card-title>\r\n        <ion-card-subtitle>Mobile login troubleshooting</ion-card-subtitle>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        \r\n        <ion-item \r\n          *ngFor=\"let diagnostic of diagnostics\" \r\n          (click)=\"showDetails(diagnostic)\"\r\n          button>\r\n          <ion-icon \r\n            [name]=\"getStatusIcon(diagnostic.status)\" \r\n            [color]=\"getStatusColor(diagnostic.status)\"\r\n            slot=\"start\">\r\n          </ion-icon>\r\n          <ion-label>\r\n            <h3>{{ diagnostic.test }}</h3>\r\n            <p>{{ diagnostic.message }}</p>\r\n          </ion-label>\r\n          <ion-icon name=\"chevron-forward\" slot=\"end\"></ion-icon>\r\n        </ion-item>\r\n\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Test Credentials -->\r\n    <ion-card *ngIf=\"!isRunning\">\r\n      <ion-card-header>\r\n        <ion-card-title>Test Credentials</ion-card-title>\r\n        <ion-card-subtitle>Credentials used for API testing</ion-card-subtitle>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        \r\n        <ion-item>\r\n          <ion-label position=\"floating\">Test Email</ion-label>\r\n          <ion-input [(ngModel)]=\"testCredentials.email\" type=\"email\"></ion-input>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-label position=\"floating\">Test Password</ion-label>\r\n          <ion-input [(ngModel)]=\"testCredentials.password\" type=\"password\"></ion-input>\r\n        </ion-item>\r\n\r\n        <p class=\"note\">\r\n          <ion-icon name=\"information-circle\" color=\"primary\"></ion-icon>\r\n          These credentials are used only for testing API connectivity. \r\n          A 401 error is expected and indicates the API is working correctly.\r\n        </p>\r\n\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Environment Info -->\r\n    <ion-card *ngIf=\"!isRunning\">\r\n      <ion-card-header>\r\n        <ion-card-title>Environment Configuration</ion-card-title>\r\n        <ion-card-subtitle>Current app configuration</ion-card-subtitle>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        \r\n        <ion-item>\r\n          <ion-label>\r\n            <h3>API URL</h3>\r\n            <p>{{ getApiUrl() }}</p>\r\n          </ion-label>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-label>\r\n            <h3>Production Mode</h3>\r\n            <p>{{ isProduction() ? 'Yes' : 'No' }}</p>\r\n          </ion-label>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-label>\r\n            <h3>Platform</h3>\r\n            <p>{{ getPlatformInfo() }}</p>\r\n          </ion-label>\r\n        </ion-item>\r\n\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Common Issues -->\r\n    <ion-card *ngIf=\"!isRunning\">\r\n      <ion-card-header>\r\n        <ion-card-title>Common Mobile Login Issues</ion-card-title>\r\n        <ion-card-subtitle>Troubleshooting guide</ion-card-subtitle>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        \r\n        <div class=\"issue-item\">\r\n          <h4>🌐 Network Connectivity</h4>\r\n          <p>Ensure your mobile device and computer are on the same WiFi network.</p>\r\n        </div>\r\n\r\n        <div class=\"issue-item\">\r\n          <h4>🔥 Firewall/Security</h4>\r\n          <p>Check if your computer's firewall is blocking port 8000.</p>\r\n        </div>\r\n\r\n        <div class=\"issue-item\">\r\n          <h4>🖥️ Backend Server</h4>\r\n          <p>Verify the Laravel backend is running on your computer.</p>\r\n        </div>\r\n\r\n        <div class=\"issue-item\">\r\n          <h4>📱 CORS Configuration</h4>\r\n          <p>Ensure CORS allows requests from mobile devices.</p>\r\n        </div>\r\n\r\n        <div class=\"issue-item\">\r\n          <h4>🔧 IP Address</h4>\r\n          <p>Verify the IP address in environment.ts matches your computer's IP.</p>\r\n        </div>\r\n\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n  </div>\r\n</ion-content>\r\n\r\n<style>\r\n.diagnostic-container {\r\n  padding: 16px;\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px;\r\n  text-align: center;\r\n}\r\n\r\nion-card {\r\n  margin-bottom: 16px;\r\n}\r\n\r\nion-item {\r\n  --padding-start: 0;\r\n  --inner-padding-end: 0;\r\n}\r\n\r\n.note {\r\n  margin-top: 16px;\r\n  padding: 12px;\r\n  background: #f0f8ff;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  color: #666;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.issue-item {\r\n  margin-bottom: 16px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.issue-item:last-child {\r\n  border-bottom: none;\r\n  margin-bottom: 0;\r\n  padding-bottom: 0;\r\n}\r\n\r\n.issue-item h4 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.issue-item p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.4;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqBI,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,aAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,wBAAA;AAAsB,IAAA,uBAAA,EAAI;;;;;;AAW3B,IAAA,yBAAA,GAAA,YAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,2EAAA;AAAA,YAAA,gBAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,aAAA,CAAuB;IAAA,CAAA;AAEhC,IAAA,oBAAA,GAAA,YAAA,EAAA;AAKA,IAAA,yBAAA,GAAA,WAAA,EAAW,GAAA,IAAA;AACL,IAAA,iBAAA,CAAA;AAAqB,IAAA,uBAAA;AACzB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA,EAAI;AAEjC,IAAA,oBAAA,GAAA,YAAA,EAAA;AACF,IAAA,uBAAA;;;;;AATI,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,cAAA,MAAA,CAAA,EAAyC,SAAA,OAAA,eAAA,cAAA,MAAA,CAAA;AAKrC,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,cAAA,IAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,cAAA,OAAA;;;;;AAlBX,IAAA,yBAAA,GAAA,UAAA,EAAuD,GAAA,iBAAA,EACpC,GAAA,gBAAA;AACC,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;AAClC,IAAA,yBAAA,GAAA,mBAAA;AAAmB,IAAA,iBAAA,GAAA,8BAAA;AAA4B,IAAA,uBAAA,EAAoB;AAErE,IAAA,yBAAA,GAAA,kBAAA;AAEE,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,YAAA,EAAA;AAgBF,IAAA,uBAAA,EAAmB;;;;AAfQ,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,WAAA;;;;;;AAmB7B,IAAA,yBAAA,GAAA,UAAA,EAA6B,GAAA,iBAAA,EACV,GAAA,gBAAA;AACC,IAAA,iBAAA,GAAA,kBAAA;AAAgB,IAAA,uBAAA;AAChC,IAAA,yBAAA,GAAA,mBAAA;AAAmB,IAAA,iBAAA,GAAA,kCAAA;AAAgC,IAAA,uBAAA,EAAoB;AAEzE,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,UAAA,EAEN,GAAA,aAAA,EAAA;AACuB,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA;AACzC,IAAA,yBAAA,IAAA,aAAA,EAAA;AAAW,IAAA,2BAAA,iBAAA,SAAA,wEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,gBAAA,OAAA,MAAA,MAAA,OAAA,gBAAA,QAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAAiD,IAAA,uBAAA,EAAY;AAG1E,IAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,EAAA;AACuB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AAC5C,IAAA,yBAAA,IAAA,aAAA,EAAA;AAAW,IAAA,2BAAA,iBAAA,SAAA,wEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,gBAAA,UAAA,MAAA,MAAA,OAAA,gBAAA,WAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAAuD,IAAA,uBAAA,EAAY;AAGhF,IAAA,yBAAA,IAAA,KAAA,EAAA;AACE,IAAA,oBAAA,IAAA,YAAA,EAAA;AACA,IAAA,iBAAA,IAAA,qIAAA;AAEF,IAAA,uBAAA,EAAI,EAEa;;;;AAdJ,IAAA,oBAAA,EAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,gBAAA,KAAA;AAKA,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,gBAAA,QAAA;;;;;AAajB,IAAA,yBAAA,GAAA,UAAA,EAA6B,GAAA,iBAAA,EACV,GAAA,gBAAA;AACC,IAAA,iBAAA,GAAA,2BAAA;AAAyB,IAAA,uBAAA;AACzC,IAAA,yBAAA,GAAA,mBAAA;AAAmB,IAAA,iBAAA,GAAA,2BAAA;AAAyB,IAAA,uBAAA,EAAoB;AAElE,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,UAAA,EAEN,GAAA,WAAA,EACG,GAAA,IAAA;AACL,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AACX,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,EAAA;AAAiB,IAAA,uBAAA,EAAI,EACd;AAGd,IAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,WAAA,EACG,IAAA,IAAA;AACL,IAAA,iBAAA,IAAA,iBAAA;AAAe,IAAA,uBAAA;AACnB,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,EAAA;AAAmC,IAAA,uBAAA,EAAI,EAChC;AAGd,IAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,WAAA,EACG,IAAA,IAAA;AACL,IAAA,iBAAA,IAAA,UAAA;AAAQ,IAAA,uBAAA;AACZ,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,EAAA;AAAuB,IAAA,uBAAA,EAAI,EACpB,EACH,EAEM;;;;AAlBV,IAAA,oBAAA,EAAA;AAAA,IAAA,4BAAA,OAAA,UAAA,CAAA;AAOA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,aAAA,IAAA,QAAA,IAAA;AAOA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,gBAAA,CAAA;;;;;AAQX,IAAA,yBAAA,GAAA,UAAA,EAA6B,GAAA,iBAAA,EACV,GAAA,gBAAA;AACC,IAAA,iBAAA,GAAA,4BAAA;AAA0B,IAAA,uBAAA;AAC1C,IAAA,yBAAA,GAAA,mBAAA;AAAmB,IAAA,iBAAA,GAAA,uBAAA;AAAqB,IAAA,uBAAA,EAAoB;AAE9D,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,OAAA,EAAA,EAEQ,GAAA,IAAA;AAClB,IAAA,iBAAA,GAAA,gCAAA;AAAuB,IAAA,uBAAA;AAC3B,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,sEAAA;AAAoE,IAAA,uBAAA,EAAI;AAG7E,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,IAAA;AAClB,IAAA,iBAAA,IAAA,6BAAA;AAAoB,IAAA,uBAAA;AACxB,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,0DAAA;AAAwD,IAAA,uBAAA,EAAI;AAGjE,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,IAAA;AAClB,IAAA,iBAAA,IAAA,gCAAA;AAAkB,IAAA,uBAAA;AACtB,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,yDAAA;AAAuD,IAAA,uBAAA,EAAI;AAGhE,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,IAAA;AAClB,IAAA,iBAAA,IAAA,8BAAA;AAAqB,IAAA,uBAAA;AACzB,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,kDAAA;AAAgD,IAAA,uBAAA,EAAI;AAGzD,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,IAAA;AAClB,IAAA,iBAAA,IAAA,sBAAA;AAAa,IAAA,uBAAA;AACjB,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,qEAAA;AAAmE,IAAA,uBAAA,EAAI,EACtE,EAEW;;;ADzHnB,IAAO,iBAAP,MAAO,gBAAc;EAUzB,YACU,MACA,UACA,WACA,WACA,aACA,YAAsB;AALtB,SAAA,OAAA;AACA,SAAA,WAAA;AACA,SAAA,YAAA;AACA,SAAA,YAAA;AACA,SAAA,cAAA;AACA,SAAA,aAAA;AAdV,SAAA,cAAkC,CAAA;AAClC,SAAA,YAAY;AAEZ,SAAA,kBAAkB;MAChB,OAAO;MACP,UAAU;;EAUR;EAEJ,WAAQ;AACN,SAAK,eAAc;EACrB;EAEM,iBAAc;;AAClB,WAAK,YAAY;AACjB,WAAK,cAAc,CAAA;AAGnB,YAAM,KAAK,aAAY;AAGvB,YAAM,KAAK,wBAAuB;AAGlC,YAAM,KAAK,wBAAuB;AAGlC,YAAM,KAAK,iBAAgB;AAG3B,YAAM,KAAK,sBAAqB;AAGhC,YAAM,KAAK,aAAY;AAGvB,YAAM,KAAK,iBAAgB;AAG3B,YAAM,KAAK,aAAY;AAEvB,WAAK,YAAY;AAGjB,YAAM,KAAK,sBAAqB;IAClC;;EAEM,eAAY;;AAChB,YAAM,SAA2B;QAC/B,MAAM;QACN,QAAQ;QACR,SAAS;;AAEX,WAAK,YAAY,KAAK,MAAM;AAE5B,UAAI;AACF,cAAM,eAAe;UACnB,UAAU,KAAK,SAAS,GAAG,QAAQ;UACnC,WAAW,KAAK,SAAS,GAAG,SAAS;UACrC,OAAO,KAAK,SAAS,GAAG,KAAK;UAC7B,WAAW,KAAK,SAAS,GAAG,SAAS;UACrC,aAAa,KAAK,SAAS,GAAG,WAAW;UACzC,WAAW,CAAC,KAAK,SAAS,GAAG,SAAS,KAAK,CAAC,KAAK,SAAS,GAAG,WAAW;UACxE,WAAW,UAAU;;AAGvB,eAAO,SAAS;AAChB,eAAO,UAAU,aAAa,KAAK,SAAS,GAAG,SAAS,IAAI,YAAY,KAAK,SAAS,GAAG,KAAK,IAAI,QAAQ,SAAS;AACnH,eAAO,UAAU;MACnB,SAAS,OAAO;AACd,eAAO,SAAS;AAChB,eAAO,UAAU,8BAA8B,KAAK;MACtD;IACF;;EAEM,0BAAuB;;AAC3B,YAAM,SAA2B;QAC/B,MAAM;QACN,QAAQ;QACR,SAAS;;AAEX,WAAK,YAAY,KAAK,MAAM;AAE5B,UAAI;AACF,cAAM,WAAW,UAAU;AAE3B,YAAI,UAAU;AAEZ,gBAAM,WAAW,MAAM,eACrB,KAAK,KAAK,IAAI,yBAAyB,CAAC;AAG1C,iBAAO,SAAS;AAChB,iBAAO,UAAU;AACjB,iBAAO,UAAU,EAAE,QAAQ,UAAU,cAAc,UAAS;QAC9D,OAAO;AACL,iBAAO,SAAS;AAChB,iBAAO,UAAU;AACjB,iBAAO,UAAU,EAAE,QAAQ,SAAQ;QACrC;MACF,SAAS,OAAO;AACd,eAAO,SAAS;AAChB,eAAO,UAAU,wBAAwB,KAAK;AAC9C,eAAO,UAAU,EAAE,QAAQ,UAAU,QAAQ,MAAY;MAC3D;IACF;;EAEM,0BAAuB;;AAC3B,YAAM,SAA2B;QAC/B,MAAM;QACN,QAAQ;QACR,SAAS;;AAEX,WAAK,YAAY,KAAK,MAAM;AAE5B,UAAI;AACF,gBAAQ,IAAI,uBAAuB,YAAY,MAAM;AAErD,cAAM,UAAU,YAAY,OAAO,QAAQ,QAAQ,KAAK;AACxD,cAAM,WAAW,MAAM,eACrB,KAAK,KAAK,IAAI,OAAO,CAAC;AAGxB,eAAO,SAAS;AAChB,eAAO,UAAU,gCAAgC,YAAY,MAAM;AACnE,eAAO,UAAU,EAAE,KAAK,SAAS,SAAkB;MACrD,SAAS,OAAY;AACnB,eAAO,SAAS;AAChB,eAAO,UAAU,kCAAkC,MAAM,UAAU,eAAe;AAClF,eAAO,UAAU;UACf,KAAK,YAAY;UACjB,OAAO,MAAM,WAAW;UACxB,QAAQ,MAAM;UACd,YAAY,MAAM;;MAEtB;IACF;;EAEM,mBAAgB;;AACpB,YAAM,SAA2B;QAC/B,MAAM;QACN,QAAQ;QACR,SAAS;;AAEX,WAAK,YAAY,KAAK,MAAM;AAE5B,UAAI;AAEF,cAAM,kBAAkB,MAAM,eAC5B,KAAK,KAAK,IAAI,GAAG,YAAY,MAAM,qBAAqB,CAAC;AAG3D,eAAO,SAAS;AAChB,eAAO,UAAU;AACjB,eAAO,UAAU;UACf,mBAAmB,MAAM,QAAQ,eAAe,IAAI,gBAAgB,SAAS;;MAEjF,SAAS,OAAY;AACnB,eAAO,SAAS;AAChB,eAAO,UAAU,iCAAiC,MAAM,UAAU,eAAe;AACjF,eAAO,UAAU,EAAE,OAAO,MAAM,WAAW,MAAK;MAClD;IACF;;EAEM,wBAAqB;;AACzB,YAAM,SAA2B;QAC/B,MAAM;QACN,QAAQ;QACR,SAAS;;AAEX,WAAK,YAAY,KAAK,MAAM;AAE5B,UAAI;AAEF,cAAM,WAAW,MAAM,eACrB,KAAK,KAAK,QAAQ,GAAG,YAAY,MAAM,SAAS;UAC9C,SAAS,EAAE,gBAAgB,mBAAkB;SAC9C,CAAC;AAGJ,eAAO,SAAS;AAChB,eAAO,UAAU;AACjB,eAAO,UAAU,EAAE,SAAkB;MACvC,SAAS,OAAY;AACnB,YAAI,MAAM,WAAW,GAAG;AACtB,iBAAO,SAAS;AAChB,iBAAO,UAAU;QACnB,OAAO;AACL,iBAAO,SAAS;AAChB,iBAAO,UAAU,2BAA2B,MAAM,MAAM;QAC1D;AACA,eAAO,UAAU,EAAE,MAAY;MACjC;IACF;;EAEM,eAAY;;AAChB,YAAM,SAA2B;QAC/B,MAAM;QACN,QAAQ;QACR,SAAS;;AAEX,WAAK,YAAY,KAAK,MAAM;AAE5B,UAAI;AACF,cAAM,QAAQ,MAAM,KAAK,WAAW,SAAQ;AAE5C,YAAI,OAAO;AACT,iBAAO,SAAS;AAChB,iBAAO,UAAU;AACjB,iBAAO,UAAU,EAAE,aAAa,MAAM,QAAQ,cAAc,MAAM,UAAU,GAAG,EAAE,IAAI,MAAK;QAC5F,OAAO;AACL,iBAAO,SAAS;AAChB,iBAAO,UAAU;AACjB,iBAAO,UAAU,EAAE,MAAY;QACjC;MACF,SAAS,OAAO;AACd,eAAO,SAAS;AAChB,eAAO,UAAU,gCAAgC,KAAK;AACtD,eAAO,UAAU,EAAE,MAAY;MACjC;IACF;;EAEM,mBAAgB;;AACpB,YAAM,SAA2B;QAC/B,MAAM;QACN,QAAQ;QACR,SAAS;;AAEX,WAAK,YAAY,KAAK,MAAM;AAE5B,UAAI;AACF,cAAM,UAAU;AAChB,cAAM,YAAY,gBAAgB,KAAK,IAAG;AAG1C,qBAAa,QAAQ,SAAS,SAAS;AAGvC,cAAM,iBAAiB,aAAa,QAAQ,OAAO;AAGnD,qBAAa,WAAW,OAAO;AAE/B,YAAI,mBAAmB,WAAW;AAChC,iBAAO,SAAS;AAChB,iBAAO,UAAU;AACjB,iBAAO,UAAU,EAAE,MAAM,SAAQ;QACnC,OAAO;AACL,iBAAO,SAAS;AAChB,iBAAO,UAAU;AACjB,iBAAO,UAAU,EAAE,UAAU,WAAW,QAAQ,eAAc;QAChE;MACF,SAAS,OAAO;AACd,eAAO,SAAS;AAChB,eAAO,UAAU,uBAAuB,KAAK;AAC7C,eAAO,UAAU,EAAE,MAAY;MACjC;IACF;;EAEM,eAAY;;AAChB,YAAM,SAA2B;QAC/B,MAAM;QACN,QAAQ;QACR,SAAS;;AAEX,WAAK,YAAY,KAAK,MAAM;AAE5B,UAAI;AAEF,cAAM,WAAW,MAAM,eACrB,KAAK,KAAK,KAAK,GAAG,YAAY,MAAM,eAAe,KAAK,eAAe,CAAC;AAG1E,eAAO,SAAS;AAChB,eAAO,UAAU;AACjB,eAAO,UAAU,EAAE,SAAkB;MACvC,SAAS,OAAY;AACnB,YAAI,MAAM,WAAW,KAAK;AACxB,iBAAO,SAAS;AAChB,iBAAO,UAAU;AACjB,iBAAO,UAAU,EAAE,QAAQ,MAAM,QAAQ,SAAS,wBAAuB;QAC3E,WAAW,MAAM,WAAW,GAAG;AAC7B,iBAAO,SAAS;AAChB,iBAAO,UAAU;AACjB,iBAAO,UAAU,EAAE,OAAO,6BAA4B;QACxD,OAAO;AACL,iBAAO,SAAS;AAChB,iBAAO,UAAU,yCAAyC,MAAM,MAAM;AACtE,iBAAO,UAAU,EAAE,QAAQ,MAAM,QAAQ,OAAO,MAAM,QAAO;QAC/D;MACF;IACF;;EAEM,wBAAqB;;AACzB,YAAM,eAAe,KAAK,YAAY,OAAO,OAAK,EAAE,WAAW,SAAS,EAAE;AAC1E,YAAM,aAAa,KAAK,YAAY,OAAO,OAAK,EAAE,WAAW,OAAO,EAAE;AACtE,YAAM,eAAe,KAAK,YAAY,OAAO,OAAK,EAAE,WAAW,SAAS,EAAE;AAE1E,UAAI,UAAU;SAA4B,YAAY;eAAe,YAAY;SAAgB,UAAU;AAE3G,UAAI,aAAa,GAAG;AAClB,mBAAW;MACb;AAEA,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC,QAAQ;QACR;QACA,SAAS,CAAC,IAAI;OACf;AAED,YAAM,MAAM,QAAO;IACrB;;EAEA,cAAc,QAAc;AAC1B,YAAQ,QAAQ;MACd,KAAK;AAAW,eAAO;MACvB,KAAK;AAAS,eAAO;MACrB,KAAK;AAAW,eAAO;MACvB,KAAK;AAAW,eAAO;MACvB;AAAS,eAAO;IAClB;EACF;EAEA,eAAe,QAAc;AAC3B,YAAQ,QAAQ;MACd,KAAK;AAAW,eAAO;MACvB,KAAK;AAAS,eAAO;MACrB,KAAK;AAAW,eAAO;MACvB,KAAK;AAAW,eAAO;MACvB;AAAS,eAAO;IAClB;EACF;EAEM,YAAY,YAA4B;;AAC5C,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC,QAAQ,WAAW;QACnB,SAAS,WAAW,WAAW,OAAO;;WAAgB,KAAK,UAAU,WAAW,SAAS,MAAM,CAAC,CAAC;QACjG,SAAS,CAAC,IAAI;OACf;AAED,YAAM,MAAM,QAAO;IACrB;;EAEA,YAAS;AACP,WAAO,YAAY;EACrB;EAEA,eAAY;AACV,WAAO,YAAY;EACrB;EAEA,kBAAe;AACb,QAAI,KAAK,SAAS,GAAG,SAAS;AAAG,aAAO;AACxC,QAAI,KAAK,SAAS,GAAG,KAAK;AAAG,aAAO;AACpC,QAAI,KAAK,SAAS,GAAG,WAAW;AAAG,aAAO;AAC1C,QAAI,KAAK,SAAS,GAAG,SAAS;AAAG,aAAO;AACxC,WAAO;EACT;;;uCA/WW,iBAAc,4BAAA,UAAA,GAAA,4BAAA,QAAA,GAAA,4BAAA,eAAA,GAAA,4BAAA,eAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,UAAA,CAAA;IAAA;EAAA;;yEAAd,iBAAc,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,KAAA,GAAA,CAAA,GAAA,SAAA,UAAA,GAAA,CAAA,QAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,YAAA,UAAA,GAAA,CAAA,QAAA,OAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,UAAA,IAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,UAAA,IAAA,GAAA,OAAA,GAAA,CAAA,QAAA,SAAA,GAAA,QAAA,OAAA,GAAA,CAAA,QAAA,mBAAA,QAAA,KAAA,GAAA,CAAA,YAAA,UAAA,GAAA,CAAA,QAAA,SAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,YAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,sBAAA,SAAA,SAAA,GAAA,CAAA,GAAA,YAAA,CAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACxB3B,QAAA,yBAAA,GAAA,cAAA,CAAA,EAAiC,GAAA,aAAA,EAClB,GAAA,WAAA;AACA,QAAA,iBAAA,GAAA,mBAAA;AAAiB,QAAA,uBAAA;AAC5B,QAAA,yBAAA,GAAA,eAAA,CAAA,EAAwB,GAAA,cAAA,CAAA;AACV,QAAA,qBAAA,SAAA,SAAA,sDAAA;AAAA,iBAAS,IAAA,eAAA;QAAgB,CAAA;AACnC,QAAA,oBAAA,GAAA,YAAA,CAAA;AACF,QAAA,uBAAA,EAAa,EACD,EACF;AAGhB,QAAA,yBAAA,GAAA,eAAA,CAAA,EAAiC,GAAA,cAAA,CAAA,EACC,GAAA,aAAA,EACjB,IAAA,aAAA,CAAA;AACa,QAAA,iBAAA,IAAA,aAAA;AAAW,QAAA,uBAAA,EAAY,EACnC;AAGhB,QAAA,yBAAA,IAAA,OAAA,CAAA;AAGE,QAAA,qBAAA,IAAA,gCAAA,GAAA,GAAA,OAAA,CAAA,EAAiD,IAAA,qCAAA,GAAA,GAAA,YAAA,CAAA,EAMM,IAAA,qCAAA,IAAA,GAAA,YAAA,CAAA,EA2B1B,IAAA,qCAAA,IAAA,GAAA,YAAA,CAAA,EA2BA,IAAA,qCAAA,IAAA,GAAA,YAAA,CAAA;AAmE/B,QAAA,uBAAA,EAAM;;;AApJI,QAAA,qBAAA,eAAA,IAAA;AAIiC,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,YAAA,IAAA,SAAA;AAOhC,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,cAAA,IAAA;AAUH,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,SAAA;AAMK,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,CAAA,IAAA,aAAA,IAAA,YAAA,SAAA,CAAA;AA2BA,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,CAAA,IAAA,SAAA;AA2BA,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,CAAA,IAAA,SAAA;AAgCA,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,CAAA,IAAA,SAAA;;sBD3FH,aAAW,WAAA,YAAA,SAAA,gBAAA,eAAA,iBAAA,cAAA,YAAA,WAAA,SAAA,UAAA,SAAA,UAAA,YAAA,UAAA,YAAA,4BAAE,cAAY,SAAA,MAAE,aAAW,iBAAA,OAAA,GAAA,QAAA,CAAA,m0FAAA,ynCAAA,EAAA,CAAA;EAAA;;;sEAErC,gBAAc,CAAA;UAP1B;uBACW,mBAAiB,YAGf,MAAI,SACP,CAAC,aAAa,cAAc,WAAW,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,20EAAA,+mCAAA,EAAA,CAAA;;;;6EAEtC,gBAAc,EAAA,WAAA,kBAAA,UAAA,iDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
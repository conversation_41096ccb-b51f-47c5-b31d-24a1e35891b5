{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-item_8.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, i as forceUpdate, h, e as Host, f as getElement, c as createEvent } from './index-527b9e34.js';\nimport { h as inheritAttributes, r as raf } from './helpers-d94bc8ad.js';\nimport { h as hostContext, c as createColorClasses, o as openURL } from './theme-01f3f29c.js';\nimport { o as chevronForward } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport { c as config } from './index-cfd9c1f2.js';\nconst itemIosCss = \":host{--border-radius:0px;--border-width:0px;--border-style:solid;--padding-top:0px;--padding-bottom:0px;--padding-end:0px;--padding-start:0px;--inner-border-width:0px;--inner-padding-top:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;--inner-padding-end:0px;--inner-box-shadow:none;--detail-icon-color:initial;--detail-icon-font-size:1.25em;--detail-icon-opacity:0.25;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:initial;text-decoration:none;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color) .item-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.ion-color) .item-native,:host(.ion-color) .item-inner{border-color:var(--ion-color-shade)}:host(.ion-activated) .item-native{color:var(--color-activated)}:host(.ion-activated) .item-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.ion-color.ion-activated) .item-native{color:var(--ion-color-contrast)}:host(.ion-focused) .item-native{color:var(--color-focused)}:host(.ion-focused) .item-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-color.ion-focused) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .item-native::after{background:var(--ion-color-contrast)}@media (any-hover: hover){:host(.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--color-hover)}:host(.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--ion-color-contrast)}}:host(.item-control-needs-pointer-cursor){cursor:pointer}:host(.item-interactive-disabled:not(.item-multiple-inputs)){cursor:default;pointer-events:none}:host(.item-disabled){cursor:default;opacity:0.3;pointer-events:none}.item-native{border-radius:var(--border-radius);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;min-height:var(--min-height);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);overflow:inherit;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}[dir=rtl] .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){.item-native:dir(rtl){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}.item-native::-moz-focus-inner{border:0}.item-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:var(--transition);transition:var(--transition);z-index:-1}button,a{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-drag:none}.item-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);-webkit-box-shadow:var(--inner-box-shadow);box-shadow:var(--inner-box-shadow);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-detail-icon{-webkit-margin-start:calc(var(--inner-padding-end) / 2);margin-inline-start:calc(var(--inner-padding-end) / 2);-webkit-margin-end:-6px;margin-inline-end:-6px;color:var(--detail-icon-color);font-size:var(--detail-icon-font-size);opacity:var(--detail-icon-opacity)}::slotted(ion-icon){font-size:1.6em}::slotted(ion-button){--margin-top:0;--margin-bottom:0;--margin-start:0;--margin-end:0;z-index:1}::slotted(ion-label:not([slot=end])){-ms-flex:1;flex:1;width:-webkit-min-content;width:-moz-min-content;width:min-content;max-width:100%}:host(.item-input){-ms-flex-align:center;align-items:center}.input-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.item-label-stacked),:host(.item-label-floating){-ms-flex-align:start;align-items:start}:host(.item-label-stacked) .input-wrapper,:host(.item-label-floating) .input-wrapper{-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}:host(.item-multiple-inputs) ::slotted(ion-checkbox),:host(.item-multiple-inputs) ::slotted(ion-datetime),:host(.item-multiple-inputs) ::slotted(ion-radio){position:relative}:host(.item-textarea){-ms-flex-align:stretch;align-items:stretch}::slotted(ion-reorder[slot]){margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}:host{--min-height:44px;--transition:background-color 200ms linear, opacity 200ms linear;--padding-start:16px;--inner-padding-end:16px;--inner-border-width:0px 0px 0.55px 0px;--background:var(--ion-item-background, var(--ion-background-color, #fff));--background-activated:var(--ion-text-color, #000);--background-focused:var(--ion-text-color, #000);--background-hover:currentColor;--background-activated-opacity:.12;--background-focused-opacity:.15;--background-hover-opacity:.04;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--color:var(--ion-item-color, var(--ion-text-color, #000));font-size:1rem}:host(.ion-activated){--transition:none}:host(.ion-color.ion-focused) .item-native::after{background:#000;opacity:0.15}:host(.ion-color.ion-activated) .item-native::after{background:#000;opacity:0.12}:host(.item-lines-full){--border-width:0px 0px 0.55px 0px}:host(.item-lines-inset){--inner-border-width:0px 0px 0.55px 0px}:host(.item-lines-inset),:host(.item-lines-none){--border-width:0px}:host(.item-lines-full),:host(.item-lines-none){--inner-border-width:0px}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:2px;margin-bottom:2px}::slotted(ion-icon[slot=start]),::slotted(ion-icon[slot=end]){margin-top:7px;margin-bottom:7px}::slotted(ion-toggle[slot=start]),::slotted(ion-toggle[slot=end]){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host(.item-label-stacked) ::slotted([slot=end]),:host(.item-label-floating) ::slotted([slot=end]){margin-top:7px;margin-bottom:7px}::slotted(.button-small){--padding-top:1px;--padding-bottom:1px;--padding-start:.5em;--padding-end:.5em;min-height:24px;font-size:0.8125rem}::slotted(ion-avatar){width:36px;height:36px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px;margin-bottom:8px}:host(.item-radio) ::slotted(ion-label),:host(.item-toggle) ::slotted(ion-label){-webkit-margin-start:0px;margin-inline-start:0px}::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:10px}:host(.item-label-floating),:host(.item-label-stacked){--min-height:68px}\";\nconst IonItemIosStyle0 = itemIosCss;\nconst itemMdCss = \":host{--border-radius:0px;--border-width:0px;--border-style:solid;--padding-top:0px;--padding-bottom:0px;--padding-end:0px;--padding-start:0px;--inner-border-width:0px;--inner-padding-top:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;--inner-padding-end:0px;--inner-box-shadow:none;--detail-icon-color:initial;--detail-icon-font-size:1.25em;--detail-icon-opacity:0.25;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:initial;text-decoration:none;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color) .item-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.ion-color) .item-native,:host(.ion-color) .item-inner{border-color:var(--ion-color-shade)}:host(.ion-activated) .item-native{color:var(--color-activated)}:host(.ion-activated) .item-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.ion-color.ion-activated) .item-native{color:var(--ion-color-contrast)}:host(.ion-focused) .item-native{color:var(--color-focused)}:host(.ion-focused) .item-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-color.ion-focused) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .item-native::after{background:var(--ion-color-contrast)}@media (any-hover: hover){:host(.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--color-hover)}:host(.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--ion-color-contrast)}}:host(.item-control-needs-pointer-cursor){cursor:pointer}:host(.item-interactive-disabled:not(.item-multiple-inputs)){cursor:default;pointer-events:none}:host(.item-disabled){cursor:default;opacity:0.3;pointer-events:none}.item-native{border-radius:var(--border-radius);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;min-height:var(--min-height);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);overflow:inherit;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}[dir=rtl] .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){.item-native:dir(rtl){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}.item-native::-moz-focus-inner{border:0}.item-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:var(--transition);transition:var(--transition);z-index:-1}button,a{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-drag:none}.item-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);-webkit-box-shadow:var(--inner-box-shadow);box-shadow:var(--inner-box-shadow);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-detail-icon{-webkit-margin-start:calc(var(--inner-padding-end) / 2);margin-inline-start:calc(var(--inner-padding-end) / 2);-webkit-margin-end:-6px;margin-inline-end:-6px;color:var(--detail-icon-color);font-size:var(--detail-icon-font-size);opacity:var(--detail-icon-opacity)}::slotted(ion-icon){font-size:1.6em}::slotted(ion-button){--margin-top:0;--margin-bottom:0;--margin-start:0;--margin-end:0;z-index:1}::slotted(ion-label:not([slot=end])){-ms-flex:1;flex:1;width:-webkit-min-content;width:-moz-min-content;width:min-content;max-width:100%}:host(.item-input){-ms-flex-align:center;align-items:center}.input-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.item-label-stacked),:host(.item-label-floating){-ms-flex-align:start;align-items:start}:host(.item-label-stacked) .input-wrapper,:host(.item-label-floating) .input-wrapper{-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}:host(.item-multiple-inputs) ::slotted(ion-checkbox),:host(.item-multiple-inputs) ::slotted(ion-datetime),:host(.item-multiple-inputs) ::slotted(ion-radio){position:relative}:host(.item-textarea){-ms-flex-align:stretch;align-items:stretch}::slotted(ion-reorder[slot]){margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}:host{--min-height:48px;--background:var(--ion-item-background, var(--ion-background-color, #fff));--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--color:var(--ion-item-color, var(--ion-text-color, #000));--transition:opacity 15ms linear, background-color 15ms linear;--padding-start:16px;--inner-padding-end:16px;--inner-border-width:0 0 1px 0;font-size:1rem;font-weight:normal;text-transform:none}:host(.ion-color.ion-activated) .item-native::after{background:transparent}:host(.item-interactive){--border-width:0 0 1px 0;--inner-border-width:0}:host(.item-lines-full){--border-width:0 0 1px 0}:host(.item-lines-inset){--inner-border-width:0 0 1px 0}:host(.item-lines-inset),:host(.item-lines-none){--border-width:0}:host(.item-lines-full),:host(.item-lines-none){--inner-border-width:0}:host(.item-multi-line) ::slotted([slot=start]),:host(.item-multi-line) ::slotted([slot=end]){margin-top:16px;margin-bottom:16px;-ms-flex-item-align:start;align-self:flex-start}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-icon){color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);font-size:1.5em}:host(.ion-color) ::slotted(ion-icon){color:var(--ion-color-contrast)}::slotted(ion-icon[slot]){margin-top:12px;margin-bottom:12px}::slotted(ion-icon[slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted(ion-icon[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-toggle[slot=start]),::slotted(ion-toggle[slot=end]){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}::slotted(ion-note){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-ms-flex-item-align:start;align-self:flex-start;font-size:0.6875rem}::slotted(ion-note[slot]){padding-left:0;padding-right:0;padding-top:18px;padding-bottom:10px}::slotted(ion-avatar){width:40px;height:40px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar),::slotted(ion-thumbnail){margin-top:8px;margin-bottom:8px}::slotted(ion-avatar[slot=start]),::slotted(ion-thumbnail[slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:10px;margin-bottom:10px}:host(.item-label-stacked) ::slotted([slot=end]),:host(.item-label-floating) ::slotted([slot=end]){margin-top:7px;margin-bottom:7px}:host(.item-toggle) ::slotted(ion-label),:host(.item-radio) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0}::slotted(.button-small){--padding-top:2px;--padding-bottom:2px;--padding-start:.6em;--padding-end:.6em;min-height:25px;font-size:0.75rem}:host(.item-label-floating),:host(.item-label-stacked){--min-height:55px}:host(.ion-focused:not(.ion-color)) ::slotted(.label-stacked),:host(.ion-focused:not(.ion-color)) ::slotted(.label-floating),:host(.item-has-focus:not(.ion-color)) ::slotted(.label-stacked),:host(.item-has-focus:not(.ion-color)) ::slotted(.label-floating){color:var(--ion-color-primary, #0054e9)}\";\nconst IonItemMdStyle0 = itemMdCss;\nconst Item = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.labelColorStyles = {};\n    this.itemStyles = new Map();\n    this.inheritedAriaAttributes = {};\n    this.multipleInputs = false;\n    this.focusable = true;\n    this.color = undefined;\n    this.button = false;\n    this.detail = undefined;\n    this.detailIcon = chevronForward;\n    this.disabled = false;\n    this.download = undefined;\n    this.href = undefined;\n    this.rel = undefined;\n    this.lines = undefined;\n    this.routerAnimation = undefined;\n    this.routerDirection = 'forward';\n    this.target = undefined;\n    this.type = 'button';\n  }\n  buttonChanged() {\n    // Update the focusable option when the button option is changed\n    this.focusable = this.isFocusable();\n  }\n  labelColorChanged(ev) {\n    const {\n      color\n    } = this;\n    // There will be a conflict with item color if\n    // we apply the label color to item, so we ignore\n    // the label color if the user sets a color on item\n    if (color === undefined) {\n      this.labelColorStyles = ev.detail;\n    }\n  }\n  itemStyle(ev) {\n    ev.stopPropagation();\n    const tagName = ev.target.tagName;\n    const updatedStyles = ev.detail;\n    const newStyles = {};\n    const childStyles = this.itemStyles.get(tagName) || {};\n    let hasStyleChange = false;\n    Object.keys(updatedStyles).forEach(key => {\n      if (updatedStyles[key]) {\n        const itemKey = `item-${key}`;\n        if (!childStyles[itemKey]) {\n          hasStyleChange = true;\n        }\n        newStyles[itemKey] = true;\n      }\n    });\n    if (!hasStyleChange && Object.keys(newStyles).length !== Object.keys(childStyles).length) {\n      hasStyleChange = true;\n    }\n    if (hasStyleChange) {\n      this.itemStyles.set(tagName, newStyles);\n      forceUpdate(this);\n    }\n  }\n  connectedCallback() {\n    this.hasStartEl();\n  }\n  componentWillLoad() {\n    this.inheritedAriaAttributes = inheritAttributes(this.el, ['aria-label']);\n  }\n  componentDidLoad() {\n    raf(() => {\n      this.setMultipleInputs();\n      this.focusable = this.isFocusable();\n    });\n  }\n  // If the item contains multiple clickable elements and/or inputs, then the item\n  // should not have a clickable input cover over the entire item to prevent\n  // interfering with their individual click events\n  setMultipleInputs() {\n    // The following elements have a clickable cover that is relative to the entire item\n    const covers = this.el.querySelectorAll('ion-checkbox, ion-datetime, ion-select, ion-radio');\n    // The following elements can accept focus alongside the previous elements\n    // therefore if these elements are also a child of item, we don't want the\n    // input cover on top of those interfering with their clicks\n    const inputs = this.el.querySelectorAll('ion-input, ion-range, ion-searchbar, ion-segment, ion-textarea, ion-toggle');\n    // The following elements should also stay clickable when an input with cover is present\n    const clickables = this.el.querySelectorAll('ion-router-link, ion-button, a, button');\n    // Check for multiple inputs to change the position of the input cover to relative\n    // for all of the covered inputs above\n    this.multipleInputs = covers.length + inputs.length > 1 || covers.length + clickables.length > 1 || covers.length > 0 && this.isClickable();\n  }\n  // If the item contains an input including a checkbox, datetime, select, or radio\n  // then the item will have a clickable input cover that covers the item\n  // that should get the hover, focused and activated states UNLESS it has multiple\n  // inputs, then those need to individually get each click\n  hasCover() {\n    const inputs = this.el.querySelectorAll('ion-checkbox, ion-datetime, ion-select, ion-radio');\n    return inputs.length === 1 && !this.multipleInputs;\n  }\n  // If the item has an href or button property it will render a native\n  // anchor or button that is clickable\n  isClickable() {\n    return this.href !== undefined || this.button;\n  }\n  canActivate() {\n    return this.isClickable() || this.hasCover();\n  }\n  isFocusable() {\n    const focusableChild = this.el.querySelector('.ion-focusable');\n    return this.canActivate() || focusableChild !== null;\n  }\n  hasStartEl() {\n    const startEl = this.el.querySelector('[slot=\"start\"]');\n    if (startEl !== null) {\n      this.el.classList.add('item-has-start-slot');\n    }\n  }\n  getFirstInteractive() {\n    const controls = this.el.querySelectorAll('ion-toggle:not([disabled]), ion-checkbox:not([disabled]), ion-radio:not([disabled]), ion-select:not([disabled]), ion-input:not([disabled]), ion-textarea:not([disabled])');\n    return controls[0];\n  }\n  render() {\n    const {\n      detail,\n      detailIcon,\n      download,\n      labelColorStyles,\n      lines,\n      disabled,\n      href,\n      rel,\n      target,\n      routerAnimation,\n      routerDirection,\n      inheritedAriaAttributes,\n      multipleInputs\n    } = this;\n    const childStyles = {};\n    const mode = getIonMode(this);\n    const clickable = this.isClickable();\n    const canActivate = this.canActivate();\n    const TagType = clickable ? href === undefined ? 'button' : 'a' : 'div';\n    const attrs = TagType === 'button' ? {\n      type: this.type\n    } : {\n      download,\n      href,\n      rel,\n      target\n    };\n    let clickFn = {};\n    const firstInteractive = this.getFirstInteractive();\n    // Only set onClick if the item is clickable to prevent screen\n    // readers from reading all items as clickable\n    if (clickable || firstInteractive !== undefined && !multipleInputs) {\n      clickFn = {\n        onClick: ev => {\n          if (clickable) {\n            openURL(href, ev, routerDirection, routerAnimation);\n          }\n          if (firstInteractive !== undefined && !multipleInputs) {\n            const path = ev.composedPath();\n            const target = path[0];\n            if (ev.isTrusted) {\n              /**\n               * Dispatches a click event to the first interactive element,\n               * when it is the result of a user clicking on the item.\n               *\n               * We check if the click target is in the shadow root,\n               * which means the user clicked on the .item-native or\n               * .item-inner padding.\n               */\n              const clickedWithinShadowRoot = this.el.shadowRoot.contains(target);\n              if (clickedWithinShadowRoot) {\n                /**\n                 * For input/textarea clicking the padding should focus the\n                 * text field (thus making it editable). For everything else,\n                 * we want to click the control so it activates.\n                 */\n                if (firstInteractive.tagName === 'ION-INPUT' || firstInteractive.tagName === 'ION-TEXTAREA') {\n                  firstInteractive.setFocus();\n                }\n                firstInteractive.click();\n                /**\n                 * Stop the item event from being triggered\n                 * as the firstInteractive click event will also\n                 * trigger the item click event.\n                 */\n                ev.stopImmediatePropagation();\n              }\n            }\n          }\n        }\n      };\n    }\n    const showDetail = detail !== undefined ? detail : mode === 'ios' && clickable;\n    this.itemStyles.forEach(value => {\n      Object.assign(childStyles, value);\n    });\n    const ariaDisabled = disabled || childStyles['item-interactive-disabled'] ? 'true' : null;\n    const inList = hostContext('ion-list', this.el) && !hostContext('ion-radio-group', this.el);\n    /**\n     * Inputs and textareas do not need to show a cursor pointer.\n     * However, other form controls such as checkboxes and radios do.\n     */\n    const firstInteractiveNeedsPointerCursor = firstInteractive !== undefined && !['ION-INPUT', 'ION-TEXTAREA'].includes(firstInteractive.tagName);\n    return h(Host, {\n      key: '15e7d3b674c25232bc2d51573b291c72548690e5',\n      \"aria-disabled\": ariaDisabled,\n      class: Object.assign(Object.assign(Object.assign({}, childStyles), labelColorStyles), createColorClasses(this.color, {\n        item: true,\n        [mode]: true,\n        'item-lines-default': lines === undefined,\n        [`item-lines-${lines}`]: lines !== undefined,\n        'item-control-needs-pointer-cursor': firstInteractiveNeedsPointerCursor,\n        'item-disabled': disabled,\n        'in-list': inList,\n        'item-multiple-inputs': this.multipleInputs,\n        'ion-activatable': canActivate,\n        'ion-focusable': this.focusable,\n        'item-rtl': document.dir === 'rtl'\n      })),\n      role: inList ? 'listitem' : null\n    }, h(TagType, Object.assign({\n      key: 'f27ae647501e29db554efe8f12f498b165d1dbf6'\n    }, attrs, inheritedAriaAttributes, {\n      class: \"item-native\",\n      part: \"native\",\n      disabled: disabled\n    }, clickFn), h(\"slot\", {\n      key: 'ba039a321df0e4bd5ce11e7a6e7e174c8623b112',\n      name: \"start\"\n    }), h(\"div\", {\n      key: 'e67fdf4ad9086d855da8402ca04ffe292cf6ce67',\n      class: \"item-inner\"\n    }, h(\"div\", {\n      key: '2434348a0b3f10052a340c57a7ac127ddc851729',\n      class: \"input-wrapper\"\n    }, h(\"slot\", {\n      key: '3e27090ae4dab734541b3fd2c94de0f98c0f0030'\n    })), h(\"slot\", {\n      key: '52dc633b60fe612d48d2efffbf2c4c62692bef85',\n      name: \"end\"\n    }), showDetail && h(\"ion-icon\", {\n      key: '4fb7397dc8cf4cecb82f19a6151f85112b73992b',\n      icon: detailIcon,\n      lazy: false,\n      class: \"item-detail-icon\",\n      part: \"detail-icon\",\n      \"aria-hidden\": \"true\",\n      \"flip-rtl\": detailIcon === chevronForward\n    })), canActivate && mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '09b2413a7a484ecd8ffb00805223727da4d4e344'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"button\": [\"buttonChanged\"]\n    };\n  }\n};\nItem.style = {\n  ios: IonItemIosStyle0,\n  md: IonItemMdStyle0\n};\nconst itemDividerIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--inner-padding-top:0px;--inner-padding-end:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);overflow:hidden;z-index:100;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.item-divider-sticky){position:-webkit-sticky;position:sticky;top:0}.item-divider-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border:0;overflow:hidden}:host-context([dir=rtl]) .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-divider-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-divider-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:hidden}:host{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--padding-start:16px;--inner-padding-end:8px;border-radius:0;position:relative;min-height:28px;font-size:1.0625rem;font-weight:600}:host([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:2px;margin-bottom:2px}::slotted(ion-icon[slot=start]),::slotted(ion-icon[slot=end]){margin-top:7px;margin-bottom:7px}::slotted(h1){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h2){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h3),::slotted(h4),::slotted(h5),::slotted(h6){margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px}::slotted(p){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;color:var(--ion-text-color-step-550, #a3a3a3);font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}::slotted(h2:last-child) ::slotted(h3:last-child),::slotted(h4:last-child),::slotted(h5:last-child),::slotted(h6:last-child),::slotted(p:last-child){margin-bottom:0}\";\nconst IonItemDividerIosStyle0 = itemDividerIosCss;\nconst itemDividerMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--inner-padding-top:0px;--inner-padding-end:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);overflow:hidden;z-index:100;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.item-divider-sticky){position:-webkit-sticky;position:sticky;top:0}.item-divider-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border:0;overflow:hidden}:host-context([dir=rtl]) .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-divider-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-divider-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:hidden}:host{--background:var(--ion-background-color, #fff);--color:var(--ion-color-step-400, var(--ion-text-color-step-600, #999999));--padding-start:16px;--inner-padding-end:16px;min-height:30px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));font-size:0.875rem}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:13px;margin-bottom:10px}::slotted(ion-icon){color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);font-size:1.7142857143em}:host(.ion-color) ::slotted(ion-icon){color:var(--ion-color-contrast)}::slotted(ion-icon[slot]){margin-top:12px;margin-bottom:12px}::slotted(ion-icon[slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted(ion-icon[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-note){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-ms-flex-item-align:start;align-self:flex-start;font-size:0.6875rem}::slotted(ion-note[slot]){padding-left:0;padding-right:0;padding-top:18px;padding-bottom:10px}::slotted(ion-avatar){width:40px;height:40px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar),::slotted(ion-thumbnail){margin-top:8px;margin-bottom:8px}::slotted(ion-avatar[slot=start]),::slotted(ion-thumbnail[slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(h1){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h2){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px}::slotted(h3,h4,h5,h6){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px}::slotted(p){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}\";\nconst IonItemDividerMdStyle0 = itemDividerMdCss;\nconst ItemDivider = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n    this.sticky = false;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '7150b1a3a881c3c312db40821acb9830c2885ccf',\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'item-divider-sticky': this.sticky,\n        item: true\n      })\n    }, h(\"slot\", {\n      key: '6e9d9615f475392a81177bc49b4b01dbdab67925',\n      name: \"start\"\n    }), h(\"div\", {\n      key: '12fe6b840ad1a1897cc34529e488207b2df448f7',\n      class: \"item-divider-inner\"\n    }, h(\"div\", {\n      key: '626776a8c9887dd2df5ecf3b7861beb742b41e03',\n      class: \"item-divider-wrapper\"\n    }, h(\"slot\", {\n      key: '90379dd99914e24ae45c9571c8d390f9b6622fd4'\n    })), h(\"slot\", {\n      key: '105644332c08530155b9cd8e8c6fa9e332072a63',\n      name: \"end\"\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nItemDivider.style = {\n  ios: IonItemDividerIosStyle0,\n  md: IonItemDividerMdStyle0\n};\nconst itemGroupIosCss = \"ion-item-group{display:block}\";\nconst IonItemGroupIosStyle0 = itemGroupIosCss;\nconst itemGroupMdCss = \"ion-item-group{display:block}\";\nconst IonItemGroupMdStyle0 = itemGroupMdCss;\nconst ItemGroup = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'de2a969ed0dda880bc560a325848b28d0287fdb9',\n      role: \"group\",\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`item-group-${mode}`]: true,\n        item: true\n      }\n    });\n  }\n};\nItemGroup.style = {\n  ios: IonItemGroupIosStyle0,\n  md: IonItemGroupMdStyle0\n};\nconst labelIosCss = \".item.sc-ion-label-ios-h,.item .sc-ion-label-ios-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-label-ios-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-ios-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-ios-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-ios-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-ios-h,.item-input .sc-ion-label-ios-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-ios-h,.item-textarea .sc-ion-label-ios-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-ios-h,.item-skeleton-text .sc-ion-label-ios-h{overflow:hidden}.label-fixed.sc-ion-label-ios-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-ios-h,.label-floating.sc-ion-label-ios-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-ios-h{-webkit-transition:none;transition:none}.sc-ion-label-ios-s h1,.sc-ion-label-ios-s h2,.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-ios-h{font-size:0.875rem;line-height:1.5}.label-stacked.sc-ion-label-ios-h{margin-bottom:4px;font-size:0.875rem}.label-floating.sc-ion-label-ios-h{margin-bottom:0;-webkit-transform:translate(0, 29px);transform:translate(0, 29px);-webkit-transform-origin:left top;transform-origin:left top;-webkit-transition:-webkit-transform 150ms ease-in-out;transition:-webkit-transform 150ms ease-in-out;transition:transform 150ms ease-in-out;transition:transform 150ms ease-in-out, -webkit-transform 150ms ease-in-out}[dir=rtl].sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl] .sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl].label-floating.sc-ion-label-ios-h,[dir=rtl] .label-floating.sc-ion-label-ios-h{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.label-floating.sc-ion-label-ios-h:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.item-textarea.label-floating.sc-ion-label-ios-h,.item-textarea .label-floating.sc-ion-label-ios-h{-webkit-transform:translate(0, 28px);transform:translate(0, 28px)}.item-has-focus.label-floating.sc-ion-label-ios-h,.item-has-focus .label-floating.sc-ion-label-ios-h,.item-has-placeholder.sc-ion-label-ios-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-ios-h,.item-has-value.label-floating.sc-ion-label-ios-h,.item-has-value .label-floating.sc-ion-label-ios-h{-webkit-transform:scale(0.82);transform:scale(0.82)}.sc-ion-label-ios-s h1{margin-left:0;margin-right:0;margin-top:3px;margin-bottom:2px;font-size:1.375rem;font-weight:normal}.sc-ion-label-ios-s h2{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.0625rem;font-weight:normal}.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-ios-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}.sc-ion-label-ios-s>p{color:var(--ion-color-step-400, var(--ion-text-color-step-600, #999999))}.sc-ion-label-ios-h.in-item-color.sc-ion-label-ios-s>p{color:inherit}.sc-ion-label-ios-s h2:last-child,.sc-ion-label-ios-s h3:last-child,.sc-ion-label-ios-s h4:last-child,.sc-ion-label-ios-s h5:last-child,.sc-ion-label-ios-s h6:last-child,.sc-ion-label-ios-s p:last-child{margin-bottom:0}\";\nconst IonLabelIosStyle0 = labelIosCss;\nconst labelMdCss = \".item.sc-ion-label-md-h,.item .sc-ion-label-md-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-label-md-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-md-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-md-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-md-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-md-h,.item-input .sc-ion-label-md-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-md-h,.item-textarea .sc-ion-label-md-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-md-h,.item-skeleton-text .sc-ion-label-md-h{overflow:hidden}.label-fixed.sc-ion-label-md-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-md-h{-webkit-transition:none;transition:none}.sc-ion-label-md-s h1,.sc-ion-label-md-s h2,.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-md-h{line-height:1.5}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:top left;transform-origin:top left}.label-stacked.label-rtl.sc-ion-label-md-h,.label-floating.label-rtl.sc-ion-label-md-h{-webkit-transform-origin:top right;transform-origin:top right}.label-stacked.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.label-floating.sc-ion-label-md-h{-webkit-transform:translateY(96%);transform:translateY(96%);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1)}.ion-focused.label-floating.sc-ion-label-md-h,.ion-focused .label-floating.sc-ion-label-md-h,.item-has-focus.label-floating.sc-ion-label-md-h,.item-has-focus .label-floating.sc-ion-label-md-h,.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h,.item-has-value.label-floating.sc-ion-label-md-h,.item-has-value .label-floating.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75)}.ion-focused.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-primary, #0054e9)}.ion-focused.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-contrast)}.ion-invalid.ion-touched.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--highlight-color-invalid)}.sc-ion-label-md-s h1{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.5rem;font-weight:normal}.sc-ion-label-md-s h2{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:1rem;font-weight:normal}.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-md-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:1.25rem;text-overflow:inherit;overflow:inherit}.sc-ion-label-md-s>p{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.sc-ion-label-md-h.in-item-color.sc-ion-label-md-s>p{color:inherit}\";\nconst IonLabelMdStyle0 = labelMdCss;\nconst Label = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionColor = createEvent(this, \"ionColor\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inRange = false;\n    this.color = undefined;\n    this.position = undefined;\n    this.noAnimate = false;\n  }\n  componentWillLoad() {\n    this.inRange = !!this.el.closest('ion-range');\n    this.noAnimate = this.position === 'floating';\n    this.emitStyle();\n    this.emitColor();\n  }\n  componentDidLoad() {\n    if (this.noAnimate) {\n      setTimeout(() => {\n        this.noAnimate = false;\n      }, 1000);\n    }\n  }\n  colorChanged() {\n    this.emitColor();\n  }\n  positionChanged() {\n    this.emitStyle();\n  }\n  emitColor() {\n    const {\n      color\n    } = this;\n    this.ionColor.emit({\n      'item-label-color': color !== undefined,\n      [`ion-color-${color}`]: color !== undefined\n    });\n  }\n  emitStyle() {\n    const {\n      inRange,\n      position\n    } = this;\n    // If the label is inside of a range we don't want\n    // to override the classes added by the label that\n    // is a direct child of the item\n    if (!inRange) {\n      this.ionStyle.emit({\n        label: true,\n        [`label-${position}`]: position !== undefined\n      });\n    }\n  }\n  render() {\n    const position = this.position;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '4da84c95351a74b547f8224ecfa66924d21398c5',\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-item-color': hostContext('ion-item.ion-color', this.el),\n        [`label-${position}`]: position !== undefined,\n        [`label-no-animate`]: this.noAnimate,\n        'label-rtl': document.dir === 'rtl'\n      })\n    }, h(\"slot\", {\n      key: 'ea158ebb620275e5492965234d0ab925d391f919'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"color\": [\"colorChanged\"],\n      \"position\": [\"positionChanged\"]\n    };\n  }\n};\nLabel.style = {\n  ios: IonLabelIosStyle0,\n  md: IonLabelMdStyle0\n};\nconst listIosCss = \"ion-list{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;contain:content;list-style-type:none}ion-list.list-inset{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:hidden}.list-ios{background:var(--ion-item-background, var(--ion-background-color, #fff))}.list-ios.list-inset{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px;border-radius:10px}.list-ios.list-inset ion-item:only-child,.list-ios.list-inset ion-item:not(:only-of-type):last-of-type,.list-ios.list-inset ion-item-sliding:last-of-type ion-item{--border-width:0;--inner-border-width:0}.list-ios.list-inset+ion-list.list-inset{margin-top:0}.list-ios-lines-none .item-lines-default{--inner-border-width:0px;--border-width:0px}.list-ios-lines-full .item-lines-default{--inner-border-width:0px;--border-width:0 0 0.55px 0}.list-ios-lines-inset .item-lines-default{--inner-border-width:0 0 0.55px 0;--border-width:0px}ion-card .list-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\nconst IonListIosStyle0 = listIosCss;\nconst listMdCss = \"ion-list{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;contain:content;list-style-type:none}ion-list.list-inset{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:hidden}.list-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;background:var(--ion-item-background, var(--ion-background-color, #fff))}.list-md>.input:last-child::after{inset-inline-start:0}.list-md.list-inset{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px;border-radius:2px}.list-md.list-inset ion-item:not(:only-of-type):last-of-type,.list-md.list-inset ion-item-sliding:last-of-type ion-item{--border-width:0;--inner-border-width:0}.list-md.list-inset ion-item:only-child{--border-width:0;--inner-border-width:0}.list-md.list-inset+ion-list.list-inset{margin-top:0}.list-md-lines-none .item-lines-default{--inner-border-width:0px;--border-width:0px}.list-md-lines-full .item-lines-default{--inner-border-width:0px;--border-width:0 0 1px 0}.list-md-lines-inset .item-lines-default{--inner-border-width:0 0 1px 0;--border-width:0px}ion-card .list-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\nconst IonListMdStyle0 = listMdCss;\nconst List = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.lines = undefined;\n    this.inset = false;\n  }\n  /**\n   * If `ion-item-sliding` are used inside the list, this method closes\n   * any open sliding item.\n   *\n   * Returns `true` if an actual `ion-item-sliding` is closed.\n   */\n  async closeSlidingItems() {\n    const item = this.el.querySelector('ion-item-sliding');\n    if (item === null || item === void 0 ? void 0 : item.closeOpened) {\n      return item.closeOpened();\n    }\n    return false;\n  }\n  render() {\n    const mode = getIonMode(this);\n    const {\n      lines,\n      inset\n    } = this;\n    return h(Host, {\n      key: '5ff2b0b3989cc99ce17abb8bcd7ec1847940d1ec',\n      role: \"list\",\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`list-${mode}`]: true,\n        'list-inset': inset,\n        [`list-lines-${lines}`]: lines !== undefined,\n        [`list-${mode}-lines-${lines}`]: lines !== undefined\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nList.style = {\n  ios: IonListIosStyle0,\n  md: IonListMdStyle0\n};\nconst listHeaderIosCss = \":host{--border-style:solid;--border-width:0;--inner-border-width:0;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:40px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);color:var(--color);overflow:hidden}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.list-header-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex:1 1 auto;flex:1 1 auto}:host(.list-header-lines-inset),:host(.list-header-lines-none){--border-width:0}:host(.list-header-lines-full),:host(.list-header-lines-none){--inner-border-width:0}:host{--background:transparent;--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));padding-right:var(--ion-safe-area-right);padding-left:calc(var(--ion-safe-area-left, 0px) + 16px);position:relative;-ms-flex-align:end;align-items:flex-end;font-size:min(1.375rem, 56.1px);font-weight:700;letter-spacing:0}:host-context([dir=rtl]){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}}::slotted(ion-button),::slotted(ion-label){margin-top:29px;margin-bottom:6px}::slotted(ion-button){--padding-top:0;--padding-bottom:0;-webkit-margin-start:3px;margin-inline-start:3px;-webkit-margin-end:3px;margin-inline-end:3px;min-height:1.4em}:host(.list-header-lines-full){--border-width:0 0 0.55px 0}:host(.list-header-lines-inset){--inner-border-width:0 0 0.55px 0}\";\nconst IonListHeaderIosStyle0 = listHeaderIosCss;\nconst listHeaderMdCss = \":host{--border-style:solid;--border-width:0;--inner-border-width:0;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:40px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);color:var(--color);overflow:hidden}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.list-header-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex:1 1 auto;flex:1 1 auto}:host(.list-header-lines-inset),:host(.list-header-lines-none){--border-width:0}:host(.list-header-lines-full),:host(.list-header-lines-none){--inner-border-width:0}:host{--background:transparent;--color:var(--ion-text-color, #000);--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));padding-right:var(--ion-safe-area-right);padding-left:calc(var(--ion-safe-area-left, 0px) + 16px);min-height:45px;font-size:0.875rem}:host-context([dir=rtl]){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}}:host(.list-header-lines-full){--border-width:0 0 1px 0}:host(.list-header-lines-inset){--inner-border-width:0 0 1px 0}\";\nconst IonListHeaderMdStyle0 = listHeaderMdCss;\nconst ListHeader = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n    this.lines = undefined;\n  }\n  render() {\n    const {\n      lines\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '95ce2135e2b1ad4d7d6020b0fb9bc6e02b3c0851',\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        [`list-header-lines-${lines}`]: lines !== undefined\n      })\n    }, h(\"div\", {\n      key: '3065b0a094bc31a90518898a5126a813c8a33816',\n      class: \"list-header-inner\"\n    }, h(\"slot\", {\n      key: 'fe15c87d7867f3e5d8185645c49c0228496697b8'\n    })));\n  }\n};\nListHeader.style = {\n  ios: IonListHeaderIosStyle0,\n  md: IonListHeaderMdStyle0\n};\nconst noteIosCss = \":host{color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-base)}:host{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6));font-size:max(14px, 1rem)}\";\nconst IonNoteIosStyle0 = noteIosCss;\nconst noteMdCss = \":host{color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-base)}:host{--color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));font-size:0.875rem}\";\nconst IonNoteMdStyle0 = noteMdCss;\nconst Note = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '3c3d9b2aa805c0bc1fdc6270a2bbf4dcc1b96c5b',\n      class: createColorClasses(this.color, {\n        [mode]: true\n      })\n    }, h(\"slot\", {\n      key: '39e34682009fcb705ecafe51825162734bdf14d3'\n    }));\n  }\n};\nNote.style = {\n  ios: IonNoteIosStyle0,\n  md: IonNoteMdStyle0\n};\nconst skeletonTextCss = \":host{--background:rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065);border-radius:var(--border-radius, inherit);display:block;width:100%;height:inherit;margin-top:4px;margin-bottom:4px;background:var(--background);line-height:10px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;pointer-events:none}span{display:inline-block}:host(.in-media){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;height:100%}:host(.skeleton-text-animated){position:relative;background:-webkit-gradient(linear, left top, right top, color-stop(8%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065)), color-stop(18%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.135)), color-stop(33%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065)));background:linear-gradient(to right, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065) 8%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.135) 18%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065) 33%);background-size:800px 104px;-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-name:shimmer;animation-name:shimmer;-webkit-animation-timing-function:linear;animation-timing-function:linear}@-webkit-keyframes shimmer{0%{background-position:-400px 0}100%{background-position:400px 0}}@keyframes shimmer{0%{background-position:-400px 0}100%{background-position:400px 0}}\";\nconst IonSkeletonTextStyle0 = skeletonTextCss;\nconst SkeletonText = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.animated = false;\n  }\n  componentWillLoad() {\n    this.emitStyle();\n  }\n  emitStyle() {\n    // The emitted property is used by item in order\n    // to add the item-skeleton-text class which applies\n    // overflow: hidden to its label\n    const style = {\n      'skeleton-text': true\n    };\n    this.ionStyle.emit(style);\n  }\n  render() {\n    const animated = this.animated && config.getBoolean('animated', true);\n    const inMedia = hostContext('ion-avatar', this.el) || hostContext('ion-thumbnail', this.el);\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'c193f9a8e8efab5139544f047bdae4b6d421aa86',\n      class: {\n        [mode]: true,\n        'skeleton-text-animated': animated,\n        'in-media': inMedia\n      }\n    }, h(\"span\", {\n      key: 'e15db679bd3c359b4df5123efd2f92e5b380fde3'\n    }, \"\\u00A0\"));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nSkeletonText.style = IonSkeletonTextStyle0;\nexport { Item as ion_item, ItemDivider as ion_item_divider, ItemGroup as ion_item_group, Label as ion_label, List as ion_list, ListHeader as ion_list_header, Note as ion_note, SkeletonText as ion_skeleton_text };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,YAAY;AAClB,IAAM,kBAAkB;AACxB,IAAM,OAAO,MAAM;AAAA,EACjB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,mBAAmB,CAAC;AACzB,SAAK,aAAa,oBAAI,IAAI;AAC1B,SAAK,0BAA0B,CAAC;AAChC,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACd;AAAA,EACA,gBAAgB;AAEd,SAAK,YAAY,KAAK,YAAY;AAAA,EACpC;AAAA,EACA,kBAAkB,IAAI;AACpB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAIJ,QAAI,UAAU,QAAW;AACvB,WAAK,mBAAmB,GAAG;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,UAAU,IAAI;AACZ,OAAG,gBAAgB;AACnB,UAAM,UAAU,GAAG,OAAO;AAC1B,UAAM,gBAAgB,GAAG;AACzB,UAAM,YAAY,CAAC;AACnB,UAAM,cAAc,KAAK,WAAW,IAAI,OAAO,KAAK,CAAC;AACrD,QAAI,iBAAiB;AACrB,WAAO,KAAK,aAAa,EAAE,QAAQ,SAAO;AACxC,UAAI,cAAc,GAAG,GAAG;AACtB,cAAM,UAAU,QAAQ,GAAG;AAC3B,YAAI,CAAC,YAAY,OAAO,GAAG;AACzB,2BAAiB;AAAA,QACnB;AACA,kBAAU,OAAO,IAAI;AAAA,MACvB;AAAA,IACF,CAAC;AACD,QAAI,CAAC,kBAAkB,OAAO,KAAK,SAAS,EAAE,WAAW,OAAO,KAAK,WAAW,EAAE,QAAQ;AACxF,uBAAiB;AAAA,IACnB;AACA,QAAI,gBAAgB;AAClB,WAAK,WAAW,IAAI,SAAS,SAAS;AACtC,kBAAY,IAAI;AAAA,IAClB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,oBAAoB;AAClB,SAAK,0BAA0B,kBAAkB,KAAK,IAAI,CAAC,YAAY,CAAC;AAAA,EAC1E;AAAA,EACA,mBAAmB;AACjB,QAAI,MAAM;AACR,WAAK,kBAAkB;AACvB,WAAK,YAAY,KAAK,YAAY;AAAA,IACpC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAElB,UAAM,SAAS,KAAK,GAAG,iBAAiB,mDAAmD;AAI3F,UAAM,SAAS,KAAK,GAAG,iBAAiB,4EAA4E;AAEpH,UAAM,aAAa,KAAK,GAAG,iBAAiB,wCAAwC;AAGpF,SAAK,iBAAiB,OAAO,SAAS,OAAO,SAAS,KAAK,OAAO,SAAS,WAAW,SAAS,KAAK,OAAO,SAAS,KAAK,KAAK,YAAY;AAAA,EAC5I;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,UAAM,SAAS,KAAK,GAAG,iBAAiB,mDAAmD;AAC3F,WAAO,OAAO,WAAW,KAAK,CAAC,KAAK;AAAA,EACtC;AAAA;AAAA;AAAA,EAGA,cAAc;AACZ,WAAO,KAAK,SAAS,UAAa,KAAK;AAAA,EACzC;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,YAAY,KAAK,KAAK,SAAS;AAAA,EAC7C;AAAA,EACA,cAAc;AACZ,UAAM,iBAAiB,KAAK,GAAG,cAAc,gBAAgB;AAC7D,WAAO,KAAK,YAAY,KAAK,mBAAmB;AAAA,EAClD;AAAA,EACA,aAAa;AACX,UAAM,UAAU,KAAK,GAAG,cAAc,gBAAgB;AACtD,QAAI,YAAY,MAAM;AACpB,WAAK,GAAG,UAAU,IAAI,qBAAqB;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,UAAM,WAAW,KAAK,GAAG,iBAAiB,0KAA0K;AACpN,WAAO,SAAS,CAAC;AAAA,EACnB;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,cAAc,CAAC;AACrB,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,YAAY,KAAK,YAAY;AACnC,UAAM,cAAc,KAAK,YAAY;AACrC,UAAM,UAAU,YAAY,SAAS,SAAY,WAAW,MAAM;AAClE,UAAM,QAAQ,YAAY,WAAW;AAAA,MACnC,MAAM,KAAK;AAAA,IACb,IAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAI,UAAU,CAAC;AACf,UAAM,mBAAmB,KAAK,oBAAoB;AAGlD,QAAI,aAAa,qBAAqB,UAAa,CAAC,gBAAgB;AAClE,gBAAU;AAAA,QACR,SAAS,QAAM;AACb,cAAI,WAAW;AACb,oBAAQ,MAAM,IAAI,iBAAiB,eAAe;AAAA,UACpD;AACA,cAAI,qBAAqB,UAAa,CAAC,gBAAgB;AACrD,kBAAM,OAAO,GAAG,aAAa;AAC7B,kBAAMA,UAAS,KAAK,CAAC;AACrB,gBAAI,GAAG,WAAW;AAShB,oBAAM,0BAA0B,KAAK,GAAG,WAAW,SAASA,OAAM;AAClE,kBAAI,yBAAyB;AAM3B,oBAAI,iBAAiB,YAAY,eAAe,iBAAiB,YAAY,gBAAgB;AAC3F,mCAAiB,SAAS;AAAA,gBAC5B;AACA,iCAAiB,MAAM;AAMvB,mBAAG,yBAAyB;AAAA,cAC9B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,aAAa,WAAW,SAAY,SAAS,SAAS,SAAS;AACrE,SAAK,WAAW,QAAQ,WAAS;AAC/B,aAAO,OAAO,aAAa,KAAK;AAAA,IAClC,CAAC;AACD,UAAM,eAAe,YAAY,YAAY,2BAA2B,IAAI,SAAS;AACrF,UAAM,SAAS,YAAY,YAAY,KAAK,EAAE,KAAK,CAAC,YAAY,mBAAmB,KAAK,EAAE;AAK1F,UAAM,qCAAqC,qBAAqB,UAAa,CAAC,CAAC,aAAa,cAAc,EAAE,SAAS,iBAAiB,OAAO;AAC7I,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,iBAAiB;AAAA,MACjB,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,gBAAgB,GAAG,mBAAmB,KAAK,OAAO;AAAA,QACnH,MAAM;AAAA,QACN,CAAC,IAAI,GAAG;AAAA,QACR,sBAAsB,UAAU;AAAA,QAChC,CAAC,cAAc,KAAK,EAAE,GAAG,UAAU;AAAA,QACnC,qCAAqC;AAAA,QACrC,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,wBAAwB,KAAK;AAAA,QAC7B,mBAAmB;AAAA,QACnB,iBAAiB,KAAK;AAAA,QACtB,YAAY,SAAS,QAAQ;AAAA,MAC/B,CAAC,CAAC;AAAA,MACF,MAAM,SAAS,aAAa;AAAA,IAC9B,GAAG,EAAE,SAAS,OAAO,OAAO;AAAA,MAC1B,KAAK;AAAA,IACP,GAAG,OAAO,yBAAyB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN;AAAA,IACF,GAAG,OAAO,GAAG,EAAE,QAAQ;AAAA,MACrB,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC,GAAG,EAAE,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,GAAG,cAAc,EAAE,YAAY;AAAA,MAC9B,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAe;AAAA,MACf,YAAY,eAAe;AAAA,IAC7B,CAAC,CAAC,GAAG,eAAe,SAAS,QAAQ,EAAE,qBAAqB;AAAA,MAC1D,KAAK;AAAA,IACP,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,UAAU,CAAC,eAAe;AAAA,IAC5B;AAAA,EACF;AACF;AACA,KAAK,QAAQ;AAAA,EACX,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,oBAAoB;AAC1B,IAAM,0BAA0B;AAChC,IAAM,mBAAmB;AACzB,IAAM,yBAAyB;AAC/B,IAAM,cAAc,MAAM;AAAA,EACxB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,mBAAmB,KAAK,OAAO;AAAA,QACpC,CAAC,IAAI,GAAG;AAAA,QACR,uBAAuB,KAAK;AAAA,QAC5B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC,GAAG,EAAE,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,YAAY,QAAQ;AAAA,EAClB,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,YAAY,MAAM;AAAA,EACtB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAAA,EAChC;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,QACL,CAAC,IAAI,GAAG;AAAA;AAAA,QAER,CAAC,cAAc,IAAI,EAAE,GAAG;AAAA,QACxB,MAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,UAAU,QAAQ;AAAA,EAChB,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,QAAQ,MAAM;AAAA,EAClB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,oBAAoB;AAClB,SAAK,UAAU,CAAC,CAAC,KAAK,GAAG,QAAQ,WAAW;AAC5C,SAAK,YAAY,KAAK,aAAa;AACnC,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,WAAW;AAClB,iBAAW,MAAM;AACf,aAAK,YAAY;AAAA,MACnB,GAAG,GAAI;AAAA,IACT;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,kBAAkB;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,YAAY;AACV,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,SAAK,SAAS,KAAK;AAAA,MACjB,oBAAoB,UAAU;AAAA,MAC9B,CAAC,aAAa,KAAK,EAAE,GAAG,UAAU;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EACA,YAAY;AACV,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AAIJ,QAAI,CAAC,SAAS;AACZ,WAAK,SAAS,KAAK;AAAA,QACjB,OAAO;AAAA,QACP,CAAC,SAAS,QAAQ,EAAE,GAAG,aAAa;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM,WAAW,KAAK;AACtB,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,mBAAmB,KAAK,OAAO;AAAA,QACpC,CAAC,IAAI,GAAG;AAAA,QACR,iBAAiB,YAAY,sBAAsB,KAAK,EAAE;AAAA,QAC1D,CAAC,SAAS,QAAQ,EAAE,GAAG,aAAa;AAAA,QACpC,CAAC,kBAAkB,GAAG,KAAK;AAAA,QAC3B,aAAa,SAAS,QAAQ;AAAA,MAChC,CAAC;AAAA,IACH,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,SAAS,CAAC,cAAc;AAAA,MACxB,YAAY,CAAC,iBAAiB;AAAA,IAChC;AAAA,EACF;AACF;AACA,MAAM,QAAQ;AAAA,EACZ,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,YAAY;AAClB,IAAM,kBAAkB;AACxB,IAAM,OAAO,MAAM;AAAA,EACjB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,oBAAoB;AAAA;AACxB,YAAM,OAAO,KAAK,GAAG,cAAc,kBAAkB;AACrD,UAAI,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,aAAa;AAChE,eAAO,KAAK,YAAY;AAAA,MAC1B;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,QACL,CAAC,IAAI,GAAG;AAAA;AAAA,QAER,CAAC,QAAQ,IAAI,EAAE,GAAG;AAAA,QAClB,cAAc;AAAA,QACd,CAAC,cAAc,KAAK,EAAE,GAAG,UAAU;AAAA,QACnC,CAAC,QAAQ,IAAI,UAAU,KAAK,EAAE,GAAG,UAAU;AAAA,MAC7C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,KAAK,QAAQ;AAAA,EACX,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,mBAAmB;AACzB,IAAM,yBAAyB;AAC/B,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,aAAa,MAAM;AAAA,EACvB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,mBAAmB,KAAK,OAAO;AAAA,QACpC,CAAC,IAAI,GAAG;AAAA,QACR,CAAC,qBAAqB,KAAK,EAAE,GAAG,UAAU;AAAA,MAC5C,CAAC;AAAA,IACH,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC,CAAC;AAAA,EACL;AACF;AACA,WAAW,QAAQ;AAAA,EACjB,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,YAAY;AAClB,IAAM,kBAAkB;AACxB,IAAM,OAAO,MAAM;AAAA,EACjB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,mBAAmB,KAAK,OAAO;AAAA,QACpC,CAAC,IAAI,GAAG;AAAA,MACV,CAAC;AAAA,IACH,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC;AAAA,EACJ;AACF;AACA,KAAK,QAAQ;AAAA,EACX,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,kBAAkB;AACxB,IAAM,wBAAwB;AAC9B,IAAM,eAAe,MAAM;AAAA,EACzB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,oBAAoB;AAClB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,YAAY;AAIV,UAAM,QAAQ;AAAA,MACZ,iBAAiB;AAAA,IACnB;AACA,SAAK,SAAS,KAAK,KAAK;AAAA,EAC1B;AAAA,EACA,SAAS;AACP,UAAM,WAAW,KAAK,YAAY,OAAO,WAAW,YAAY,IAAI;AACpE,UAAM,UAAU,YAAY,cAAc,KAAK,EAAE,KAAK,YAAY,iBAAiB,KAAK,EAAE;AAC1F,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO;AAAA,QACL,CAAC,IAAI,GAAG;AAAA,QACR,0BAA0B;AAAA,QAC1B,YAAY;AAAA,MACd;AAAA,IACF,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,GAAG,MAAQ,CAAC;AAAA,EACd;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,aAAa,QAAQ;", "names": ["target"], "x_google_ignoreList": [0]}
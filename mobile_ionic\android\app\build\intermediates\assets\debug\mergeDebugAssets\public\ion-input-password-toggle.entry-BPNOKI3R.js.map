{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-input-password-toggle.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { p as printIonWarning } from './index-cfd9c1f2.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { x as eye, y as eyeOff } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nconst iosInputPasswordToggleCss = \"\";\nconst IonInputPasswordToggleIosStyle0 = iosInputPasswordToggleCss;\nconst mdInputPasswordToggleCss = \"\";\nconst IonInputPasswordToggleMdStyle0 = mdInputPasswordToggleCss;\nconst InputPasswordToggle = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.togglePasswordVisibility = () => {\n      const {\n        inputElRef\n      } = this;\n      if (!inputElRef) {\n        return;\n      }\n      inputElRef.type = inputElRef.type === 'text' ? 'password' : 'text';\n    };\n    this.color = undefined;\n    this.showIcon = undefined;\n    this.hideIcon = undefined;\n    this.type = 'password';\n  }\n  /**\n   * Whenever the input type changes we need to re-run validation to ensure the password\n   * toggle is being used with the correct input type. If the application changes the type\n   * outside of this component we also need to re-render so the correct icon is shown.\n   */\n  onTypeChange(newValue) {\n    if (newValue !== 'text' && newValue !== 'password') {\n      printIonWarning(`[ion-input-password-toggle] - Only inputs of type \"text\" or \"password\" are supported. Input of type \"${newValue}\" is not compatible.`, this.el);\n      return;\n    }\n  }\n  connectedCallback() {\n    const {\n      el\n    } = this;\n    const inputElRef = this.inputElRef = el.closest('ion-input');\n    if (!inputElRef) {\n      printIonWarning('[ion-input-password-toggle] - No ancestor ion-input found. This component must be slotted inside of an ion-input.', el);\n      return;\n    }\n    /**\n     * Important: Set the type in connectedCallback because the default value\n     * of this.type may not always be accurate. Usually inputs have the \"password\" type\n     * but it is possible to have the input to initially have the \"text\" type. In that scenario\n     * the wrong icon will show briefly before switching to the correct icon. Setting the\n     * type here allows us to avoid that flicker.\n     */\n    this.type = inputElRef.type;\n  }\n  disconnectedCallback() {\n    this.inputElRef = null;\n  }\n  render() {\n    var _a, _b;\n    const {\n      color,\n      type\n    } = this;\n    const mode = getIonMode(this);\n    const showPasswordIcon = (_a = this.showIcon) !== null && _a !== void 0 ? _a : eye;\n    const hidePasswordIcon = (_b = this.hideIcon) !== null && _b !== void 0 ? _b : eyeOff;\n    const isPasswordVisible = type === 'text';\n    return h(Host, {\n      key: '1a28e078c83e74c72d8bb8189ece93ec2e3fa3d0',\n      class: createColorClasses(color, {\n        [mode]: true\n      })\n    }, h(\"ion-button\", {\n      key: '039d1bab764093bb6fe4a34299b0872abda087fd',\n      mode: mode,\n      color: color,\n      fill: \"clear\",\n      shape: \"round\",\n      \"aria-checked\": isPasswordVisible ? 'true' : 'false',\n      \"aria-label\": \"show password\",\n      role: \"switch\",\n      type: \"button\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the password toggle\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onClick: this.togglePasswordVisibility\n    }, h(\"ion-icon\", {\n      key: '26477ee97b808c3d79944bf5e33d4e05f1ae0b3f',\n      slot: \"icon-only\",\n      \"aria-hidden\": \"true\",\n      icon: isPasswordVisible ? hidePasswordIcon : showPasswordIcon\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"type\": [\"onTypeChange\"]\n    };\n  }\n};\nInputPasswordToggle.style = {\n  ios: IonInputPasswordToggleIosStyle0,\n  md: IonInputPasswordToggleMdStyle0\n};\nexport { InputPasswordToggle as ion_input_password_toggle };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,4BAA4B;AAClC,IAAM,kCAAkC;AACxC,IAAM,2BAA2B;AACjC,IAAM,iCAAiC;AACvC,IAAM,sBAAsB,MAAM;AAAA,EAChC,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,2BAA2B,MAAM;AACpC,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,CAAC,YAAY;AACf;AAAA,MACF;AACA,iBAAW,OAAO,WAAW,SAAS,SAAS,aAAa;AAAA,IAC9D;AACA,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,UAAU;AACrB,QAAI,aAAa,UAAU,aAAa,YAAY;AAClD,sBAAgB,wGAAwG,QAAQ,wBAAwB,KAAK,EAAE;AAC/J;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,KAAK,aAAa,GAAG,QAAQ,WAAW;AAC3D,QAAI,CAAC,YAAY;AACf,sBAAgB,qHAAqH,EAAE;AACvI;AAAA,IACF;AAQA,SAAK,OAAO,WAAW;AAAA,EACzB;AAAA,EACA,uBAAuB;AACrB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,SAAS;AACP,QAAI,IAAI;AACR,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,oBAAoB,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK;AAC/E,UAAM,oBAAoB,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK;AAC/E,UAAM,oBAAoB,SAAS;AACnC,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,mBAAmB,OAAO;AAAA,QAC/B,CAAC,IAAI,GAAG;AAAA,MACV,CAAC;AAAA,IACH,GAAG,EAAE,cAAc;AAAA,MACjB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,OAAO;AAAA,MACP,gBAAgB,oBAAoB,SAAS;AAAA,MAC7C,cAAc;AAAA,MACd,MAAM;AAAA,MACN,MAAM;AAAA,MACN,eAAe,QAAM;AAMnB,WAAG,eAAe;AAAA,MACpB;AAAA,MACA,SAAS,KAAK;AAAA,IAChB,GAAG,EAAE,YAAY;AAAA,MACf,KAAK;AAAA,MACL,MAAM;AAAA,MACN,eAAe;AAAA,MACf,MAAM,oBAAoB,mBAAmB;AAAA,IAC/C,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,QAAQ,CAAC,cAAc;AAAA,IACzB;AAAA,EACF;AACF;AACA,oBAAoB,QAAQ;AAAA,EAC1B,KAAK;AAAA,EACL,IAAI;AACN;", "names": [], "x_google_ignoreList": [0]}
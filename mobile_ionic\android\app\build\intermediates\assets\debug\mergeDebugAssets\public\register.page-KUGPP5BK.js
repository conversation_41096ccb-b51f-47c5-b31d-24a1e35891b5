import {
  AuthService
} from "./chunk-XKXQPGS3.js";
import "./chunk-FKALCVFZ.js";
import {
  FcmService
} from "./chunk-RDFT5QPW.js";
import {
  environment
} from "./chunk-I7MI46CM.js";
import "./chunk-TAZAZ6IP.js";
import {
  AlertController,
  Component,
  FormsModule,
  HttpClient,
  IonButton,
  IonContent,
  IonInput,
  IonItem,
  IonLabel,
  IonText,
  IonicModule,
  NgControlStatus,
  NgControlStatusGroup,
  NgForm,
  NgModel,
  Platform,
  RequiredValidator,
  Router,
  TextValueAccessorDirective,
  setClassMetadata,
  ɵNgNoValidate,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵtext,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import {
  __async
} from "./chunk-UL2P3LPA.js";

// src/app/pages/register/register.page.ts
var RegisterPage = class _RegisterPage {
  constructor(authService, router, http, platform, fcmService, alertController) {
    this.authService = authService;
    this.router = router;
    this.http = http;
    this.platform = platform;
    this.fcmService = fcmService;
    this.alertController = alertController;
    this.user = {
      full_name: "",
      email: "",
      password: "",
      confirmPassword: ""
    };
    this.fcmToken = "";
    this.fcmTokenReady = false;
  }
  ngOnInit() {
    return __async(this, null, function* () {
      console.log("\u{1F525} Register page initializing...");
      yield this.initializeFCM();
    });
  }
  /**
   * Initialize FCM service and get token
   */
  initializeFCM() {
    return __async(this, null, function* () {
      try {
        console.log("\u{1F525} Initializing FCM for registration...");
        yield this.fcmService.initPush();
        yield this.getFCMToken();
        console.log("\u2705 FCM initialization complete, token ready:", !!this.fcmToken);
        this.fcmTokenReady = true;
      } catch (error) {
        console.error("\u274C FCM initialization failed:", error);
        this.fcmTokenReady = false;
      }
    });
  }
  getFCMToken() {
    return __async(this, null, function* () {
      try {
        if (!this.platform.is("cordova") && !this.platform.is("capacitor")) {
          console.log("Running in browser, using mock FCM token");
          this.fcmToken = "browser-mock-token-" + Math.random().toString(36).substring(2, 15);
          console.log("Mock FCM Token:", this.fcmToken);
          return;
        }
        console.log("Getting FCM token from service...");
        this.fcmToken = yield this.fcmService.getToken();
        console.log("\u2705 FCM Token obtained:", this.fcmToken.substring(0, 20) + "...");
      } catch (error) {
        console.error("\u274C Error getting FCM token from service:", error);
        this.fcmToken = "";
      }
    });
  }
  onRegister() {
    return __async(this, null, function* () {
      if (this.user.password !== this.user.confirmPassword) {
        yield this.presentAlert("Registration Failed", "Passwords do not match!");
        return;
      }
      this.authService.register({
        full_name: this.user.full_name,
        email: this.user.email,
        password: this.user.password,
        password_confirmation: this.user.confirmPassword
      }).subscribe({
        next: (res) => __async(this, null, function* () {
          console.log("Registration successful:", res);
          if (this.fcmToken) {
            console.log("Registering FCM token after registration:", this.fcmToken);
            const payload = {
              token: this.fcmToken,
              device_type: this.platform.is("ios") ? "ios" : "android",
              project_id: environment.firebase.projectId
              // Note: We don't have user_id yet since we're not logged in
            };
            console.log("Token registration payload:", payload);
            this.fcmService.registerTokenWithBackend(this.fcmToken);
          } else {
            console.warn("No FCM token available to register after registration");
          }
          yield this.presentAlert("Registration Successful", "Your account has been created successfully. Please log in.");
          this.router.navigate(["/login"]);
        }),
        error: (err) => __async(this, null, function* () {
          console.error("Registration error:", err);
          yield this.presentAlert("Registration Failed", "Registration failed: " + (err.error?.message || "Unknown error"));
        })
      });
    });
  }
  /**
   * Helper method to register a token with multiple endpoints
   * @param payload The token payload to send
   */
  registerTokenWithEndpoints(payload) {
    return __async(this, null, function* () {
      const endpoints = [
        `${environment.apiUrl}/device-token`,
        "http://localhost:8000/api/device-token",
        "https://7af9-43-226-6-217.ngrok-free.app/api/device-token"
      ];
      for (const endpoint of endpoints) {
        try {
          const response = yield this.http.post(endpoint, payload).toPromise();
          console.log(`FCM token registered with ${endpoint}:`, response);
          localStorage.setItem("fcm_token", this.fcmToken);
          break;
        } catch (error) {
          console.error(`Error registering token with ${endpoint}:`, error);
        }
      }
    });
  }
  presentAlert(header, message) {
    return __async(this, null, function* () {
      const alert = yield this.alertController.create({
        header,
        message,
        buttons: ["OK"]
      });
      yield alert.present();
    });
  }
  goToLogin() {
    this.router.navigate(["/login"]);
  }
  static {
    this.\u0275fac = function RegisterPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _RegisterPage)(\u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(HttpClient), \u0275\u0275directiveInject(Platform), \u0275\u0275directiveInject(FcmService), \u0275\u0275directiveInject(AlertController));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RegisterPage, selectors: [["app-register"]], decls: 33, vars: 4, consts: [[1, "ion-padding", "register-bg"], [1, "register-wrapper"], ["src", "assets/ALERTO.png", "alt", "App Logo", 1, "register-logo"], [1, "register-title"], [1, "register-form", 3, "ngSubmit"], ["position", "floating"], ["type", "text", "name", "full_name", "required", "", 3, "ngModelChange", "ngModel"], ["type", "email", "name", "email", "required", "", 3, "ngModelChange", "ngModel"], ["type", "password", "name", "password", "required", "", 3, "ngModelChange", "ngModel"], ["type", "password", "name", "confirmPassword", "required", "", 3, "ngModelChange", "ngModel"], ["expand", "block", "type", "submit", 1, "register-btn"], [1, "ion-text-center", "ion-margin-top"], [3, "click"]], template: function RegisterPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-content", 0)(1, "div", 1);
        \u0275\u0275element(2, "img", 2);
        \u0275\u0275elementStart(3, "h1", 3);
        \u0275\u0275text(4, "Sign Up Here!");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(5, "form", 4);
        \u0275\u0275listener("ngSubmit", function RegisterPage_Template_form_ngSubmit_5_listener() {
          return ctx.onRegister();
        });
        \u0275\u0275elementStart(6, "ion-item")(7, "ion-label", 5);
        \u0275\u0275text(8, "Full Name:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(9, "ion-input", 6);
        \u0275\u0275twoWayListener("ngModelChange", function RegisterPage_Template_ion_input_ngModelChange_9_listener($event) {
          \u0275\u0275twoWayBindingSet(ctx.user.full_name, $event) || (ctx.user.full_name = $event);
          return $event;
        });
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(10, "ion-item")(11, "ion-label", 5);
        \u0275\u0275text(12, "Email:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(13, "ion-input", 7);
        \u0275\u0275twoWayListener("ngModelChange", function RegisterPage_Template_ion_input_ngModelChange_13_listener($event) {
          \u0275\u0275twoWayBindingSet(ctx.user.email, $event) || (ctx.user.email = $event);
          return $event;
        });
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(14, "ion-item")(15, "ion-label", 5);
        \u0275\u0275text(16, "Password:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(17, "ion-input", 8);
        \u0275\u0275twoWayListener("ngModelChange", function RegisterPage_Template_ion_input_ngModelChange_17_listener($event) {
          \u0275\u0275twoWayBindingSet(ctx.user.password, $event) || (ctx.user.password = $event);
          return $event;
        });
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(18, "ion-item")(19, "ion-label", 5);
        \u0275\u0275text(20, "Confirm Password:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(21, "ion-input", 9);
        \u0275\u0275twoWayListener("ngModelChange", function RegisterPage_Template_ion_input_ngModelChange_21_listener($event) {
          \u0275\u0275twoWayBindingSet(ctx.user.confirmPassword, $event) || (ctx.user.confirmPassword = $event);
          return $event;
        });
        \u0275\u0275elementEnd()();
        \u0275\u0275element(22, "br")(23, "br");
        \u0275\u0275elementStart(24, "ion-button", 10);
        \u0275\u0275text(25, "Register");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(26, "div", 11)(27, "ion-text");
        \u0275\u0275text(28, "Already have an account? ");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(29, "a", 12);
        \u0275\u0275listener("click", function RegisterPage_Template_a_click_29_listener() {
          return ctx.goToLogin();
        });
        \u0275\u0275elementStart(30, "strong")(31, "u");
        \u0275\u0275text(32, "Log In");
        \u0275\u0275elementEnd()()()()()();
      }
      if (rf & 2) {
        \u0275\u0275advance(9);
        \u0275\u0275twoWayProperty("ngModel", ctx.user.full_name);
        \u0275\u0275advance(4);
        \u0275\u0275twoWayProperty("ngModel", ctx.user.email);
        \u0275\u0275advance(4);
        \u0275\u0275twoWayProperty("ngModel", ctx.user.password);
        \u0275\u0275advance(4);
        \u0275\u0275twoWayProperty("ngModel", ctx.user.confirmPassword);
      }
    }, dependencies: [IonicModule, IonButton, IonContent, IonInput, IonItem, IonLabel, IonText, TextValueAccessorDirective, FormsModule, \u0275NgNoValidate, NgControlStatus, NgControlStatusGroup, RequiredValidator, NgModel, NgForm], styles: ["\n\n.register-container[_ngcontent-%COMP%] {\n  height: 80vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.register-wrapper[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 420px;\n  padding: 32px 28px;\n  margin: 0 auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n}\n.register-logo[_ngcontent-%COMP%] {\n  width: 280px;\n  height: 280px;\n}\n.register-title[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  font-weight: 700;\n}\n.register-desc[_ngcontent-%COMP%] {\n  color: #888;\n  font-size: 1.1rem;\n}\n.register-form[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%] {\n  font-size: 18px;\n  color: #888;\n}\n.register-form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\n  width: 100%;\n  --highlight-color-focused: xz#000000;\n  --min-height: 44px;\n  --padding-start: 0;\n  --padding-end: 0;\n  --inner-padding-end: 0;\n  --inner-padding-start: 0;\n}\n.forgot-link[_ngcontent-%COMP%] {\n  text-align: right;\n  font-size: 0.95rem;\n}\n.forgot-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  color: #1565c0;\n  text-decoration: none;\n}\n.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  color: #1565c0;\n  text-decoration: none;\n  font-weight: 600;\n}\n.register-link[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: #444;\n}\n.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  color: #1565c0;\n  text-decoration: none;\n  font-weight: 600;\n}\n.register-btn[_ngcontent-%COMP%] {\n  --border-radius: 25px;\n  font-size: 1.1rem;\n  height: 48px;\n  width: 100%;\n}\n/*# sourceMappingURL=register.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RegisterPage, [{
    type: Component,
    args: [{ standalone: true, imports: [IonicModule, FormsModule], selector: "app-register", template: '\r\n\r\n<ion-content class="ion-padding register-bg">\r\n  <div class="register-wrapper">\r\n    <img src="assets/ALERTO.png" alt="App Logo" class="register-logo" />\r\n    <h1 class="register-title">Sign Up Here!</h1>\r\n    <form (ngSubmit)="onRegister()" class="register-form">\r\n      <ion-item>\r\n        <ion-label position="floating">Full Name:</ion-label>\r\n        <ion-input type="text" [(ngModel)]="user.full_name" name="full_name" required></ion-input>\r\n      </ion-item>\r\n\r\n      <ion-item>\r\n        <ion-label position="floating">Email:</ion-label>\r\n        <ion-input type="email" [(ngModel)]="user.email" name="email" required></ion-input>\r\n      </ion-item>\r\n\r\n      <ion-item>\r\n        <ion-label position="floating">Password:</ion-label>\r\n        <ion-input type="password" [(ngModel)]="user.password" name="password" required></ion-input>\r\n      </ion-item>\r\n\r\n      <ion-item>\r\n        <ion-label position="floating">Confirm Password:</ion-label>\r\n        <ion-input type="password" [(ngModel)]="user.confirmPassword" name="confirmPassword" required></ion-input>\r\n      </ion-item>\r\n<br>  <br>\r\n      <ion-button expand="block" type="submit" class="register-btn">Register</ion-button>\r\n    </form>\r\n\r\n    <div class="ion-text-center ion-margin-top">\r\n      <ion-text>Already have an account? </ion-text>\r\n      <a (click)="goToLogin()"><strong><u>Log In</u></strong></a>\r\n    </div>\r\n\r\n    \r\n  </div>\r\n</ion-content>\r\n', styles: ["/* src/app/pages/register/register.page.scss */\n.register-container {\n  height: 80vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.register-wrapper {\n  width: 100%;\n  max-width: 420px;\n  padding: 32px 28px;\n  margin: 0 auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n}\n.register-logo {\n  width: 280px;\n  height: 280px;\n}\n.register-title {\n  font-size: 2rem;\n  font-weight: 700;\n}\n.register-desc {\n  color: #888;\n  font-size: 1.1rem;\n}\n.register-form ion-label {\n  font-size: 18px;\n  color: #888;\n}\n.register-form ion-item {\n  width: 100%;\n  --highlight-color-focused: xz#000000;\n  --min-height: 44px;\n  --padding-start: 0;\n  --padding-end: 0;\n  --inner-padding-end: 0;\n  --inner-padding-start: 0;\n}\n.forgot-link {\n  text-align: right;\n  font-size: 0.95rem;\n}\n.forgot-link a {\n  color: #1565c0;\n  text-decoration: none;\n}\n.register-link a {\n  color: #1565c0;\n  text-decoration: none;\n  font-weight: 600;\n}\n.register-link {\n  font-size: 1rem;\n  color: #444;\n}\n.register-link a {\n  color: #1565c0;\n  text-decoration: none;\n  font-weight: 600;\n}\n.register-btn {\n  --border-radius: 25px;\n  font-size: 1.1rem;\n  height: 48px;\n  width: 100%;\n}\n/*# sourceMappingURL=register.page.css.map */\n"] }]
  }], () => [{ type: AuthService }, { type: Router }, { type: HttpClient }, { type: Platform }, { type: FcmService }, { type: AlertController }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RegisterPage, { className: "RegisterPage", filePath: "src/app/pages/register/register.page.ts", lineNumber: 17 });
})();
export {
  RegisterPage
};
//# sourceMappingURL=register.page-KUGPP5BK.js.map

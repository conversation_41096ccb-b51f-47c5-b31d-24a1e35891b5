{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/keyboard-52278bd7.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { K as Keyboard } from './keyboard-73175e24.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\nconst KEYBOARD_DID_OPEN = 'ionKeyboardDidShow';\nconst KEYBOARD_DID_CLOSE = 'ionKeyboardDidHide';\nconst KEYBOARD_THRESHOLD = 150;\n// TODO(FW-2832): types\nlet previousVisualViewport = {};\nlet currentVisualViewport = {};\nlet keyboardOpen = false;\n/**\n * This is only used for tests\n */\nconst resetKeyboardAssist = () => {\n  previousVisualViewport = {};\n  currentVisualViewport = {};\n  keyboardOpen = false;\n};\nconst startKeyboardAssist = win => {\n  const nativeEngine = Keyboard.getEngine();\n  /**\n   * If the native keyboard plugin is available\n   * then we are running in a native environment. As a result\n   * we should only listen on the native events instead of\n   * using the Visual Viewport as the Ionic webview manipulates\n   * how it resizes such that the Visual Viewport API is not\n   * reliable here.\n   */\n  if (nativeEngine) {\n    startNativeListeners(win);\n  } else {\n    if (!win.visualViewport) {\n      return;\n    }\n    currentVisualViewport = copyVisualViewport(win.visualViewport);\n    win.visualViewport.onresize = () => {\n      trackViewportChanges(win);\n      if (keyboardDidOpen() || keyboardDidResize(win)) {\n        setKeyboardOpen(win);\n      } else if (keyboardDidClose(win)) {\n        setKeyboardClose(win);\n      }\n    };\n  }\n};\n/**\n * Listen for events fired by native keyboard plugin\n * in Capacitor/Cordova so devs only need to listen\n * in one place.\n */\nconst startNativeListeners = win => {\n  win.addEventListener('keyboardDidShow', ev => setKeyboardOpen(win, ev));\n  win.addEventListener('keyboardDidHide', () => setKeyboardClose(win));\n};\nconst setKeyboardOpen = (win, ev) => {\n  fireKeyboardOpenEvent(win, ev);\n  keyboardOpen = true;\n};\nconst setKeyboardClose = win => {\n  fireKeyboardCloseEvent(win);\n  keyboardOpen = false;\n};\n/**\n * Returns `true` if the `keyboardOpen` flag is not\n * set, the previous visual viewport width equal the current\n * visual viewport width, and if the scaled difference\n * of the previous visual viewport height minus the current\n * visual viewport height is greater than KEYBOARD_THRESHOLD\n *\n * We need to be able to accommodate users who have zooming\n * enabled in their browser (or have zoomed in manually) which\n * is why we take into account the current visual viewport's\n * scale value.\n */\nconst keyboardDidOpen = () => {\n  const scaledHeightDifference = (previousVisualViewport.height - currentVisualViewport.height) * currentVisualViewport.scale;\n  return !keyboardOpen && previousVisualViewport.width === currentVisualViewport.width && scaledHeightDifference > KEYBOARD_THRESHOLD;\n};\n/**\n * Returns `true` if the keyboard is open,\n * but the keyboard did not close\n */\nconst keyboardDidResize = win => {\n  return keyboardOpen && !keyboardDidClose(win);\n};\n/**\n * Determine if the keyboard was closed\n * Returns `true` if the `keyboardOpen` flag is set and\n * the current visual viewport height equals the\n * layout viewport height.\n */\nconst keyboardDidClose = win => {\n  return keyboardOpen && currentVisualViewport.height === win.innerHeight;\n};\n/**\n * Dispatch a keyboard open event\n */\nconst fireKeyboardOpenEvent = (win, nativeEv) => {\n  const keyboardHeight = nativeEv ? nativeEv.keyboardHeight : win.innerHeight - currentVisualViewport.height;\n  const ev = new CustomEvent(KEYBOARD_DID_OPEN, {\n    detail: {\n      keyboardHeight\n    }\n  });\n  win.dispatchEvent(ev);\n};\n/**\n * Dispatch a keyboard close event\n */\nconst fireKeyboardCloseEvent = win => {\n  const ev = new CustomEvent(KEYBOARD_DID_CLOSE);\n  win.dispatchEvent(ev);\n};\n/**\n * Given a window object, create a copy of\n * the current visual and layout viewport states\n * while also preserving the previous visual and\n * layout viewport states\n */\nconst trackViewportChanges = win => {\n  previousVisualViewport = Object.assign({}, currentVisualViewport);\n  currentVisualViewport = copyVisualViewport(win.visualViewport);\n};\n/**\n * Creates a deep copy of the visual viewport\n * at a given state\n */\nconst copyVisualViewport = visualViewport => {\n  return {\n    width: Math.round(visualViewport.width),\n    height: Math.round(visualViewport.height),\n    offsetTop: visualViewport.offsetTop,\n    offsetLeft: visualViewport.offsetLeft,\n    pageTop: visualViewport.pageTop,\n    pageLeft: visualViewport.pageLeft,\n    scale: visualViewport.scale\n  };\n};\nexport { KEYBOARD_DID_CLOSE, KEYBOARD_DID_OPEN, copyVisualViewport, keyboardDidClose, keyboardDidOpen, keyboardDidResize, resetKeyboardAssist, setKeyboardClose, setKeyboardOpen, startKeyboardAssist, trackViewportChanges };"], "mappings": ";;;;;AAMA,IAAM,oBAAoB;AAC1B,IAAM,qBAAqB;AAC3B,IAAM,qBAAqB;AAE3B,IAAI,yBAAyB,CAAC;AAC9B,IAAI,wBAAwB,CAAC;AAC7B,IAAI,eAAe;AAInB,IAAM,sBAAsB,MAAM;AAChC,2BAAyB,CAAC;AAC1B,0BAAwB,CAAC;AACzB,iBAAe;AACjB;AACA,IAAM,sBAAsB,SAAO;AACjC,QAAM,eAAe,SAAS,UAAU;AASxC,MAAI,cAAc;AAChB,yBAAqB,GAAG;AAAA,EAC1B,OAAO;AACL,QAAI,CAAC,IAAI,gBAAgB;AACvB;AAAA,IACF;AACA,4BAAwB,mBAAmB,IAAI,cAAc;AAC7D,QAAI,eAAe,WAAW,MAAM;AAClC,2BAAqB,GAAG;AACxB,UAAI,gBAAgB,KAAK,kBAAkB,GAAG,GAAG;AAC/C,wBAAgB,GAAG;AAAA,MACrB,WAAW,iBAAiB,GAAG,GAAG;AAChC,yBAAiB,GAAG;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACF;AAMA,IAAM,uBAAuB,SAAO;AAClC,MAAI,iBAAiB,mBAAmB,QAAM,gBAAgB,KAAK,EAAE,CAAC;AACtE,MAAI,iBAAiB,mBAAmB,MAAM,iBAAiB,GAAG,CAAC;AACrE;AACA,IAAM,kBAAkB,CAAC,KAAK,OAAO;AACnC,wBAAsB,KAAK,EAAE;AAC7B,iBAAe;AACjB;AACA,IAAM,mBAAmB,SAAO;AAC9B,yBAAuB,GAAG;AAC1B,iBAAe;AACjB;AAaA,IAAM,kBAAkB,MAAM;AAC5B,QAAM,0BAA0B,uBAAuB,SAAS,sBAAsB,UAAU,sBAAsB;AACtH,SAAO,CAAC,gBAAgB,uBAAuB,UAAU,sBAAsB,SAAS,yBAAyB;AACnH;AAKA,IAAM,oBAAoB,SAAO;AAC/B,SAAO,gBAAgB,CAAC,iBAAiB,GAAG;AAC9C;AAOA,IAAM,mBAAmB,SAAO;AAC9B,SAAO,gBAAgB,sBAAsB,WAAW,IAAI;AAC9D;AAIA,IAAM,wBAAwB,CAAC,KAAK,aAAa;AAC/C,QAAM,iBAAiB,WAAW,SAAS,iBAAiB,IAAI,cAAc,sBAAsB;AACpG,QAAM,KAAK,IAAI,YAAY,mBAAmB;AAAA,IAC5C,QAAQ;AAAA,MACN;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,cAAc,EAAE;AACtB;AAIA,IAAM,yBAAyB,SAAO;AACpC,QAAM,KAAK,IAAI,YAAY,kBAAkB;AAC7C,MAAI,cAAc,EAAE;AACtB;AAOA,IAAM,uBAAuB,SAAO;AAClC,2BAAyB,OAAO,OAAO,CAAC,GAAG,qBAAqB;AAChE,0BAAwB,mBAAmB,IAAI,cAAc;AAC/D;AAKA,IAAM,qBAAqB,oBAAkB;AAC3C,SAAO;AAAA,IACL,OAAO,KAAK,MAAM,eAAe,KAAK;AAAA,IACtC,QAAQ,KAAK,MAAM,eAAe,MAAM;AAAA,IACxC,WAAW,eAAe;AAAA,IAC1B,YAAY,eAAe;AAAA,IAC3B,SAAS,eAAe;AAAA,IACxB,UAAU,eAAe;AAAA,IACzB,OAAO,eAAe;AAAA,EACxB;AACF;", "names": [], "x_google_ignoreList": [0]}
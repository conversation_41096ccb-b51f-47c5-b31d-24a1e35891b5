/* node_modules/@ionic/angular/css/core.css */
:root {
  --ion-color-primary: #0054e9;
  --ion-color-primary-rgb:
    0,
    84,
    233;
  --ion-color-primary-contrast: #fff;
  --ion-color-primary-contrast-rgb:
    255,
    255,
    255;
  --ion-color-primary-shade: #004acd;
  --ion-color-primary-tint: #1a65eb;
  --ion-color-secondary: #0163aa;
  --ion-color-secondary-rgb:
    1,
    99,
    170;
  --ion-color-secondary-contrast: #fff;
  --ion-color-secondary-contrast-rgb:
    255,
    255,
    255;
  --ion-color-secondary-shade: #015796;
  --ion-color-secondary-tint: #1a73b3;
  --ion-color-tertiary: #6030ff;
  --ion-color-tertiary-rgb:
    96,
    48,
    255;
  --ion-color-tertiary-contrast: #fff;
  --ion-color-tertiary-contrast-rgb:
    255,
    255,
    255;
  --ion-color-tertiary-shade: #542ae0;
  --ion-color-tertiary-tint: #7045ff;
  --ion-color-success: #2dd55b;
  --ion-color-success-rgb:
    45,
    213,
    91;
  --ion-color-success-contrast: #000;
  --ion-color-success-contrast-rgb:
    0,
    0,
    0;
  --ion-color-success-shade: #28bb50;
  --ion-color-success-tint: #42d96b;
  --ion-color-warning: #ffc409;
  --ion-color-warning-rgb:
    255,
    196,
    9;
  --ion-color-warning-contrast: #000;
  --ion-color-warning-contrast-rgb:
    0,
    0,
    0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;
  --ion-color-danger: #c5000f;
  --ion-color-danger-rgb:
    197,
    0,
    15;
  --ion-color-danger-contrast: #fff;
  --ion-color-danger-contrast-rgb:
    255,
    255,
    255;
  --ion-color-danger-shade: #ad000d;
  --ion-color-danger-tint: #cb1a27;
  --ion-color-light: #f4f5f8;
  --ion-color-light-rgb:
    244,
    245,
    248;
  --ion-color-light-contrast: #000;
  --ion-color-light-contrast-rgb:
    0,
    0,
    0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;
  --ion-color-medium: #636469;
  --ion-color-medium-rgb:
    99,
    100,
    105;
  --ion-color-medium-contrast: #fff;
  --ion-color-medium-contrast-rgb:
    255,
    255,
    255;
  --ion-color-medium-shade: #57585c;
  --ion-color-medium-tint: #737478;
  --ion-color-dark: #222428;
  --ion-color-dark-rgb:
    34,
    36,
    40;
  --ion-color-dark-contrast: #fff;
  --ion-color-dark-contrast-rgb:
    255,
    255,
    255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;
}
html.ios {
  --ion-default-font:
    -apple-system,
    BlinkMacSystemFont,
    "Helvetica Neue",
    "Roboto",
    sans-serif;
}
html.md {
  --ion-default-font:
    "Roboto",
    "Helvetica Neue",
    sans-serif;
}
html {
  --ion-dynamic-font: -apple-system-body;
  --ion-font-family: var(--ion-default-font);
}
body {
  background: var(--ion-background-color);
  color: var(--ion-text-color);
}
body.backdrop-no-scroll {
  overflow: hidden;
}
html.ios ion-modal.modal-card ion-header ion-toolbar:first-of-type,
html.ios ion-modal.modal-sheet ion-header ion-toolbar:first-of-type,
html.ios ion-modal ion-footer ion-toolbar:first-of-type {
  padding-top: 6px;
}
html.ios ion-modal.modal-card ion-header ion-toolbar:last-of-type,
html.ios ion-modal.modal-sheet ion-header ion-toolbar:last-of-type {
  padding-bottom: 6px;
}
html.ios ion-modal ion-toolbar {
  padding-right: calc(var(--ion-safe-area-right) + 8px);
  padding-left: calc(var(--ion-safe-area-left) + 8px);
}
@media screen and (min-width: 768px) {
  html.ios ion-modal.modal-card:first-of-type {
    --backdrop-opacity: 0.18;
  }
}
ion-modal.modal-default.show-modal ~ ion-modal.modal-default {
  --backdrop-opacity: 0;
  --box-shadow: none;
}
html.ios ion-modal.modal-card .ion-page {
  border-top-left-radius: var(--border-radius);
}
.ion-color-primary {
  --ion-color-base: var(--ion-color-primary, #0054e9) !important;
  --ion-color-base-rgb: var(--ion-color-primary-rgb, 0, 84, 233) !important;
  --ion-color-contrast: var(--ion-color-primary-contrast, #fff) !important;
  --ion-color-contrast-rgb: var(--ion-color-primary-contrast-rgb, 255, 255, 255) !important;
  --ion-color-shade: var(--ion-color-primary-shade, #004acd) !important;
  --ion-color-tint: var(--ion-color-primary-tint, #1a65eb) !important;
}
.ion-color-secondary {
  --ion-color-base: var(--ion-color-secondary, #0163aa) !important;
  --ion-color-base-rgb: var(--ion-color-secondary-rgb, 1, 99, 170) !important;
  --ion-color-contrast: var(--ion-color-secondary-contrast, #fff) !important;
  --ion-color-contrast-rgb: var(--ion-color-secondary-contrast-rgb, 255, 255, 255) !important;
  --ion-color-shade: var(--ion-color-secondary-shade, #015796) !important;
  --ion-color-tint: var(--ion-color-secondary-tint, #1a73b3) !important;
}
.ion-color-tertiary {
  --ion-color-base: var(--ion-color-tertiary, #6030ff) !important;
  --ion-color-base-rgb: var(--ion-color-tertiary-rgb, 96, 48, 255) !important;
  --ion-color-contrast: var(--ion-color-tertiary-contrast, #fff) !important;
  --ion-color-contrast-rgb: var(--ion-color-tertiary-contrast-rgb, 255, 255, 255) !important;
  --ion-color-shade: var(--ion-color-tertiary-shade, #542ae0) !important;
  --ion-color-tint: var(--ion-color-tertiary-tint, #7045ff) !important;
}
.ion-color-success {
  --ion-color-base: var(--ion-color-success, #2dd55b) !important;
  --ion-color-base-rgb: var(--ion-color-success-rgb, 45, 213, 91) !important;
  --ion-color-contrast: var(--ion-color-success-contrast, #000) !important;
  --ion-color-contrast-rgb: var(--ion-color-success-contrast-rgb, 0, 0, 0) !important;
  --ion-color-shade: var(--ion-color-success-shade, #28bb50) !important;
  --ion-color-tint: var(--ion-color-success-tint, #42d96b) !important;
}
.ion-color-warning {
  --ion-color-base: var(--ion-color-warning, #ffc409) !important;
  --ion-color-base-rgb: var(--ion-color-warning-rgb, 255, 196, 9) !important;
  --ion-color-contrast: var(--ion-color-warning-contrast, #000) !important;
  --ion-color-contrast-rgb: var(--ion-color-warning-contrast-rgb, 0, 0, 0) !important;
  --ion-color-shade: var(--ion-color-warning-shade, #e0ac08) !important;
  --ion-color-tint: var(--ion-color-warning-tint, #ffca22) !important;
}
.ion-color-danger {
  --ion-color-base: var(--ion-color-danger, #c5000f) !important;
  --ion-color-base-rgb: var(--ion-color-danger-rgb, 197, 0, 15) !important;
  --ion-color-contrast: var(--ion-color-danger-contrast, #fff) !important;
  --ion-color-contrast-rgb: var(--ion-color-danger-contrast-rgb, 255, 255, 255) !important;
  --ion-color-shade: var(--ion-color-danger-shade, #ad000d) !important;
  --ion-color-tint: var(--ion-color-danger-tint, #cb1a27) !important;
}
.ion-color-light {
  --ion-color-base: var(--ion-color-light, #f4f5f8) !important;
  --ion-color-base-rgb: var(--ion-color-light-rgb, 244, 245, 248) !important;
  --ion-color-contrast: var(--ion-color-light-contrast, #000) !important;
  --ion-color-contrast-rgb: var(--ion-color-light-contrast-rgb, 0, 0, 0) !important;
  --ion-color-shade: var(--ion-color-light-shade, #d7d8da) !important;
  --ion-color-tint: var(--ion-color-light-tint, #f5f6f9) !important;
}
.ion-color-medium {
  --ion-color-base: var(--ion-color-medium, #636469) !important;
  --ion-color-base-rgb: var(--ion-color-medium-rgb, 99, 100, 105) !important;
  --ion-color-contrast: var(--ion-color-medium-contrast, #fff) !important;
  --ion-color-contrast-rgb: var(--ion-color-medium-contrast-rgb, 255, 255, 255) !important;
  --ion-color-shade: var(--ion-color-medium-shade, #57585c) !important;
  --ion-color-tint: var(--ion-color-medium-tint, #737478) !important;
}
.ion-color-dark {
  --ion-color-base: var(--ion-color-dark, #222428) !important;
  --ion-color-base-rgb: var(--ion-color-dark-rgb, 34, 36, 40) !important;
  --ion-color-contrast: var(--ion-color-dark-contrast, #fff) !important;
  --ion-color-contrast-rgb: var(--ion-color-dark-contrast-rgb, 255, 255, 255) !important;
  --ion-color-shade: var(--ion-color-dark-shade, #1e2023) !important;
  --ion-color-tint: var(--ion-color-dark-tint, #383a3e) !important;
}
.ion-page {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  position: absolute;
  flex-direction: column;
  justify-content: space-between;
  contain: layout size style;
  z-index: 0;
}
ion-modal > .ion-page {
  position: relative;
  contain: layout style;
  height: 100%;
}
.split-pane-visible > .ion-page.split-pane-main {
  position: relative;
}
ion-route,
ion-route-redirect,
ion-router,
ion-select-option,
ion-nav-controller,
ion-menu-controller,
ion-action-sheet-controller,
ion-alert-controller,
ion-loading-controller,
ion-modal-controller,
ion-picker-controller,
ion-popover-controller,
ion-toast-controller,
.ion-page-hidden {
  display: none !important;
}
.ion-page-invisible {
  opacity: 0;
}
.can-go-back > ion-header ion-back-button {
  display: block;
}
html.plt-ios.plt-hybrid,
html.plt-ios.plt-pwa {
  --ion-statusbar-padding: 20px;
}
@supports (padding-top: 20px) {
  html {
    --ion-safe-area-top: var(--ion-statusbar-padding);
  }
}
@supports (padding-top: env(safe-area-inset-top)) {
  html {
    --ion-safe-area-top: env(safe-area-inset-top);
    --ion-safe-area-bottom: env(safe-area-inset-bottom);
    --ion-safe-area-left: env(safe-area-inset-left);
    --ion-safe-area-right: env(safe-area-inset-right);
  }
}
ion-card.ion-color .ion-inherit-color,
ion-card-header.ion-color .ion-inherit-color {
  color: inherit;
}
.menu-content {
  transform: translate3d(0, 0, 0);
}
.menu-content-open {
  cursor: pointer;
  touch-action: manipulation;
  pointer-events: none;
  overflow-y: hidden;
}
.menu-content-open ion-content {
  --overflow: hidden;
}
.menu-content-open .ion-content-scroll-host {
  overflow: hidden;
}
.ios .menu-content-reveal {
  box-shadow: -8px 0 42px rgba(0, 0, 0, .08);
}
[dir=rtl].ios .menu-content-reveal {
  box-shadow: 8px 0 42px rgba(0, 0, 0, .08);
}
.md .menu-content-reveal {
  box-shadow: 4px 0px 16px rgba(0, 0, 0, .18);
}
.md .menu-content-push {
  box-shadow: 4px 0px 16px rgba(0, 0, 0, .18);
}
ion-accordion-group.accordion-group-expand-inset > ion-accordion:first-of-type {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
ion-accordion-group.accordion-group-expand-inset > ion-accordion:last-of-type {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
ion-accordion-group > ion-accordion:last-of-type ion-item[slot=header] {
  --border-width: 0px;
}
ion-accordion.accordion-animated > [slot=header] .ion-accordion-toggle-icon {
  transition: 300ms transform cubic-bezier(0.25, 0.8, 0.5, 1);
}
@media (prefers-reduced-motion: reduce) {
  ion-accordion .ion-accordion-toggle-icon {
    transition: none !important;
  }
}
ion-accordion.accordion-expanding > [slot=header] .ion-accordion-toggle-icon,
ion-accordion.accordion-expanded > [slot=header] .ion-accordion-toggle-icon {
  transform: rotate(180deg);
}
ion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-previous ion-item[slot=header] {
  --border-width: 0px;
  --inner-border-width: 0px;
}
ion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-expanding:first-of-type,
ion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-expanded:first-of-type {
  margin-top: 0;
}
ion-input input::-webkit-date-and-time-value {
  text-align: start;
}
.ion-datetime-button-overlay {
  --width: fit-content;
  --height: fit-content;
}
.ion-datetime-button-overlay ion-datetime.datetime-grid {
  width: 320px;
  min-height: 320px;
}
[ion-last-focus],
header[tabindex="-1"]:focus,
[role=banner][tabindex="-1"]:focus,
main[tabindex="-1"]:focus,
[role=main][tabindex="-1"]:focus,
h1[tabindex="-1"]:focus,
[role=heading][aria-level="1"][tabindex="-1"]:focus {
  outline: none;
}
.popover-viewport:has(> ion-content) {
  overflow: hidden;
}
@supports not selector(:has(> ion-content)) {
  .popover-viewport {
    overflow: hidden;
  }
}

/* node_modules/@ionic/angular/css/normalize.css */
audio,
canvas,
progress,
video {
  vertical-align: baseline;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
b,
strong {
  font-weight: bold;
}
img {
  max-width: 100%;
}
hr {
  height: 1px;
  border-width: 0;
  box-sizing: content-box;
}
pre {
  overflow: auto;
}
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
label,
input,
select,
textarea {
  font-family: inherit;
  line-height: normal;
}
textarea {
  overflow: auto;
  height: auto;
  font: inherit;
  color: inherit;
}
textarea::placeholder {
  padding-left: 2px;
}
form,
input,
optgroup,
select {
  margin: 0;
  font: inherit;
  color: inherit;
}
html input[type=button],
input[type=reset],
input[type=submit] {
  cursor: pointer;
  -webkit-appearance: button;
}
a,
a div,
a span,
a ion-icon,
a ion-label,
button,
button div,
button span,
button ion-icon,
button ion-label,
.ion-tappable,
[tappable],
[tappable] div,
[tappable] span,
[tappable] ion-icon,
[tappable] ion-label,
input,
textarea {
  touch-action: manipulation;
}
a ion-label,
button ion-label {
  pointer-events: none;
}
button {
  padding: 0;
  border: 0;
  border-radius: 0;
  font-family: inherit;
  font-style: inherit;
  font-variant: inherit;
  line-height: 1;
  text-transform: none;
  cursor: pointer;
  -webkit-appearance: button;
}
[tappable] {
  cursor: pointer;
}
a[disabled],
button[disabled],
html input[disabled] {
  cursor: default;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  padding: 0;
  border: 0;
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  height: auto;
}
input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
td,
th {
  padding: 0;
}

/* node_modules/@ionic/angular/css/structure.css */
* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}
html {
  width: 100%;
  height: 100%;
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}
html:not(.hydrated) body {
  display: none;
}
html.ion-ce body {
  display: block;
}
html.plt-pwa {
  height: 100vh;
}
body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  margin-left: 0;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 0;
  padding-right: 0;
  padding-top: 0;
  padding-bottom: 0;
  position: fixed;
  width: 100%;
  max-width: 100%;
  height: 100%;
  max-height: 100%;
  transform: translateZ(0);
  text-rendering: optimizeLegibility;
  overflow: hidden;
  touch-action: manipulation;
  -webkit-user-drag: none;
  -ms-content-zooming: none;
  word-wrap: break-word;
  overscroll-behavior-y: none;
  -webkit-text-size-adjust: none;
  text-size-adjust: none;
}

/* node_modules/@ionic/angular/css/typography.css */
html {
  font-family: var(--ion-font-family);
}
@supports (-webkit-touch-callout: none) {
  html {
    font: var(--ion-dynamic-font, 16px var(--ion-font-family));
  }
}
a {
  background-color: transparent;
  color: var(--ion-color-primary, #0054e9);
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 16px;
  margin-bottom: 10px;
  font-weight: 500;
  line-height: 1.2;
}
h1 {
  margin-top: 20px;
  font-size: 1.625rem;
}
h2 {
  margin-top: 18px;
  font-size: 1.5rem;
}
h3 {
  font-size: 1.375rem;
}
h4 {
  font-size: 1.25rem;
}
h5 {
  font-size: 1.125rem;
}
h6 {
  font-size: 1rem;
}
small {
  font-size: 75%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}

/* node_modules/@ionic/angular/css/display.css */
.ion-hide {
  display: none !important;
}
.ion-hide-up {
  display: none !important;
}
.ion-hide-down {
  display: none !important;
}
@media (min-width: 576px) {
  .ion-hide-sm-up {
    display: none !important;
  }
}
@media (max-width: 575.98px) {
  .ion-hide-sm-down {
    display: none !important;
  }
}
@media (min-width: 768px) {
  .ion-hide-md-up {
    display: none !important;
  }
}
@media (max-width: 767.98px) {
  .ion-hide-md-down {
    display: none !important;
  }
}
@media (min-width: 992px) {
  .ion-hide-lg-up {
    display: none !important;
  }
}
@media (max-width: 991.98px) {
  .ion-hide-lg-down {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .ion-hide-xl-up {
    display: none !important;
  }
}
@media (max-width: 1199.98px) {
  .ion-hide-xl-down {
    display: none !important;
  }
}

/* node_modules/@ionic/angular/css/padding.css */
.ion-no-padding {
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 0;
  --padding-bottom: 0;
  padding-left: 0;
  padding-right: 0;
  padding-top: 0;
  padding-bottom: 0;
}
.ion-padding {
  --padding-start: var(--ion-padding, 16px);
  --padding-end: var(--ion-padding, 16px);
  --padding-top: var(--ion-padding, 16px);
  --padding-bottom: var(--ion-padding, 16px);
  -webkit-padding-start: var(--ion-padding, 16px);
  padding-inline-start: var(--ion-padding, 16px);
  -webkit-padding-end: var(--ion-padding, 16px);
  padding-inline-end: var(--ion-padding, 16px);
  padding-top: var(--ion-padding, 16px);
  padding-bottom: var(--ion-padding, 16px);
}
.ion-padding-top {
  --padding-top: var(--ion-padding, 16px);
  padding-top: var(--ion-padding, 16px);
}
.ion-padding-start {
  --padding-start: var(--ion-padding, 16px);
  -webkit-padding-start: var(--ion-padding, 16px);
  padding-inline-start: var(--ion-padding, 16px);
}
.ion-padding-end {
  --padding-end: var(--ion-padding, 16px);
  -webkit-padding-end: var(--ion-padding, 16px);
  padding-inline-end: var(--ion-padding, 16px);
}
.ion-padding-bottom {
  --padding-bottom: var(--ion-padding, 16px);
  padding-bottom: var(--ion-padding, 16px);
}
.ion-padding-vertical {
  --padding-top: var(--ion-padding, 16px);
  --padding-bottom: var(--ion-padding, 16px);
  padding-top: var(--ion-padding, 16px);
  padding-bottom: var(--ion-padding, 16px);
}
.ion-padding-horizontal {
  --padding-start: var(--ion-padding, 16px);
  --padding-end: var(--ion-padding, 16px);
  -webkit-padding-start: var(--ion-padding, 16px);
  padding-inline-start: var(--ion-padding, 16px);
  -webkit-padding-end: var(--ion-padding, 16px);
  padding-inline-end: var(--ion-padding, 16px);
}
.ion-no-margin {
  --margin-start: 0;
  --margin-end: 0;
  --margin-top: 0;
  --margin-bottom: 0;
  margin-left: 0;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
}
.ion-margin {
  --margin-start: var(--ion-margin, 16px);
  --margin-end: var(--ion-margin, 16px);
  --margin-top: var(--ion-margin, 16px);
  --margin-bottom: var(--ion-margin, 16px);
  -webkit-margin-start: var(--ion-margin, 16px);
  margin-inline-start: var(--ion-margin, 16px);
  -webkit-margin-end: var(--ion-margin, 16px);
  margin-inline-end: var(--ion-margin, 16px);
  margin-top: var(--ion-margin, 16px);
  margin-bottom: var(--ion-margin, 16px);
}
.ion-margin-top {
  --margin-top: var(--ion-margin, 16px);
  margin-top: var(--ion-margin, 16px);
}
.ion-margin-start {
  --margin-start: var(--ion-margin, 16px);
  -webkit-margin-start: var(--ion-margin, 16px);
  margin-inline-start: var(--ion-margin, 16px);
}
.ion-margin-end {
  --margin-end: var(--ion-margin, 16px);
  -webkit-margin-end: var(--ion-margin, 16px);
  margin-inline-end: var(--ion-margin, 16px);
}
.ion-margin-bottom {
  --margin-bottom: var(--ion-margin, 16px);
  margin-bottom: var(--ion-margin, 16px);
}
.ion-margin-vertical {
  --margin-top: var(--ion-margin, 16px);
  --margin-bottom: var(--ion-margin, 16px);
  margin-top: var(--ion-margin, 16px);
  margin-bottom: var(--ion-margin, 16px);
}
.ion-margin-horizontal {
  --margin-start: var(--ion-margin, 16px);
  --margin-end: var(--ion-margin, 16px);
  -webkit-margin-start: var(--ion-margin, 16px);
  margin-inline-start: var(--ion-margin, 16px);
  -webkit-margin-end: var(--ion-margin, 16px);
  margin-inline-end: var(--ion-margin, 16px);
}

/* node_modules/@ionic/angular/css/float-elements.css */
.ion-float-left {
  float: left !important;
}
.ion-float-right {
  float: right !important;
}
.ion-float-start {
  float: left !important;
}
:host-context([dir=rtl]) .ion-float-start {
  float: right !important;
}
[dir=rtl] .ion-float-start {
  float: right !important;
}
@supports selector(:dir(rtl)) {
  .ion-float-start:dir(rtl) {
    float: right !important;
  }
}
.ion-float-end {
  float: right !important;
}
:host-context([dir=rtl]) .ion-float-end {
  float: left !important;
}
[dir=rtl] .ion-float-end {
  float: left !important;
}
@supports selector(:dir(rtl)) {
  .ion-float-end:dir(rtl) {
    float: left !important;
  }
}
@media (min-width: 576px) {
  .ion-float-sm-left {
    float: left !important;
  }
  .ion-float-sm-right {
    float: right !important;
  }
  .ion-float-sm-start {
    float: left !important;
  }
  :host-context([dir=rtl]) .ion-float-sm-start {
    float: right !important;
  }
  [dir=rtl] .ion-float-sm-start {
    float: right !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-sm-start:dir(rtl) {
      float: right !important;
    }
  }
  .ion-float-sm-end {
    float: right !important;
  }
  :host-context([dir=rtl]) .ion-float-sm-end {
    float: left !important;
  }
  [dir=rtl] .ion-float-sm-end {
    float: left !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-sm-end:dir(rtl) {
      float: left !important;
    }
  }
}
@media (min-width: 768px) {
  .ion-float-md-left {
    float: left !important;
  }
  .ion-float-md-right {
    float: right !important;
  }
  .ion-float-md-start {
    float: left !important;
  }
  :host-context([dir=rtl]) .ion-float-md-start {
    float: right !important;
  }
  [dir=rtl] .ion-float-md-start {
    float: right !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-md-start:dir(rtl) {
      float: right !important;
    }
  }
  .ion-float-md-end {
    float: right !important;
  }
  :host-context([dir=rtl]) .ion-float-md-end {
    float: left !important;
  }
  [dir=rtl] .ion-float-md-end {
    float: left !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-md-end:dir(rtl) {
      float: left !important;
    }
  }
}
@media (min-width: 992px) {
  .ion-float-lg-left {
    float: left !important;
  }
  .ion-float-lg-right {
    float: right !important;
  }
  .ion-float-lg-start {
    float: left !important;
  }
  :host-context([dir=rtl]) .ion-float-lg-start {
    float: right !important;
  }
  [dir=rtl] .ion-float-lg-start {
    float: right !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-lg-start:dir(rtl) {
      float: right !important;
    }
  }
  .ion-float-lg-end {
    float: right !important;
  }
  :host-context([dir=rtl]) .ion-float-lg-end {
    float: left !important;
  }
  [dir=rtl] .ion-float-lg-end {
    float: left !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-lg-end:dir(rtl) {
      float: left !important;
    }
  }
}
@media (min-width: 1200px) {
  .ion-float-xl-left {
    float: left !important;
  }
  .ion-float-xl-right {
    float: right !important;
  }
  .ion-float-xl-start {
    float: left !important;
  }
  :host-context([dir=rtl]) .ion-float-xl-start {
    float: right !important;
  }
  [dir=rtl] .ion-float-xl-start {
    float: right !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-xl-start:dir(rtl) {
      float: right !important;
    }
  }
  .ion-float-xl-end {
    float: right !important;
  }
  :host-context([dir=rtl]) .ion-float-xl-end {
    float: left !important;
  }
  [dir=rtl] .ion-float-xl-end {
    float: left !important;
  }
  @supports selector(:dir(rtl)) {
    .ion-float-xl-end:dir(rtl) {
      float: left !important;
    }
  }
}

/* node_modules/@ionic/angular/css/text-alignment.css */
.ion-text-center {
  text-align: center !important;
}
.ion-text-justify {
  text-align: justify !important;
}
.ion-text-start {
  text-align: start !important;
}
.ion-text-end {
  text-align: end !important;
}
.ion-text-left {
  text-align: left !important;
}
.ion-text-right {
  text-align: right !important;
}
.ion-text-nowrap {
  white-space: nowrap !important;
}
.ion-text-wrap {
  white-space: normal !important;
}
@media (min-width: 576px) {
  .ion-text-sm-center {
    text-align: center !important;
  }
  .ion-text-sm-justify {
    text-align: justify !important;
  }
  .ion-text-sm-start {
    text-align: start !important;
  }
  .ion-text-sm-end {
    text-align: end !important;
  }
  .ion-text-sm-left {
    text-align: left !important;
  }
  .ion-text-sm-right {
    text-align: right !important;
  }
  .ion-text-sm-nowrap {
    white-space: nowrap !important;
  }
  .ion-text-sm-wrap {
    white-space: normal !important;
  }
}
@media (min-width: 768px) {
  .ion-text-md-center {
    text-align: center !important;
  }
  .ion-text-md-justify {
    text-align: justify !important;
  }
  .ion-text-md-start {
    text-align: start !important;
  }
  .ion-text-md-end {
    text-align: end !important;
  }
  .ion-text-md-left {
    text-align: left !important;
  }
  .ion-text-md-right {
    text-align: right !important;
  }
  .ion-text-md-nowrap {
    white-space: nowrap !important;
  }
  .ion-text-md-wrap {
    white-space: normal !important;
  }
}
@media (min-width: 992px) {
  .ion-text-lg-center {
    text-align: center !important;
  }
  .ion-text-lg-justify {
    text-align: justify !important;
  }
  .ion-text-lg-start {
    text-align: start !important;
  }
  .ion-text-lg-end {
    text-align: end !important;
  }
  .ion-text-lg-left {
    text-align: left !important;
  }
  .ion-text-lg-right {
    text-align: right !important;
  }
  .ion-text-lg-nowrap {
    white-space: nowrap !important;
  }
  .ion-text-lg-wrap {
    white-space: normal !important;
  }
}
@media (min-width: 1200px) {
  .ion-text-xl-center {
    text-align: center !important;
  }
  .ion-text-xl-justify {
    text-align: justify !important;
  }
  .ion-text-xl-start {
    text-align: start !important;
  }
  .ion-text-xl-end {
    text-align: end !important;
  }
  .ion-text-xl-left {
    text-align: left !important;
  }
  .ion-text-xl-right {
    text-align: right !important;
  }
  .ion-text-xl-nowrap {
    white-space: nowrap !important;
  }
  .ion-text-xl-wrap {
    white-space: normal !important;
  }
}

/* node_modules/@ionic/angular/css/text-transformation.css */
.ion-text-uppercase {
  text-transform: uppercase !important;
}
.ion-text-lowercase {
  text-transform: lowercase !important;
}
.ion-text-capitalize {
  text-transform: capitalize !important;
}
@media (min-width: 576px) {
  .ion-text-sm-uppercase {
    text-transform: uppercase !important;
  }
  .ion-text-sm-lowercase {
    text-transform: lowercase !important;
  }
  .ion-text-sm-capitalize {
    text-transform: capitalize !important;
  }
}
@media (min-width: 768px) {
  .ion-text-md-uppercase {
    text-transform: uppercase !important;
  }
  .ion-text-md-lowercase {
    text-transform: lowercase !important;
  }
  .ion-text-md-capitalize {
    text-transform: capitalize !important;
  }
}
@media (min-width: 992px) {
  .ion-text-lg-uppercase {
    text-transform: uppercase !important;
  }
  .ion-text-lg-lowercase {
    text-transform: lowercase !important;
  }
  .ion-text-lg-capitalize {
    text-transform: capitalize !important;
  }
}
@media (min-width: 1200px) {
  .ion-text-xl-uppercase {
    text-transform: uppercase !important;
  }
  .ion-text-xl-lowercase {
    text-transform: lowercase !important;
  }
  .ion-text-xl-capitalize {
    text-transform: capitalize !important;
  }
}

/* node_modules/@ionic/angular/css/flex-utils.css */
.ion-align-self-start {
  align-self: flex-start !important;
}
.ion-align-self-end {
  align-self: flex-end !important;
}
.ion-align-self-center {
  align-self: center !important;
}
.ion-align-self-stretch {
  align-self: stretch !important;
}
.ion-align-self-baseline {
  align-self: baseline !important;
}
.ion-align-self-auto {
  align-self: auto !important;
}
.ion-wrap {
  flex-wrap: wrap !important;
}
.ion-nowrap {
  flex-wrap: nowrap !important;
}
.ion-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}
.ion-justify-content-start {
  justify-content: flex-start !important;
}
.ion-justify-content-center {
  justify-content: center !important;
}
.ion-justify-content-end {
  justify-content: flex-end !important;
}
.ion-justify-content-around {
  justify-content: space-around !important;
}
.ion-justify-content-between {
  justify-content: space-between !important;
}
.ion-justify-content-evenly {
  justify-content: space-evenly !important;
}
.ion-align-items-start {
  align-items: flex-start !important;
}
.ion-align-items-center {
  align-items: center !important;
}
.ion-align-items-end {
  align-items: flex-end !important;
}
.ion-align-items-stretch {
  align-items: stretch !important;
}
.ion-align-items-baseline {
  align-items: baseline !important;
}

/* node_modules/@ionic/angular/css/palettes/dark.system.css */
@media (prefers-color-scheme: dark) {
  :root {
    --ion-color-primary: #4d8dff;
    --ion-color-primary-rgb:
      77,
      141,
      255;
    --ion-color-primary-contrast: #000;
    --ion-color-primary-contrast-rgb:
      0,
      0,
      0;
    --ion-color-primary-shade: #447ce0;
    --ion-color-primary-tint: #5f98ff;
    --ion-color-secondary: #46b1ff;
    --ion-color-secondary-rgb:
      70,
      177,
      255;
    --ion-color-secondary-contrast: #000;
    --ion-color-secondary-contrast-rgb:
      0,
      0,
      0;
    --ion-color-secondary-shade: #3e9ce0;
    --ion-color-secondary-tint: #59b9ff;
    --ion-color-tertiary: #8482fb;
    --ion-color-tertiary-rgb:
      132,
      130,
      251;
    --ion-color-tertiary-contrast: #000;
    --ion-color-tertiary-contrast-rgb:
      0,
      0,
      0;
    --ion-color-tertiary-shade: #7472dd;
    --ion-color-tertiary-tint: #908ffb;
    --ion-color-success: #2dd55b;
    --ion-color-success-rgb:
      45,
      213,
      91;
    --ion-color-success-contrast: #000;
    --ion-color-success-contrast-rgb:
      0,
      0,
      0;
    --ion-color-success-shade: #28bb50;
    --ion-color-success-tint: #42d96b;
    --ion-color-warning: #ffce31;
    --ion-color-warning-rgb:
      255,
      206,
      49;
    --ion-color-warning-contrast: #000;
    --ion-color-warning-contrast-rgb:
      0,
      0,
      0;
    --ion-color-warning-shade: #e0b52b;
    --ion-color-warning-tint: #ffd346;
    --ion-color-danger: #f24c58;
    --ion-color-danger-rgb:
      242,
      76,
      88;
    --ion-color-danger-contrast: #000;
    --ion-color-danger-contrast-rgb:
      0,
      0,
      0;
    --ion-color-danger-shade: #d5434d;
    --ion-color-danger-tint: #f35e69;
    --ion-color-light: #222428;
    --ion-color-light-rgb:
      34,
      36,
      40;
    --ion-color-light-contrast: #fff;
    --ion-color-light-contrast-rgb:
      255,
      255,
      255;
    --ion-color-light-shade: #1e2023;
    --ion-color-light-tint: #383a3e;
    --ion-color-medium: #989aa2;
    --ion-color-medium-rgb:
      152,
      154,
      162;
    --ion-color-medium-contrast: #000;
    --ion-color-medium-contrast-rgb:
      0,
      0,
      0;
    --ion-color-medium-shade: #86888f;
    --ion-color-medium-tint: #a2a4ab;
    --ion-color-dark: #f4f5f8;
    --ion-color-dark-rgb:
      244,
      245,
      248;
    --ion-color-dark-contrast: #000;
    --ion-color-dark-contrast-rgb:
      0,
      0,
      0;
    --ion-color-dark-shade: #d7d8da;
    --ion-color-dark-tint: #f5f6f9;
  }
  :root.ios {
    --ion-background-color: #000000;
    --ion-background-color-rgb:
      0,
      0,
      0;
    --ion-text-color: #ffffff;
    --ion-text-color-rgb:
      255,
      255,
      255;
    --ion-background-color-step-50: #0d0d0d;
    --ion-background-color-step-100: #1a1a1a;
    --ion-background-color-step-150: #262626;
    --ion-background-color-step-200: #333333;
    --ion-background-color-step-250: #404040;
    --ion-background-color-step-300: #4d4d4d;
    --ion-background-color-step-350: #595959;
    --ion-background-color-step-400: #666666;
    --ion-background-color-step-450: #737373;
    --ion-background-color-step-500: #808080;
    --ion-background-color-step-550: #8c8c8c;
    --ion-background-color-step-600: #999999;
    --ion-background-color-step-650: #a6a6a6;
    --ion-background-color-step-700: #b3b3b3;
    --ion-background-color-step-750: #bfbfbf;
    --ion-background-color-step-800: #cccccc;
    --ion-background-color-step-850: #d9d9d9;
    --ion-background-color-step-900: #e6e6e6;
    --ion-background-color-step-950: #f2f2f2;
    --ion-text-color-step-50: #f2f2f2;
    --ion-text-color-step-100: #e6e6e6;
    --ion-text-color-step-150: #d9d9d9;
    --ion-text-color-step-200: #cccccc;
    --ion-text-color-step-250: #bfbfbf;
    --ion-text-color-step-300: #b3b3b3;
    --ion-text-color-step-350: #a6a6a6;
    --ion-text-color-step-400: #999999;
    --ion-text-color-step-450: #8c8c8c;
    --ion-text-color-step-500: #808080;
    --ion-text-color-step-550: #737373;
    --ion-text-color-step-600: #666666;
    --ion-text-color-step-650: #595959;
    --ion-text-color-step-700: #4d4d4d;
    --ion-text-color-step-750: #404040;
    --ion-text-color-step-800: #333333;
    --ion-text-color-step-850: #262626;
    --ion-text-color-step-900: #1a1a1a;
    --ion-text-color-step-950: #0d0d0d;
    --ion-item-background: #000000;
    --ion-card-background: #1c1c1d;
  }
  :root.ios ion-modal {
    --ion-background-color: var(--ion-color-step-100, var(--ion-background-color-step-100));
    --ion-toolbar-background: var(--ion-color-step-150, var(--ion-background-color-step-150));
    --ion-toolbar-border-color: var(--ion-color-step-250, var(--ion-background-color-step-250));
  }
  :root.md {
    --ion-background-color: #121212;
    --ion-background-color-rgb:
      18,
      18,
      18;
    --ion-text-color: #ffffff;
    --ion-text-color-rgb:
      255,
      255,
      255;
    --ion-background-color-step-50: #1e1e1e;
    --ion-background-color-step-100: #2a2a2a;
    --ion-background-color-step-150: #363636;
    --ion-background-color-step-200: #414141;
    --ion-background-color-step-250: #4d4d4d;
    --ion-background-color-step-300: #595959;
    --ion-background-color-step-350: #656565;
    --ion-background-color-step-400: #717171;
    --ion-background-color-step-450: #7d7d7d;
    --ion-background-color-step-500: #898989;
    --ion-background-color-step-550: #949494;
    --ion-background-color-step-600: #a0a0a0;
    --ion-background-color-step-650: #acacac;
    --ion-background-color-step-700: #b8b8b8;
    --ion-background-color-step-750: #c4c4c4;
    --ion-background-color-step-800: #d0d0d0;
    --ion-background-color-step-850: #dbdbdb;
    --ion-background-color-step-900: #e7e7e7;
    --ion-background-color-step-950: #f3f3f3;
    --ion-text-color-step-50: #f3f3f3;
    --ion-text-color-step-100: #e7e7e7;
    --ion-text-color-step-150: #dbdbdb;
    --ion-text-color-step-200: #d0d0d0;
    --ion-text-color-step-250: #c4c4c4;
    --ion-text-color-step-300: #b8b8b8;
    --ion-text-color-step-350: #acacac;
    --ion-text-color-step-400: #a0a0a0;
    --ion-text-color-step-450: #949494;
    --ion-text-color-step-500: #898989;
    --ion-text-color-step-550: #7d7d7d;
    --ion-text-color-step-600: #717171;
    --ion-text-color-step-650: #656565;
    --ion-text-color-step-700: #595959;
    --ion-text-color-step-750: #4d4d4d;
    --ion-text-color-step-800: #414141;
    --ion-text-color-step-850: #363636;
    --ion-text-color-step-900: #2a2a2a;
    --ion-text-color-step-950: #1e1e1e;
    --ion-item-background: #1e1e1e;
    --ion-toolbar-background: #1f1f1f;
    --ion-tab-bar-background: #1f1f1f;
    --ion-card-background: #1e1e1e;
  }
}

/* src/global.scss */
.emergency-notification {
  --backdrop-opacity: 0.9;
}
.emergency-notification .alert-wrapper {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 3px solid;
  animation: emergencyPulse 2s infinite;
}
.emergency-notification .alert-head {
  padding: 20px 20px 10px 20px;
  text-align: center;
}
.emergency-notification .alert-title {
  font-size: 1.4rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
}
.emergency-notification .alert-sub-title {
  font-size: 1rem;
  font-weight: 600;
  opacity: 0.9;
  margin-bottom: 0;
}
.emergency-notification .alert-message {
  font-size: 1.1rem;
  line-height: 1.5;
  padding: 10px 20px 20px 20px;
  text-align: center;
}
.emergency-notification .alert-button-group {
  padding: 0 20px 20px 20px;
  display: flex;
  gap: 12px;
}
.emergency-notification .alert-button {
  flex: 1;
  font-weight: 600;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 1rem;
}
.emergency-notification .alert-button-primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.5);
}
.emergency-notification .alert-button-secondary {
  background: rgba(0, 0, 0, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}
.earthquake-alert .alert-wrapper {
  background:
    linear-gradient(
      135deg,
      #FF8C00,
      #FFA500);
  border-color: #FF6B00;
  color: white;
}
.flood-alert .alert-wrapper {
  background:
    linear-gradient(
      135deg,
      #0066CC,
      #4A90E2);
  border-color: #0052A3;
  color: white;
}
.typhoon-alert .alert-wrapper {
  background:
    linear-gradient(
      135deg,
      #228B22,
      #32CD32);
  border-color: #1F7A1F;
  color: white;
}
.fire-alert .alert-wrapper {
  background:
    linear-gradient(
      135deg,
      #DC143C,
      #FF4500);
  border-color: #B91C3C;
  color: white;
}
.general-alert .alert-wrapper {
  background:
    linear-gradient(
      135deg,
      #666666,
      #888888);
  border-color: #555555;
  color: white;
}
@keyframes emergencyPulse {
  0% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 8px 32px rgba(255, 0, 0, 0.4), 0 0 20px rgba(255, 0, 0, 0.3);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transform: scale(1);
  }
}
.emergency-toast {
  --background:
    linear-gradient(
      135deg,
      #FF8C00,
      #FFA500);
  --color: white;
  --border-radius: 12px;
  --box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}
.emergency-toast .toast-wrapper {
  border: 2px solid rgba(255, 255, 255, 0.3);
}
.emergency-toast .toast-header {
  font-weight: 700;
  font-size: 1.1rem;
}
.emergency-toast .toast-message {
  font-size: 1rem;
  line-height: 1.4;
}
.emergency-toast .toast-button {
  --color: white;
  font-weight: 600;
}
@media (max-width: 480px) {
  .emergency-notification .alert-wrapper {
    margin: 20px;
    max-width: calc(100vw - 40px);
  }
  .emergency-notification .alert-title {
    font-size: 1.2rem;
  }
  .emergency-notification .alert-message {
    font-size: 1rem;
  }
  .emergency-notification .alert-button {
    font-size: 0.9rem;
    padding: 10px 12px;
  }
}
@media (prefers-contrast: high) {
  .emergency-notification .alert-wrapper {
    border-width: 4px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  }
}
@media (prefers-reduced-motion: reduce) {
  .emergency-notification .alert-wrapper {
    animation: none;
  }
  @keyframes emergencyPulse {
    0%, 100% {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      transform: scale(1);
    }
  }
}
.evacuation-center-modal {
  --backdrop-opacity: 0.6;
  --width: 90%;
  --height: auto;
  --border-radius: 12px;
  --box-shadow: 0 28px 48px rgba(0, 0, 0, 0.4);
}
.evacuation-center-modal .modal-wrapper {
  position: relative;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  border-radius: 12px;
}
.evacuation-center-modal .ion-page {
  position: relative;
  display: block;
  contain: content;
}
.center-image img[src$="evacuation-placeholder.jpg"] {
  object-fit: cover;
  background-color: #f0f0f0;
  background-image:
    linear-gradient(
      45deg,
      #e0e0e0 25%,
      transparent 25%,
      transparent 75%,
      #e0e0e0 75%,
      #e0e0e0),
    linear-gradient(
      45deg,
      #e0e0e0 25%,
      transparent 25%,
      transparent 75%,
      #e0e0e0 75%,
      #e0e0e0);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}
.evacuation-popup {
  padding: 5px;
  max-width: 250px;
}
.evacuation-popup h3 {
  margin: 0 0 8px 0;
  color: #1565c0;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}
.evacuation-popup p {
  margin: 5px 0;
  font-size: 14px;
  line-height: 1.4;
}
.evacuation-popup strong {
  font-weight: 600;
  color: #333;
}
.evacuation-details-modal {
  --backdrop-opacity: 0.6;
  --border-radius: 16px 16px 0 0;
  --box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.12);
}
.leaflet-marker-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: transform 0.2s ease-in-out;
}
.leaflet-marker-icon:hover {
  transform: scale(1.1);
}
.evacuation-details-modal .modal-wrapper {
  position: absolute;
  bottom: 0;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.12);
}
.evacuation-details-modal ion-content {
  --padding-top: 16px;
  --padding-bottom: 16px;
  --padding-start: 16px;
  --padding-end: 16px;
}
.evacuation-details-modal ion-header {
  position: relative;
}
.evacuation-details-modal ion-header::after {
  content: "";
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 4px;
  background-color: var(--ion-color-medium);
  border-radius: 2px;
  opacity: 0.5;
}
.emergency-alert {
  --backdrop-opacity: 0.9;
  --background: #f44336;
  --height: auto;
}
.emergency-alert .alert-head {
  background-color: #d32f2f;
  color: white;
  padding: 16px;
  text-align: center;
}
.emergency-alert .alert-title {
  font-size: 20px;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
}
.emergency-alert .alert-sub-title {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 4px;
}
.emergency-alert .alert-message {
  color: white;
  font-size: 16px;
  padding: 16px;
  text-align: center;
}
.emergency-alert .alert-button-group {
  padding: 8px;
}
.emergency-alert .alert-button {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  margin: 4px;
}
.emergency-alert .alert-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}
.emergency-notification {
  --backdrop-opacity: 0.95;
  --width: 90%;
  --max-width: 400px;
  --border-radius: 16px;
  --box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}
.emergency-notification .alert-wrapper {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}
.emergency-notification .alert-head {
  padding: 24px 20px 16px 20px;
  text-align: center;
  position: relative;
}
.emergency-notification .alert-head::before {
  content: "!";
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #d32f2f;
  color: white;
  font-size: 24px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid white;
}
.emergency-notification .alert-title {
  font-size: 18px;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
  margin-top: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
.emergency-notification .alert-sub-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.95);
  margin-top: 4px;
  font-weight: 600;
}
.emergency-notification .alert-message {
  color: white;
  font-size: 16px;
  padding: 16px 20px;
  text-align: center;
  line-height: 1.4;
  background-color: rgba(0, 0, 0, 0.1);
}
.emergency-notification .alert-button-group {
  padding: 16px;
  background-color: rgba(255, 255, 255, 0.1);
}
.emergency-notification .alert-button {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  margin: 4px;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.emergency-notification .alert-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}
.earthquake-alert {
  --background: #ffa500;
}
.earthquake-alert .alert-head {
  background:
    linear-gradient(
      135deg,
      #ffa500 0%,
      #ff8c00 100%);
}
.earthquake-alert .alert-head::before {
  background-color: #cc7a00;
}
.flood-alert {
  --background: #0066cc;
}
.flood-alert .alert-head {
  background:
    linear-gradient(
      135deg,
      #0066cc 0%,
      #0052a3 100%);
}
.flood-alert .alert-head::before {
  background-color: #004080;
}
.typhoon-alert {
  --background: #008000;
}
.typhoon-alert .alert-head {
  background:
    linear-gradient(
      135deg,
      #008000 0%,
      #006600 100%);
}
.typhoon-alert .alert-head::before {
  background-color: #004d00;
}
.fire-alert {
  --background: #ff0000;
}
.fire-alert .alert-head {
  background:
    linear-gradient(
      135deg,
      #ff0000 0%,
      #cc0000 100%);
}
.fire-alert .alert-head::before {
  background-color: #990000;
}
.general-alert {
  --background: #666666;
}
.general-alert .alert-head {
  background:
    linear-gradient(
      135deg,
      #666666 0%,
      #555555 100%);
}
.general-alert .alert-head::before {
  background-color: #444444;
}

/* src/theme/variables.scss */

/* node_modules/leaflet/dist/leaflet.css */
.leaflet-pane,
.leaflet-tile,
.leaflet-marker-icon,
.leaflet-marker-shadow,
.leaflet-tile-container,
.leaflet-pane > svg,
.leaflet-pane > canvas,
.leaflet-zoom-box,
.leaflet-image-layer,
.leaflet-layer {
  position: absolute;
  left: 0;
  top: 0;
}
.leaflet-container {
  overflow: hidden;
}
.leaflet-tile,
.leaflet-marker-icon,
.leaflet-marker-shadow {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
}
.leaflet-tile::selection {
  background: transparent;
}
.leaflet-safari .leaflet-tile {
  image-rendering: -webkit-optimize-contrast;
}
.leaflet-safari .leaflet-tile-container {
  width: 1600px;
  height: 1600px;
  -webkit-transform-origin: 0 0;
}
.leaflet-marker-icon,
.leaflet-marker-shadow {
  display: block;
}
.leaflet-container .leaflet-overlay-pane svg {
  max-width: none !important;
  max-height: none !important;
}
.leaflet-container .leaflet-marker-pane img,
.leaflet-container .leaflet-shadow-pane img,
.leaflet-container .leaflet-tile-pane img,
.leaflet-container img.leaflet-image-layer,
.leaflet-container .leaflet-tile {
  max-width: none !important;
  max-height: none !important;
  width: auto;
  padding: 0;
}
.leaflet-container img.leaflet-tile {
  mix-blend-mode: plus-lighter;
}
.leaflet-container.leaflet-touch-zoom {
  -ms-touch-action: pan-x pan-y;
  touch-action: pan-x pan-y;
}
.leaflet-container.leaflet-touch-drag {
  -ms-touch-action: pinch-zoom;
  touch-action: none;
  touch-action: pinch-zoom;
}
.leaflet-container.leaflet-touch-drag.leaflet-touch-zoom {
  -ms-touch-action: none;
  touch-action: none;
}
.leaflet-container {
  -webkit-tap-highlight-color: transparent;
}
.leaflet-container a {
  -webkit-tap-highlight-color: rgba(51, 181, 229, 0.4);
}
.leaflet-tile {
  filter: inherit;
  visibility: hidden;
}
.leaflet-tile-loaded {
  visibility: inherit;
}
.leaflet-zoom-box {
  width: 0;
  height: 0;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 800;
}
.leaflet-overlay-pane svg {
  -moz-user-select: none;
}
.leaflet-pane {
  z-index: 400;
}
.leaflet-tile-pane {
  z-index: 200;
}
.leaflet-overlay-pane {
  z-index: 400;
}
.leaflet-shadow-pane {
  z-index: 500;
}
.leaflet-marker-pane {
  z-index: 600;
}
.leaflet-tooltip-pane {
  z-index: 650;
}
.leaflet-popup-pane {
  z-index: 700;
}
.leaflet-map-pane canvas {
  z-index: 100;
}
.leaflet-map-pane svg {
  z-index: 200;
}
.leaflet-vml-shape {
  width: 1px;
  height: 1px;
}
.lvml {
  behavior: url(#default#VML);
  display: inline-block;
  position: absolute;
}
.leaflet-control {
  position: relative;
  z-index: 800;
  pointer-events: visiblePainted;
  pointer-events: auto;
}
.leaflet-top,
.leaflet-bottom {
  position: absolute;
  z-index: 1000;
  pointer-events: none;
}
.leaflet-top {
  top: 0;
}
.leaflet-right {
  right: 0;
}
.leaflet-bottom {
  bottom: 0;
}
.leaflet-left {
  left: 0;
}
.leaflet-control {
  float: left;
  clear: both;
}
.leaflet-right .leaflet-control {
  float: right;
}
.leaflet-top .leaflet-control {
  margin-top: 10px;
}
.leaflet-bottom .leaflet-control {
  margin-bottom: 10px;
}
.leaflet-left .leaflet-control {
  margin-left: 10px;
}
.leaflet-right .leaflet-control {
  margin-right: 10px;
}
.leaflet-fade-anim .leaflet-popup {
  opacity: 0;
  -webkit-transition: opacity 0.2s linear;
  -moz-transition: opacity 0.2s linear;
  transition: opacity 0.2s linear;
}
.leaflet-fade-anim .leaflet-map-pane .leaflet-popup {
  opacity: 1;
}
.leaflet-zoom-animated {
  -webkit-transform-origin: 0 0;
  -ms-transform-origin: 0 0;
  transform-origin: 0 0;
}
svg.leaflet-zoom-animated {
  will-change: transform;
}
.leaflet-zoom-anim .leaflet-zoom-animated {
  -webkit-transition: -webkit-transform 0.25s cubic-bezier(0, 0, 0.25, 1);
  -moz-transition: -moz-transform 0.25s cubic-bezier(0, 0, 0.25, 1);
  transition: transform 0.25s cubic-bezier(0, 0, 0.25, 1);
}
.leaflet-zoom-anim .leaflet-tile,
.leaflet-pan-anim .leaflet-tile {
  -webkit-transition: none;
  -moz-transition: none;
  transition: none;
}
.leaflet-zoom-anim .leaflet-zoom-hide {
  visibility: hidden;
}
.leaflet-interactive {
  cursor: pointer;
}
.leaflet-grab {
  cursor: -webkit-grab;
  cursor: -moz-grab;
  cursor: grab;
}
.leaflet-crosshair,
.leaflet-crosshair .leaflet-interactive {
  cursor: crosshair;
}
.leaflet-popup-pane,
.leaflet-control {
  cursor: auto;
}
.leaflet-dragging .leaflet-grab,
.leaflet-dragging .leaflet-grab .leaflet-interactive,
.leaflet-dragging .leaflet-marker-draggable {
  cursor: move;
  cursor: -webkit-grabbing;
  cursor: -moz-grabbing;
  cursor: grabbing;
}
.leaflet-marker-icon,
.leaflet-marker-shadow,
.leaflet-image-layer,
.leaflet-pane > svg path,
.leaflet-tile-container {
  pointer-events: none;
}
.leaflet-marker-icon.leaflet-interactive,
.leaflet-image-layer.leaflet-interactive,
.leaflet-pane > svg path.leaflet-interactive,
svg.leaflet-image-layer.leaflet-interactive path {
  pointer-events: visiblePainted;
  pointer-events: auto;
}
.leaflet-container {
  background: #ddd;
  outline-offset: 1px;
}
.leaflet-container a {
  color: #0078A8;
}
.leaflet-zoom-box {
  border: 2px dotted #38f;
  background: rgba(255, 255, 255, 0.5);
}
.leaflet-container {
  font-family:
    "Helvetica Neue",
    Arial,
    Helvetica,
    sans-serif;
  font-size: 12px;
  font-size: 0.75rem;
  line-height: 1.5;
}
.leaflet-bar {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
  border-radius: 4px;
}
.leaflet-bar a {
  background-color: #fff;
  border-bottom: 1px solid #ccc;
  width: 26px;
  height: 26px;
  line-height: 26px;
  display: block;
  text-align: center;
  text-decoration: none;
  color: black;
}
.leaflet-bar a,
.leaflet-control-layers-toggle {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  display: block;
}
.leaflet-bar a:hover,
.leaflet-bar a:focus {
  background-color: #f4f4f4;
}
.leaflet-bar a:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.leaflet-bar a:last-child {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border-bottom: none;
}
.leaflet-bar a.leaflet-disabled {
  cursor: default;
  background-color: #f4f4f4;
  color: #bbb;
}
.leaflet-touch .leaflet-bar a {
  width: 30px;
  height: 30px;
  line-height: 30px;
}
.leaflet-touch .leaflet-bar a:first-child {
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}
.leaflet-touch .leaflet-bar a:last-child {
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px;
}
.leaflet-control-zoom-in,
.leaflet-control-zoom-out {
  font:
    bold 18px "Lucida Console",
    Monaco,
    monospace;
  text-indent: 1px;
}
.leaflet-touch .leaflet-control-zoom-in,
.leaflet-touch .leaflet-control-zoom-out {
  font-size: 22px;
}
.leaflet-control-layers {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
  background: #fff;
  border-radius: 5px;
}
.leaflet-control-layers-toggle {
  background-image: url("./media/layers.png");
  width: 36px;
  height: 36px;
}
.leaflet-retina .leaflet-control-layers-toggle {
  background-image: url("./media/layers-2x.png");
  background-size: 26px 26px;
}
.leaflet-touch .leaflet-control-layers-toggle {
  width: 44px;
  height: 44px;
}
.leaflet-control-layers .leaflet-control-layers-list,
.leaflet-control-layers-expanded .leaflet-control-layers-toggle {
  display: none;
}
.leaflet-control-layers-expanded .leaflet-control-layers-list {
  display: block;
  position: relative;
}
.leaflet-control-layers-expanded {
  padding: 6px 10px 6px 6px;
  color: #333;
  background: #fff;
}
.leaflet-control-layers-scrollbar {
  overflow-y: scroll;
  overflow-x: hidden;
  padding-right: 5px;
}
.leaflet-control-layers-selector {
  margin-top: 2px;
  position: relative;
  top: 1px;
}
.leaflet-control-layers label {
  display: block;
  font-size: 13px;
  font-size: 1.08333em;
}
.leaflet-control-layers-separator {
  height: 0;
  border-top: 1px solid #ddd;
  margin: 5px -10px 5px -6px;
}
.leaflet-default-icon-path {
  background-image: url("./media/marker-icon.png");
}
.leaflet-container .leaflet-control-attribution {
  background: #fff;
  background: rgba(255, 255, 255, 0.8);
  margin: 0;
}
.leaflet-control-attribution,
.leaflet-control-scale-line {
  padding: 0 5px;
  color: #333;
  line-height: 1.4;
}
.leaflet-control-attribution a {
  text-decoration: none;
}
.leaflet-control-attribution a:hover,
.leaflet-control-attribution a:focus {
  text-decoration: underline;
}
.leaflet-attribution-flag {
  display: inline !important;
  vertical-align: baseline !important;
  width: 1em;
  height: 0.6669em;
}
.leaflet-left .leaflet-control-scale {
  margin-left: 5px;
}
.leaflet-bottom .leaflet-control-scale {
  margin-bottom: 5px;
}
.leaflet-control-scale-line {
  border: 2px solid #777;
  border-top: none;
  line-height: 1.1;
  padding: 2px 5px 1px;
  white-space: nowrap;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.8);
  text-shadow: 1px 1px #fff;
}
.leaflet-control-scale-line:not(:first-child) {
  border-top: 2px solid #777;
  border-bottom: none;
  margin-top: -2px;
}
.leaflet-control-scale-line:not(:first-child):not(:last-child) {
  border-bottom: 2px solid #777;
}
.leaflet-touch .leaflet-control-attribution,
.leaflet-touch .leaflet-control-layers,
.leaflet-touch .leaflet-bar {
  box-shadow: none;
}
.leaflet-touch .leaflet-control-layers,
.leaflet-touch .leaflet-bar {
  border: 2px solid rgba(0, 0, 0, 0.2);
  background-clip: padding-box;
}
.leaflet-popup {
  position: absolute;
  text-align: center;
  margin-bottom: 20px;
}
.leaflet-popup-content-wrapper {
  padding: 1px;
  text-align: left;
  border-radius: 12px;
}
.leaflet-popup-content {
  margin: 13px 24px 13px 20px;
  line-height: 1.3;
  font-size: 13px;
  font-size: 1.08333em;
  min-height: 1px;
}
.leaflet-popup-content p {
  margin: 17px 0;
  margin: 1.3em 0;
}
.leaflet-popup-tip-container {
  width: 40px;
  height: 20px;
  position: absolute;
  left: 50%;
  margin-top: -1px;
  margin-left: -20px;
  overflow: hidden;
  pointer-events: none;
}
.leaflet-popup-tip {
  width: 17px;
  height: 17px;
  padding: 1px;
  margin: -10px auto 0;
  pointer-events: auto;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.leaflet-popup-content-wrapper,
.leaflet-popup-tip {
  background: white;
  color: #333;
  box-shadow: 0 3px 14px rgba(0, 0, 0, 0.4);
}
.leaflet-container a.leaflet-popup-close-button {
  position: absolute;
  top: 0;
  right: 0;
  border: none;
  text-align: center;
  width: 24px;
  height: 24px;
  font:
    16px/24px Tahoma,
    Verdana,
    sans-serif;
  color: #757575;
  text-decoration: none;
  background: transparent;
}
.leaflet-container a.leaflet-popup-close-button:hover,
.leaflet-container a.leaflet-popup-close-button:focus {
  color: #585858;
}
.leaflet-popup-scrolled {
  overflow: auto;
}
.leaflet-oldie .leaflet-popup-content-wrapper {
  -ms-zoom: 1;
}
.leaflet-oldie .leaflet-popup-tip {
  width: 24px;
  margin: 0 auto;
  -ms-filter: "progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678)";
  filter: progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678);
}
.leaflet-oldie .leaflet-control-zoom,
.leaflet-oldie .leaflet-control-layers,
.leaflet-oldie .leaflet-popup-content-wrapper,
.leaflet-oldie .leaflet-popup-tip {
  border: 1px solid #999;
}
.leaflet-div-icon {
  background: #fff;
  border: 1px solid #666;
}
.leaflet-tooltip {
  position: absolute;
  padding: 6px;
  background-color: #fff;
  border: 1px solid #fff;
  border-radius: 3px;
  color: #222;
  white-space: nowrap;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}
.leaflet-tooltip.leaflet-interactive {
  cursor: pointer;
  pointer-events: auto;
}
.leaflet-tooltip-top:before,
.leaflet-tooltip-bottom:before,
.leaflet-tooltip-left:before,
.leaflet-tooltip-right:before {
  position: absolute;
  pointer-events: none;
  border: 6px solid transparent;
  background: transparent;
  content: "";
}
.leaflet-tooltip-bottom {
  margin-top: 6px;
}
.leaflet-tooltip-top {
  margin-top: -6px;
}
.leaflet-tooltip-bottom:before,
.leaflet-tooltip-top:before {
  left: 50%;
  margin-left: -6px;
}
.leaflet-tooltip-top:before {
  bottom: 0;
  margin-bottom: -12px;
  border-top-color: #fff;
}
.leaflet-tooltip-bottom:before {
  top: 0;
  margin-top: -12px;
  margin-left: -6px;
  border-bottom-color: #fff;
}
.leaflet-tooltip-left {
  margin-left: -6px;
}
.leaflet-tooltip-right {
  margin-left: 6px;
}
.leaflet-tooltip-left:before,
.leaflet-tooltip-right:before {
  top: 50%;
  margin-top: -6px;
}
.leaflet-tooltip-left:before {
  right: 0;
  margin-right: -12px;
  border-left-color: #fff;
}
.leaflet-tooltip-right:before {
  left: 0;
  margin-left: -12px;
  border-right-color: #fff;
}
@media print {
  .leaflet-control {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}

/* angular:styles/global:styles */
/*# sourceMappingURL=styles.css.map */

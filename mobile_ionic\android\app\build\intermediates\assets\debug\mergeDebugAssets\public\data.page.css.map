{"version": 3, "sources": ["src/app/pages/data/data.page.scss"], "sourcesContent": [".profile-container {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  max-width: 420px;\r\n  margin: 0 auto;\r\n  padding: 32px 20px;\r\n}\r\n\r\nform {\r\n  width: 100%;\r\n}\r\n\r\nion-item {\r\n  --background: #f9f9f9;\r\n  --border-radius: 25px;\r\n  --padding-start: 10px;\r\n  --padding-end: 10px;\r\n  --inner-padding-top: 5px;\r\n  --inner-padding-bottom: 5px;\r\n  margin-bottom: 10px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\r\n  height: 65px;\r\n}\r\n\r\nion-label {\r\n  font-size: 5px;\r\n  color: #333;\r\n}\r\n\r\nion-input, ion-select {\r\n  font-size: 15px;\r\n}\r\n\r\n.terms-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 12px;\r\n  font-size: 14px;\r\n  color: #444;\r\n  flex-wrap: wrap;\r\n  line-height: 1.4;\r\n}\r\n\r\n.terms-checkbox ion-checkbox {\r\n  margin-right: 8px;\r\n}\r\n\r\n// Terms and Conditions Modal Styles\r\n.terms-content {\r\n  h2 {\r\n    color: #03b2dd;\r\n    text-align: center;\r\n    margin-bottom: 10px;\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  h3 {\r\n    color: #333;\r\n    text-align: center;\r\n    margin-bottom: 20px;\r\n    font-size: 1.2rem;\r\n  }\r\n\r\n  h4 {\r\n    color: #03b2dd;\r\n    margin-top: 20px;\r\n    margin-bottom: 10px;\r\n    font-size: 1.1rem;\r\n    font-weight: 600;\r\n  }\r\n\r\n  p {\r\n    color: #444;\r\n    line-height: 1.6;\r\n    margin-bottom: 12px;\r\n    text-align: justify;\r\n  }\r\n\r\n  p:last-child {\r\n    font-weight: 600;\r\n    color: #333;\r\n    text-align: center;\r\n    margin-top: 20px;\r\n    padding: 15px;\r\n    background-color: #f0f9ff;\r\n    border-radius: 8px;\r\n    border-left: 4px solid #03b2dd;\r\n  }\r\n}\r\n\r\n.modal-buttons {\r\n  margin-top: 30px;\r\n  padding: 20px 0;\r\n\r\n  ion-button {\r\n    margin-bottom: 10px;\r\n    --border-radius: 25px;\r\n    height: 45px;\r\n    font-weight: 600;\r\n  }\r\n\r\n  ion-button[fill=\"outline\"] {\r\n    --color: #666;\r\n    --border-color: #666;\r\n  }\r\n}\r\n\r\n.terms-checkbox a {\r\n  color: #1565c0;\r\n  text-decoration: underline;\r\n  font-weight: 500;\r\n  margin-left: 4px;\r\n}\r\n\r\nion-button {\r\n  margin-top: 24px;\r\n  --background: #1565c0;\r\n  --border-radius: 10px;\r\n  --box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.error-message {\r\n  text-align: center;\r\n  margin: 10px 0;\r\n  color: #e53935;\r\n\r\n  p {\r\n    margin: 0;\r\n    font-size: 14px;\r\n  }\r\n}\r\n.header-logo {\r\n  text-align: center;\r\n\r\n\r\n  img {\r\n    width: 80px;\r\n    height: auto;\r\n  }\r\n\r\n  h2 {\r\n    font-size: 18px;\r\n    color: #222;\r\n    font-weight: 600;\r\n    text-shadow: 0px 2px 4px rgba(0,0,0,0.15);\r\n  }\r\n}\r\n\r\n.home-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 15px;\r\n  font-weight: 700;\r\n  letter-spacing: 1px;\r\n  text-shadow: 1px 2px 4px #ccc;\r\n  padding-top: 59px;\r\n}"], "mappings": ";AAAA,CAAA;AACE,UAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,aAAA;AACA,UAAA,EAAA;AACA,WAAA,KAAA;;AAGF;AACE,SAAA;;AAGF;AACE,gBAAA;AACA,mBAAA;AACA,mBAAA;AACA,iBAAA;AACA,uBAAA;AACA,0BAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,UAAA;;AAGF;AACE,aAAA;AACA,SAAA;;AAGF;AAAA;AACE,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,cAAA;AACA,aAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAVA,eAUA;AACE,gBAAA;;AAKA,CAAA,cAAA;AACE,SAAA;AACA,cAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAPA,cAOA;AACE,SAAA;AACA,cAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAdA,cAcA;AACE,SAAA;AACA,cAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAtBA,cAsBA;AACE,SAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA;;AAGF,CA7BA,cA6BA,CAAA;AACE,eAAA;AACA,SAAA;AACA,cAAA;AACA,cAAA;AACA,WAAA;AACA,oBAAA;AACA,iBAAA;AACA,eAAA,IAAA,MAAA;;AAIJ,CAAA;AACE,cAAA;AACA,WAAA,KAAA;;AAEA,CAJF,cAIE;AACE,iBAAA;AACA,mBAAA;AACA,UAAA;AACA,eAAA;;AAGF,CAXF,cAWE,UAAA,CAAA;AACE,WAAA;AACA,kBAAA;;AAIJ,CA1EA,eA0EA;AACE,SAAA;AACA,mBAAA;AACA,eAAA;AACA,eAAA;;AAGF;AACE,cAAA;AACA,gBAAA;AACA,mBAAA;AACA,gBAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,cAAA;AACA,UAAA,KAAA;AACA,SAAA;;AAEA,CALF,cAKE;AACE,UAAA;AACA,aAAA;;AAGJ,CAAA;AACE,cAAA;;AAGA,CAJF,YAIE;AACE,SAAA;AACA,UAAA;;AAGF,CATF,YASE;AACE,aAAA;AACA,SAAA;AACA,eAAA;AACA,eAAA,IAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAIJ,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,kBAAA;AACA,eAAA,IAAA,IAAA,IAAA;AACA,eAAA;;", "names": []}
import {
  AuthService
} from "./chunk-XKXQPGS3.js";
import "./chunk-FKALCVFZ.js";
import {
  FcmService
} from "./chunk-RDFT5QPW.js";
import "./chunk-I7MI46CM.js";
import "./chunk-TAZAZ6IP.js";
import {
  Alert<PERSON>ontroller,
  CommonModule,
  Component,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardSubtitle,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonItemDivider,
  IonItemGroup,
  IonLabel,
  IonList,
  IonTitle,
  IonToolbar,
  IonicModule,
  LoadingController,
  Router,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵtext
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import {
  __async
} from "./chunk-UL2P3LPA.js";

// src/app/components/fcm-refresh/fcm-refresh.component.ts
var FcmRefreshComponent = class _FcmRefreshComponent {
  constructor(fcmService, authService, alertController, loadingController) {
    this.fcmService = fcmService;
    this.authService = authService;
    this.alertController = alertController;
    this.loadingController = loadingController;
    this.userId = null;
  }
  ngOnInit() {
    const token = localStorage.getItem("token");
    if (token) {
      try {
        const tokenData = this.parseJwt(token);
        if (tokenData && tokenData.sub) {
          this.userId = tokenData.sub;
        }
      } catch (error) {
        console.error("Error parsing JWT token:", error);
      }
    }
  }
  /**
   * Parse a JWT token to get the payload
   * @param token The JWT token to parse
   * @returns The decoded token payload
   */
  parseJwt(token) {
    try {
      const base64Url = token.split(".")[1];
      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(atob(base64).split("").map(function(c) {
        return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(""));
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error("Error parsing JWT token:", error);
      return null;
    }
  }
  /**
   * Refresh the FCM token and re-register with the backend
   */
  refreshFCMToken() {
    return __async(this, null, function* () {
      const loading = yield this.loadingController.create({
        message: "Refreshing notification settings...",
        spinner: "circles"
      });
      yield loading.present();
      try {
        const success = yield this.fcmService.refreshFCMToken(this.userId || void 0);
        yield loading.dismiss();
        if (success) {
          const alert = yield this.alertController.create({
            header: "Success",
            message: "Notification settings refreshed successfully. You should now be able to receive notifications.",
            buttons: ["OK"]
          });
          yield alert.present();
        } else {
          const alert = yield this.alertController.create({
            header: "Error",
            message: "Failed to refresh notification settings. Please try again later.",
            buttons: ["OK"]
          });
          yield alert.present();
        }
      } catch (error) {
        yield loading.dismiss();
        const alert = yield this.alertController.create({
          header: "Error",
          message: "An error occurred while refreshing notification settings. Please try again later.",
          buttons: ["OK"]
        });
        yield alert.present();
      }
    });
  }
  static {
    this.\u0275fac = function FcmRefreshComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _FcmRefreshComponent)(\u0275\u0275directiveInject(FcmService), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(AlertController), \u0275\u0275directiveInject(LoadingController));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _FcmRefreshComponent, selectors: [["app-fcm-refresh"]], decls: 14, vars: 0, consts: [["expand", "block", 3, "click"], ["name", "refresh-outline", "slot", "start"]], template: function FcmRefreshComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-card")(1, "ion-card-header")(2, "ion-card-title");
        \u0275\u0275text(3, "Notification Settings");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-card-subtitle");
        \u0275\u0275text(5, "Not receiving notifications?");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(6, "ion-card-content")(7, "p");
        \u0275\u0275text(8, "If you're not receiving notifications, you can try refreshing your notification settings.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(9, "p");
        \u0275\u0275text(10, "This will generate a new notification token and register it with our servers.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(11, "ion-button", 0);
        \u0275\u0275listener("click", function FcmRefreshComponent_Template_ion_button_click_11_listener() {
          return ctx.refreshFCMToken();
        });
        \u0275\u0275element(12, "ion-icon", 1);
        \u0275\u0275text(13, " Refresh Notification Settings ");
        \u0275\u0275elementEnd()()();
      }
    }, dependencies: [IonicModule, IonButton, IonCard, IonCardContent, IonCardHeader, IonCardSubtitle, IonCardTitle, IonIcon, CommonModule], styles: ["\n\nion-card[_ngcontent-%COMP%] {\n  margin: 16px;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\nion-card-title[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  font-weight: 600;\n}\nion-card-subtitle[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n  color: var(--ion-color-medium);\n}\nion-card-content[_ngcontent-%COMP%] {\n  padding: 16px;\n}\np[_ngcontent-%COMP%] {\n  margin-bottom: 12px;\n  font-size: 0.9rem;\n  line-height: 1.4;\n  color: var(--ion-color-dark);\n}\nion-button[_ngcontent-%COMP%] {\n  margin-top: 16px;\n}\n/*# sourceMappingURL=fcm-refresh.component.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FcmRefreshComponent, [{
    type: Component,
    args: [{ selector: "app-fcm-refresh", standalone: true, imports: [IonicModule, CommonModule], template: `<ion-card>\r
  <ion-card-header>\r
    <ion-card-title>Notification Settings</ion-card-title>\r
    <ion-card-subtitle>Not receiving notifications?</ion-card-subtitle>\r
  </ion-card-header>\r
  \r
  <ion-card-content>\r
    <p>If you're not receiving notifications, you can try refreshing your notification settings.</p>\r
    <p>This will generate a new notification token and register it with our servers.</p>\r
    \r
    <ion-button expand="block" (click)="refreshFCMToken()">\r
      <ion-icon name="refresh-outline" slot="start"></ion-icon>\r
      Refresh Notification Settings\r
    </ion-button>\r
  </ion-card-content>\r
</ion-card>\r
`, styles: ["/* src/app/components/fcm-refresh/fcm-refresh.component.scss */\nion-card {\n  margin: 16px;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\nion-card-title {\n  font-size: 1.2rem;\n  font-weight: 600;\n}\nion-card-subtitle {\n  font-size: 0.9rem;\n  color: var(--ion-color-medium);\n}\nion-card-content {\n  padding: 16px;\n}\np {\n  margin-bottom: 12px;\n  font-size: 0.9rem;\n  line-height: 1.4;\n  color: var(--ion-color-dark);\n}\nion-button {\n  margin-top: 16px;\n}\n/*# sourceMappingURL=fcm-refresh.component.css.map */\n"] }]
  }], () => [{ type: FcmService }, { type: AuthService }, { type: AlertController }, { type: LoadingController }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(FcmRefreshComponent, { className: "FcmRefreshComponent", filePath: "src/app/components/fcm-refresh/fcm-refresh.component.ts", lineNumber: 14 });
})();

// src/app/pages/settings/settings.page.ts
var SettingsPage = class _SettingsPage {
  constructor(router) {
    this.router = router;
  }
  ngOnInit() {
  }
  goBack() {
    this.router.navigate(["/tabs/home"]);
  }
  logout() {
    localStorage.removeItem("token");
    this.router.navigate(["/login"]);
  }
  static {
    this.\u0275fac = function SettingsPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _SettingsPage)(\u0275\u0275directiveInject(Router));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SettingsPage, selectors: [["app-settings"]], decls: 22, vars: 0, consts: [["slot", "start"], [3, "click"], ["slot", "icon-only", "name", "arrow-back"], ["button", "", 3, "click"], ["name", "log-out-outline", "slot", "start", "color", "danger"]], template: function SettingsPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-buttons", 0)(3, "ion-button", 1);
        \u0275\u0275listener("click", function SettingsPage_Template_ion_button_click_3_listener() {
          return ctx.goBack();
        });
        \u0275\u0275element(4, "ion-icon", 2);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(5, "ion-title");
        \u0275\u0275text(6, "Settings");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(7, "ion-content")(8, "ion-list")(9, "ion-item-group")(10, "ion-item-divider")(11, "ion-label");
        \u0275\u0275text(12, "Notifications");
        \u0275\u0275elementEnd()();
        \u0275\u0275element(13, "app-fcm-refresh");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(14, "ion-item-group")(15, "ion-item-divider")(16, "ion-label");
        \u0275\u0275text(17, "Account");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(18, "ion-item", 3);
        \u0275\u0275listener("click", function SettingsPage_Template_ion_item_click_18_listener() {
          return ctx.logout();
        });
        \u0275\u0275element(19, "ion-icon", 4);
        \u0275\u0275elementStart(20, "ion-label");
        \u0275\u0275text(21, "Logout");
        \u0275\u0275elementEnd()()()()();
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonContent, IonHeader, IonIcon, IonItem, IonItemDivider, IonItemGroup, IonLabel, IonList, IonTitle, IonToolbar, CommonModule, FcmRefreshComponent], styles: ["\n\nion-item-divider[_ngcontent-%COMP%] {\n  --background: var(--ion-color-light);\n  --color: var(--ion-color-medium);\n  font-size: 0.9rem;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n  text-transform: uppercase;\n  padding-top: 16px;\n}\nion-item[_ngcontent-%COMP%] {\n  --padding-start: 16px;\n  --inner-padding-end: 16px;\n}\n/*# sourceMappingURL=settings.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SettingsPage, [{
    type: Component,
    args: [{ selector: "app-settings", standalone: true, imports: [IonicModule, CommonModule, FcmRefreshComponent], template: '<ion-header>\r\n  <ion-toolbar>\r\n    <ion-buttons slot="start">\r\n      <ion-button (click)="goBack()">\r\n        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n    <ion-title>Settings</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content>\r\n  <ion-list>\r\n    <ion-item-group>\r\n      <ion-item-divider>\r\n        <ion-label>Notifications</ion-label>\r\n      </ion-item-divider>\r\n      \r\n      <!-- FCM Refresh Component -->\r\n      <app-fcm-refresh></app-fcm-refresh>\r\n    </ion-item-group>\r\n    \r\n    <ion-item-group>\r\n      <ion-item-divider>\r\n        <ion-label>Account</ion-label>\r\n      </ion-item-divider>\r\n      \r\n      <ion-item button (click)="logout()">\r\n        <ion-icon name="log-out-outline" slot="start" color="danger"></ion-icon>\r\n        <ion-label>Logout</ion-label>\r\n      </ion-item>\r\n    </ion-item-group>\r\n  </ion-list>\r\n</ion-content>\r\n', styles: ["/* src/app/pages/settings/settings.page.scss */\nion-item-divider {\n  --background: var(--ion-color-light);\n  --color: var(--ion-color-medium);\n  font-size: 0.9rem;\n  font-weight: 600;\n  letter-spacing: 0.5px;\n  text-transform: uppercase;\n  padding-top: 16px;\n}\nion-item {\n  --padding-start: 16px;\n  --inner-padding-end: 16px;\n}\n/*# sourceMappingURL=settings.page.css.map */\n"] }]
  }], () => [{ type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SettingsPage, { className: "SettingsPage", filePath: "src/app/pages/settings/settings.page.ts", lineNumber: 14 });
})();
export {
  SettingsPage
};
//# sourceMappingURL=settings.page-LLH5UPY5.js.map

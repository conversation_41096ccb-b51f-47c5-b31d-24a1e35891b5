import {
  CommonModule,
  Component,
  <PERSON>Label,
  <PERSON>TabBar,
  IonTabButton,
  IonTabs2 as IonTabs,
  IonicModule,
  RouterModule,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import "./chunk-UL2P3LPA.js";

// src/app/pages/tabs/tabs.page.ts
var TabsPage = class _TabsPage {
  constructor() {
  }
  static {
    this.\u0275fac = function TabsPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TabsPage)();
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _TabsPage, selectors: [["app-tabs"]], decls: 18, vars: 0, consts: [["slot", "bottom"], ["tab", "home"], ["src", "assets/homePage.png", 2, "width", "24px", "height", "24px", "display", "block", "margin", "auto"], ["tab", "search"], ["src", "assets/searchPlace.png", 2, "width", "24px", "height", "24px", "display", "block", "margin", "auto"], ["tab", "map"], ["src", "assets/map.png", 2, "width", "24px", "height", "24px", "display", "block", "margin", "auto"], ["tab", "profile"], ["src", "assets/setting.png", 2, "width", "24px", "height", "24px", "display", "block", "margin", "auto"]], template: function TabsPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-tabs")(1, "ion-tab-bar", 0)(2, "ion-tab-button", 1);
        \u0275\u0275element(3, "img", 2);
        \u0275\u0275elementStart(4, "ion-label");
        \u0275\u0275text(5, "Home");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(6, "ion-tab-button", 3);
        \u0275\u0275element(7, "img", 4);
        \u0275\u0275elementStart(8, "ion-label");
        \u0275\u0275text(9, "Search");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(10, "ion-tab-button", 5);
        \u0275\u0275element(11, "img", 6);
        \u0275\u0275elementStart(12, "ion-label");
        \u0275\u0275text(13, "Map");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(14, "ion-tab-button", 7);
        \u0275\u0275element(15, "img", 8);
        \u0275\u0275elementStart(16, "ion-label");
        \u0275\u0275text(17, "Settings");
        \u0275\u0275elementEnd()()()();
      }
    }, dependencies: [IonicModule, IonLabel, IonTabBar, IonTabButton, IonTabs, CommonModule, RouterModule], encapsulation: 2 });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TabsPage, [{
    type: Component,
    args: [{ selector: "app-tabs", standalone: true, imports: [IonicModule, CommonModule, RouterModule], template: '<ion-tabs>\r\n  <ion-tab-bar slot="bottom">\r\n    <ion-tab-button tab="home">\r\n      <img src="assets/homePage.png" style="width:24px; height:24px; display:block; margin:auto;" />\r\n      <ion-label>Home</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab="search">\r\n      <img src="assets/searchPlace.png" style="width:24px; height:24px; display:block; margin:auto;" />\r\n      <ion-label>Search</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab="map">\r\n      <img src="assets/map.png" style="width:24px; height:24px; display:block; margin:auto;" />\r\n      <ion-label>Map</ion-label>\r\n    </ion-tab-button>\r\n\r\n    <ion-tab-button tab="profile">\r\n      <img src="assets/setting.png" style="width:24px; height:24px; display:block; margin:auto;" />\r\n      <ion-label>Settings</ion-label>\r\n    </ion-tab-button>\r\n  </ion-tab-bar>\r\n</ion-tabs> ' }]
  }], () => [], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(TabsPage, { className: "TabsPage", filePath: "src/app/pages/tabs/tabs.page.ts", lineNumber: 13 });
})();
export {
  TabsPage
};
//# sourceMappingURL=tabs.page-PZRIWQXB.js.map

{"version": 3, "sources": ["src/app/pages/map/directions-panel.component.scss"], "sourcesContent": [".directions-panel {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background-color: white;\r\n  border-top-left-radius: 15px;\r\n  border-top-right-radius: 15px;\r\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);\r\n  max-height: 50vh;\r\n  overflow-y: auto;\r\n  z-index: 1000;\r\n}\r\n\r\n.directions-header {\r\n  padding: 10px 0;\r\n  border-bottom: 1px solid #eee;\r\n  position: sticky;\r\n  top: 0;\r\n  background-color: white;\r\n  z-index: 1001;\r\n}\r\n\r\n.directions-list {\r\n  max-height: calc(50vh - 60px);\r\n  overflow-y: auto;\r\n}\r\n\r\nion-item {\r\n  --padding-start: 16px;\r\n  --inner-padding-end: 16px;\r\n}\r\n\r\nion-icon {\r\n  font-size: 24px;\r\n}\r\n\r\nh2 {\r\n  font-weight: 600;\r\n  margin: 0;\r\n}\r\n\r\nh3 {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  margin: 0;\r\n}\r\n\r\np {\r\n  font-size: 12px;\r\n  color: var(--ion-color-medium);\r\n  margin: 4px 0 0;\r\n}\r\n\r\nion-note {\r\n  font-size: 12px;\r\n  padding: 4px 8px;\r\n  border-radius: 50%;\r\n  background-color: var(--ion-color-light);\r\n  color: var(--ion-color-dark);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-width: 24px;\r\n  min-height: 24px;\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,YAAA;AACA,UAAA;AACA,QAAA;AACA,SAAA;AACA,oBAAA;AACA,0BAAA;AACA,2BAAA;AACA,cAAA,EAAA,KAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA;AACA,cAAA;AACA,WAAA;;AAGF,CAAA;AACE,WAAA,KAAA;AACA,iBAAA,IAAA,MAAA;AACA,YAAA;AACA,OAAA;AACA,oBAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA,KAAA,KAAA,EAAA;AACA,cAAA;;AAGF;AACE,mBAAA;AACA,uBAAA;;AAGF;AACE,aAAA;;AAGF;AACE,eAAA;AACA,UAAA;;AAGF;AACE,aAAA;AACA,eAAA;AACA,UAAA;;AAGF;AACE,aAAA;AACA,SAAA,IAAA;AACA,UAAA,IAAA,EAAA;;AAGF;AACE,aAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,oBAAA,IAAA;AACA,SAAA,IAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,aAAA;AACA,cAAA;;", "names": []}
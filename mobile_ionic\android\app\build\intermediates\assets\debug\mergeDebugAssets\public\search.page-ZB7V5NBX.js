import {
  LoadingService
} from "./chunk-5HXGJ7V4.js";
import "./chunk-GNSGQYNI.js";
import "./chunk-IREJNTLA.js";
import "./chunk-H4CMHWOA.js";
import "./chunk-KQEJHESJ.js";
import "./chunk-LHYYZWFK.js";
import {
  environment
} from "./chunk-I7MI46CM.js";
import {
  CommonModule,
  Component,
  FormsModule,
  HttpClient,
  Input,
  IonBadge,
  IonButton,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonRefresher,
  IonRefresherContent,
  IonSearchbar,
  IonSpinner,
  IonText,
  IonTitle,
  IonToolbar,
  IonicModule,
  ModalController,
  NgControlStatus,
  NgForOf,
  NgIf,
  NgModel,
  Router,
  TextValueAccessorDirective,
  ToastController,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpropertyInterpolate,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import {
  __async
} from "./chunk-UL2P3LPA.js";

// src/app/pages/search/evacuation-center-modal.component.ts
var EvacuationCenterModalComponent = class _EvacuationCenterModalComponent {
  constructor(modalCtrl, router, toastCtrl) {
    this.modalCtrl = modalCtrl;
    this.router = router;
    this.toastCtrl = toastCtrl;
  }
  dismiss() {
    this.modalCtrl.dismiss();
  }
  viewOnMap() {
    this.modalCtrl.dismiss();
    this.router.navigate(["/tabs/map"], {
      queryParams: {
        lat: this.center.latitude,
        lng: this.center.longitude,
        name: this.center.name,
        viewOnly: "true"
      }
    });
    this.toastCtrl.create({
      message: `Showing ${this.center.name} on map`,
      duration: 2e3,
      color: "success"
    }).then((toast) => toast.present());
  }
  getDirections() {
    this.modalCtrl.dismiss();
    this.router.navigate(["/tabs/map"], {
      queryParams: {
        lat: this.center.latitude,
        lng: this.center.longitude,
        name: this.center.name,
        directions: "true"
      }
    });
    this.toastCtrl.create({
      message: `Getting directions to ${this.center.name}`,
      duration: 2e3,
      color: "success"
    }).then((toast) => toast.present());
  }
  getDisasterTypeIcon(type) {
    if (!type)
      return "alert-circle-outline";
    const normalizedType = type.toLowerCase();
    if (normalizedType.includes("earthquake") || normalizedType.includes("quake")) {
      return "earth-outline";
    } else if (normalizedType.includes("flood") || normalizedType.includes("flash")) {
      return "water-outline";
    } else if (normalizedType.includes("typhoon") || normalizedType.includes("storm")) {
      return "thunderstorm-outline";
    } else if (normalizedType.includes("fire")) {
      return "flame-outline";
    }
    return "alert-circle-outline";
  }
  getStatusColor(status) {
    if (!status)
      return "medium";
    const normalizedStatus = status.toLowerCase();
    if (normalizedStatus.includes("active") || normalizedStatus.includes("open")) {
      return "success";
    } else if (normalizedStatus.includes("inactive") || normalizedStatus.includes("closed")) {
      return "warning";
    } else if (normalizedStatus.includes("full")) {
      return "danger";
    }
    return "medium";
  }
  static {
    this.\u0275fac = function EvacuationCenterModalComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _EvacuationCenterModalComponent)(\u0275\u0275directiveInject(ModalController), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(ToastController));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _EvacuationCenterModalComponent, selectors: [["app-evacuation-center-modal"]], inputs: { center: "center" }, decls: 25, vars: 4, consts: [[1, "modal-container"], [1, "close-button", 3, "click"], ["name", "close-circle", "color", "danger"], [1, "center-name"], [1, "center-image"], ["src", "assets/evacuation-center.jpg", "onerror", "this.src='assets/evacuation-placeholder.jpg'", 3, "alt"], [1, "info-section"], [1, "info-label"], [1, "info-value", "contact"], ["name", "call-outline"], [1, "info-value", "address"], ["name", "location-outline"], [1, "directions-button"], ["expand", "block", "color", "primary", 3, "click"], ["name", "navigate", "slot", "start"]], template: function EvacuationCenterModalComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "div", 0)(1, "div", 1);
        \u0275\u0275listener("click", function EvacuationCenterModalComponent_Template_div_click_1_listener() {
          return ctx.dismiss();
        });
        \u0275\u0275element(2, "ion-icon", 2);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(3, "h2", 3);
        \u0275\u0275text(4);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(5, "div", 4);
        \u0275\u0275element(6, "img", 5);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(7, "div", 6)(8, "div", 7);
        \u0275\u0275text(9, "Contact Number");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(10, "div", 8);
        \u0275\u0275element(11, "ion-icon", 9);
        \u0275\u0275elementStart(12, "span");
        \u0275\u0275text(13);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(14, "div", 6)(15, "div", 7);
        \u0275\u0275text(16, "Address");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(17, "div", 10);
        \u0275\u0275element(18, "ion-icon", 11);
        \u0275\u0275elementStart(19, "span");
        \u0275\u0275text(20);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(21, "div", 12)(22, "ion-button", 13);
        \u0275\u0275listener("click", function EvacuationCenterModalComponent_Template_ion_button_click_22_listener() {
          return ctx.getDirections();
        });
        \u0275\u0275element(23, "ion-icon", 14);
        \u0275\u0275text(24, " Get Directions ");
        \u0275\u0275elementEnd()()();
      }
      if (rf & 2) {
        \u0275\u0275advance(4);
        \u0275\u0275textInterpolate(ctx.center.name);
        \u0275\u0275advance(2);
        \u0275\u0275propertyInterpolate("alt", ctx.center.name);
        \u0275\u0275advance(7);
        \u0275\u0275textInterpolate(ctx.center.contact || "No contact available");
        \u0275\u0275advance(7);
        \u0275\u0275textInterpolate(ctx.center.address);
      }
    }, dependencies: [IonicModule, IonButton, IonIcon, CommonModule], styles: ["\n\n.modal-container[_ngcontent-%COMP%] {\n  background-color: white;\n  border-radius: 12px;\n  overflow: hidden;\n  width: 100%;\n  max-width: 350px;\n  margin: 0 auto;\n  position: relative;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n.close-button[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  z-index: 10;\n}\n.close-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n  background: white;\n  border-radius: 50%;\n}\n.center-name[_ngcontent-%COMP%] {\n  font-size: 18px;\n  font-weight: 600;\n  text-align: center;\n  margin: 15px 15px 10px;\n  color: #000;\n}\n.center-image[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 160px;\n  overflow: hidden;\n}\n.center-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n.info-section[_ngcontent-%COMP%] {\n  padding: 10px 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n.info-section[_ngcontent-%COMP%]:last-of-type {\n  border-bottom: none;\n}\n.info-label[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #0099ff;\n  margin-bottom: 5px;\n}\n.info-value[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  font-size: 15px;\n  color: #333;\n}\n.info-value[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  margin-right: 8px;\n  font-size: 18px;\n  min-width: 18px;\n}\n.info-value.contact[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  color: #333;\n}\n.info-value.address[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  color: #ff4961;\n}\n.directions-button[_ngcontent-%COMP%] {\n  padding: 10px 15px 15px;\n}\n.directions-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  --border-radius: 8px;\n  --background: #0099ff;\n  font-weight: 500;\n  margin: 0;\n}\n.directions-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  margin-right: 5px;\n}\n/*# sourceMappingURL=evacuation-center-modal.component.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(EvacuationCenterModalComponent, [{
    type: Component,
    args: [{ selector: "app-evacuation-center-modal", standalone: true, imports: [IonicModule, CommonModule], template: `<div class="modal-container">\r
  <!-- Close button -->\r
  <div class="close-button" (click)="dismiss()">\r
    <ion-icon name="close-circle" color="danger"></ion-icon>\r
  </div>\r
\r
  <!-- Center name -->\r
  <h2 class="center-name">{{ center.name }}</h2>\r
\r
  <!-- Center image -->\r
  <div class="center-image">\r
    <!-- Use a placeholder image for now -->\r
    <img src="assets/evacuation-center.jpg" alt="{{ center.name }}"\r
         onerror="this.src='assets/evacuation-placeholder.jpg'">\r
  </div>\r
\r
  <!-- Contact info -->\r
  <div class="info-section">\r
    <div class="info-label">Contact Number</div>\r
    <div class="info-value contact">\r
      <ion-icon name="call-outline"></ion-icon>\r
      <span>{{ center.contact || 'No contact available' }}</span>\r
    </div>\r
  </div>\r
\r
  <!-- Address info -->\r
  <div class="info-section">\r
    <div class="info-label">Address</div>\r
    <div class="info-value address">\r
      <ion-icon name="location-outline"></ion-icon>\r
      <span>{{ center.address }}</span>\r
    </div>\r
  </div>\r
\r
  <!-- Get Directions button -->\r
  <div class="directions-button">\r
    <ion-button expand="block" color="primary" (click)="getDirections()">\r
      <ion-icon name="navigate" slot="start"></ion-icon>\r
      Get Directions\r
    </ion-button>\r
  </div>\r
</div>\r
`, styles: ["/* src/app/pages/search/evacuation-center-modal.component.scss */\n.modal-container {\n  background-color: white;\n  border-radius: 12px;\n  overflow: hidden;\n  width: 100%;\n  max-width: 350px;\n  margin: 0 auto;\n  position: relative;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n.close-button {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  z-index: 10;\n}\n.close-button ion-icon {\n  font-size: 24px;\n  background: white;\n  border-radius: 50%;\n}\n.center-name {\n  font-size: 18px;\n  font-weight: 600;\n  text-align: center;\n  margin: 15px 15px 10px;\n  color: #000;\n}\n.center-image {\n  width: 100%;\n  height: 160px;\n  overflow: hidden;\n}\n.center-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n.info-section {\n  padding: 10px 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n.info-section:last-of-type {\n  border-bottom: none;\n}\n.info-label {\n  font-size: 14px;\n  color: #0099ff;\n  margin-bottom: 5px;\n}\n.info-value {\n  display: flex;\n  align-items: center;\n  font-size: 15px;\n  color: #333;\n}\n.info-value ion-icon {\n  margin-right: 8px;\n  font-size: 18px;\n  min-width: 18px;\n}\n.info-value.contact ion-icon {\n  color: #333;\n}\n.info-value.address ion-icon {\n  color: #ff4961;\n}\n.directions-button {\n  padding: 10px 15px 15px;\n}\n.directions-button ion-button {\n  --border-radius: 8px;\n  --background: #0099ff;\n  font-weight: 500;\n  margin: 0;\n}\n.directions-button ion-button ion-icon {\n  margin-right: 5px;\n}\n/*# sourceMappingURL=evacuation-center-modal.component.css.map */\n"] }]
  }], () => [{ type: ModalController }, { type: Router }, { type: ToastController }], { center: [{
    type: Input
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(EvacuationCenterModalComponent, { className: "EvacuationCenterModalComponent", filePath: "src/app/pages/search/evacuation-center-modal.component.ts", lineNumber: 25 });
})();

// src/app/pages/search/search.page.ts
function SearchPage_ion_text_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-text", 11)(1, "p");
    \u0275\u0275text(2, "Search by center name, address, or disaster type");
    \u0275\u0275elementEnd()();
  }
}
function SearchPage_div_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 12);
    \u0275\u0275element(1, "ion-spinner", 13);
    \u0275\u0275elementStart(2, "ion-text", 14)(3, "p");
    \u0275\u0275text(4, "Loading evacuation centers...");
    \u0275\u0275elementEnd()()();
  }
}
function SearchPage_div_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 15);
    \u0275\u0275element(1, "ion-icon", 16);
    \u0275\u0275elementStart(2, "ion-text", 17)(3, "p");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "ion-button", 18);
    \u0275\u0275listener("click", function SearchPage_div_12_Template_ion_button_click_5_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.loadEvacuationCenters());
    });
    \u0275\u0275element(6, "ion-icon", 19);
    \u0275\u0275text(7, " Try Again ");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r1.errorMessage);
  }
}
function SearchPage_ion_list_13_ion_item_1_p_7_ion_badge_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-badge", 25);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const location_r4 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275propertyInterpolate("color", location_r4.status === "Active" ? "success" : "warning");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", location_r4.status, " ");
  }
}
function SearchPage_ion_list_13_ion_item_1_p_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p")(1, "ion-badge", 23);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275template(3, SearchPage_ion_list_13_ion_item_1_p_7_ion_badge_3_Template, 2, 2, "ion-badge", 24);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const location_r4 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(location_r4.disaster_type);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", location_r4.status);
  }
}
function SearchPage_ion_list_13_ion_item_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-item", 21);
    \u0275\u0275listener("click", function SearchPage_ion_list_13_ion_item_1_Template_ion_item_click_0_listener() {
      const location_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.viewOnMap(location_r4));
    });
    \u0275\u0275element(1, "ion-icon", 22);
    \u0275\u0275elementStart(2, "ion-label")(3, "h2");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275template(7, SearchPage_ion_list_13_ion_item_1_p_7_Template, 4, 2, "p", 8);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const location_r4 = ctx.$implicit;
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(location_r4.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(location_r4.address);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", location_r4.disaster_type);
  }
}
function SearchPage_ion_list_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-list");
    \u0275\u0275template(1, SearchPage_ion_list_13_ion_item_1_Template, 8, 3, "ion-item", 20);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r1.locations);
  }
}
function SearchPage_div_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 26);
    \u0275\u0275element(1, "ion-icon", 27);
    \u0275\u0275elementStart(2, "ion-text", 14)(3, "p");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1('No evacuation centers found matching "', ctx_r1.searchQuery, '"');
  }
}
function SearchPage_div_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 28);
    \u0275\u0275element(1, "ion-icon", 29);
    \u0275\u0275elementStart(2, "ion-text", 14)(3, "h3");
    \u0275\u0275text(4, "Search for Evacuation Centers");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6, "Enter a name, address, or disaster type to find evacuation centers");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "ion-button", 30);
    \u0275\u0275listener("click", function SearchPage_div_15_Template_ion_button_click_7_listener() {
      \u0275\u0275restoreView(_r5);
      const ctx_r1 = \u0275\u0275nextContext();
      ctx_r1.searchQuery = "all";
      return \u0275\u0275resetView(ctx_r1.onSearch({ target: { value: "all" } }));
    });
    \u0275\u0275text(8, " Show All Centers ");
    \u0275\u0275elementEnd()();
  }
}
var SearchPage = class _SearchPage {
  constructor(http, loadingService, toastCtrl, router, modalCtrl) {
    this.http = http;
    this.loadingService = loadingService;
    this.toastCtrl = toastCtrl;
    this.router = router;
    this.modalCtrl = modalCtrl;
    this.searchQuery = "";
    this.locations = [];
    this.allCenters = [];
    this.isLoading = false;
    this.hasError = false;
    this.errorMessage = "";
  }
  ngOnInit() {
    this.loadEvacuationCenters();
  }
  loadEvacuationCenters() {
    return __async(this, null, function* () {
      yield this.loadingService.showLoading("Loading evacuation centers...");
      this.isLoading = true;
      try {
        this.http.get(`${environment.apiUrl}/evacuation-centers`).subscribe({
          next: (data) => {
            console.log("Loaded evacuation centers:", data);
            this.allCenters = data || [];
            this.isLoading = false;
            this.loadingService.dismissLoading();
          },
          error: (error) => {
            console.error("Error loading evacuation centers:", error);
            this.hasError = true;
            this.errorMessage = "Failed to load evacuation centers. Please try again later.";
            this.isLoading = false;
            this.loadingService.dismissLoading();
            this.toastCtrl.create({
              message: "Failed to load evacuation centers. Please try again later.",
              duration: 3e3,
              color: "danger"
            }).then((toast) => toast.present());
          }
        });
      } catch (error) {
        console.error("Exception loading evacuation centers:", error);
        this.hasError = true;
        this.errorMessage = "An unexpected error occurred. Please try again later.";
        this.isLoading = false;
        this.loadingService.dismissLoading();
      }
    });
  }
  onSearch(event) {
    const query = event.target.value.toLowerCase().trim();
    console.log("Searching for:", query);
    if (!query) {
      this.locations = [];
      return;
    }
    this.locations = this.allCenters.filter((center) => {
      const nameStartsWith = center.name.toLowerCase().startsWith(query);
      const nameIncludes = center.name.toLowerCase().includes(query);
      const addressIncludes = center.address?.toLowerCase().includes(query);
      const disasterTypeIncludes = center.disaster_type?.toLowerCase().includes(query);
      return nameStartsWith || nameIncludes || addressIncludes || disasterTypeIncludes;
    });
    this.locations.sort((a, b) => {
      const aStartsWith = a.name.toLowerCase().startsWith(query);
      const bStartsWith = b.name.toLowerCase().startsWith(query);
      if (aStartsWith && !bStartsWith)
        return -1;
      if (!aStartsWith && bStartsWith)
        return 1;
      return 0;
    });
  }
  clearSearch() {
    this.searchQuery = "";
    this.locations = [];
  }
  refreshCenters(event) {
    this.loadEvacuationCenters().then(() => {
      event.target.complete();
    });
  }
  viewOnMap(center) {
    return __async(this, null, function* () {
      const modal = yield this.modalCtrl.create({
        component: EvacuationCenterModalComponent,
        componentProps: {
          center
        },
        cssClass: "evacuation-center-modal"
      });
      yield modal.present();
    });
  }
  static {
    this.\u0275fac = function SearchPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _SearchPage)(\u0275\u0275directiveInject(HttpClient), \u0275\u0275directiveInject(LoadingService), \u0275\u0275directiveInject(ToastController), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(ModalController));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SearchPage, selectors: [["app-search"]], decls: 16, vars: 7, consts: [["slot", "fixed", 3, "ionRefresh"], ["pullingIcon", "chevron-down-circle-outline", "pullingText", "Pull to refresh", "refreshingSpinner", "circles", "refreshingText", "Refreshing..."], [1, "search-container"], ["placeholder", "Search evacuation centers by name", "animated", "true", "showCancelButton", "focus", "debounce", "300", 3, "ngModelChange", "ionInput", "ionClear", "ngModel"], ["color", "medium", "class", "search-hint", 4, "ngIf"], [1, "search-results"], ["class", "loading-container", 4, "ngIf"], ["class", "error-container", 4, "ngIf"], [4, "ngIf"], ["class", "no-results", 4, "ngIf"], ["class", "empty-state", 4, "ngIf"], ["color", "medium", 1, "search-hint"], [1, "loading-container"], ["name", "circles"], ["color", "medium"], [1, "error-container"], ["name", "alert-circle-outline", "color", "danger", "size", "large"], ["color", "danger"], ["fill", "outline", "size", "small", 3, "click"], ["name", "refresh-outline", "slot", "start"], ["button", "", "detail", "", 3, "click", 4, "ngFor", "ngForOf"], ["button", "", "detail", "", 3, "click"], ["name", "location-outline", "slot", "start", "color", "primary"], ["color", "secondary"], [3, "color", 4, "ngIf"], [3, "color"], [1, "no-results"], ["name", "search-outline", "color", "medium", "size", "large"], [1, "empty-state"], ["name", "search", "color", "primary", "size", "large"], ["fill", "outline", 3, "click"]], template: function SearchPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-title");
        \u0275\u0275text(3, "Search Evacuation Centers");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(4, "ion-content")(5, "ion-refresher", 0);
        \u0275\u0275listener("ionRefresh", function SearchPage_Template_ion_refresher_ionRefresh_5_listener($event) {
          return ctx.refreshCenters($event);
        });
        \u0275\u0275element(6, "ion-refresher-content", 1);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(7, "div", 2)(8, "ion-searchbar", 3);
        \u0275\u0275twoWayListener("ngModelChange", function SearchPage_Template_ion_searchbar_ngModelChange_8_listener($event) {
          \u0275\u0275twoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);
          return $event;
        });
        \u0275\u0275listener("ionInput", function SearchPage_Template_ion_searchbar_ionInput_8_listener($event) {
          return ctx.onSearch($event);
        })("ionClear", function SearchPage_Template_ion_searchbar_ionClear_8_listener() {
          return ctx.clearSearch();
        });
        \u0275\u0275elementEnd();
        \u0275\u0275template(9, SearchPage_ion_text_9_Template, 3, 0, "ion-text", 4);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(10, "div", 5);
        \u0275\u0275template(11, SearchPage_div_11_Template, 5, 0, "div", 6)(12, SearchPage_div_12_Template, 8, 1, "div", 7)(13, SearchPage_ion_list_13_Template, 2, 1, "ion-list", 8)(14, SearchPage_div_14_Template, 5, 1, "div", 9)(15, SearchPage_div_15_Template, 9, 0, "div", 10);
        \u0275\u0275elementEnd()();
      }
      if (rf & 2) {
        \u0275\u0275advance(8);
        \u0275\u0275twoWayProperty("ngModel", ctx.searchQuery);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.searchQuery);
        \u0275\u0275advance(2);
        \u0275\u0275property("ngIf", ctx.isLoading);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.hasError);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.locations.length > 0);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.searchQuery && ctx.locations.length === 0 && !ctx.isLoading && !ctx.hasError);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.searchQuery && !ctx.isLoading && !ctx.hasError && ctx.allCenters.length > 0);
      }
    }, dependencies: [IonicModule, IonBadge, IonButton, IonContent, IonHeader, IonIcon, IonItem, IonLabel, IonList, IonRefresher, IonRefresherContent, IonSearchbar, IonSpinner, IonText, IonTitle, IonToolbar, TextValueAccessorDirective, CommonModule, NgForOf, NgIf, FormsModule, NgControlStatus, NgModel], styles: ["\n\nion-content[_ngcontent-%COMP%] {\n  --background: #f8f9fa;\n}\n.search-container[_ngcontent-%COMP%] {\n  padding: 10px 16px 0;\n}\n.search-container[_ngcontent-%COMP%]   ion-searchbar[_ngcontent-%COMP%] {\n  --border-radius: 10px;\n  --box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n  --placeholder-color: var(--ion-color-medium);\n  --icon-color: var(--ion-color-primary);\n}\n.search-container[_ngcontent-%COMP%]   .search-hint[_ngcontent-%COMP%] {\n  font-size: 12px;\n  margin: 0 0 10px 16px;\n  display: block;\n}\n.search-results[_ngcontent-%COMP%] {\n  padding: 0 16px 16px;\n}\n.loading-container[_ngcontent-%COMP%], \n.error-container[_ngcontent-%COMP%], \n.no-results[_ngcontent-%COMP%], \n.empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  padding: 32px 16px;\n  min-height: 200px;\n}\n.loading-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], \n.error-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], \n.no-results[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], \n.empty-state[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \n.error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \n.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 8px 0;\n  font-size: 16px;\n}\n.loading-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], \n.error-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], \n.no-results[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], \n.empty-state[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  margin-top: 16px;\n}\nion-list[_ngcontent-%COMP%] {\n  background: transparent;\n  padding: 0;\n}\nion-item[_ngcontent-%COMP%] {\n  --padding-start: 16px;\n  --inner-padding-end: 16px;\n  --background: white;\n  margin-bottom: 10px;\n  border-radius: 10px;\n  --border-radius: 10px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\nion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: 500;\n  margin-bottom: 4px;\n  color: var(--ion-color-dark);\n}\nion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: var(--ion-color-medium);\n  margin: 2px 0;\n}\nion-item[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%] {\n  margin-right: 6px;\n  padding: 4px 8px;\n  border-radius: 4px;\n}\n.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 18px;\n  font-weight: 500;\n  margin: 8px 0;\n  color: var(--ion-color-dark);\n}\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n/*# sourceMappingURL=search.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SearchPage, [{
    type: Component,
    args: [{ selector: "app-search", standalone: true, imports: [IonicModule, CommonModule, FormsModule], template: `\r
<ion-header>\r
  <ion-toolbar>\r
    <ion-title>Search Evacuation Centers</ion-title>\r
  </ion-toolbar>\r
</ion-header>\r
\r
<ion-content>\r
  <ion-refresher slot="fixed" (ionRefresh)="refreshCenters($event)">\r
    <ion-refresher-content\r
      pullingIcon="chevron-down-circle-outline"\r
      pullingText="Pull to refresh"\r
      refreshingSpinner="circles"\r
      refreshingText="Refreshing...">\r
    </ion-refresher-content>\r
  </ion-refresher>\r
\r
  <div class="search-container">\r
    <ion-searchbar\r
      [(ngModel)]="searchQuery"\r
      (ionInput)="onSearch($event)"\r
      (ionClear)="clearSearch()"\r
      placeholder="Search evacuation centers by name"\r
      animated="true"\r
      showCancelButton="focus"\r
      debounce="300"\r
    ></ion-searchbar>\r
\r
    <ion-text color="medium" class="search-hint" *ngIf="!searchQuery">\r
      <p>Search by center name, address, or disaster type</p>\r
    </ion-text>\r
  </div>\r
\r
  <div class="search-results">\r
    <!-- Loading indicator -->\r
    <div class="loading-container" *ngIf="isLoading">\r
      <ion-spinner name="circles"></ion-spinner>\r
      <ion-text color="medium">\r
        <p>Loading evacuation centers...</p>\r
      </ion-text>\r
    </div>\r
\r
    <!-- Error message -->\r
    <div class="error-container" *ngIf="hasError">\r
      <ion-icon name="alert-circle-outline" color="danger" size="large"></ion-icon>\r
      <ion-text color="danger">\r
        <p>{{ errorMessage }}</p>\r
      </ion-text>\r
      <ion-button (click)="loadEvacuationCenters()" fill="outline" size="small">\r
        <ion-icon name="refresh-outline" slot="start"></ion-icon>\r
        Try Again\r
      </ion-button>\r
    </div>\r
\r
    <!-- Search results -->\r
    <ion-list *ngIf="locations.length > 0">\r
      <ion-item *ngFor="let location of locations" button detail (click)="viewOnMap(location)">\r
        <ion-icon name="location-outline" slot="start" color="primary"></ion-icon>\r
        <ion-label>\r
          <h2>{{ location.name }}</h2>\r
          <p>{{ location.address }}</p>\r
          <p *ngIf="location.disaster_type">\r
            <ion-badge color="secondary">{{ location.disaster_type }}</ion-badge>\r
            <ion-badge color="{{ location.status === 'Active' ? 'success' : 'warning' }}" *ngIf="location.status">\r
              {{ location.status }}\r
            </ion-badge>\r
          </p>\r
        </ion-label>\r
      </ion-item>\r
    </ion-list>\r
\r
    <!-- No results message -->\r
    <div class="no-results" *ngIf="searchQuery && locations.length === 0 && !isLoading && !hasError">\r
      <ion-icon name="search-outline" color="medium" size="large"></ion-icon>\r
      <ion-text color="medium">\r
        <p>No evacuation centers found matching "{{ searchQuery }}"</p>\r
      </ion-text>\r
    </div>\r
\r
    <!-- Empty state when no search is performed -->\r
    <div class="empty-state" *ngIf="!searchQuery && !isLoading && !hasError && allCenters.length > 0">\r
      <ion-icon name="search" color="primary" size="large"></ion-icon>\r
      <ion-text color="medium">\r
        <h3>Search for Evacuation Centers</h3>\r
        <p>Enter a name, address, or disaster type to find evacuation centers</p>\r
      </ion-text>\r
      <ion-button (click)="searchQuery = 'all'; onSearch({target: {value: 'all'}})" fill="outline">\r
        Show All Centers\r
      </ion-button>\r
    </div>\r
  </div>\r
</ion-content>`, styles: ["/* src/app/pages/search/search.page.scss */\nion-content {\n  --background: #f8f9fa;\n}\n.search-container {\n  padding: 10px 16px 0;\n}\n.search-container ion-searchbar {\n  --border-radius: 10px;\n  --box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n  --placeholder-color: var(--ion-color-medium);\n  --icon-color: var(--ion-color-primary);\n}\n.search-container .search-hint {\n  font-size: 12px;\n  margin: 0 0 10px 16px;\n  display: block;\n}\n.search-results {\n  padding: 0 16px 16px;\n}\n.loading-container,\n.error-container,\n.no-results,\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  padding: 32px 16px;\n  min-height: 200px;\n}\n.loading-container ion-icon,\n.error-container ion-icon,\n.no-results ion-icon,\n.empty-state ion-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n.loading-container p,\n.error-container p,\n.no-results p,\n.empty-state p {\n  margin: 8px 0;\n  font-size: 16px;\n}\n.loading-container ion-button,\n.error-container ion-button,\n.no-results ion-button,\n.empty-state ion-button {\n  margin-top: 16px;\n}\nion-list {\n  background: transparent;\n  padding: 0;\n}\nion-item {\n  --padding-start: 16px;\n  --inner-padding-end: 16px;\n  --background: white;\n  margin-bottom: 10px;\n  border-radius: 10px;\n  --border-radius: 10px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\nion-item h2 {\n  font-size: 16px;\n  font-weight: 500;\n  margin-bottom: 4px;\n  color: var(--ion-color-dark);\n}\nion-item p {\n  font-size: 14px;\n  color: var(--ion-color-medium);\n  margin: 2px 0;\n}\nion-item ion-badge {\n  margin-right: 6px;\n  padding: 4px 8px;\n  border-radius: 4px;\n}\n.empty-state h3 {\n  font-size: 18px;\n  font-weight: 500;\n  margin: 8px 0;\n  color: var(--ion-color-dark);\n}\n.empty-state p {\n  margin-bottom: 20px;\n}\n/*# sourceMappingURL=search.page.css.map */\n"] }]
  }], () => [{ type: HttpClient }, { type: LoadingService }, { type: ToastController }, { type: Router }, { type: ModalController }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SearchPage, { className: "SearchPage", filePath: "src/app/pages/search/search.page.ts", lineNumber: 30 });
})();
export {
  SearchPage
};
//# sourceMappingURL=search.page-ZB7V5NBX.js.map

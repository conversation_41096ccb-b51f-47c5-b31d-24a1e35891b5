{"version": 3, "sources": ["node_modules/@ionic/core/components/dir.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Returns `true` if the document or host element\n * has a `dir` set to `rtl`. The host value will always\n * take priority over the root document value.\n */\nconst isRTL = hostEl => {\n  if (hostEl) {\n    if (hostEl.dir !== '') {\n      return hostEl.dir.toLowerCase() === 'rtl';\n    }\n  }\n  return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === 'rtl';\n};\nexport { isRTL as i };"], "mappings": ";AAQA,IAAM,QAAQ,YAAU;AACtB,MAAI,QAAQ;AACV,QAAI,OAAO,QAAQ,IAAI;AACrB,aAAO,OAAO,IAAI,YAAY,MAAM;AAAA,IACtC;AAAA,EACF;AACA,UAAQ,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,IAAI,YAAY,OAAO;AAC9F;", "names": [], "x_google_ignoreList": [0]}
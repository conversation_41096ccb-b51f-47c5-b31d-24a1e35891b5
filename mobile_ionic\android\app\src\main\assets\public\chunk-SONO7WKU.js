import{a as Q}from"./chunk-WTHM6RC7.js";import{$a as L,B as w,C as d,Cb as z,D as v,E as e,F as i,G as c,H as x,I as p,J as u,L as o,La as P,M as f,Ma as T,N as b,Na as O,Oa as M,Pa as R,Qa as $,Ra as U,Sa as F,U as I,V as k,Va as D,W as A,_a as B,bb as V,da as y,eb as j,fb as N,q as E,r as S,tb as q,ub as K,x as l,y as g,yb as W,z as C,zb as H}from"./chunk-PBKSAHK2.js";import"./chunk-MBKQLJTW.js";import"./chunk-F3654E4N.js";import"./chunk-FHR3DP7J.js";import"./chunk-A4FGPDGZ.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-AUB5HKS7.js";import"./chunk-RS5W3JWO.js";import"./chunk-LOLLZ3RS.js";import"./chunk-XZOVPSKP.js";import"./chunk-7LH2AG5T.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-SPZFNIGG.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-KY4M3ZA2.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-4EI7TLDT.js";import"./chunk-FED6QSGK.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{g as m}from"./chunk-2R6CW7ES.js";function J(s,h){if(s&1&&(e(0,"p"),o(1),i()),s&2){let t=u().$implicit;v("success-message",t.testResult.success)("error-message",!t.testResult.success),l(),b(" ",t.testResult.message," ")}}function X(s,h){if(s&1){let t=x();e(0,"ion-button",19),p("click",function(r){E(t);let a=u().$implicit;return u().selectEndpoint(a),S(r.stopPropagation())}),c(1,"ion-icon",23),i()}}function Y(s,h){s&1&&(e(0,"ion-badge",24),o(1," Active "),i())}function Z(s,h){if(s&1){let t=x();e(0,"ion-item",14),c(1,"ion-icon",15),e(2,"ion-label")(3,"h2"),o(4),i(),e(5,"p"),o(6),i(),e(7,"p",16),o(8),i(),w(9,J,2,5,"p",17),i(),e(10,"ion-buttons",18)(11,"ion-button",19),p("click",function(r){let a=E(t).$implicit;return u().testEndpoint(a),S(r.stopPropagation())}),c(12,"ion-icon",20),i(),w(13,X,2,0,"ion-button",21)(14,Y,2,0,"ion-badge",22),i()()}if(s&2){let t=h.$implicit,n=u();v("active-endpoint",t.isActive),l(),d("name",n.getStatusIcon(t))("color",n.getStatusColor(t)),l(3),f(t.name),l(2),f(t.description),l(2),f(t.url),l(),d("ngIf",t.testResult),l(4),d("ngIf",!t.isActive),l(),d("ngIf",t.isActive)}}var st=(()=>{class s{constructor(t,n,r,a){this.envSwitcher=t,this.alertController=n,this.loadingController=r,this.router=a,this.endpoints=[],this.currentApiUrl="",this.isLoading=!1}ngOnInit(){this.loadEndpoints(),this.currentApiUrl=this.envSwitcher.getCurrentApiUrl()}loadEndpoints(){this.endpoints=this.envSwitcher.getApiEndpoints()}selectEndpoint(t){return m(this,null,function*(){yield(yield this.alertController.create({header:"Switch API Endpoint",message:`Switch to ${t.name}?

${t.description}`,buttons:[{text:"Cancel",role:"cancel"},{text:"Switch",handler:()=>{this.envSwitcher.setApiUrl(t.url),this.currentApiUrl=t.url,this.loadEndpoints(),this.presentSuccessAlert("API endpoint switched successfully!")}}]})).present()})}testEndpoint(t){return m(this,null,function*(){let n=yield this.loadingController.create({message:`Testing ${t.name}...`,duration:1e4});yield n.present();try{let r=yield this.envSwitcher.testEndpoint(t.url),a=this.endpoints.findIndex(G=>G.url===t.url);a!==-1&&(this.endpoints[a].testResult=r),yield n.dismiss(),yield(yield this.alertController.create({header:"Connection Test",message:`${t.name}

${r.message}`,buttons:["OK"]})).present()}catch{yield n.dismiss(),this.presentErrorAlert("Test failed","Unable to test endpoint")}})}testAllEndpoints(){return m(this,null,function*(){let t=yield this.loadingController.create({message:"Testing all endpoints...",duration:3e4});yield t.present();try{let n=yield this.envSwitcher.testAllEndpoints();this.endpoints=n,yield t.dismiss();let r=n.filter(a=>a.testResult.success);if(r.length>0){let a=`Found ${r.length} working endpoint(s):

`+r.map(_=>`\u2705 ${_.name}`).join(`
`);this.presentSuccessAlert(a)}else this.presentErrorAlert("No Working Endpoints","All endpoints failed connectivity test")}catch{yield t.dismiss(),this.presentErrorAlert("Test Failed","Unable to test endpoints")}})}autoDetect(){return m(this,null,function*(){let t=yield this.loadingController.create({message:"Auto-detecting best endpoint...",duration:3e4});yield t.present();try{let n=yield this.envSwitcher.autoDetectBestEndpoint();yield t.dismiss(),n?(this.currentApiUrl=n.url,this.loadEndpoints(),this.presentSuccessAlert(`Auto-detected and switched to: ${n.name}`)):this.presentErrorAlert("Auto-Detection Failed","No working endpoints found")}catch{yield t.dismiss(),this.presentErrorAlert("Auto-Detection Failed","Unable to detect working endpoint")}})}getStatusIcon(t){return t.testResult?t.testResult.success?"checkmark-circle":"close-circle":"help-circle-outline"}getStatusColor(t){return t.testResult?t.testResult.success?"success":"danger":"medium"}goBack(){this.router.navigate(["/login"])}presentSuccessAlert(t){return m(this,null,function*(){yield(yield this.alertController.create({header:"Success",message:t,buttons:["OK"]})).present()})}presentErrorAlert(t,n){return m(this,null,function*(){yield(yield this.alertController.create({header:t,message:n,buttons:["OK"]})).present()})}static{this.\u0275fac=function(n){return new(n||s)(g(Q),g(W),g(H),g(y))}}static{this.\u0275cmp=C({type:s,selectors:[["app-environment-switcher"]],decls:65,vars:4,consts:[[3,"translucent"],["slot","start"],[3,"click"],["name","chevron-back-outline"],[1,"ion-padding",3,"fullscreen"],[1,"switcher-container"],["expand","block","fill","outline",3,"click"],["name","search-outline","slot","start"],["expand","block",3,"click"],["name","flash-outline","slot","start"],["button","",3,"active-endpoint",4,"ngFor","ngForOf"],["name","wifi-outline","slot","start","color","primary"],["name","home-outline","slot","start","color","warning"],["name","desktop-outline","slot","start","color","medium"],["button",""],["slot","start",3,"name","color"],[1,"endpoint-url"],[3,"success-message","error-message",4,"ngIf"],["slot","end"],["fill","clear",3,"click"],["name","refresh-outline"],["fill","clear",3,"click",4,"ngIf"],["color","primary",4,"ngIf"],["name","checkmark-outline"],["color","primary"]],template:function(n,r){n&1&&(e(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1)(3,"ion-button",2),p("click",function(){return r.goBack()}),c(4,"ion-icon",3),i()(),e(5,"ion-title"),o(6,"API Endpoint Switcher"),i()()(),e(7,"ion-content",4)(8,"div",5)(9,"ion-card")(10,"ion-card-header")(11,"ion-card-title"),o(12,"Current API Endpoint"),i()(),e(13,"ion-card-content")(14,"p")(15,"strong"),o(16,"URL:"),i(),o(17),i(),e(18,"ion-button",6),p("click",function(){return r.autoDetect()}),c(19,"ion-icon",7),o(20," Auto-Detect Best Endpoint "),i()()(),e(21,"ion-card")(22,"ion-card-header")(23,"ion-card-title"),o(24,"Quick Actions"),i()(),e(25,"ion-card-content")(26,"ion-button",8),p("click",function(){return r.testAllEndpoints()}),c(27,"ion-icon",9),o(28," Test All Endpoints "),i()()(),e(29,"ion-card")(30,"ion-card-header")(31,"ion-card-title"),o(32,"Available Endpoints"),i(),e(33,"ion-card-subtitle"),o(34,"Select an endpoint to switch to"),i()(),e(35,"ion-card-content")(36,"ion-list"),w(37,Z,15,10,"ion-item",10),i()()(),e(38,"ion-card")(39,"ion-card-header")(40,"ion-card-title"),o(41,"Troubleshooting Tips"),i()(),e(42,"ion-card-content")(43,"ion-list")(44,"ion-item"),c(45,"ion-icon",11),e(46,"ion-label")(47,"h3"),o(48,"ngrok (Recommended)"),i(),e(49,"p"),o(50,"Most reliable for device testing. Works from anywhere."),i()()(),e(51,"ion-item"),c(52,"ion-icon",12),e(53,"ion-label")(54,"h3"),o(55,"Local IP"),i(),e(56,"p"),o(57,"Requires same WiFi network and firewall configuration."),i()()(),e(58,"ion-item"),c(59,"ion-icon",13),e(60,"ion-label")(61,"h3"),o(62,"Localhost"),i(),e(63,"p"),o(64,"Only works in web browser, not on mobile devices."),i()()()()()()()()),n&2&&(d("translucent",!0),l(7),d("fullscreen",!0),l(10),b(" ",r.currentApiUrl,""),l(20),d("ngForOf",r.endpoints))},dependencies:[z,P,T,O,M,R,$,U,F,D,B,L,V,j,N,q,K,A,I,k],styles:[".switcher-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto}.active-endpoint[_ngcontent-%COMP%]{--background: var(--ion-color-primary-tint);--border-color: var(--ion-color-primary);border-left:4px solid var(--ion-color-primary)}.endpoint-url[_ngcontent-%COMP%]{font-family:monospace;font-size:.8em;color:var(--ion-color-medium);word-break:break-all}.success-message[_ngcontent-%COMP%]{color:var(--ion-color-success);font-weight:500}.error-message[_ngcontent-%COMP%]{color:var(--ion-color-danger);font-weight:500}ion-card[_ngcontent-%COMP%]{margin-bottom:16px}ion-card-title[_ngcontent-%COMP%]{color:var(--ion-color-primary)}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--padding-end: 16px}ion-button[_ngcontent-%COMP%]{--border-radius: 8px}.quick-actions[_ngcontent-%COMP%]{display:flex;gap:8px;margin-bottom:16px}.quick-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{flex:1}"]})}}return s})();export{st as EnvironmentSwitcherPage};

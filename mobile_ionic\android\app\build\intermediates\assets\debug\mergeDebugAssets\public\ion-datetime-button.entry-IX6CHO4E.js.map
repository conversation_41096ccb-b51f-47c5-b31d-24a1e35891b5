{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-datetime-button.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { a as addEventListener, c as componentOnReady } from './helpers-d94bc8ad.js';\nimport { d as printIonError } from './index-cfd9c1f2.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport { s as parseDate, x as getToday, L as getHourCycle, N as getLocalizedDateTime, M as getLocalizedTime } from './data-0d7ea6eb.js';\nconst datetimeButtonIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:13px;padding-inline-start:13px;-webkit-padding-end:13px;padding-inline-end:13px;padding-top:7px;padding-bottom:7px}:host button.ion-activated{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}\";\nconst IonDatetimeButtonIosStyle0 = datetimeButtonIosCss;\nconst datetimeButtonMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}\";\nconst IonDatetimeButtonMdStyle0 = datetimeButtonMdCss;\nconst DatetimeButton = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.datetimeEl = null;\n    this.overlayEl = null;\n    /**\n     * Accepts one or more string values and converts\n     * them to DatetimeParts. This is done so datetime-button\n     * can work with an array internally and not need\n     * to keep checking if the datetime value is `string` or `string[]`.\n     */\n    this.getParsedDateValues = value => {\n      if (value === undefined || value === null) {\n        return [];\n      }\n      if (Array.isArray(value)) {\n        return value;\n      }\n      return [value];\n    };\n    /**\n     * Check the value property on the linked\n     * ion-datetime and then format it according\n     * to the locale specified on ion-datetime.\n     */\n    this.setDateTimeText = () => {\n      var _a, _b, _c, _d, _e;\n      const {\n        datetimeEl,\n        datetimePresentation\n      } = this;\n      if (!datetimeEl) {\n        return;\n      }\n      const {\n        value,\n        locale,\n        formatOptions,\n        hourCycle,\n        preferWheel,\n        multiple,\n        titleSelectedDatesFormatter\n      } = datetimeEl;\n      const parsedValues = this.getParsedDateValues(value);\n      /**\n       * Both ion-datetime and ion-datetime-button default\n       * to today's date and time if no value is set.\n       */\n      const parsedDatetimes = parseDate(parsedValues.length > 0 ? parsedValues : [getToday()]);\n      if (!parsedDatetimes) {\n        return;\n      }\n      /**\n       * If developers incorrectly use multiple=\"true\"\n       * with non \"date\" datetimes, then just select\n       * the first value so the interface does\n       * not appear broken. Datetime will provide a\n       * warning in the console.\n       */\n      const firstParsedDatetime = parsedDatetimes[0];\n      const computedHourCycle = getHourCycle(locale, hourCycle);\n      this.dateText = this.timeText = undefined;\n      switch (datetimePresentation) {\n        case 'date-time':\n        case 'time-date':\n          const dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _a !== void 0 ? _a : {\n            month: 'short',\n            day: 'numeric',\n            year: 'numeric'\n          });\n          const timeText = getLocalizedTime(locale, firstParsedDatetime, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time);\n          if (preferWheel) {\n            this.dateText = `${dateText} ${timeText}`;\n          } else {\n            this.dateText = dateText;\n            this.timeText = timeText;\n          }\n          break;\n        case 'date':\n          if (multiple && parsedValues.length !== 1) {\n            let headerText = `${parsedValues.length} days`; // default/fallback for multiple selection\n            if (titleSelectedDatesFormatter !== undefined) {\n              try {\n                headerText = titleSelectedDatesFormatter(parsedValues);\n              } catch (e) {\n                printIonError('[ion-datetime-button] - Exception in provided `titleSelectedDatesFormatter`:', e);\n              }\n            }\n            this.dateText = headerText;\n          } else {\n            this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_b = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _b !== void 0 ? _b : {\n              month: 'short',\n              day: 'numeric',\n              year: 'numeric'\n            });\n          }\n          break;\n        case 'time':\n          this.timeText = getLocalizedTime(locale, firstParsedDatetime, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time);\n          break;\n        case 'month-year':\n          this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_c = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _c !== void 0 ? _c : {\n            month: 'long',\n            year: 'numeric'\n          });\n          break;\n        case 'month':\n          this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_d = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) !== null && _d !== void 0 ? _d : {\n            month: 'long'\n          });\n          break;\n        case 'year':\n          this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_e = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) !== null && _e !== void 0 ? _e : {\n            year: 'numeric'\n          });\n          break;\n      }\n    };\n    /**\n     * Waits for the ion-datetime to re-render.\n     * This is needed in order to correctly position\n     * a popover relative to the trigger element.\n     */\n    this.waitForDatetimeChanges = async () => {\n      const {\n        datetimeEl\n      } = this;\n      if (!datetimeEl) {\n        return Promise.resolve();\n      }\n      return new Promise(resolve => {\n        addEventListener(datetimeEl, 'ionRender', resolve, {\n          once: true\n        });\n      });\n    };\n    this.handleDateClick = async ev => {\n      const {\n        datetimeEl,\n        datetimePresentation\n      } = this;\n      if (!datetimeEl) {\n        return;\n      }\n      let needsPresentationChange = false;\n      /**\n       * When clicking the date button,\n       * we need to make sure that only a date\n       * picker is displayed. For presentation styles\n       * that display content other than a date picker,\n       * we need to update the presentation style.\n       */\n      switch (datetimePresentation) {\n        case 'date-time':\n        case 'time-date':\n          const needsChange = datetimeEl.presentation !== 'date';\n          /**\n           * The date+time wheel picker\n           * shows date and time together,\n           * so do not adjust the presentation\n           * in that case.\n           */\n          if (!datetimeEl.preferWheel && needsChange) {\n            datetimeEl.presentation = 'date';\n            needsPresentationChange = true;\n          }\n          break;\n      }\n      /**\n       * Track which button was clicked\n       * so that it can have the correct\n       * activated styles applied when\n       * the modal/popover containing\n       * the datetime is opened.\n       */\n      this.selectedButton = 'date';\n      this.presentOverlay(ev, needsPresentationChange, this.dateTargetEl);\n    };\n    this.handleTimeClick = ev => {\n      const {\n        datetimeEl,\n        datetimePresentation\n      } = this;\n      if (!datetimeEl) {\n        return;\n      }\n      let needsPresentationChange = false;\n      /**\n       * When clicking the time button,\n       * we need to make sure that only a time\n       * picker is displayed. For presentation styles\n       * that display content other than a time picker,\n       * we need to update the presentation style.\n       */\n      switch (datetimePresentation) {\n        case 'date-time':\n        case 'time-date':\n          const needsChange = datetimeEl.presentation !== 'time';\n          if (needsChange) {\n            datetimeEl.presentation = 'time';\n            needsPresentationChange = true;\n          }\n          break;\n      }\n      /**\n       * Track which button was clicked\n       * so that it can have the correct\n       * activated styles applied when\n       * the modal/popover containing\n       * the datetime is opened.\n       */\n      this.selectedButton = 'time';\n      this.presentOverlay(ev, needsPresentationChange, this.timeTargetEl);\n    };\n    /**\n     * If the datetime is presented in an\n     * overlay, the datetime and overlay\n     * should be appropriately sized.\n     * These classes provide default sizing values\n     * that developers can customize.\n     * The goal is to provide an overlay that is\n     * reasonably sized with a datetime that\n     * fills the entire container.\n     */\n    this.presentOverlay = async (ev, needsPresentationChange, triggerEl) => {\n      const {\n        overlayEl\n      } = this;\n      if (!overlayEl) {\n        return;\n      }\n      if (overlayEl.tagName === 'ION-POPOVER') {\n        /**\n         * When the presentation on datetime changes,\n         * we need to wait for the component to re-render\n         * otherwise the computed width/height of the\n         * popover content will be wrong, causing\n         * the popover to not align with the trigger element.\n         */\n        if (needsPresentationChange) {\n          await this.waitForDatetimeChanges();\n        }\n        /**\n         * We pass the trigger button element\n         * so that the popover aligns with the individual\n         * button that was clicked, not the component container.\n         */\n        overlayEl.present(Object.assign(Object.assign({}, ev), {\n          detail: {\n            ionShadowTarget: triggerEl\n          }\n        }));\n      } else {\n        overlayEl.present();\n      }\n    };\n    this.datetimePresentation = 'date-time';\n    this.dateText = undefined;\n    this.timeText = undefined;\n    this.datetimeActive = false;\n    this.selectedButton = undefined;\n    this.color = 'primary';\n    this.disabled = false;\n    this.datetime = undefined;\n  }\n  async componentWillLoad() {\n    const {\n      datetime\n    } = this;\n    if (!datetime) {\n      printIonError('[ion-datetime-button] - An ID associated with an ion-datetime instance is required to function properly.', this.el);\n      return;\n    }\n    const datetimeEl = this.datetimeEl = document.getElementById(datetime);\n    if (!datetimeEl) {\n      printIonError(`[ion-datetime-button] - No ion-datetime instance found for ID '${datetime}'.`, this.el);\n      return;\n    }\n    /**\n     * The element reference must be an ion-datetime. Print an error\n     * if a non-datetime element was provided.\n     */\n    if (datetimeEl.tagName !== 'ION-DATETIME') {\n      printIonError(`[ion-datetime-button] - Expected an ion-datetime instance for ID '${datetime}' but received '${datetimeEl.tagName.toLowerCase()}' instead.`, datetimeEl);\n      return;\n    }\n    /**\n     * Since the datetime can be used in any context (overlays, accordion, etc)\n     * we track when it is visible to determine when it is active.\n     * This informs which button is highlighted as well as the\n     * aria-expanded state.\n     */\n    const io = new IntersectionObserver(entries => {\n      const ev = entries[0];\n      this.datetimeActive = ev.isIntersecting;\n    }, {\n      threshold: 0.01\n    });\n    io.observe(datetimeEl);\n    /**\n     * Get a reference to any modal/popover\n     * the datetime is being used in so we can\n     * correctly size it when it is presented.\n     */\n    const overlayEl = this.overlayEl = datetimeEl.closest('ion-modal, ion-popover');\n    /**\n     * The .ion-datetime-button-overlay class contains\n     * styles that allow any modal/popover to be\n     * sized according to the dimensions of the datetime.\n     * If developers want a smaller/larger overlay all they need\n     * to do is change the width/height of the datetime.\n     * Additionally, this lets us avoid having to set\n     * explicit widths on each variant of datetime.\n     */\n    if (overlayEl) {\n      overlayEl.classList.add('ion-datetime-button-overlay');\n    }\n    componentOnReady(datetimeEl, () => {\n      const datetimePresentation = this.datetimePresentation = datetimeEl.presentation || 'date-time';\n      /**\n       * Set the initial display\n       * in the rendered buttons.\n       *\n       * From there, we need to listen\n       * for ionChange to be emitted\n       * from datetime so we know when\n       * to re-render the displayed\n       * text in the buttons.\n       */\n      this.setDateTimeText();\n      addEventListener(datetimeEl, 'ionValueChange', this.setDateTimeText);\n      /**\n       * Configure the initial selected button\n       * in the event that the datetime is displayed\n       * without clicking one of the datetime buttons.\n       * For example, a datetime could be expanded\n       * in an accordion. In this case users only\n       * need to click the accordion header to show\n       * the datetime.\n       */\n      switch (datetimePresentation) {\n        case 'date-time':\n        case 'date':\n        case 'month-year':\n        case 'month':\n        case 'year':\n          this.selectedButton = 'date';\n          break;\n        case 'time-date':\n        case 'time':\n          this.selectedButton = 'time';\n          break;\n      }\n    });\n  }\n  render() {\n    const {\n      color,\n      dateText,\n      timeText,\n      selectedButton,\n      datetimeActive,\n      disabled\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '11d037e6ab061e5116842970760b04850b42f2c7',\n      class: createColorClasses(color, {\n        [mode]: true,\n        [`${selectedButton}-active`]: datetimeActive,\n        ['datetime-button-disabled']: disabled\n      })\n    }, dateText && h(\"button\", {\n      key: '08ecb62da0fcbf7466a1f2403276712a3ff17fbc',\n      class: \"ion-activatable\",\n      id: \"date-button\",\n      \"aria-expanded\": datetimeActive ? 'true' : 'false',\n      onClick: this.handleDateClick,\n      disabled: disabled,\n      part: \"native\",\n      ref: el => this.dateTargetEl = el\n    }, h(\"slot\", {\n      key: '1c04853d4d23c0f1a594602bde44511c98355644',\n      name: \"date-target\"\n    }, dateText), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '5fc566cd4bc885bcf983ce99e3dc65d7f485bf9b'\n    })), timeText && h(\"button\", {\n      key: 'c9c5c34ac338badf8659da22bea5829d62c51169',\n      class: \"ion-activatable\",\n      id: \"time-button\",\n      \"aria-expanded\": datetimeActive ? 'true' : 'false',\n      onClick: this.handleTimeClick,\n      disabled: disabled,\n      part: \"native\",\n      ref: el => this.timeTargetEl = el\n    }, h(\"slot\", {\n      key: '147a9d2069dbf737f6fc64787823d6d5af5aa653',\n      name: \"time-target\"\n    }, timeText), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '70a5e25b75ed90ac6bba003468435f67aa9d8f0a'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nDatetimeButton.style = {\n  ios: IonDatetimeButtonIosStyle0,\n  md: IonDatetimeButtonMdStyle0\n};\nexport { DatetimeButton as ion_datetime_button };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,uBAAuB;AAC7B,IAAM,6BAA6B;AACnC,IAAM,sBAAsB;AAC5B,IAAM,4BAA4B;AAClC,IAAM,iBAAiB,MAAM;AAAA,EAC3B,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,aAAa;AAClB,SAAK,YAAY;AAOjB,SAAK,sBAAsB,WAAS;AAClC,UAAI,UAAU,UAAa,UAAU,MAAM;AACzC,eAAO,CAAC;AAAA,MACV;AACA,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,eAAO;AAAA,MACT;AACA,aAAO,CAAC,KAAK;AAAA,IACf;AAMA,SAAK,kBAAkB,MAAM;AAC3B,UAAI,IAAI,IAAI,IAAI,IAAI;AACpB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,CAAC,YAAY;AACf;AAAA,MACF;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,eAAe,KAAK,oBAAoB,KAAK;AAKnD,YAAM,kBAAkB,UAAU,aAAa,SAAS,IAAI,eAAe,CAAC,SAAS,CAAC,CAAC;AACvF,UAAI,CAAC,iBAAiB;AACpB;AAAA,MACF;AAQA,YAAM,sBAAsB,gBAAgB,CAAC;AAC7C,YAAM,oBAAoB,aAAa,QAAQ,SAAS;AACxD,WAAK,WAAW,KAAK,WAAW;AAChC,cAAQ,sBAAsB;AAAA,QAC5B,KAAK;AAAA,QACL,KAAK;AACH,gBAAM,WAAW,qBAAqB,QAAQ,sBAAsB,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,KAAK;AAAA,YAC1L,OAAO;AAAA,YACP,KAAK;AAAA,YACL,MAAM;AAAA,UACR,CAAC;AACD,gBAAM,WAAW,iBAAiB,QAAQ,qBAAqB,mBAAmB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,IAAI;AAClK,cAAI,aAAa;AACf,iBAAK,WAAW,GAAG,QAAQ,IAAI,QAAQ;AAAA,UACzC,OAAO;AACL,iBAAK,WAAW;AAChB,iBAAK,WAAW;AAAA,UAClB;AACA;AAAA,QACF,KAAK;AACH,cAAI,YAAY,aAAa,WAAW,GAAG;AACzC,gBAAI,aAAa,GAAG,aAAa,MAAM;AACvC,gBAAI,gCAAgC,QAAW;AAC7C,kBAAI;AACF,6BAAa,4BAA4B,YAAY;AAAA,cACvD,SAAS,GAAG;AACV,8BAAc,gFAAgF,CAAC;AAAA,cACjG;AAAA,YACF;AACA,iBAAK,WAAW;AAAA,UAClB,OAAO;AACL,iBAAK,WAAW,qBAAqB,QAAQ,sBAAsB,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,KAAK;AAAA,cACzL,OAAO;AAAA,cACP,KAAK;AAAA,cACL,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AACA;AAAA,QACF,KAAK;AACH,eAAK,WAAW,iBAAiB,QAAQ,qBAAqB,mBAAmB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,IAAI;AACjK;AAAA,QACF,KAAK;AACH,eAAK,WAAW,qBAAqB,QAAQ,sBAAsB,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,KAAK;AAAA,YACzL,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD;AAAA,QACF,KAAK;AACH,eAAK,WAAW,qBAAqB,QAAQ,sBAAsB,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,KAAK;AAAA,YACzL,OAAO;AAAA,UACT,CAAC;AACD;AAAA,QACF,KAAK;AACH,eAAK,WAAW,qBAAqB,QAAQ,sBAAsB,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,QAAQ,OAAO,SAAS,KAAK;AAAA,YACzL,MAAM;AAAA,UACR,CAAC;AACD;AAAA,MACJ;AAAA,IACF;AAMA,SAAK,yBAAyB,MAAY;AACxC,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,CAAC,YAAY;AACf,eAAO,QAAQ,QAAQ;AAAA,MACzB;AACA,aAAO,IAAI,QAAQ,aAAW;AAC5B,yBAAiB,YAAY,aAAa,SAAS;AAAA,UACjD,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,SAAK,kBAAkB,CAAM,OAAM;AACjC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,CAAC,YAAY;AACf;AAAA,MACF;AACA,UAAI,0BAA0B;AAQ9B,cAAQ,sBAAsB;AAAA,QAC5B,KAAK;AAAA,QACL,KAAK;AACH,gBAAM,cAAc,WAAW,iBAAiB;AAOhD,cAAI,CAAC,WAAW,eAAe,aAAa;AAC1C,uBAAW,eAAe;AAC1B,sCAA0B;AAAA,UAC5B;AACA;AAAA,MACJ;AAQA,WAAK,iBAAiB;AACtB,WAAK,eAAe,IAAI,yBAAyB,KAAK,YAAY;AAAA,IACpE;AACA,SAAK,kBAAkB,QAAM;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,CAAC,YAAY;AACf;AAAA,MACF;AACA,UAAI,0BAA0B;AAQ9B,cAAQ,sBAAsB;AAAA,QAC5B,KAAK;AAAA,QACL,KAAK;AACH,gBAAM,cAAc,WAAW,iBAAiB;AAChD,cAAI,aAAa;AACf,uBAAW,eAAe;AAC1B,sCAA0B;AAAA,UAC5B;AACA;AAAA,MACJ;AAQA,WAAK,iBAAiB;AACtB,WAAK,eAAe,IAAI,yBAAyB,KAAK,YAAY;AAAA,IACpE;AAWA,SAAK,iBAAiB,CAAO,IAAI,yBAAyB,cAAc;AACtE,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AACA,UAAI,UAAU,YAAY,eAAe;AAQvC,YAAI,yBAAyB;AAC3B,gBAAM,KAAK,uBAAuB;AAAA,QACpC;AAMA,kBAAU,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,EAAE,GAAG;AAAA,UACrD,QAAQ;AAAA,YACN,iBAAiB;AAAA,UACnB;AAAA,QACF,CAAC,CAAC;AAAA,MACJ,OAAO;AACL,kBAAU,QAAQ;AAAA,MACpB;AAAA,IACF;AACA,SAAK,uBAAuB;AAC5B,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EAClB;AAAA,EACM,oBAAoB;AAAA;AACxB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,CAAC,UAAU;AACb,sBAAc,4GAA4G,KAAK,EAAE;AACjI;AAAA,MACF;AACA,YAAM,aAAa,KAAK,aAAa,SAAS,eAAe,QAAQ;AACrE,UAAI,CAAC,YAAY;AACf,sBAAc,kEAAkE,QAAQ,MAAM,KAAK,EAAE;AACrG;AAAA,MACF;AAKA,UAAI,WAAW,YAAY,gBAAgB;AACzC,sBAAc,qEAAqE,QAAQ,mBAAmB,WAAW,QAAQ,YAAY,CAAC,cAAc,UAAU;AACtK;AAAA,MACF;AAOA,YAAM,KAAK,IAAI,qBAAqB,aAAW;AAC7C,cAAM,KAAK,QAAQ,CAAC;AACpB,aAAK,iBAAiB,GAAG;AAAA,MAC3B,GAAG;AAAA,QACD,WAAW;AAAA,MACb,CAAC;AACD,SAAG,QAAQ,UAAU;AAMrB,YAAM,YAAY,KAAK,YAAY,WAAW,QAAQ,wBAAwB;AAU9E,UAAI,WAAW;AACb,kBAAU,UAAU,IAAI,6BAA6B;AAAA,MACvD;AACA,uBAAiB,YAAY,MAAM;AACjC,cAAM,uBAAuB,KAAK,uBAAuB,WAAW,gBAAgB;AAWpF,aAAK,gBAAgB;AACrB,yBAAiB,YAAY,kBAAkB,KAAK,eAAe;AAUnE,gBAAQ,sBAAsB;AAAA,UAC5B,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,iBAAK,iBAAiB;AACtB;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AACH,iBAAK,iBAAiB;AACtB;AAAA,QACJ;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,mBAAmB,OAAO;AAAA,QAC/B,CAAC,IAAI,GAAG;AAAA,QACR,CAAC,GAAG,cAAc,SAAS,GAAG;AAAA,QAC9B,CAAC,0BAA0B,GAAG;AAAA,MAChC,CAAC;AAAA,IACH,GAAG,YAAY,EAAE,UAAU;AAAA,MACzB,KAAK;AAAA,MACL,OAAO;AAAA,MACP,IAAI;AAAA,MACJ,iBAAiB,iBAAiB,SAAS;AAAA,MAC3C,SAAS,KAAK;AAAA,MACd;AAAA,MACA,MAAM;AAAA,MACN,KAAK,QAAM,KAAK,eAAe;AAAA,IACjC,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG,QAAQ,GAAG,SAAS,QAAQ,EAAE,qBAAqB;AAAA,MACpD,KAAK;AAAA,IACP,CAAC,CAAC,GAAG,YAAY,EAAE,UAAU;AAAA,MAC3B,KAAK;AAAA,MACL,OAAO;AAAA,MACP,IAAI;AAAA,MACJ,iBAAiB,iBAAiB,SAAS;AAAA,MAC3C,SAAS,KAAK;AAAA,MACd;AAAA,MACA,MAAM;AAAA,MACN,KAAK,QAAM,KAAK,eAAe;AAAA,IACjC,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG,QAAQ,GAAG,SAAS,QAAQ,EAAE,qBAAqB;AAAA,MACpD,KAAK;AAAA,IACP,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,eAAe,QAAQ;AAAA,EACrB,KAAK;AAAA,EACL,IAAI;AACN;", "names": [], "x_google_ignoreList": [0]}
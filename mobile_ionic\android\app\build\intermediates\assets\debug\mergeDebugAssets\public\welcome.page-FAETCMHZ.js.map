{"version": 3, "sources": ["src/app/pages/welcome/welcome.page.ts", "src/app/pages/welcome/welcome.page.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-welcome',\r\n  templateUrl: './welcome.page.html',\r\n  styleUrls: ['./welcome.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule]\r\n})\r\nexport class WelcomePage {\r\n  constructor(private router: Router) {}\r\n\r\n  getStarted() {\r\n    this.router.navigate(['/data']);\r\n  }\r\n} ", "<ion-content class=\"ion-padding welcome-bg\">\r\n  <div class=\"welcome-wrapper\">\r\n    <img src=\"assets/ALERTO.png\" alt=\"App Logo\" class=\"welcome-logo\" />\r\n    <h4 class=\"welcome-title\">Welcome To <strong style=\"color: #ff0000; font-weight: 700; font-size: 35px;\">Alerto!</strong></h4>\r\n    <ion-button expand=\"block\" class=\"welcome-btn\" (click)=\"getStarted()\">Get Started</ion-button>\r\n    \r\n  </div>\r\n</ion-content>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaM,IAAO,cAAP,MAAO,aAAW;EACtB,YAAoB,QAAc;AAAd,SAAA,SAAA;EAAiB;EAErC,aAAU;AACR,SAAK,OAAO,SAAS,CAAC,OAAO,CAAC;EAChC;;;uCALW,cAAW,4BAAA,MAAA,CAAA;IAAA;EAAA;;yEAAX,cAAW,WAAA,CAAA,CAAA,aAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,eAAA,YAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,OAAA,qBAAA,OAAA,YAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,SAAA,WAAA,eAAA,OAAA,aAAA,MAAA,GAAA,CAAA,UAAA,SAAA,GAAA,eAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,qBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACbxB,QAAA,yBAAA,GAAA,eAAA,CAAA,EAA4C,GAAA,OAAA,CAAA;AAExC,QAAA,oBAAA,GAAA,OAAA,CAAA;AACA,QAAA,yBAAA,GAAA,MAAA,CAAA;AAA0B,QAAA,iBAAA,GAAA,aAAA;AAAW,QAAA,yBAAA,GAAA,UAAA,CAAA;AAAmE,QAAA,iBAAA,GAAA,SAAA;AAAO,QAAA,uBAAA,EAAS;AACxH,QAAA,yBAAA,GAAA,cAAA,CAAA;AAA+C,QAAA,qBAAA,SAAA,SAAA,mDAAA;AAAA,iBAAS,IAAA,WAAA;QAAY,CAAA;AAAE,QAAA,iBAAA,GAAA,aAAA;AAAW,QAAA,uBAAA,EAAa,EAE1F;;sBDKI,aAAW,WAAA,YAAE,cAAc,WAAW,GAAA,QAAA,CAAA,wyBAAA,EAAA,CAAA;EAAA;;;sEAErC,aAAW,CAAA;UAPvB;uBACW,eAAa,YAGX,MAAI,SACP,CAAC,aAAa,cAAc,WAAW,GAAC,UAAA,+aAAA,QAAA,CAAA,2rBAAA,EAAA,CAAA;;;;6EAEtC,aAAW,EAAA,WAAA,eAAA,UAAA,yCAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
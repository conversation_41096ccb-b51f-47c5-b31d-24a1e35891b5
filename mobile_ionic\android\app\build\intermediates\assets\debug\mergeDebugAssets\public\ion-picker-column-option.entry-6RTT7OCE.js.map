{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-picker-column-option.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { h as inheritAttributes } from './helpers-d94bc8ad.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport './index-cfd9c1f2.js';\nconst pickerColumnOptionIosCss = \"button{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden}:host(.option-disabled){opacity:0.4}:host(.option-disabled) button{cursor:default}\";\nconst IonPickerColumnOptionIosStyle0 = pickerColumnOptionIosCss;\nconst pickerColumnOptionMdCss = \"button{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden}:host(.option-disabled){opacity:0.4}:host(.option-disabled) button{cursor:default}:host(.option-active){color:var(--ion-color-base)}\";\nconst IonPickerColumnOptionMdStyle0 = pickerColumnOptionMdCss;\nconst PickerColumnOption = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    /**\n     * We keep track of the parent picker column\n     * so we can update the value of it when\n     * clicking an enable option.\n     */\n    this.pickerColumn = null;\n    this.ariaLabel = null;\n    this.disabled = false;\n    this.value = undefined;\n    this.color = 'primary';\n  }\n  /**\n   * The aria-label of the option has changed after the\n   * first render and needs to be updated within the component.\n   *\n   * @param ariaLbl The new aria-label value.\n   */\n  onAriaLabelChange(ariaLbl) {\n    this.ariaLabel = ariaLbl;\n  }\n  componentWillLoad() {\n    const inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n    /**\n     * The initial value of `aria-label` needs to be set for\n     * the first render.\n          */\n    this.ariaLabel = inheritedAttributes['aria-label'] || null;\n  }\n  connectedCallback() {\n    this.pickerColumn = this.el.closest('ion-picker-column');\n  }\n  disconnectedCallback() {\n    this.pickerColumn = null;\n  }\n  /**\n   * The column options can load at any time\n   * so the options needs to tell the\n   * parent picker column when it is loaded\n   * so the picker column can ensure it is\n   * centered in the view.\n   *\n   * We intentionally run this for every\n   * option. If we only ran this from\n   * the selected option then if the newly\n   * loaded options were not selected then\n   * scrollActiveItemIntoView would not be called.\n   */\n  componentDidLoad() {\n    const {\n      pickerColumn\n    } = this;\n    if (pickerColumn !== null) {\n      pickerColumn.scrollActiveItemIntoView();\n    }\n  }\n  /**\n   * When an option is clicked, update the\n   * parent picker column value. This\n   * component will handle centering the option\n   * in the column view.\n   */\n  onClick() {\n    const {\n      pickerColumn\n    } = this;\n    if (pickerColumn !== null) {\n      pickerColumn.setValue(this.value);\n    }\n  }\n  render() {\n    const {\n      color,\n      disabled,\n      ariaLabel\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'c1353e99c2aa19c0e3ddbe433557ed18e72e1c66',\n      class: createColorClasses(color, {\n        [mode]: true,\n        ['option-disabled']: disabled\n      })\n    }, h(\"button\", {\n      key: 'b4ee62ecf7458a07a56e8aa494485766a87a3fcb',\n      tabindex: \"-1\",\n      \"aria-label\": ariaLabel,\n      disabled: disabled,\n      onClick: () => this.onClick()\n    }, h(\"slot\", {\n      key: '9ab1e4700c27103b676670a4b3521c183c6ab83d'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"aria-label\": [\"onAriaLabelChange\"]\n    };\n  }\n};\nPickerColumnOption.style = {\n  ios: IonPickerColumnOptionIosStyle0,\n  md: IonPickerColumnOptionMdStyle0\n};\nexport { PickerColumnOption as ion_picker_column_option };"], "mappings": ";;;;;;;;;;;;;;;;;;;AAQA,IAAM,2BAA2B;AACjC,IAAM,iCAAiC;AACvC,IAAM,0BAA0B;AAChC,IAAM,gCAAgC;AACtC,IAAM,qBAAqB,MAAM;AAAA,EAC/B,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAM9B,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,SAAS;AACzB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,oBAAoB;AAClB,UAAM,sBAAsB,kBAAkB,KAAK,IAAI,CAAC,YAAY,CAAC;AAKrE,SAAK,YAAY,oBAAoB,YAAY,KAAK;AAAA,EACxD;AAAA,EACA,oBAAoB;AAClB,SAAK,eAAe,KAAK,GAAG,QAAQ,mBAAmB;AAAA,EACzD;AAAA,EACA,uBAAuB;AACrB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,mBAAmB;AACjB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,iBAAiB,MAAM;AACzB,mBAAa,yBAAyB;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,iBAAiB,MAAM;AACzB,mBAAa,SAAS,KAAK,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,mBAAmB,OAAO;AAAA,QAC/B,CAAC,IAAI,GAAG;AAAA,QACR,CAAC,iBAAiB,GAAG;AAAA,MACvB,CAAC;AAAA,IACH,GAAG,EAAE,UAAU;AAAA,MACb,KAAK;AAAA,MACL,UAAU;AAAA,MACV,cAAc;AAAA,MACd;AAAA,MACA,SAAS,MAAM,KAAK,QAAQ;AAAA,IAC9B,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,cAAc,CAAC,mBAAmB;AAAA,IACpC;AAAA,EACF;AACF;AACA,mBAAmB,QAAQ;AAAA,EACzB,KAAK;AAAA,EACL,IAAI;AACN;", "names": [], "x_google_ignoreList": [0]}
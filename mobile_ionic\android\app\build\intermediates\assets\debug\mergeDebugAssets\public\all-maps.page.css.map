{"version": 3, "sources": ["src/app/pages/disaster-maps/all-maps.page.scss"], "sourcesContent": ["#all-maps {\r\n  height: 100%;\r\n  width: 100%;\r\n  z-index: 1;\r\n}\r\n\r\n.floating-info {\r\n  position: absolute;\r\n  top: 20px;\r\n  right: 20px;\r\n  z-index: 1000;\r\n  max-width: 280px;\r\n\r\n  ion-card {\r\n    margin: 0;\r\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\r\n    border-radius: 12px;\r\n    background: rgba(255, 255, 255, 0.95);\r\n    backdrop-filter: blur(10px);\r\n  }\r\n\r\n  ion-card-content {\r\n    padding: 12px;\r\n  }\r\n\r\n  .info-header {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-weight: 600;\r\n    color: var(--ion-color-secondary);\r\n    margin-bottom: 8px;\r\n\r\n    ion-icon {\r\n      font-size: 18px;\r\n    }\r\n  }\r\n\r\n  .disaster-counts {\r\n    margin: 8px 0;\r\n\r\n    .count-row {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 6px;\r\n      margin: 4px 0;\r\n      font-size: 13px;\r\n\r\n      .disaster-icon {\r\n        font-size: 14px;\r\n        width: 16px;\r\n        text-align: center;\r\n      }\r\n\r\n      .disaster-label {\r\n        flex: 1;\r\n        color: var(--ion-color-dark);\r\n      }\r\n\r\n      .disaster-count {\r\n        font-weight: 600;\r\n        color: var(--ion-color-secondary);\r\n        min-width: 20px;\r\n        text-align: right;\r\n      }\r\n    }\r\n  }\r\n\r\n  .info-text {\r\n    font-size: 11px;\r\n    color: var(--ion-color-medium);\r\n    line-height: 1.3;\r\n    margin-top: 8px;\r\n    padding-top: 8px;\r\n    border-top: 1px solid var(--ion-color-light);\r\n  }\r\n}\r\n\r\n// All maps styling\r\nion-toolbar {\r\n  --background: var(--ion-color-secondary);\r\n  --color: white;\r\n}\r\n\r\nion-title {\r\n  font-weight: 600;\r\n}\r\n\r\n// Transportation Controls\r\n.transport-controls {\r\n  position: absolute;\r\n  bottom: 120px;\r\n  left: 20px;\r\n  z-index: 1000;\r\n  max-width: 280px;\r\n\r\n  ion-card {\r\n    margin: 0;\r\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\r\n    border-radius: 12px;\r\n    background: rgba(255, 255, 255, 0.95);\r\n    backdrop-filter: blur(10px);\r\n  }\r\n\r\n  ion-card-content {\r\n    padding: 12px;\r\n  }\r\n\r\n  .transport-header {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-weight: 600;\r\n    color: var(--ion-color-primary);\r\n    margin-bottom: 8px;\r\n\r\n    ion-icon {\r\n      font-size: 18px;\r\n    }\r\n  }\r\n\r\n  ion-segment {\r\n    --background: rgba(var(--ion-color-light-rgb), 0.3);\r\n    border-radius: 8px;\r\n  }\r\n\r\n  ion-segment-button {\r\n    --color: var(--ion-color-medium);\r\n    --color-checked: var(--ion-color-primary);\r\n    --indicator-color: var(--ion-color-primary);\r\n    min-height: 40px;\r\n\r\n    ion-icon {\r\n      font-size: 16px;\r\n      margin-bottom: 2px;\r\n    }\r\n\r\n    ion-label {\r\n      font-size: 12px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n// Route Information\r\n.route-info {\r\n  position: absolute;\r\n  bottom: 20px;\r\n  left: 20px;\r\n  z-index: 1000;\r\n  max-width: 200px;\r\n\r\n  ion-card {\r\n    margin: 0;\r\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\r\n    border-radius: 12px;\r\n    background: rgba(255, 255, 255, 0.95);\r\n    backdrop-filter: blur(10px);\r\n  }\r\n\r\n  ion-card-content {\r\n    padding: 12px;\r\n  }\r\n\r\n  .route-header {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-weight: 600;\r\n    color: var(--ion-color-success);\r\n    margin-bottom: 8px;\r\n\r\n    ion-icon {\r\n      font-size: 18px;\r\n    }\r\n  }\r\n\r\n  .route-details {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n  }\r\n\r\n  .route-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-size: 14px;\r\n    color: var(--ion-color-dark);\r\n\r\n    ion-icon {\r\n      font-size: 16px;\r\n      color: var(--ion-color-primary);\r\n    }\r\n  }\r\n}\r\n\r\n// FAB Label\r\n.fab-label {\r\n  position: absolute;\r\n  right: 60px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background: rgba(0, 0, 0, 0.7);\r\n  color: white;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  pointer-events: none;\r\n}\r\n\r\n// Pulsing Marker Animation\r\n:global(.pulsing-marker) {\r\n  .pulse-container {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n\r\n  .pulse {\r\n    position: absolute;\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    opacity: 0.6;\r\n    animation: pulse 2s infinite;\r\n    z-index: 1;\r\n  }\r\n\r\n  .marker-icon {\r\n    width: 40px;\r\n    height: 40px;\r\n    z-index: 2;\r\n    position: relative;\r\n  }\r\n\r\n  .marker-label {\r\n    position: absolute;\r\n    top: -8px;\r\n    right: -8px;\r\n    background: var(--ion-color-primary);\r\n    color: white;\r\n    border-radius: 50%;\r\n    width: 20px;\r\n    height: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 12px;\r\n    font-weight: bold;\r\n    z-index: 3;\r\n    border: 2px solid white;\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(0.8);\r\n    opacity: 0.8;\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n    opacity: 0.4;\r\n  }\r\n  100% {\r\n    transform: scale(0.8);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n// Popup styling for all centers\r\n:global(.leaflet-popup-content) {\r\n  .evacuation-popup {\r\n    text-align: center;\r\n    min-width: 200px;\r\n\r\n    h3 {\r\n      margin: 0 0 8px 0;\r\n      color: var(--ion-color-secondary);\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    h4 {\r\n      margin: 4px 0;\r\n      color: var(--ion-color-dark);\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n    }\r\n\r\n    p {\r\n      margin: 4px 0;\r\n      font-size: 14px;\r\n\r\n      strong {\r\n        color: var(--ion-color-dark);\r\n      }\r\n    }\r\n\r\n    &.nearest-popup {\r\n      h3 {\r\n        color: var(--ion-color-success);\r\n        font-size: 18px;\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,UAAA;AACA,SAAA;AACA,WAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,WAAA;AACA,aAAA;;AAEA,CAPF,cAOE;AACE,UAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,2BAAA,KAAA;AAAA,mBAAA,KAAA;;AAGF,CAfF,cAeE;AACE,WAAA;;AAGF,CAnBF,cAmBE,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,iBAAA;;AAEA,CA3BJ,cA2BI,CARF,YAQE;AACE,aAAA;;AAIJ,CAhCF,cAgCE,CAAA;AACE,UAAA,IAAA;;AAEA,CAnCJ,cAmCI,CAHF,gBAGE,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA,IAAA;AACA,aAAA;;AAEA,CA1CN,cA0CM,CAVJ,gBAUI,CAPF,UAOE,CAAA;AACE,aAAA;AACA,SAAA;AACA,cAAA;;AAGF,CAhDN,cAgDM,CAhBJ,gBAgBI,CAbF,UAaE,CAAA;AACE,QAAA;AACA,SAAA,IAAA;;AAGF,CArDN,cAqDM,CArBJ,gBAqBI,CAlBF,UAkBE,CAAA;AACE,eAAA;AACA,SAAA,IAAA;AACA,aAAA;AACA,cAAA;;AAKN,CA9DF,cA8DE,CAAA;AACE,aAAA;AACA,SAAA,IAAA;AACA,eAAA;AACA,cAAA;AACA,eAAA;AACA,cAAA,IAAA,MAAA,IAAA;;AAKJ;AACE,gBAAA,IAAA;AACA,WAAA;;AAGF;AACE,eAAA;;AAIF,CAAA;AACE,YAAA;AACA,UAAA;AACA,QAAA;AACA,WAAA;AACA,aAAA;;AAEA,CAPF,mBAOE;AACE,UAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,2BAAA,KAAA;AAAA,mBAAA,KAAA;;AAGF,CAfF,mBAeE;AACE,WAAA;;AAGF,CAnBF,mBAmBE,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,iBAAA;;AAEA,CA3BJ,mBA2BI,CARF,iBAQE;AACE,aAAA;;AAIJ,CAhCF,mBAgCE;AACE,gBAAA,KAAA,IAAA,sBAAA,EAAA;AACA,iBAAA;;AAGF,CArCF,mBAqCE;AACE,WAAA,IAAA;AACA,mBAAA,IAAA;AACA,qBAAA,IAAA;AACA,cAAA;;AAEA,CA3CJ,mBA2CI,mBAAA;AACE,aAAA;AACA,iBAAA;;AAGF,CAhDJ,mBAgDI,mBAAA;AACE,aAAA;AACA,eAAA;;AAMN,CAAA;AACE,YAAA;AACA,UAAA;AACA,QAAA;AACA,WAAA;AACA,aAAA;;AAEA,CAPF,WAOE;AACE,UAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,2BAAA,KAAA;AAAA,mBAAA,KAAA;;AAGF,CAfF,WAeE;AACE,WAAA;;AAGF,CAnBF,WAmBE,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,iBAAA;;AAEA,CA3BJ,WA2BI,CARF,aAQE;AACE,aAAA;;AAIJ,CAhCF,WAgCE,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAtCF,WAsCE,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,aAAA;AACA,SAAA,IAAA;;AAEA,CA7CJ,WA6CI,CAPF,WAOE;AACE,aAAA;AACA,SAAA,IAAA;;AAMN,CAAA;AACE,YAAA;AACA,SAAA;AACA,OAAA;AACA,aAAA,WAAA;AACA,cAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,SAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;AACA,kBAAA;;AAKA,QAAA,CAAA,gBAAA,CAAA;AACE,YAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;;AAGF,QAAA,CAPA,gBAOA,CAAA;AACE,YAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,WAAA;AACA,aAAA,MAAA,GAAA;AACA,WAAA;;AAGF,QAAA,CAjBA,gBAiBA,CAAA;AACE,SAAA;AACA,UAAA;AACA,WAAA;AACA,YAAA;;AAGF,QAAA,CAxBA,gBAwBA,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,cAAA,IAAA;AACA,SAAA;AACA,iBAAA;AACA,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,WAAA;AACA,UAAA,IAAA,MAAA;;AAIJ,WApCE;AAqCA;AACE,eAAA,MAAA;AACA,aAAA;;AAEF;AACE,eAAA,MAAA;AACA,aAAA;;AAEF;AACE,eAAA,MAAA;AACA,aAAA;;;AAMF,QAAA,CAAA,uBAAA,CAAA;AACE,cAAA;AACA,aAAA;;AAEA,QAAA,CAJF,uBAIE,CAJF,iBAIE;AACE,UAAA,EAAA,EAAA,IAAA;AACA,SAAA,IAAA;AACA,aAAA;AACA,eAAA;;AAGF,QAAA,CAXF,uBAWE,CAXF,iBAWE;AACE,UAAA,IAAA;AACA,SAAA,IAAA;AACA,aAAA;AACA,eAAA;;AAGF,QAAA,CAlBF,uBAkBE,CAlBF,iBAkBE;AACE,UAAA,IAAA;AACA,aAAA;;AAEA,QAAA,CAtBJ,uBAsBI,CAtBJ,iBAsBI,EAAA;AACE,SAAA,IAAA;;AAKF,QAAA,CA5BJ,uBA4BI,CA5BJ,gBA4BI,CAAA,cAAA;AACE,SAAA,IAAA;AACA,aAAA;;", "names": []}
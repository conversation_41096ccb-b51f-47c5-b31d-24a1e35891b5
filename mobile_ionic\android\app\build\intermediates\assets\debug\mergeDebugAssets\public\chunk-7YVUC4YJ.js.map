{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/index-68c0d151.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as config, p as printIonWarning } from './index-cfd9c1f2.js';\nimport { w as writeTask, B as Build } from './index-527b9e34.js';\nimport { r as raf } from './helpers-d94bc8ad.js';\nconst LIFECYCLE_WILL_ENTER = 'ionViewWillEnter';\nconst LIFECYCLE_DID_ENTER = 'ionViewDidEnter';\nconst LIFECYCLE_WILL_LEAVE = 'ionViewWillLeave';\nconst LIFECYCLE_DID_LEAVE = 'ionViewDidLeave';\nconst LIFECYCLE_WILL_UNLOAD = 'ionViewWillUnload';\n\n/**\n * Moves focus to a specified element. Note that we do not remove the tabindex\n * because that can result in an unintentional blur. Non-focusables can't be\n * focused, so the body will get focused again.\n */\nconst moveFocus = el => {\n  el.tabIndex = -1;\n  el.focus();\n};\n/**\n * Elements that are hidden using `display: none` should not be focused even if\n * they are present in the DOM.\n */\nconst isVisible = el => {\n  return el.offsetParent !== null;\n};\n/**\n * The focus controller allows us to manage focus within a view so assistive\n * technologies can inform users of changes to the navigation state. Traditional\n * native apps have a way of informing assistive technology about a navigation\n * state change. Mobile browsers have this too, but only when doing a full page\n * load. In a single page app we do not do that, so we need to build this\n * integration ourselves.\n */\nconst createFocusController = () => {\n  const saveViewFocus = referenceEl => {\n    const focusManagerEnabled = config.get('focusManagerPriority', false);\n    /**\n     * When going back to a previously visited page focus should typically be moved\n     * back to the element that was last focused when the user was on this view.\n     */\n    if (focusManagerEnabled) {\n      const activeEl = document.activeElement;\n      if (activeEl !== null && (referenceEl === null || referenceEl === void 0 ? void 0 : referenceEl.contains(activeEl))) {\n        activeEl.setAttribute(LAST_FOCUS, 'true');\n      }\n    }\n  };\n  const setViewFocus = referenceEl => {\n    const focusManagerPriorities = config.get('focusManagerPriority', false);\n    /**\n     * If the focused element is a descendant of the referenceEl then it's possible\n     * that the app developer manually moved focus, so we do not want to override that.\n     * This can happen with inputs the are focused when a view transitions in.\n     */\n    if (Array.isArray(focusManagerPriorities) && !referenceEl.contains(document.activeElement)) {\n      /**\n       * When going back to a previously visited view focus should always be moved back\n       * to the element that the user was last focused on when they were on this view.\n       */\n      const lastFocus = referenceEl.querySelector(`[${LAST_FOCUS}]`);\n      if (lastFocus && isVisible(lastFocus)) {\n        moveFocus(lastFocus);\n        return;\n      }\n      for (const priority of focusManagerPriorities) {\n        /**\n         * For each recognized case (excluding the default case) make sure to return\n         * so that the fallback focus behavior does not run.\n         *\n         * We intentionally query for specific roles/semantic elements so that the\n         * transition manager can work with both Ionic and non-Ionic UI components.\n         *\n         * If new selectors are added, be sure to remove the outline ring by adding\n         * new selectors to rule in core.scss.\n         */\n        switch (priority) {\n          case 'content':\n            const content = referenceEl.querySelector('main, [role=\"main\"]');\n            if (content && isVisible(content)) {\n              moveFocus(content);\n              return;\n            }\n            break;\n          case 'heading':\n            const headingOne = referenceEl.querySelector('h1, [role=\"heading\"][aria-level=\"1\"]');\n            if (headingOne && isVisible(headingOne)) {\n              moveFocus(headingOne);\n              return;\n            }\n            break;\n          case 'banner':\n            const header = referenceEl.querySelector('header, [role=\"banner\"]');\n            if (header && isVisible(header)) {\n              moveFocus(header);\n              return;\n            }\n            break;\n          default:\n            printIonWarning(`Unrecognized focus manager priority value ${priority}`);\n            break;\n        }\n      }\n      /**\n       * If there is nothing to focus then focus the page so focus at least moves to\n       * the correct view. The browser will then determine where within the page to\n       * move focus to.\n       */\n      moveFocus(referenceEl);\n    }\n  };\n  return {\n    saveViewFocus,\n    setViewFocus\n  };\n};\nconst LAST_FOCUS = 'ion-last-focus';\nconst iosTransitionAnimation = () => import('./ios.transition-4047cb68.js');\nconst mdTransitionAnimation = () => import('./md.transition-30ce8d1b.js');\nconst focusController = createFocusController();\n// TODO(FW-2832): types\nconst transition = opts => {\n  return new Promise((resolve, reject) => {\n    writeTask(() => {\n      beforeTransition(opts);\n      runTransition(opts).then(result => {\n        if (result.animation) {\n          result.animation.destroy();\n        }\n        afterTransition(opts);\n        resolve(result);\n      }, error => {\n        afterTransition(opts);\n        reject(error);\n      });\n    });\n  });\n};\nconst beforeTransition = opts => {\n  const enteringEl = opts.enteringEl;\n  const leavingEl = opts.leavingEl;\n  focusController.saveViewFocus(leavingEl);\n  setZIndex(enteringEl, leavingEl, opts.direction);\n  if (opts.showGoBack) {\n    enteringEl.classList.add('can-go-back');\n  } else {\n    enteringEl.classList.remove('can-go-back');\n  }\n  setPageHidden(enteringEl, false);\n  /**\n   * When transitioning, the page should not\n   * respond to click events. This resolves small\n   * issues like users double tapping the ion-back-button.\n   * These pointer events are removed in `afterTransition`.\n   */\n  enteringEl.style.setProperty('pointer-events', 'none');\n  if (leavingEl) {\n    setPageHidden(leavingEl, false);\n    leavingEl.style.setProperty('pointer-events', 'none');\n  }\n};\nconst runTransition = async opts => {\n  const animationBuilder = await getAnimationBuilder(opts);\n  const ani = animationBuilder && Build.isBrowser ? animation(animationBuilder, opts) : noAnimation(opts); // fast path for no animation\n  return ani;\n};\nconst afterTransition = opts => {\n  const enteringEl = opts.enteringEl;\n  const leavingEl = opts.leavingEl;\n  enteringEl.classList.remove('ion-page-invisible');\n  enteringEl.style.removeProperty('pointer-events');\n  if (leavingEl !== undefined) {\n    leavingEl.classList.remove('ion-page-invisible');\n    leavingEl.style.removeProperty('pointer-events');\n  }\n  focusController.setViewFocus(enteringEl);\n};\nconst getAnimationBuilder = async opts => {\n  if (!opts.leavingEl || !opts.animated || opts.duration === 0) {\n    return undefined;\n  }\n  if (opts.animationBuilder) {\n    return opts.animationBuilder;\n  }\n  const getAnimation = opts.mode === 'ios' ? (await iosTransitionAnimation()).iosTransitionAnimation : (await mdTransitionAnimation()).mdTransitionAnimation;\n  return getAnimation;\n};\nconst animation = async (animationBuilder, opts) => {\n  await waitForReady(opts, true);\n  const trans = animationBuilder(opts.baseEl, opts);\n  fireWillEvents(opts.enteringEl, opts.leavingEl);\n  const didComplete = await playTransition(trans, opts);\n  if (opts.progressCallback) {\n    opts.progressCallback(undefined);\n  }\n  if (didComplete) {\n    fireDidEvents(opts.enteringEl, opts.leavingEl);\n  }\n  return {\n    hasCompleted: didComplete,\n    animation: trans\n  };\n};\nconst noAnimation = async opts => {\n  const enteringEl = opts.enteringEl;\n  const leavingEl = opts.leavingEl;\n  const focusManagerEnabled = config.get('focusManagerPriority', false);\n  /**\n   * If the focus manager is enabled then we need to wait for Ionic components to be\n   * rendered otherwise the component to focus may not be focused because it is hidden.\n   */\n  await waitForReady(opts, focusManagerEnabled);\n  fireWillEvents(enteringEl, leavingEl);\n  fireDidEvents(enteringEl, leavingEl);\n  return {\n    hasCompleted: true\n  };\n};\nconst waitForReady = async (opts, defaultDeep) => {\n  const deep = opts.deepWait !== undefined ? opts.deepWait : defaultDeep;\n  if (deep) {\n    await Promise.all([deepReady(opts.enteringEl), deepReady(opts.leavingEl)]);\n  }\n  await notifyViewReady(opts.viewIsReady, opts.enteringEl);\n};\nconst notifyViewReady = async (viewIsReady, enteringEl) => {\n  if (viewIsReady) {\n    await viewIsReady(enteringEl);\n  }\n};\nconst playTransition = (trans, opts) => {\n  const progressCallback = opts.progressCallback;\n  const promise = new Promise(resolve => {\n    trans.onFinish(currentStep => resolve(currentStep === 1));\n  });\n  // cool, let's do this, start the transition\n  if (progressCallback) {\n    // this is a swipe to go back, just get the transition progress ready\n    // kick off the swipe animation start\n    trans.progressStart(true);\n    progressCallback(trans);\n  } else {\n    // only the top level transition should actually start \"play\"\n    // kick it off and let it play through\n    // ******** DOM WRITE ****************\n    trans.play();\n  }\n  // create a callback for when the animation is done\n  return promise;\n};\nconst fireWillEvents = (enteringEl, leavingEl) => {\n  lifecycle(leavingEl, LIFECYCLE_WILL_LEAVE);\n  lifecycle(enteringEl, LIFECYCLE_WILL_ENTER);\n};\nconst fireDidEvents = (enteringEl, leavingEl) => {\n  lifecycle(enteringEl, LIFECYCLE_DID_ENTER);\n  lifecycle(leavingEl, LIFECYCLE_DID_LEAVE);\n};\nconst lifecycle = (el, eventName) => {\n  if (el) {\n    const ev = new CustomEvent(eventName, {\n      bubbles: false,\n      cancelable: false\n    });\n    el.dispatchEvent(ev);\n  }\n};\n/**\n * Wait two request animation frame loops.\n * This allows the framework implementations enough time to mount\n * the user-defined contents. This is often needed when using inline\n * modals and popovers that accept user components. For popover,\n * the contents must be mounted for the popover to be sized correctly.\n * For modals, the contents must be mounted for iOS to run the\n * transition correctly.\n *\n * On Angular and React, a single raf is enough time, but for Vue\n * we need to wait two rafs. As a result we are using two rafs for\n * all frameworks to ensure contents are mounted.\n */\nconst waitForMount = () => {\n  return new Promise(resolve => raf(() => raf(() => resolve())));\n};\nconst deepReady = async el => {\n  const element = el;\n  if (element) {\n    if (element.componentOnReady != null) {\n      // eslint-disable-next-line custom-rules/no-component-on-ready-method\n      const stencilEl = await element.componentOnReady();\n      if (stencilEl != null) {\n        return;\n      }\n      /**\n       * Custom elements in Stencil will have __registerHost.\n       */\n    } else if (element.__registerHost != null) {\n      /**\n       * Non-lazy loaded custom elements need to wait\n       * one frame for component to be loaded.\n       */\n      const waitForCustomElement = new Promise(resolve => raf(resolve));\n      await waitForCustomElement;\n      return;\n    }\n    await Promise.all(Array.from(element.children).map(deepReady));\n  }\n};\nconst setPageHidden = (el, hidden) => {\n  if (hidden) {\n    el.setAttribute('aria-hidden', 'true');\n    el.classList.add('ion-page-hidden');\n  } else {\n    el.hidden = false;\n    el.removeAttribute('aria-hidden');\n    el.classList.remove('ion-page-hidden');\n  }\n};\nconst setZIndex = (enteringEl, leavingEl, direction) => {\n  if (enteringEl !== undefined) {\n    enteringEl.style.zIndex = direction === 'back' ? '99' : '101';\n  }\n  if (leavingEl !== undefined) {\n    leavingEl.style.zIndex = '100';\n  }\n};\nconst getIonPageElement = element => {\n  if (element.classList.contains('ion-page')) {\n    return element;\n  }\n  const ionPage = element.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs');\n  if (ionPage) {\n    return ionPage;\n  }\n  // idk, return the original element so at least something animates and we don't have a null pointer\n  return element;\n};\nexport { LIFECYCLE_WILL_ENTER as L, LIFECYCLE_DID_ENTER as a, LIFECYCLE_WILL_LEAVE as b, LIFECYCLE_DID_LEAVE as c, LIFECYCLE_WILL_UNLOAD as d, deepReady as e, getIonPageElement as g, lifecycle as l, setPageHidden as s, transition as t, waitForMount as w };"], "mappings": ";;;;;;;;;;;;;;;;AAMA,IAAM,uBAAuB;AAC7B,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAC7B,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAO9B,IAAM,YAAY,QAAM;AACtB,KAAG,WAAW;AACd,KAAG,MAAM;AACX;AAKA,IAAM,YAAY,QAAM;AACtB,SAAO,GAAG,iBAAiB;AAC7B;AASA,IAAM,wBAAwB,MAAM;AAClC,QAAM,gBAAgB,iBAAe;AACnC,UAAM,sBAAsB,OAAO,IAAI,wBAAwB,KAAK;AAKpE,QAAI,qBAAqB;AACvB,YAAM,WAAW,SAAS;AAC1B,UAAI,aAAa,SAAS,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,SAAS,QAAQ,IAAI;AACnH,iBAAS,aAAa,YAAY,MAAM;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AACA,QAAM,eAAe,iBAAe;AAClC,UAAM,yBAAyB,OAAO,IAAI,wBAAwB,KAAK;AAMvE,QAAI,MAAM,QAAQ,sBAAsB,KAAK,CAAC,YAAY,SAAS,SAAS,aAAa,GAAG;AAK1F,YAAM,YAAY,YAAY,cAAc,IAAI,UAAU,GAAG;AAC7D,UAAI,aAAa,UAAU,SAAS,GAAG;AACrC,kBAAU,SAAS;AACnB;AAAA,MACF;AACA,iBAAW,YAAY,wBAAwB;AAW7C,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,kBAAM,UAAU,YAAY,cAAc,qBAAqB;AAC/D,gBAAI,WAAW,UAAU,OAAO,GAAG;AACjC,wBAAU,OAAO;AACjB;AAAA,YACF;AACA;AAAA,UACF,KAAK;AACH,kBAAM,aAAa,YAAY,cAAc,sCAAsC;AACnF,gBAAI,cAAc,UAAU,UAAU,GAAG;AACvC,wBAAU,UAAU;AACpB;AAAA,YACF;AACA;AAAA,UACF,KAAK;AACH,kBAAM,SAAS,YAAY,cAAc,yBAAyB;AAClE,gBAAI,UAAU,UAAU,MAAM,GAAG;AAC/B,wBAAU,MAAM;AAChB;AAAA,YACF;AACA;AAAA,UACF;AACE,4BAAgB,6CAA6C,QAAQ,EAAE;AACvE;AAAA,QACJ;AAAA,MACF;AAMA,gBAAU,WAAW;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,aAAa;AACnB,IAAM,yBAAyB,MAAM,OAAO,uCAA8B;AAC1E,IAAM,wBAAwB,MAAM,OAAO,sCAA6B;AACxE,IAAM,kBAAkB,sBAAsB;AAE9C,IAAM,aAAa,UAAQ;AACzB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAU,MAAM;AACd,uBAAiB,IAAI;AACrB,oBAAc,IAAI,EAAE,KAAK,YAAU;AACjC,YAAI,OAAO,WAAW;AACpB,iBAAO,UAAU,QAAQ;AAAA,QAC3B;AACA,wBAAgB,IAAI;AACpB,gBAAQ,MAAM;AAAA,MAChB,GAAG,WAAS;AACV,wBAAgB,IAAI;AACpB,eAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAM,mBAAmB,UAAQ;AAC/B,QAAM,aAAa,KAAK;AACxB,QAAM,YAAY,KAAK;AACvB,kBAAgB,cAAc,SAAS;AACvC,YAAU,YAAY,WAAW,KAAK,SAAS;AAC/C,MAAI,KAAK,YAAY;AACnB,eAAW,UAAU,IAAI,aAAa;AAAA,EACxC,OAAO;AACL,eAAW,UAAU,OAAO,aAAa;AAAA,EAC3C;AACA,gBAAc,YAAY,KAAK;AAO/B,aAAW,MAAM,YAAY,kBAAkB,MAAM;AACrD,MAAI,WAAW;AACb,kBAAc,WAAW,KAAK;AAC9B,cAAU,MAAM,YAAY,kBAAkB,MAAM;AAAA,EACtD;AACF;AACA,IAAM,gBAAgB,CAAM,SAAQ;AAClC,QAAM,mBAAmB,MAAM,oBAAoB,IAAI;AACvD,QAAM,MAAM,oBAAoB,MAAM,YAAY,UAAU,kBAAkB,IAAI,IAAI,YAAY,IAAI;AACtG,SAAO;AACT;AACA,IAAM,kBAAkB,UAAQ;AAC9B,QAAM,aAAa,KAAK;AACxB,QAAM,YAAY,KAAK;AACvB,aAAW,UAAU,OAAO,oBAAoB;AAChD,aAAW,MAAM,eAAe,gBAAgB;AAChD,MAAI,cAAc,QAAW;AAC3B,cAAU,UAAU,OAAO,oBAAoB;AAC/C,cAAU,MAAM,eAAe,gBAAgB;AAAA,EACjD;AACA,kBAAgB,aAAa,UAAU;AACzC;AACA,IAAM,sBAAsB,CAAM,SAAQ;AACxC,MAAI,CAAC,KAAK,aAAa,CAAC,KAAK,YAAY,KAAK,aAAa,GAAG;AAC5D,WAAO;AAAA,EACT;AACA,MAAI,KAAK,kBAAkB;AACzB,WAAO,KAAK;AAAA,EACd;AACA,QAAM,eAAe,KAAK,SAAS,SAAS,MAAM,uBAAuB,GAAG,0BAA0B,MAAM,sBAAsB,GAAG;AACrI,SAAO;AACT;AACA,IAAM,YAAY,CAAO,kBAAkB,SAAS;AAClD,QAAM,aAAa,MAAM,IAAI;AAC7B,QAAM,QAAQ,iBAAiB,KAAK,QAAQ,IAAI;AAChD,iBAAe,KAAK,YAAY,KAAK,SAAS;AAC9C,QAAM,cAAc,MAAM,eAAe,OAAO,IAAI;AACpD,MAAI,KAAK,kBAAkB;AACzB,SAAK,iBAAiB,MAAS;AAAA,EACjC;AACA,MAAI,aAAa;AACf,kBAAc,KAAK,YAAY,KAAK,SAAS;AAAA,EAC/C;AACA,SAAO;AAAA,IACL,cAAc;AAAA,IACd,WAAW;AAAA,EACb;AACF;AACA,IAAM,cAAc,CAAM,SAAQ;AAChC,QAAM,aAAa,KAAK;AACxB,QAAM,YAAY,KAAK;AACvB,QAAM,sBAAsB,OAAO,IAAI,wBAAwB,KAAK;AAKpE,QAAM,aAAa,MAAM,mBAAmB;AAC5C,iBAAe,YAAY,SAAS;AACpC,gBAAc,YAAY,SAAS;AACnC,SAAO;AAAA,IACL,cAAc;AAAA,EAChB;AACF;AACA,IAAM,eAAe,CAAO,MAAM,gBAAgB;AAChD,QAAM,OAAO,KAAK,aAAa,SAAY,KAAK,WAAW;AAC3D,MAAI,MAAM;AACR,UAAM,QAAQ,IAAI,CAAC,UAAU,KAAK,UAAU,GAAG,UAAU,KAAK,SAAS,CAAC,CAAC;AAAA,EAC3E;AACA,QAAM,gBAAgB,KAAK,aAAa,KAAK,UAAU;AACzD;AACA,IAAM,kBAAkB,CAAO,aAAa,eAAe;AACzD,MAAI,aAAa;AACf,UAAM,YAAY,UAAU;AAAA,EAC9B;AACF;AACA,IAAM,iBAAiB,CAAC,OAAO,SAAS;AACtC,QAAM,mBAAmB,KAAK;AAC9B,QAAM,UAAU,IAAI,QAAQ,aAAW;AACrC,UAAM,SAAS,iBAAe,QAAQ,gBAAgB,CAAC,CAAC;AAAA,EAC1D,CAAC;AAED,MAAI,kBAAkB;AAGpB,UAAM,cAAc,IAAI;AACxB,qBAAiB,KAAK;AAAA,EACxB,OAAO;AAIL,UAAM,KAAK;AAAA,EACb;AAEA,SAAO;AACT;AACA,IAAM,iBAAiB,CAAC,YAAY,cAAc;AAChD,YAAU,WAAW,oBAAoB;AACzC,YAAU,YAAY,oBAAoB;AAC5C;AACA,IAAM,gBAAgB,CAAC,YAAY,cAAc;AAC/C,YAAU,YAAY,mBAAmB;AACzC,YAAU,WAAW,mBAAmB;AAC1C;AACA,IAAM,YAAY,CAAC,IAAI,cAAc;AACnC,MAAI,IAAI;AACN,UAAM,KAAK,IAAI,YAAY,WAAW;AAAA,MACpC,SAAS;AAAA,MACT,YAAY;AAAA,IACd,CAAC;AACD,OAAG,cAAc,EAAE;AAAA,EACrB;AACF;AAcA,IAAM,eAAe,MAAM;AACzB,SAAO,IAAI,QAAQ,aAAW,IAAI,MAAM,IAAI,MAAM,QAAQ,CAAC,CAAC,CAAC;AAC/D;AACA,IAAM,YAAY,CAAM,OAAM;AAC5B,QAAM,UAAU;AAChB,MAAI,SAAS;AACX,QAAI,QAAQ,oBAAoB,MAAM;AAEpC,YAAM,YAAY,MAAM,QAAQ,iBAAiB;AACjD,UAAI,aAAa,MAAM;AACrB;AAAA,MACF;AAAA,IAIF,WAAW,QAAQ,kBAAkB,MAAM;AAKzC,YAAM,uBAAuB,IAAI,QAAQ,aAAW,IAAI,OAAO,CAAC;AAChE,YAAM;AACN;AAAA,IACF;AACA,UAAM,QAAQ,IAAI,MAAM,KAAK,QAAQ,QAAQ,EAAE,IAAI,SAAS,CAAC;AAAA,EAC/D;AACF;AACA,IAAM,gBAAgB,CAAC,IAAI,WAAW;AACpC,MAAI,QAAQ;AACV,OAAG,aAAa,eAAe,MAAM;AACrC,OAAG,UAAU,IAAI,iBAAiB;AAAA,EACpC,OAAO;AACL,OAAG,SAAS;AACZ,OAAG,gBAAgB,aAAa;AAChC,OAAG,UAAU,OAAO,iBAAiB;AAAA,EACvC;AACF;AACA,IAAM,YAAY,CAAC,YAAY,WAAW,cAAc;AACtD,MAAI,eAAe,QAAW;AAC5B,eAAW,MAAM,SAAS,cAAc,SAAS,OAAO;AAAA,EAC1D;AACA,MAAI,cAAc,QAAW;AAC3B,cAAU,MAAM,SAAS;AAAA,EAC3B;AACF;AACA,IAAM,oBAAoB,aAAW;AACnC,MAAI,QAAQ,UAAU,SAAS,UAAU,GAAG;AAC1C,WAAO;AAAA,EACT;AACA,QAAM,UAAU,QAAQ,cAAc,yDAAyD;AAC/F,MAAI,SAAS;AACX,WAAO;AAAA,EACT;AAEA,SAAO;AACT;", "names": [], "x_google_ignoreList": [0]}
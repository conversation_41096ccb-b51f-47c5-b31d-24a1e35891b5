{"version": 3, "sources": ["node_modules/@ionic/core/components/swipe-back.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { k as clamp } from './helpers.js';\nimport { i as isRTL } from './dir.js';\nimport { createGesture } from './index3.js';\nconst createSwipeBackGesture = (el, canStartHandler, onStartHandler, onMoveHandler, onEndHandler) => {\n  const win = el.ownerDocument.defaultView;\n  let rtl = isRTL(el);\n  /**\n   * Determine if a gesture is near the edge\n   * of the screen. If true, then the swipe\n   * to go back gesture should proceed.\n   */\n  const isAtEdge = detail => {\n    const threshold = 50;\n    const {\n      startX\n    } = detail;\n    if (rtl) {\n      return startX >= win.innerWidth - threshold;\n    }\n    return startX <= threshold;\n  };\n  const getDeltaX = detail => {\n    return rtl ? -detail.deltaX : detail.deltaX;\n  };\n  const getVelocityX = detail => {\n    return rtl ? -detail.velocityX : detail.velocityX;\n  };\n  const canStart = detail => {\n    /**\n     * The user's locale can change mid-session,\n     * so we need to check text direction at\n     * the beginning of every gesture.\n     */\n    rtl = isRTL(el);\n    return isAtEdge(detail) && canStartHandler();\n  };\n  const onMove = detail => {\n    // set the transition animation's progress\n    const delta = getDeltaX(detail);\n    const stepValue = delta / win.innerWidth;\n    onMoveHandler(stepValue);\n  };\n  const onEnd = detail => {\n    // the swipe back gesture has ended\n    const delta = getDeltaX(detail);\n    const width = win.innerWidth;\n    const stepValue = delta / width;\n    const velocity = getVelocityX(detail);\n    const z = width / 2.0;\n    const shouldComplete = velocity >= 0 && (velocity > 0.2 || delta > z);\n    const missing = shouldComplete ? 1 - stepValue : stepValue;\n    const missingDistance = missing * width;\n    let realDur = 0;\n    if (missingDistance > 5) {\n      const dur = missingDistance / Math.abs(velocity);\n      realDur = Math.min(dur, 540);\n    }\n    onEndHandler(shouldComplete, stepValue <= 0 ? 0.01 : clamp(0, stepValue, 0.9999), realDur);\n  };\n  return createGesture({\n    el,\n    gestureName: 'goback-swipe',\n    /**\n     * Swipe to go back should have priority over other horizontal swipe\n     * gestures. These gestures have a priority of 100 which is why 101 was chosen here.\n     */\n    gesturePriority: 101,\n    threshold: 10,\n    canStart,\n    onStart: onStartHandler,\n    onMove,\n    onEnd\n  });\n};\nexport { createSwipeBackGesture };"], "mappings": ";;;;;;;;;;;;;AAMA,IAAM,yBAAyB,CAAC,IAAI,iBAAiB,gBAAgB,eAAe,iBAAiB;AACnG,QAAM,MAAM,GAAG,cAAc;AAC7B,MAAI,MAAM,MAAM,EAAE;AAMlB,QAAM,WAAW,YAAU;AACzB,UAAM,YAAY;AAClB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK;AACP,aAAO,UAAU,IAAI,aAAa;AAAA,IACpC;AACA,WAAO,UAAU;AAAA,EACnB;AACA,QAAM,YAAY,YAAU;AAC1B,WAAO,MAAM,CAAC,OAAO,SAAS,OAAO;AAAA,EACvC;AACA,QAAM,eAAe,YAAU;AAC7B,WAAO,MAAM,CAAC,OAAO,YAAY,OAAO;AAAA,EAC1C;AACA,QAAM,WAAW,YAAU;AAMzB,UAAM,MAAM,EAAE;AACd,WAAO,SAAS,MAAM,KAAK,gBAAgB;AAAA,EAC7C;AACA,QAAM,SAAS,YAAU;AAEvB,UAAM,QAAQ,UAAU,MAAM;AAC9B,UAAM,YAAY,QAAQ,IAAI;AAC9B,kBAAc,SAAS;AAAA,EACzB;AACA,QAAM,QAAQ,YAAU;AAEtB,UAAM,QAAQ,UAAU,MAAM;AAC9B,UAAM,QAAQ,IAAI;AAClB,UAAM,YAAY,QAAQ;AAC1B,UAAM,WAAW,aAAa,MAAM;AACpC,UAAM,IAAI,QAAQ;AAClB,UAAM,iBAAiB,YAAY,MAAM,WAAW,OAAO,QAAQ;AACnE,UAAM,UAAU,iBAAiB,IAAI,YAAY;AACjD,UAAM,kBAAkB,UAAU;AAClC,QAAI,UAAU;AACd,QAAI,kBAAkB,GAAG;AACvB,YAAM,MAAM,kBAAkB,KAAK,IAAI,QAAQ;AAC/C,gBAAU,KAAK,IAAI,KAAK,GAAG;AAAA,IAC7B;AACA,iBAAa,gBAAgB,aAAa,IAAI,OAAO,MAAM,GAAG,WAAW,MAAM,GAAG,OAAO;AAAA,EAC3F;AACA,SAAO,cAAc;AAAA,IACnB;AAAA,IACA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,IAKb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF,CAAC;AACH;", "names": [], "x_google_ignoreList": [0]}
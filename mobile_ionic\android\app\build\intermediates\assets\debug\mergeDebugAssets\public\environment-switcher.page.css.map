{"version": 3, "sources": ["src/app/pages/environment-switcher/environment-switcher.page.scss"], "sourcesContent": [".switcher-container {\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.active-endpoint {\r\n  --background: var(--ion-color-primary-tint);\r\n  --border-color: var(--ion-color-primary);\r\n  border-left: 4px solid var(--ion-color-primary);\r\n}\r\n\r\n.endpoint-url {\r\n  font-family: monospace;\r\n  font-size: 0.8em;\r\n  color: var(--ion-color-medium);\r\n  word-break: break-all;\r\n}\r\n\r\n.success-message {\r\n  color: var(--ion-color-success);\r\n  font-weight: 500;\r\n}\r\n\r\n.error-message {\r\n  color: var(--ion-color-danger);\r\n  font-weight: 500;\r\n}\r\n\r\nion-card {\r\n  margin-bottom: 16px;\r\n}\r\n\r\nion-card-title {\r\n  color: var(--ion-color-primary);\r\n}\r\n\r\nion-item {\r\n  --padding-start: 16px;\r\n  --padding-end: 16px;\r\n}\r\n\r\nion-button {\r\n  --border-radius: 8px;\r\n}\r\n\r\n.quick-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.quick-actions ion-button {\r\n  flex: 1;\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,gBAAA,IAAA;AACA,kBAAA,IAAA;AACA,eAAA,IAAA,MAAA,IAAA;;AAGF,CAAA;AACE,eAAA;AACA,aAAA;AACA,SAAA,IAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA,IAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA,IAAA;AACA,eAAA;;AAGF;AACE,iBAAA;;AAGF;AACE,SAAA,IAAA;;AAGF;AACE,mBAAA;AACA,iBAAA;;AAGF;AACE,mBAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CANA,cAMA;AACE,QAAA;;", "names": []}
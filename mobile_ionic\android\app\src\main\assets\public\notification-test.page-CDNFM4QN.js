import {
  FcmService
} from "./chunk-RDFT5QPW.js";
import "./chunk-I7MI46CM.js";
import "./chunk-TAZAZ6IP.js";
import {
  AlertController,
  CommonModule,
  Component,
  FormsModule,
  IonBadge,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardSubtitle,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonTitle,
  IonToolbar,
  IonicModule,
  LoadingController,
  NgForOf,
  Router,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import {
  __async
} from "./chunk-UL2P3LPA.js";

// src/app/pages/notification-test/notification-test.page.ts
function NotificationTestPage_ion_item_46_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-item", 14);
    \u0275\u0275element(1, "ion-icon", 15);
    \u0275\u0275elementStart(2, "ion-label")(3, "h2");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "p", 16)(8, "ion-badge", 17);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "ion-badge", 17);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(12, "ion-buttons", 18)(13, "ion-button", 19);
    \u0275\u0275listener("click", function NotificationTestPage_ion_item_46_Template_ion_button_click_13_listener() {
      const notification_r2 = \u0275\u0275restoreView(_r1).$implicit;
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.testForegroundNotification(notification_r2));
    });
    \u0275\u0275element(14, "ion-icon", 20);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const notification_r2 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("name", ctx_r2.getDisasterIcon(notification_r2.category))("color", ctx_r2.getDisasterColor(notification_r2.category));
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(notification_r2.title);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(notification_r2.body);
    \u0275\u0275advance(2);
    \u0275\u0275property("color", ctx_r2.getDisasterColor(notification_r2.category));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", notification_r2.category.toUpperCase(), " ");
    \u0275\u0275advance();
    \u0275\u0275property("color", notification_r2.severity === "high" ? "danger" : "warning");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", notification_r2.severity.toUpperCase(), " ");
    \u0275\u0275advance(2);
    \u0275\u0275property("color", ctx_r2.getDisasterColor(notification_r2.category));
  }
}
var NotificationTestPage = class _NotificationTestPage {
  constructor(fcmService, alertController, loadingController, router) {
    this.fcmService = fcmService;
    this.alertController = alertController;
    this.loadingController = loadingController;
    this.router = router;
    this.testNotifications = [
      {
        title: "EARTHQUAKE ALERT",
        body: "Magnitude 7.2 earthquake detected. Evacuate to nearest safe area immediately.",
        category: "earthquake",
        severity: "high"
      },
      {
        title: "FLOOD WARNING",
        body: "Flash flood warning in your area. Move to higher ground immediately.",
        category: "flood",
        severity: "high"
      },
      {
        title: "TYPHOON ALERT",
        body: "Typhoon approaching. Seek shelter in a sturdy building.",
        category: "typhoon",
        severity: "medium"
      },
      {
        title: "FIRE EMERGENCY",
        body: "Fire reported in your vicinity. Evacuate the area immediately.",
        category: "fire",
        severity: "high"
      },
      {
        title: "GENERAL ALERT",
        body: "Emergency situation detected. Follow local authorities instructions.",
        category: "general",
        severity: "medium"
      }
    ];
  }
  testForegroundNotification(notification) {
    return __async(this, null, function* () {
      const loading = yield this.loadingController.create({
        message: "Testing foreground notification...",
        duration: 3e3
      });
      yield loading.present();
      try {
        const mockNotification = {
          title: notification.title,
          body: notification.body,
          category: notification.category,
          severity: notification.severity,
          wasTapped: false,
          data: {
            category: notification.category,
            severity: notification.severity
          }
        };
        yield this.fcmService.simulateForegroundNotification(mockNotification);
        yield loading.dismiss();
        const alert = yield this.alertController.create({
          header: "Test Complete",
          message: "Foreground notification test completed. Check if the emergency modal appeared.",
          buttons: ["OK"]
        });
        yield alert.present();
      } catch (error) {
        yield loading.dismiss();
        const alert = yield this.alertController.create({
          header: "Test Failed",
          message: `Error testing notification: ${error}`,
          buttons: ["OK"]
        });
        yield alert.present();
      }
    });
  }
  testBackgroundNotification(notification) {
    return __async(this, null, function* () {
      const loading = yield this.loadingController.create({
        message: "Testing background notification...",
        duration: 3e3
      });
      yield loading.present();
      try {
        const mockNotification = {
          title: notification.title,
          body: notification.body,
          category: notification.category,
          severity: notification.severity,
          wasTapped: true,
          // Simulate background tap
          data: {
            category: notification.category,
            severity: notification.severity
          }
        };
        yield this.fcmService.simulateBackgroundNotification(mockNotification);
        yield loading.dismiss();
        const alert = yield this.alertController.create({
          header: "Test Complete",
          message: "Background notification test completed. Check if the emergency modal appeared.",
          buttons: ["OK"]
        });
        yield alert.present();
      } catch (error) {
        yield loading.dismiss();
        const alert = yield this.alertController.create({
          header: "Test Failed",
          message: `Error testing notification: ${error}`,
          buttons: ["OK"]
        });
        yield alert.present();
      }
    });
  }
  testAllNotifications() {
    return __async(this, null, function* () {
      const loading = yield this.loadingController.create({
        message: "Testing all notification types...",
        duration: 15e3
      });
      yield loading.present();
      try {
        for (let i = 0; i < this.testNotifications.length; i++) {
          const notification = this.testNotifications[i];
          if (i > 0) {
            yield new Promise((resolve) => setTimeout(resolve, 3e3));
          }
          const mockNotification = {
            title: notification.title,
            body: notification.body,
            category: notification.category,
            severity: notification.severity,
            wasTapped: false,
            data: {
              category: notification.category,
              severity: notification.severity
            }
          };
          yield this.fcmService.simulateForegroundNotification(mockNotification);
        }
        yield loading.dismiss();
        const alert = yield this.alertController.create({
          header: "All Tests Complete",
          message: "All notification types have been tested. Check if emergency modals appeared for each.",
          buttons: ["OK"]
        });
        yield alert.present();
      } catch (error) {
        yield loading.dismiss();
        const alert = yield this.alertController.create({
          header: "Test Failed",
          message: `Error during batch testing: ${error}`,
          buttons: ["OK"]
        });
        yield alert.present();
      }
    });
  }
  getDisasterIcon(category) {
    switch (category.toLowerCase()) {
      case "earthquake":
        return "warning-outline";
      case "flood":
        return "water-outline";
      case "typhoon":
        return "cloudy-outline";
      case "fire":
        return "flame-outline";
      default:
        return "notifications-outline";
    }
  }
  getDisasterColor(category) {
    switch (category.toLowerCase()) {
      case "earthquake":
        return "warning";
      case "flood":
        return "primary";
      case "typhoon":
        return "success";
      case "fire":
        return "danger";
      default:
        return "medium";
    }
  }
  goBack() {
    this.router.navigate(["/tabs/profile"]);
  }
  static {
    this.\u0275fac = function NotificationTestPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _NotificationTestPage)(\u0275\u0275directiveInject(FcmService), \u0275\u0275directiveInject(AlertController), \u0275\u0275directiveInject(LoadingController), \u0275\u0275directiveInject(Router));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _NotificationTestPage, selectors: [["app-notification-test"]], decls: 94, vars: 3, consts: [[3, "translucent"], ["slot", "start"], [3, "click"], ["name", "chevron-back-outline"], [1, "ion-padding", 3, "fullscreen"], [1, "test-container"], ["expand", "block", "color", "danger", 3, "click"], ["name", "flash-outline", "slot", "start"], ["class", "notification-item", 4, "ngFor", "ngForOf"], ["name", "phone-portrait-outline", "slot", "start", "color", "primary"], ["name", "moon-outline", "slot", "start", "color", "secondary"], ["name", "checkmark-circle-outline", "slot", "start", "color", "success"], ["name", "close-circle-outline", "slot", "start", "color", "danger"], ["name", "warning-outline", "slot", "start", "color", "warning"], [1, "notification-item"], ["slot", "start", 3, "name", "color"], [1, "category-badge"], [3, "color"], ["slot", "end"], ["fill", "clear", 3, "click", "color"], ["name", "play-outline"]], template: function NotificationTestPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header", 0)(1, "ion-toolbar")(2, "ion-buttons", 1)(3, "ion-button", 2);
        \u0275\u0275listener("click", function NotificationTestPage_Template_ion_button_click_3_listener() {
          return ctx.goBack();
        });
        \u0275\u0275element(4, "ion-icon", 3);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(5, "ion-title");
        \u0275\u0275text(6, "Emergency Notification Test");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(7, "ion-content", 4)(8, "div", 5)(9, "ion-card")(10, "ion-card-header")(11, "ion-card-title");
        \u0275\u0275text(12, "\u{1F6A8} Emergency Notification Testing");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(13, "ion-card-subtitle");
        \u0275\u0275text(14, "Test emergency modals and notifications");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(15, "ion-card-content")(16, "p");
        \u0275\u0275text(17, "This page allows you to test emergency notifications to ensure they work properly when the app is active.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(18, "p")(19, "strong");
        \u0275\u0275text(20, "What to expect:");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(21, "ul")(22, "li");
        \u0275\u0275text(23, "Emergency modal should appear immediately");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(24, "li");
        \u0275\u0275text(25, "Device should vibrate");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(26, "li");
        \u0275\u0275text(27, "Modal should have disaster-specific colors");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(28, "li");
        \u0275\u0275text(29, "App should NOT crash or close");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(30, "ion-card")(31, "ion-card-header")(32, "ion-card-title");
        \u0275\u0275text(33, "Quick Test");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(34, "ion-card-content")(35, "ion-button", 6);
        \u0275\u0275listener("click", function NotificationTestPage_Template_ion_button_click_35_listener() {
          return ctx.testAllNotifications();
        });
        \u0275\u0275element(36, "ion-icon", 7);
        \u0275\u0275text(37, " Test All Notification Types ");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(38, "ion-card")(39, "ion-card-header")(40, "ion-card-title");
        \u0275\u0275text(41, "Individual Tests");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(42, "ion-card-subtitle");
        \u0275\u0275text(43, "Test specific disaster types");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(44, "ion-card-content")(45, "ion-list");
        \u0275\u0275template(46, NotificationTestPage_ion_item_46_Template, 15, 9, "ion-item", 8);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(47, "ion-card")(48, "ion-card-header")(49, "ion-card-title");
        \u0275\u0275text(50, "Test Types");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(51, "ion-card-content")(52, "ion-list")(53, "ion-item");
        \u0275\u0275element(54, "ion-icon", 9);
        \u0275\u0275elementStart(55, "ion-label")(56, "h3");
        \u0275\u0275text(57, "Foreground Test");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(58, "p");
        \u0275\u0275text(59, "Tests notifications when app is active and visible");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(60, "ion-item");
        \u0275\u0275element(61, "ion-icon", 10);
        \u0275\u0275elementStart(62, "ion-label")(63, "h3");
        \u0275\u0275text(64, "Background Test");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(65, "p");
        \u0275\u0275text(66, "Simulates notifications when app was in background");
        \u0275\u0275elementEnd()()()()()();
        \u0275\u0275elementStart(67, "ion-card")(68, "ion-card-header")(69, "ion-card-title");
        \u0275\u0275text(70, "Troubleshooting");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(71, "ion-card-content")(72, "ion-list")(73, "ion-item");
        \u0275\u0275element(74, "ion-icon", 11);
        \u0275\u0275elementStart(75, "ion-label")(76, "h3");
        \u0275\u0275text(77, "\u2705 Working Correctly");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(78, "p");
        \u0275\u0275text(79, "Modal appears, device vibrates, app stays open");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(80, "ion-item");
        \u0275\u0275element(81, "ion-icon", 12);
        \u0275\u0275elementStart(82, "ion-label")(83, "h3");
        \u0275\u0275text(84, "\u274C App Crashes");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(85, "p");
        \u0275\u0275text(86, "Check console logs, modal creation might be failing");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(87, "ion-item");
        \u0275\u0275element(88, "ion-icon", 13);
        \u0275\u0275elementStart(89, "ion-label")(90, "h3");
        \u0275\u0275text(91, "\u26A0\uFE0F No Modal");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(92, "p");
        \u0275\u0275text(93, "Check if fallback toast appears instead");
        \u0275\u0275elementEnd()()()()()()()();
      }
      if (rf & 2) {
        \u0275\u0275property("translucent", true);
        \u0275\u0275advance(7);
        \u0275\u0275property("fullscreen", true);
        \u0275\u0275advance(39);
        \u0275\u0275property("ngForOf", ctx.testNotifications);
      }
    }, dependencies: [IonicModule, IonBadge, IonButton, IonButtons, IonCard, IonCardContent, IonCardHeader, IonCardSubtitle, IonCardTitle, IonContent, IonHeader, IonIcon, IonItem, IonLabel, IonList, IonTitle, IonToolbar, CommonModule, NgForOf, FormsModule], styles: ["\n\n.test-container[_ngcontent-%COMP%] {\n  max-width: 600px;\n  margin: 0 auto;\n}\n.notification-item[_ngcontent-%COMP%] {\n  margin-bottom: 8px;\n  border-radius: 8px;\n  --background: var(--ion-color-light);\n}\n.category-badge[_ngcontent-%COMP%] {\n  margin-top: 8px;\n  display: flex;\n  gap: 8px;\n}\nion-badge[_ngcontent-%COMP%] {\n  font-size: 0.7rem;\n  padding: 4px 8px;\n}\nion-card[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\nion-card-title[_ngcontent-%COMP%] {\n  color: var(--ion-color-primary);\n}\n.test-button[_ngcontent-%COMP%] {\n  margin: 8px 0;\n}\nion-list[_ngcontent-%COMP%] {\n  background: transparent;\n}\nion-item[_ngcontent-%COMP%] {\n  --padding-start: 16px;\n  --padding-end: 16px;\n  margin-bottom: 4px;\n}\nh3[_ngcontent-%COMP%] {\n  margin: 0 0 4px 0;\n  font-weight: 600;\n}\np[_ngcontent-%COMP%] {\n  margin: 0;\n  color: var(--ion-color-medium);\n  font-size: 0.9rem;\n}\n/*# sourceMappingURL=notification-test.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NotificationTestPage, [{
    type: Component,
    args: [{ standalone: true, imports: [IonicModule, CommonModule, FormsModule], selector: "app-notification-test", template: `<ion-header [translucent]="true">\r
  <ion-toolbar>\r
    <ion-buttons slot="start">\r
      <ion-button (click)="goBack()">\r
        <ion-icon name="chevron-back-outline"></ion-icon>\r
      </ion-button>\r
    </ion-buttons>\r
    <ion-title>Emergency Notification Test</ion-title>\r
  </ion-toolbar>\r
</ion-header>\r
\r
<ion-content [fullscreen]="true" class="ion-padding">\r
  <div class="test-container">\r
    \r
    <!-- Info Card -->\r
    <ion-card>\r
      <ion-card-header>\r
        <ion-card-title>\u{1F6A8} Emergency Notification Testing</ion-card-title>\r
        <ion-card-subtitle>Test emergency modals and notifications</ion-card-subtitle>\r
      </ion-card-header>\r
      <ion-card-content>\r
        <p>This page allows you to test emergency notifications to ensure they work properly when the app is active.</p>\r
        <p><strong>What to expect:</strong></p>\r
        <ul>\r
          <li>Emergency modal should appear immediately</li>\r
          <li>Device should vibrate</li>\r
          <li>Modal should have disaster-specific colors</li>\r
          <li>App should NOT crash or close</li>\r
        </ul>\r
      </ion-card-content>\r
    </ion-card>\r
\r
    <!-- Quick Test Button -->\r
    <ion-card>\r
      <ion-card-header>\r
        <ion-card-title>Quick Test</ion-card-title>\r
      </ion-card-header>\r
      <ion-card-content>\r
        <ion-button \r
          expand="block" \r
          color="danger" \r
          (click)="testAllNotifications()">\r
          <ion-icon name="flash-outline" slot="start"></ion-icon>\r
          Test All Notification Types\r
        </ion-button>\r
      </ion-card-content>\r
    </ion-card>\r
\r
    <!-- Individual Tests -->\r
    <ion-card>\r
      <ion-card-header>\r
        <ion-card-title>Individual Tests</ion-card-title>\r
        <ion-card-subtitle>Test specific disaster types</ion-card-subtitle>\r
      </ion-card-header>\r
      <ion-card-content>\r
        \r
        <ion-list>\r
          <ion-item \r
            *ngFor="let notification of testNotifications" \r
            class="notification-item">\r
            \r
            <ion-icon \r
              [name]="getDisasterIcon(notification.category)" \r
              [color]="getDisasterColor(notification.category)"\r
              slot="start">\r
            </ion-icon>\r
            \r
            <ion-label>\r
              <h2>{{ notification.title }}</h2>\r
              <p>{{ notification.body }}</p>\r
              <p class="category-badge">\r
                <ion-badge [color]="getDisasterColor(notification.category)">\r
                  {{ notification.category.toUpperCase() }}\r
                </ion-badge>\r
                <ion-badge [color]="notification.severity === 'high' ? 'danger' : 'warning'">\r
                  {{ notification.severity.toUpperCase() }}\r
                </ion-badge>\r
              </p>\r
            </ion-label>\r
\r
            <ion-buttons slot="end">\r
              <ion-button \r
                fill="clear" \r
                [color]="getDisasterColor(notification.category)"\r
                (click)="testForegroundNotification(notification)">\r
                <ion-icon name="play-outline"></ion-icon>\r
              </ion-button>\r
            </ion-buttons>\r
          </ion-item>\r
        </ion-list>\r
\r
      </ion-card-content>\r
    </ion-card>\r
\r
    <!-- Test Types -->\r
    <ion-card>\r
      <ion-card-header>\r
        <ion-card-title>Test Types</ion-card-title>\r
      </ion-card-header>\r
      <ion-card-content>\r
        \r
        <ion-list>\r
          <ion-item>\r
            <ion-icon name="phone-portrait-outline" slot="start" color="primary"></ion-icon>\r
            <ion-label>\r
              <h3>Foreground Test</h3>\r
              <p>Tests notifications when app is active and visible</p>\r
            </ion-label>\r
          </ion-item>\r
          \r
          <ion-item>\r
            <ion-icon name="moon-outline" slot="start" color="secondary"></ion-icon>\r
            <ion-label>\r
              <h3>Background Test</h3>\r
              <p>Simulates notifications when app was in background</p>\r
            </ion-label>\r
          </ion-item>\r
        </ion-list>\r
\r
      </ion-card-content>\r
    </ion-card>\r
\r
    <!-- Troubleshooting -->\r
    <ion-card>\r
      <ion-card-header>\r
        <ion-card-title>Troubleshooting</ion-card-title>\r
      </ion-card-header>\r
      <ion-card-content>\r
        <ion-list>\r
          <ion-item>\r
            <ion-icon name="checkmark-circle-outline" slot="start" color="success"></ion-icon>\r
            <ion-label>\r
              <h3>\u2705 Working Correctly</h3>\r
              <p>Modal appears, device vibrates, app stays open</p>\r
            </ion-label>\r
          </ion-item>\r
          \r
          <ion-item>\r
            <ion-icon name="close-circle-outline" slot="start" color="danger"></ion-icon>\r
            <ion-label>\r
              <h3>\u274C App Crashes</h3>\r
              <p>Check console logs, modal creation might be failing</p>\r
            </ion-label>\r
          </ion-item>\r
          \r
          <ion-item>\r
            <ion-icon name="warning-outline" slot="start" color="warning"></ion-icon>\r
            <ion-label>\r
              <h3>\u26A0\uFE0F No Modal</h3>\r
              <p>Check if fallback toast appears instead</p>\r
            </ion-label>\r
          </ion-item>\r
        </ion-list>\r
      </ion-card-content>\r
    </ion-card>\r
\r
  </div>\r
</ion-content>\r
`, styles: ["/* src/app/pages/notification-test/notification-test.page.scss */\n.test-container {\n  max-width: 600px;\n  margin: 0 auto;\n}\n.notification-item {\n  margin-bottom: 8px;\n  border-radius: 8px;\n  --background: var(--ion-color-light);\n}\n.category-badge {\n  margin-top: 8px;\n  display: flex;\n  gap: 8px;\n}\nion-badge {\n  font-size: 0.7rem;\n  padding: 4px 8px;\n}\nion-card {\n  margin-bottom: 16px;\n}\nion-card-title {\n  color: var(--ion-color-primary);\n}\n.test-button {\n  margin: 8px 0;\n}\nion-list {\n  background: transparent;\n}\nion-item {\n  --padding-start: 16px;\n  --padding-end: 16px;\n  margin-bottom: 4px;\n}\nh3 {\n  margin: 0 0 4px 0;\n  font-weight: 600;\n}\np {\n  margin: 0;\n  color: var(--ion-color-medium);\n  font-size: 0.9rem;\n}\n/*# sourceMappingURL=notification-test.page.css.map */\n"] }]
  }], () => [{ type: FcmService }, { type: AlertController }, { type: LoadingController }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(NotificationTestPage, { className: "NotificationTestPage", filePath: "src/app/pages/notification-test/notification-test.page.ts", lineNumber: 15 });
})();
export {
  NotificationTestPage
};
//# sourceMappingURL=notification-test.page-CDNFM4QN.js.map

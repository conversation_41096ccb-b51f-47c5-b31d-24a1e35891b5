import {
  Geolocation,
  MapboxRoutingService,
  require_leaflet_src
} from "./chunk-WDZAZAAD.js";
import {
  OfflineStorageService
} from "./chunk-73VFBDTI.js";
import {
  environment
} from "./chunk-I7MI46CM.js";
import "./chunk-TAZAZ6IP.js";
import {
  ActivatedRoute,
  AlertController,
  CommonModule,
  Component,
  HttpClient,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonContent,
  IonHeader,
  IonIcon,
  IonTitle,
  IonToolbar,
  IonicModule,
  LoadingController,
  Router,
  ToastController,
  firstValueFrom,
  inject,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵproperty,
  ɵɵtext,
  ɵɵtextInterpolate1
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import {
  __async,
  __toESM
} from "./chunk-UL2P3LPA.js";

// src/app/pages/disaster-maps/typhoon-map.page.ts
var L = __toESM(require_leaflet_src());
var TyphoonMapPage = class _TyphoonMapPage {
  constructor() {
    this.userMarker = null;
    this.evacuationCenters = [];
    this.userLocation = null;
    this.newCenterId = null;
    this.highlightCenter = false;
    this.centerLat = null;
    this.centerLng = null;
    this.loadingCtrl = inject(LoadingController);
    this.toastCtrl = inject(ToastController);
    this.alertCtrl = inject(AlertController);
    this.http = inject(HttpClient);
    this.router = inject(Router);
    this.route = inject(ActivatedRoute);
    this.mapboxRouting = inject(MapboxRoutingService);
    this.offlineStorage = inject(OfflineStorageService);
  }
  ngOnInit() {
    console.log("\u{1F7E2} TYPHOON MAP: Component initialized...");
    this.route.queryParams.subscribe((params) => {
      if (params["newCenterId"]) {
        this.newCenterId = params["newCenterId"];
        this.highlightCenter = params["highlightCenter"] === "true";
        this.centerLat = params["centerLat"] ? parseFloat(params["centerLat"]) : null;
        this.centerLng = params["centerLng"] ? parseFloat(params["centerLng"]) : null;
        console.log("\u{1F7E2} TYPHOON MAP: New center to highlight:", this.newCenterId);
      }
    });
  }
  ngAfterViewInit() {
    return __async(this, null, function* () {
      console.log("\u{1F7E2} TYPHOON MAP: View initialized, loading map...");
      setTimeout(() => __async(this, null, function* () {
        yield this.loadTyphoonMap();
      }), 100);
    });
  }
  loadTyphoonMap() {
    return __async(this, null, function* () {
      const loading = yield this.loadingCtrl.create({
        message: "Loading typhoon evacuation centers...",
        spinner: "crescent"
      });
      yield loading.present();
      try {
        const position = yield Geolocation.getCurrentPosition({
          enableHighAccuracy: true,
          timeout: 2e4
        });
        const userLat = position.coords.latitude;
        const userLng = position.coords.longitude;
        console.log(`\u{1F7E2} TYPHOON MAP: User location [${userLat}, ${userLng}]`);
        this.userLocation = { lat: userLat, lng: userLng };
        this.initializeMap(userLat, userLng);
        yield this.loadTyphoonCenters(userLat, userLng);
        yield loading.dismiss();
        const toast = yield this.toastCtrl.create({
          message: `\u{1F7E2} Showing ${this.evacuationCenters.length} typhoon evacuation centers`,
          duration: 3e3,
          color: "success",
          position: "top"
        });
        yield toast.present();
      } catch (error) {
        yield loading.dismiss();
        console.error("\u{1F7E2} TYPHOON MAP: Error loading map", error);
        const alert = yield this.alertCtrl.create({
          header: "Location Error",
          message: "Unable to get your location. Please enable GPS and try again.",
          buttons: [
            {
              text: "Retry",
              handler: () => this.loadTyphoonMap()
            },
            {
              text: "Go Back",
              handler: () => this.router.navigate(["/tabs/home"])
            }
          ]
        });
        yield alert.present();
      }
    });
  }
  initializeMap(lat, lng) {
    console.log(`\u{1F7E2} TYPHOON MAP: Initializing map at [${lat}, ${lng}]`);
    const container = document.getElementById("typhoon-map");
    if (!container) {
      console.error("\u{1F7E2} TYPHOON MAP: Container #typhoon-map not found!");
      throw new Error("Map container not found. Please ensure the view is properly loaded.");
    }
    if (this.map) {
      this.map.remove();
    }
    this.map = L.map("typhoon-map").setView([lat, lng], 13);
    L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: "OpenStreetMap contributors"
    }).addTo(this.map);
    this.userMarker = L.marker([lat, lng], {
      icon: L.icon({
        iconUrl: "assets/Location.png",
        iconSize: [30, 30],
        iconAnchor: [15, 30]
      })
    }).addTo(this.map);
    this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup();
  }
  loadTyphoonCenters(userLat, userLng) {
    return __async(this, null, function* () {
      try {
        console.log("\u{1F7E2} TYPHOON MAP: Fetching typhoon centers...");
        let allCenters = [];
        if (this.offlineStorage.isOfflineMode() || !navigator.onLine) {
          console.log("\u{1F504} Loading typhoon centers from offline storage");
          allCenters = yield this.offlineStorage.getEvacuationCenters();
          console.log("\u{1F4F1} OFFLINE DATA:", allCenters);
          if (allCenters.length === 0) {
            console.warn("\u26A0\uFE0F No cached evacuation centers found");
            const alert = yield this.alertCtrl.create({
              header: "No Offline Data",
              message: "No offline evacuation data available. Please sync data when online.",
              buttons: ["OK"]
            });
            yield alert.present();
            return;
          }
        } else {
          try {
            allCenters = yield firstValueFrom(this.http.get(`${environment.apiUrl}/evacuation-centers`));
            console.log("\u{1F7E2} TYPHOON MAP: Total centers received from API:", allCenters?.length || 0);
          } catch (apiError) {
            console.error("\u274C API failed, falling back to offline data:", apiError);
            allCenters = yield this.offlineStorage.getEvacuationCenters();
            if (allCenters.length === 0) {
              const alert = yield this.alertCtrl.create({
                header: "Connection Error",
                message: "Cannot connect to server and no offline data available. Please check your connection or sync data when online.",
                buttons: ["OK"]
              });
              yield alert.present();
              return;
            }
          }
        }
        this.evacuationCenters = allCenters.filter((center) => center.disaster_type === "Typhoon");
        console.log(`\u{1F7E2} TYPHOON MAP: Filtered to ${this.evacuationCenters.length} typhoon centers`);
        if (this.evacuationCenters.length === 0) {
          const alert = yield this.alertCtrl.create({
            header: "No Typhoon Centers",
            message: "No typhoon evacuation centers found in the database.",
            buttons: ["OK"]
          });
          yield alert.present();
          return;
        }
        const isOfflineMode = this.offlineStorage.isOfflineMode() || !navigator.onLine;
        this.evacuationCenters.forEach((center) => {
          const lat = Number(center.latitude);
          const lng = Number(center.longitude);
          if (!isNaN(lat) && !isNaN(lng)) {
            const marker2 = L.marker([lat, lng], {
              icon: L.icon({
                iconUrl: "assets/forTyphoon.png",
                iconSize: [40, 40],
                iconAnchor: [20, 40],
                popupAnchor: [0, -40]
              })
            });
            const distance = this.calculateDistance(userLat, userLng, lat, lng);
            marker2.on("click", () => {
              if (isOfflineMode) {
                this.showOfflineMarkerInfo(center, distance);
              } else {
                this.showTransportationOptions(center);
              }
            });
            const isNewCenter = this.newCenterId && center.id.toString() === this.newCenterId;
            const offlineIndicator = isOfflineMode ? "<p><em>\u{1F4F1} Offline Mode - Limited functionality</em></p>" : "<p><em>Click marker for route options</em></p>";
            marker2.bindPopup(`
            <div class="evacuation-popup">
              <h3>\u{1F7E2} ${center.name} ${isNewCenter ? "\u2B50 NEW!" : ""}</h3>
              <p><strong>Type:</strong> Typhoon Center</p>
              <p><strong>Distance:</strong> ${(distance / 1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${center.capacity || "N/A"}</p>
              ${offlineIndicator}
              ${isNewCenter ? "<p><strong>\u{1F195} Recently Added!</strong></p>" : ""}
            </div>
          `);
            if (isNewCenter) {
              marker2.openPopup();
              this.map.setView([lat, lng], 15);
              this.toastCtrl.create({
                message: `\u{1F195} New typhoon evacuation center: ${center.name}`,
                duration: 5e3,
                color: "success",
                position: "top"
              }).then((toast) => toast.present());
            }
            marker2.addTo(this.map);
            console.log(`\u{1F7E2} Added typhoon marker: ${center.name}`);
          }
        });
        if (!isOfflineMode) {
          console.log("\u{1F7E2} Online mode: Auto-routing to 2 nearest typhoon centers...");
          yield this.routeToTwoNearestCenters();
        } else {
          console.log("\u{1F7E2} Offline mode: Showing markers only (no routing)");
        }
        if (this.evacuationCenters.length > 0) {
          const bounds = L.latLngBounds([]);
          bounds.extend([userLat, userLng]);
          this.evacuationCenters.forEach((center) => {
            bounds.extend([Number(center.latitude), Number(center.longitude)]);
          });
          this.map.fitBounds(bounds, { padding: [50, 50] });
        }
      } catch (error) {
        console.error("\u{1F7E2} TYPHOON MAP: Error loading centers", error);
        const toast = yield this.toastCtrl.create({
          message: "Error loading typhoon centers. Please check your connection.",
          duration: 3e3,
          color: "danger"
        });
        yield toast.present();
      }
    });
  }
  // Auto-route to 2 nearest typhoon centers
  routeToTwoNearestCenters() {
    return __async(this, null, function* () {
      if (!this.userLocation || this.evacuationCenters.length === 0) {
        console.log("\u{1F7E2} TYPHOON MAP: No user location or evacuation centers available");
        return;
      }
      try {
        console.log("\u{1F7E2} TYPHOON MAP: Finding 2 nearest typhoon centers...");
        const nearestCenters = this.getTwoNearestCenters(this.userLocation.lat, this.userLocation.lng);
        if (nearestCenters.length === 0) {
          return;
        }
        this.clearRoutes();
        yield this.calculateRoutes(nearestCenters);
      } catch (error) {
        console.error("\u{1F7E2} TYPHOON MAP: Error calculating routes", error);
      }
    });
  }
  getTwoNearestCenters(userLat, userLng) {
    const sorted = [...this.evacuationCenters].sort((a, b) => {
      const distA = this.calculateDistance(userLat, userLng, Number(a.latitude), Number(a.longitude));
      const distB = this.calculateDistance(userLat, userLng, Number(b.latitude), Number(b.longitude));
      return distA - distB;
    });
    return sorted.slice(0, 2);
  }
  clearRoutes() {
    this.map.eachLayer((layer) => {
      if (layer instanceof L.GeoJSON) {
        this.map.removeLayer(layer);
      }
    });
  }
  calculateRoutes(centers) {
    return __async(this, null, function* () {
      for (const center of centers) {
        yield this.calculateRoute(center, "walking");
      }
    });
  }
  calculateRoute(center, travelMode) {
    return __async(this, null, function* () {
      try {
        if (!this.userLocation) {
          console.error("\u{1F7E2} TYPHOON MAP: No user location available for routing");
          return;
        }
        const response = yield fetch(`https://api.mapbox.com/directions/v5/mapbox/${travelMode}/${this.userLocation.lng},${this.userLocation.lat};${center.longitude},${center.latitude}?geometries=geojson&access_token=${environment.mapboxAccessToken}`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = yield response.json();
        if (data.routes && data.routes.length > 0) {
          const route = data.routes[0];
          const routeGeoJSON = {
            type: "Feature",
            geometry: route.geometry,
            properties: {}
          };
          L.geoJSON(routeGeoJSON, {
            style: {
              color: "#008000",
              // Green for typhoon
              weight: 4,
              opacity: 0.8
            }
          }).addTo(this.map);
          console.log(`\u{1F7E2} TYPHOON MAP: Route added to ${center.name}`);
        }
      } catch (error) {
        console.error("\u{1F7E2} TYPHOON MAP: Error calculating route:", error);
      }
    });
  }
  // Show offline marker information when clicked in offline mode
  showOfflineMarkerInfo(center, distance) {
    return __async(this, null, function* () {
      const alert = yield this.alertCtrl.create({
        header: `\u{1F4F1} ${center.name}`,
        message: `
        <div style="text-align: left;">
          <p><strong>Type:</strong> Typhoon Center</p>
          <p><strong>Distance:</strong> ${(distance / 1e3).toFixed(2)} km</p>
          <p><strong>Address:</strong> ${center.address || "N/A"}</p>
          <p><strong>Capacity:</strong> ${center.capacity || "N/A"}</p>
          <p><strong>Status:</strong> ${center.status || "N/A"}</p>
          <br>
          <p><em>\u{1F4F1} Offline Mode: Routing not available. Use external navigation apps for directions.</em></p>
        </div>
      `,
        buttons: [
          {
            text: "Open in Maps",
            handler: () => {
              this.openInExternalMaps(center);
            }
          },
          {
            text: "Close",
            role: "cancel"
          }
        ]
      });
      yield alert.present();
    });
  }
  // Open evacuation center in external maps app
  openInExternalMaps(center) {
    return __async(this, null, function* () {
      const lat = Number(center.latitude);
      const lng = Number(center.longitude);
      const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&travelmode=walking`;
      try {
        window.open(mapsUrl, "_system");
      } catch (error) {
        console.error("Error opening external maps:", error);
        const toast = yield this.toastCtrl.create({
          message: "Could not open external maps app",
          duration: 3e3,
          color: "danger"
        });
        yield toast.present();
      }
    });
  }
  // Show transportation options when marker is clicked (online mode)
  showTransportationOptions(center) {
    return __async(this, null, function* () {
      const isOfflineMode = this.offlineStorage.isOfflineMode() || !navigator.onLine;
      if (isOfflineMode) {
        const distance = this.calculateDistance(this.userLocation?.lat || 0, this.userLocation?.lng || 0, Number(center.latitude), Number(center.longitude));
        yield this.showOfflineMarkerInfo(center, distance);
        return;
      }
      const alert = yield this.alertCtrl.create({
        header: `Route to ${center.name}`,
        message: "Choose your transportation mode:",
        buttons: [
          {
            text: "\u{1F6B6}\u200D\u2642\uFE0F Walk",
            handler: () => {
              this.routeToCenter(center, "walking");
            }
          },
          {
            text: "\u{1F6B4}\u200D\u2642\uFE0F Cycle",
            handler: () => {
              this.routeToCenter(center, "cycling");
            }
          },
          {
            text: "\u{1F697} Drive",
            handler: () => {
              this.routeToCenter(center, "driving");
            }
          },
          {
            text: "Cancel",
            role: "cancel"
          }
        ]
      });
      yield alert.present();
    });
  }
  // Route to specific center with chosen transportation mode
  routeToCenter(center, travelMode) {
    return __async(this, null, function* () {
      if (!this.userLocation)
        return;
      const isOfflineMode = this.offlineStorage.isOfflineMode() || !navigator.onLine;
      if (isOfflineMode) {
        console.log("\u{1F7E2} Offline mode: Cannot calculate routes");
        const toast = yield this.toastCtrl.create({
          message: "\u{1F4F1} Offline mode: Routing not available. Use external navigation apps.",
          duration: 4e3,
          color: "warning"
        });
        yield toast.present();
        yield this.openInExternalMaps(center);
        return;
      }
      try {
        this.clearRoutes();
        let mapboxMode = "walking";
        switch (travelMode) {
          case "walking":
            mapboxMode = "walking";
            break;
          case "cycling":
            mapboxMode = "cycling";
            break;
          case "driving":
            mapboxMode = "driving";
            break;
        }
        const response = yield fetch(`https://api.mapbox.com/directions/v5/mapbox/${mapboxMode}/${this.userLocation.lng},${this.userLocation.lat};${center.longitude},${center.latitude}?geometries=geojson&access_token=${environment.mapboxAccessToken}`);
        if (response.ok) {
          const routeData = yield response.json();
          if (routeData && routeData.routes && routeData.routes.length > 0) {
            const route = routeData.routes[0];
            const routeColor = "#008000";
            const routeLine = L.polyline(route.geometry.coordinates.map((coord) => [coord[1], coord[0]]), {
              color: routeColor,
              weight: 5,
              opacity: 0.8
            });
            routeLine.addTo(this.map);
            const toast = yield this.toastCtrl.create({
              message: `\u{1F7E2} Route: ${(route.distance / 1e3).toFixed(2)}km, ${(route.duration / 60).toFixed(0)}min via ${travelMode}`,
              duration: 4e3,
              color: "success"
            });
            yield toast.present();
            this.map.fitBounds(routeLine.getBounds(), { padding: [50, 50] });
          }
        }
      } catch (error) {
        console.error("\u{1F7E2} TYPHOON MAP: Error calculating individual route:", error);
        const toast = yield this.toastCtrl.create({
          message: "Error calculating route. Please try again.",
          duration: 3e3,
          color: "danger"
        });
        yield toast.present();
      }
    });
  }
  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371e3;
    const \u03C61 = lat1 * Math.PI / 180;
    const \u03C62 = lat2 * Math.PI / 180;
    const \u0394\u03C6 = (lat2 - lat1) * Math.PI / 180;
    const \u0394\u03BB = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(\u0394\u03C6 / 2) * Math.sin(\u0394\u03C6 / 2) + Math.cos(\u03C61) * Math.cos(\u03C62) * Math.sin(\u0394\u03BB / 2) * Math.sin(\u0394\u03BB / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }
  goBack() {
    this.router.navigate(["/tabs/home"]);
  }
  ionViewWillLeave() {
    if (this.map) {
      this.map.remove();
    }
  }
  static {
    this.\u0275fac = function TyphoonMapPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TyphoonMapPage)();
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _TyphoonMapPage, selectors: [["app-typhoon-map"]], decls: 18, vars: 3, consts: [[3, "translucent"], ["color", "success"], ["slot", "start"], [3, "click"], ["name", "chevron-back-outline"], [3, "fullscreen"], ["id", "typhoon-map", 2, "height", "100%", "width", "100%"], [1, "floating-info"], [1, "info-row"], ["name", "thunderstorm", "color", "success"], [1, "info-text"]], template: function TyphoonMapPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header", 0)(1, "ion-toolbar", 1)(2, "ion-buttons", 2)(3, "ion-button", 3);
        \u0275\u0275listener("click", function TyphoonMapPage_Template_ion_button_click_3_listener() {
          return ctx.goBack();
        });
        \u0275\u0275element(4, "ion-icon", 4);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(5, "ion-title");
        \u0275\u0275text(6, "\u{1F7E2} Typhoon Evacuation Centers");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(7, "ion-content", 5);
        \u0275\u0275element(8, "div", 6);
        \u0275\u0275elementStart(9, "div", 7)(10, "ion-card")(11, "ion-card-content")(12, "div", 8);
        \u0275\u0275element(13, "ion-icon", 9);
        \u0275\u0275elementStart(14, "span");
        \u0275\u0275text(15);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(16, "div", 10);
        \u0275\u0275text(17, " Showing evacuation centers specifically for typhoon disasters ");
        \u0275\u0275elementEnd()()()()();
      }
      if (rf & 2) {
        \u0275\u0275property("translucent", true);
        \u0275\u0275advance(7);
        \u0275\u0275property("fullscreen", true);
        \u0275\u0275advance(8);
        \u0275\u0275textInterpolate1("Typhoon Centers: ", ctx.evacuationCenters.length, "");
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonCard, IonCardContent, IonContent, IonHeader, IonIcon, IonTitle, IonToolbar, CommonModule], styles: ["\n\n#typhoon-map[_ngcontent-%COMP%] {\n  height: 100%;\n  width: 100%;\n  z-index: 1;\n}\n.floating-info[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  z-index: 1000;\n  max-width: 250px;\n}\n.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%] {\n  margin: 0;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  padding: 12px;\n}\n.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: var(--ion-color-success);\n  margin-bottom: 4px;\n}\n.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 18px;\n}\n.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: var(--ion-color-medium);\n  line-height: 1.3;\n}\nion-toolbar[_ngcontent-%COMP%] {\n  --background: var(--ion-color-success);\n  --color: white;\n}\nion-title[_ngcontent-%COMP%] {\n  font-weight: 600;\n}\n[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%] {\n  text-align: center;\n  min-width: 200px;\n}\n[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0 0 8px 0;\n  color: var(--ion-color-success);\n  font-size: 16px;\n  font-weight: 600;\n}\n[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 4px 0;\n  font-size: 14px;\n}\n[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  color: var(--ion-color-dark);\n}\n/*# sourceMappingURL=typhoon-map.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TyphoonMapPage, [{
    type: Component,
    args: [{ selector: "app-typhoon-map", standalone: true, imports: [IonicModule, CommonModule], template: '<ion-header [translucent]="true">\r\n  <ion-toolbar color="success">\r\n    <ion-buttons slot="start">\r\n      <ion-button (click)="goBack()">\r\n        <ion-icon name="chevron-back-outline"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n    <ion-title>\u{1F7E2} Typhoon Evacuation Centers</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]="true">\r\n  <div id="typhoon-map" style="height: 100%; width: 100%;"></div>\r\n  \r\n  <!-- Floating info card -->\r\n  <div class="floating-info">\r\n    <ion-card>\r\n      <ion-card-content>\r\n        <div class="info-row">\r\n          <ion-icon name="thunderstorm" color="success"></ion-icon>\r\n          <span>Typhoon Centers: {{ evacuationCenters.length }}</span>\r\n        </div>\r\n        <div class="info-text">\r\n          Showing evacuation centers specifically for typhoon disasters\r\n        </div>\r\n      </ion-card-content>\r\n    </ion-card>\r\n  </div>\r\n</ion-content>\r\n', styles: ["/* src/app/pages/disaster-maps/typhoon-map.page.scss */\n#typhoon-map {\n  height: 100%;\n  width: 100%;\n  z-index: 1;\n}\n.floating-info {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  z-index: 1000;\n  max-width: 250px;\n}\n.floating-info ion-card {\n  margin: 0;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  border-radius: 12px;\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.floating-info ion-card-content {\n  padding: 12px;\n}\n.floating-info .info-row {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: var(--ion-color-success);\n  margin-bottom: 4px;\n}\n.floating-info .info-row ion-icon {\n  font-size: 18px;\n}\n.floating-info .info-text {\n  font-size: 12px;\n  color: var(--ion-color-medium);\n  line-height: 1.3;\n}\nion-toolbar {\n  --background: var(--ion-color-success);\n  --color: white;\n}\nion-title {\n  font-weight: 600;\n}\n:global(.leaflet-popup-content) .evacuation-popup {\n  text-align: center;\n  min-width: 200px;\n}\n:global(.leaflet-popup-content) .evacuation-popup h3 {\n  margin: 0 0 8px 0;\n  color: var(--ion-color-success);\n  font-size: 16px;\n  font-weight: 600;\n}\n:global(.leaflet-popup-content) .evacuation-popup p {\n  margin: 4px 0;\n  font-size: 14px;\n}\n:global(.leaflet-popup-content) .evacuation-popup p strong {\n  color: var(--ion-color-dark);\n}\n/*# sourceMappingURL=typhoon-map.page.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(TyphoonMapPage, { className: "TyphoonMapPage", filePath: "src/app/pages/disaster-maps/typhoon-map.page.ts", lineNumber: 32 });
})();
export {
  TyphoonMapPage
};
//# sourceMappingURL=typhoon-map.page-LNO3BPDH.js.map

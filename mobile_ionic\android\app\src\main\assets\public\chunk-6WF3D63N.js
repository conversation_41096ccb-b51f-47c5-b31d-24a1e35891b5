import{a as st,b as lt,c as pt}from"./chunk-C4VSGXVP.js";import{a as rt}from"./chunk-FULEFYAM.js";import"./chunk-X4DCX5TK.js";import{$a as J,B as T,Bb as it,C as x,Cb as at,E as i,F as r,G as d,Ha as B,I as _,J as S,L as s,M as b,Ma as W,N as P,Na as j,Oa as U,P as E,Pa as G,Q as A,R as N,V as D,Va as V,W as I,Wa as H,Xa as Y,_ as z,_a as K,da as F,eb as Q,g as k,ga as $,ja as R,lb as X,mb as Z,na as q,p as M,tb as tt,ub as et,x as m,yb as nt,z as L,zb as ot}from"./chunk-PBKSAHK2.js";import"./chunk-MBKQLJTW.js";import"./chunk-F3654E4N.js";import"./chunk-FHR3DP7J.js";import"./chunk-A4FGPDGZ.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-AUB5HKS7.js";import"./chunk-RS5W3JWO.js";import"./chunk-LOLLZ3RS.js";import"./chunk-XZOVPSKP.js";import"./chunk-7LH2AG5T.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-SPZFNIGG.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-KY4M3ZA2.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-4EI7TLDT.js";import"./chunk-FED6QSGK.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{a as O,b as w,f as ct,g as h}from"./chunk-2R6CW7ES.js";var l=ct(pt());function ut(C,gt){if(C&1&&(i(0,"div",31)(1,"ion-card")(2,"ion-card-content")(3,"div",32),d(4,"ion-icon",33),i(5,"span"),s(6,"Route to Nearest Center"),r()(),i(7,"div",34)(8,"div",35),d(9,"ion-icon",36),i(10,"span"),s(11),r()(),i(12,"div",35),d(13,"ion-icon",37),i(14,"span"),s(15),r()()()()()()),C&2){let t=S();m(9),x("name",t.travelMode==="walking"?"walk-outline":t.travelMode==="cycling"?"bicycle-outline":"car-outline"),m(2),P("",(t.routeTime/60).toFixed(0)," min"),m(4),P("",(t.routeDistance/1e3).toFixed(2)," km")}}var Lt=(()=>{class C{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.centerCounts={earthquake:0,typhoon:0,flood:0,total:0},this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.userLocation=null,this.loadingCtrl=M(ot),this.toastCtrl=M(it),this.alertCtrl=M(nt),this.http=M(z),this.router=M(F),this.mapboxRouting=M(lt)}ngOnInit(){return h(this,null,function*(){console.log("\u{1F5FA}\uFE0F ALL MAPS: Initializing..."),yield this.loadAllMaps()})}loadAllMaps(){return h(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading all evacuation centers...",spinner:"crescent"});yield t.present();try{let e=yield st.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),o=e.coords.latitude,n=e.coords.longitude;this.userLocation={lat:o,lng:n},console.log(`\u{1F5FA}\uFE0F ALL MAPS: User location [${o}, ${n}]`),this.initializeMap(o,n),yield this.loadAllCenters(o,n),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Showing all ${this.centerCounts.total} evacuation centers`,duration:3e3,color:"secondary",position:"top"})).present()}catch(e){yield t.dismiss(),console.error("\u{1F5FA}\uFE0F ALL MAPS: Error loading map",e),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadAllMaps()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,e){console.log(`\u{1F5FA}\uFE0F ALL MAPS: Initializing map at [${t}, ${e}]`),this.map&&this.map.remove(),this.map=l.map("all-maps").setView([t,e],12),l.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=l.marker([t,e],{icon:l.icon({iconUrl:"assets/icons/user-location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadAllCenters(t,e){return h(this,null,function*(){try{console.log("\u{1F5FA}\uFE0F ALL MAPS: Fetching all evacuation centers...");let o=yield k(this.http.get(`${rt.apiUrl}/evacuation-centers`));if(console.log("\u{1F5FA}\uFE0F ALL MAPS: Total centers received:",o?.length||0),this.evacuationCenters=o||[],this.centerCounts.earthquake=this.evacuationCenters.filter(n=>n.disaster_type==="Earthquake").length,this.centerCounts.typhoon=this.evacuationCenters.filter(n=>n.disaster_type==="Typhoon").length,this.centerCounts.flood=this.evacuationCenters.filter(n=>n.disaster_type==="Flash Flood").length,this.centerCounts.total=this.evacuationCenters.length,console.log("\u{1F5FA}\uFE0F ALL MAPS: Center counts:",this.centerCounts),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Evacuation Centers",message:"No evacuation centers found in the database.",buttons:["OK"]})).present();return}if(this.evacuationCenters.forEach(n=>{let a=Number(n.latitude),g=Number(n.longitude);if(!isNaN(a)&&!isNaN(g)){let c="assets/Location.png",p="\u26AA";switch(n.disaster_type){case"Earthquake":c="assets/forEarthquake.png",p="\u{1F7E0}";break;case"Typhoon":c="assets/forTyphoon.png",p="\u{1F7E2}";break;case"Flood":c="assets/forFlood.png",p="\u{1F535}";break}let u=l.marker([a,g],{icon:l.icon({iconUrl:c,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),f=this.calculateDistance(t,e,a,g);u.on("click",()=>{this.showTransportationOptions(n)}),u.bindPopup(`
            <div class="evacuation-popup">
              <h3>${p} ${n.name}</h3>
              <p><strong>Type:</strong> ${n.disaster_type||"General"}</p>
              <p><strong>Distance:</strong> ${(f/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${n.capacity||"N/A"}</p>
              <p><em>Click marker for route options</em></p>
            </div>
          `),u.addTo(this.map),console.log(`\u{1F5FA}\uFE0F Added ${n.disaster_type} marker: ${n.name}`)}}),this.evacuationCenters.length>0){let n=l.latLngBounds([]);n.extend([t,e]),this.evacuationCenters.forEach(a=>{n.extend([Number(a.latitude),Number(a.longitude)])}),this.map.fitBounds(n,{padding:[50,50]})}}catch(o){console.error("\u{1F5FA}\uFE0F ALL MAPS: Error loading centers",o),yield(yield this.toastCtrl.create({message:"Error loading evacuation centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}calculateDistance(t,e,o,n){let g=t*Math.PI/180,c=o*Math.PI/180,p=(o-t)*Math.PI/180,u=(n-e)*Math.PI/180,f=Math.sin(p/2)*Math.sin(p/2)+Math.cos(g)*Math.cos(c)*Math.sin(u/2)*Math.sin(u/2);return 6371e3*(2*Math.atan2(Math.sqrt(f),Math.sqrt(1-f)))}routeToTwoNearestCenters(){return h(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F5FA}\uFE0F ALL MAPS: No user location or evacuation centers available");return}try{console.log("\u{1F5FA}\uFE0F ALL MAPS: Finding 2 nearest centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0){yield(yield this.toastCtrl.create({message:"No evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(t),yield this.calculateRoutes(t),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Showing routes to ${t.length} nearest centers via ${this.travelMode}`,duration:3e3,color:"success",position:"top"})).present()}catch(t){console.error("\u{1F5FA}\uFE0F ALL MAPS: Error calculating routes",t),yield(yield this.toastCtrl.create({message:"Error calculating routes. Please try again.",duration:3e3,color:"danger"})).present()}})}getTwoNearestCenters(t,e){return this.evacuationCenters.map(n=>w(O({},n),{distance:this.calculateDistance(t,e,Number(n.latitude),Number(n.longitude))})).sort((n,a)=>n.distance-a.distance).slice(0,2)}addPulsingMarkers(t){t.forEach((e,o)=>{let n=Number(e.latitude),a=Number(e.longitude);if(!isNaN(n)&&!isNaN(a)){let g="assets/Location.png",c="#3880ff";e.disaster_type==="Earthquake"?(g="assets/forEarthquake.png",c="#ff9500"):e.disaster_type==="Typhoon"?(g="assets/forTyphoon.png",c="#2dd36f"):e.disaster_type==="Flood"&&(g="assets/forFlood.png",c="#3dc2ff");let p=l.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <div class="pulse" style="background-color: ${c}"></div>
              <img src="${g}" class="marker-icon" />
              <div class="marker-label">${o+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),u=l.marker([n,a],{icon:p});u.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${o+1}</h3>
            <h4>${e.name}</h4>
            <p><strong>Type:</strong> ${e.disaster_type}</p>
            <p><strong>Distance:</strong> ${(e.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${e.capacity||"N/A"}</p>
          </div>
        `),u.addTo(this.map),this.nearestMarkers.push(u)}})}calculateRoutes(t){return h(this,null,function*(){if(this.userLocation){this.routeLayer=l.layerGroup().addTo(this.map);for(let e=0;e<t.length;e++){let o=t[e],n=Number(o.latitude),a=Number(o.longitude);if(!isNaN(n)&&!isNaN(a))try{let g=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),c=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,a,n,g,{geometries:"geojson",overview:"simplified",steps:!1});if(c&&c.routes&&c.routes.length>0){let p=c.routes[0],u="#3880ff";o.disaster_type==="Earthquake"?u="#ff9500":o.disaster_type==="Typhoon"?u="#2dd36f":o.disaster_type==="Flash Flood"&&(u="#3dc2ff"),l.polyline(p.geometry.coordinates.map(v=>[v[1],v[0]]),{color:u,weight:4,opacity:.8,dashArray:e===0?void 0:"10, 10"}).addTo(this.routeLayer),e===0&&(this.routeTime=p.duration,this.routeDistance=p.distance),console.log(`\u{1F5FA}\uFE0F Route ${e+1}: ${(p.distance/1e3).toFixed(2)}km, ${(p.duration/60).toFixed(0)}min`)}}catch(g){console.error(`\u{1F5FA}\uFE0F Error calculating route to center ${e+1}:`,g)}}}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers.forEach(t=>{this.map.removeLayer(t)}),this.nearestMarkers=[],this.routeTime=0,this.routeDistance=0}onTravelModeChange(t){let e=t.detail.value;(e==="walking"||e==="cycling"||e==="driving")&&this.changeTravelMode(e)}changeTravelMode(t){return h(this,null,function*(){this.travelMode=t,yield(yield this.toastCtrl.create({message:`\u{1F6B6}\u200D\u2642\uFE0F Travel mode changed to ${t}`,duration:2e3,color:"primary"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}showTransportationOptions(t){return h(this,null,function*(){yield(yield this.alertCtrl.create({header:`Route to ${t.name}`,message:"Choose your transportation mode:",buttons:[{text:"\u{1F6B6}\u200D\u2642\uFE0F Walk",handler:()=>{this.routeToCenter(t,"walking")}},{text:"\u{1F6B4}\u200D\u2642\uFE0F Cycle",handler:()=>{this.routeToCenter(t,"cycling")}},{text:"\u{1F697} Drive",handler:()=>{this.routeToCenter(t,"driving")}},{text:"Cancel",role:"cancel"}]})).present()})}routeToCenter(t,e){return h(this,null,function*(){if(this.userLocation)try{this.clearRoutes();let o=Number(t.latitude),n=Number(t.longitude);if(!isNaN(o)&&!isNaN(n)){let a=this.mapboxRouting.convertTravelModeToProfile(e),g=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,n,o,a,{geometries:"geojson",overview:"full",steps:!1});if(g&&g.routes&&g.routes.length>0){let c=g.routes[0],p="#3880ff",u="\u{1F535}";t.disaster_type==="Earthquake"?(p="#ff9500",u="\u{1F7E0}"):t.disaster_type==="Typhoon"?(p="#2dd36f",u="\u{1F7E2}"):t.disaster_type==="Flash Flood"&&(p="#3dc2ff",u="\u{1F535}"),this.routeLayer=l.layerGroup().addTo(this.map);let f=l.polyline(c.geometry.coordinates.map(y=>[y[1],y[0]]),{color:p,weight:5,opacity:.8});f.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`${u} Route: ${(c.distance/1e3).toFixed(2)}km, ${(c.duration/60).toFixed(0)}min via ${e}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(f.getBounds(),{padding:[50,50]})}}}catch(o){console.error("\u{1F5FA}\uFE0F Error routing to center:",o),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}goBack(){this.router.navigate(["/tabs/home"])}ionViewWillLeave(){this.clearRoutes(),this.map&&this.map.remove()}static{this.\u0275fac=function(e){return new(e||C)}}static{this.\u0275cmp=L({type:C,selectors:[["app-all-maps"]],decls:66,vars:8,consts:[[3,"translucent"],["color","secondary"],["slot","start"],[3,"click"],["name","chevron-back-outline"],[3,"fullscreen"],["id","all-maps",2,"height","100%","width","100%"],[1,"floating-info"],[1,"info-header"],["name","map","color","secondary"],[1,"disaster-counts"],[1,"count-row"],[1,"disaster-icon"],[1,"disaster-label"],[1,"disaster-count"],[1,"info-text"],[1,"transport-controls"],[1,"transport-header"],["name","navigate-outline","color","primary"],[3,"ngModelChange","ionChange","ngModel"],["value","walking"],["name","walk-outline"],["value","cycling"],["name","bicycle-outline"],["value","driving"],["name","car-outline"],["class","route-info",4,"ngIf"],["vertical","bottom","horizontal","end","slot","fixed"],["color","primary",3,"click"],["name","navigate-outline"],[1,"fab-label"],[1,"route-info"],[1,"route-header"],["name","time-outline","color","success"],[1,"route-details"],[1,"route-item"],[3,"name"],["name","location-outline"]],template:function(e,o){e&1&&(i(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),_("click",function(){return o.goBack()}),d(4,"ion-icon",4),r()(),i(5,"ion-title"),s(6,"\u{1F5FA}\uFE0F All Evacuation Centers"),r()()(),i(7,"ion-content",5),d(8,"div",6),i(9,"div",7)(10,"ion-card")(11,"ion-card-content")(12,"div",8),d(13,"ion-icon",9),i(14,"span"),s(15),r()(),i(16,"div",10)(17,"div",11)(18,"span",12),s(19,"\u{1F7E0}"),r(),i(20,"span",13),s(21,"Earthquake:"),r(),i(22,"span",14),s(23),r()(),i(24,"div",11)(25,"span",12),s(26,"\u{1F7E2}"),r(),i(27,"span",13),s(28,"Typhoon:"),r(),i(29,"span",14),s(30),r()(),i(31,"div",11)(32,"span",12),s(33,"\u{1F535}"),r(),i(34,"span",13),s(35,"Flood:"),r(),i(36,"span",14),s(37),r()()(),i(38,"div",15),s(39," Complete overview of all evacuation centers by disaster type "),r()()()(),i(40,"div",16)(41,"ion-card")(42,"ion-card-content")(43,"div",17),d(44,"ion-icon",18),i(45,"span"),s(46,"Travel Mode"),r()(),i(47,"ion-segment",19),N("ngModelChange",function(a){return A(o.travelMode,a)||(o.travelMode=a),a}),_("ionChange",function(a){return o.onTravelModeChange(a)}),i(48,"ion-segment-button",20),d(49,"ion-icon",21),i(50,"ion-label"),s(51,"Walk"),r()(),i(52,"ion-segment-button",22),d(53,"ion-icon",23),i(54,"ion-label"),s(55,"Bike"),r()(),i(56,"ion-segment-button",24),d(57,"ion-icon",25),i(58,"ion-label"),s(59,"Drive"),r()()()()()(),T(60,ut,16,3,"div",26),i(61,"ion-fab",27)(62,"ion-fab-button",28),_("click",function(){return o.routeToTwoNearestCenters()}),d(63,"ion-icon",29),r(),i(64,"ion-label",30),s(65,"Route to 2 Nearest Centers"),r()()()),e&2&&(x("translucent",!0),m(7),x("fullscreen",!0),m(8),P("All Centers: ",o.centerCounts.total,""),m(8),b(o.centerCounts.earthquake),m(7),b(o.centerCounts.typhoon),m(7),b(o.centerCounts.flood),m(10),E("ngModel",o.travelMode),m(13),x("ngIf",o.routeTime&&o.routeDistance))},dependencies:[at,W,j,U,G,V,H,Y,K,J,Q,X,Z,tt,et,B,I,D,q,$,R],styles:["#all-maps[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:280px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-secondary);margin-bottom:8px}.floating-info[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]{margin:8px 0}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;margin:4px 0;font-size:13px}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-icon[_ngcontent-%COMP%]{font-size:14px;width:16px;text-align:center}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-label[_ngcontent-%COMP%]{flex:1;color:var(--ion-color-dark)}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-count[_ngcontent-%COMP%]{font-weight:600;color:var(--ion-color-secondary);min-width:20px;text-align:right}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:11px;color:var(--ion-color-medium);line-height:1.3;margin-top:8px;padding-top:8px;border-top:1px solid var(--ion-color-light)}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-secondary);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}.transport-controls[_ngcontent-%COMP%]{position:absolute;bottom:120px;left:20px;z-index:1000;max-width:280px}.transport-controls[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.transport-controls[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.transport-controls[_ngcontent-%COMP%]   .transport-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-primary);margin-bottom:8px}.transport-controls[_ngcontent-%COMP%]   .transport-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.transport-controls[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{--background: rgba(var(--ion-color-light-rgb), .3);border-radius:8px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--color-checked: var(--ion-color-primary);--indicator-color: var(--ion-color-primary);min-height:40px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:2px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-info[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;z-index:1000;max-width:200px}.route-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.route-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-success);margin-bottom:8px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.route-info[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;color:var(--ion-color-dark)}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;color:var(--ion-color-primary)}.fab-label[_ngcontent-%COMP%]{position:absolute;right:60px;top:50%;transform:translateY(-50%);background:#000000b3;color:#fff;padding:4px 8px;border-radius:4px;font-size:12px;white-space:nowrap;pointer-events:none}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse-container[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse[_ngcontent-%COMP%]{position:absolute;width:60px;height:60px;border-radius:50%;opacity:.6;animation:_ngcontent-%COMP%_pulse 2s infinite;z-index:1}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-icon[_ngcontent-%COMP%]{width:40px;height:40px;z-index:2;position:relative}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-label[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:var(--ion-color-primary);color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;z-index:3;border:2px solid white}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.2);opacity:.4}to{transform:scale(.8);opacity:.8}}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-secondary);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:4px 0;color:var(--ion-color-dark);font-size:14px;font-weight:500}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup.nearest-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-success);font-size:18px}"]})}}return C})();export{Lt as AllMapsPage};

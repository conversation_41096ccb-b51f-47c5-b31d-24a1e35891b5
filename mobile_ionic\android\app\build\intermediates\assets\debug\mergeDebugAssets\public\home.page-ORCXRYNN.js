import {
  OfflineMapService
} from "./chunk-YBHKUHM7.js";
import {
  Geolocation,
  MapboxRoutingService,
  require_leaflet_src
} from "./chunk-WDZAZAAD.js";
import {
  OfflineStorageService
} from "./chunk-73VFBDTI.js";
import {
  FcmService
} from "./chunk-RDFT5QPW.js";
import {
  environment
} from "./chunk-I7MI46CM.js";
import "./chunk-TAZAZ6IP.js";
import {
  AlertController,
  CommonModule,
  Component,
  EventEmitter,
  HttpClient,
  Injectable,
  IonBadge,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonContent,
  IonHeader,
  IonIcon,
  IonProgressBar,
  IonText,
  IonTitle,
  IonToolbar,
  IonicModule,
  LoadingController,
  NgClass,
  NgIf,
  Output,
  Router,
  ToastController,
  interval,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import {
  __async,
  __spreadProps,
  __spreadValues,
  __toESM
} from "./chunk-UL2P3LPA.js";

// src/app/services/offline-routing.service.ts
var L = __toESM(require_leaflet_src());
var OfflineRoutingService = class _OfflineRoutingService {
  constructor(http, offlineStorage, mapboxRouting) {
    this.http = http;
    this.offlineStorage = offlineStorage;
    this.mapboxRouting = mapboxRouting;
  }
  /**
   * Get route with offline fallback
   */
  getRoute(startLat, startLng, endLat, endLng, travelMode = "walking") {
    return __async(this, null, function* () {
      const cachedRoute = yield this.offlineStorage.getRoute(startLat, startLng, endLat, endLng, travelMode);
      if (cachedRoute) {
        console.log("\u{1F4CD} Using cached route");
        return {
          coordinates: JSON.parse(cachedRoute.route_data),
          distance: cachedRoute.distance,
          duration: cachedRoute.duration,
          isOffline: true,
          travelMode: cachedRoute.travel_mode
        };
      }
      if (this.offlineStorage.isOnline() && !this.offlineStorage.isOfflineMode()) {
        try {
          const onlineRoute = yield this.getOnlineRoute(startLat, startLng, endLat, endLng, travelMode);
          if (onlineRoute) {
            yield this.cacheRoute(startLat, startLng, endLat, endLng, onlineRoute, travelMode);
            return __spreadProps(__spreadValues({}, onlineRoute), {
              isOffline: false
            });
          }
        } catch (error) {
          console.warn("\u26A0\uFE0F Online routing failed, falling back to offline:", error);
        }
      }
      console.log("\u26A0\uFE0F Offline mode: No routing available. Show evacuation centers only.");
      return null;
    });
  }
  /**
   * Get route from online service (Mapbox)
   */
  getOnlineRoute(startLat, startLng, endLat, endLng, travelMode) {
    return __async(this, null, function* () {
      try {
        const response = yield this.mapboxRouting.getDirections(startLng, startLat, endLng, endLat, travelMode);
        if (response.routes && response.routes.length > 0) {
          const route = response.routes[0];
          const coordinates = route.geometry.coordinates.map((coord) => [coord[1], coord[0]]);
          return {
            coordinates,
            distance: route.distance,
            duration: route.duration,
            isOffline: false,
            travelMode
          };
        }
      } catch (error) {
        console.error("\u274C Mapbox routing error:", error);
      }
      return null;
    });
  }
  /**
   * Cache route for offline use
   */
  cacheRoute(startLat, startLng, endLat, endLng, route, travelMode) {
    return __async(this, null, function* () {
      const offlineRoute = {
        start_lat: startLat,
        start_lng: startLng,
        end_lat: endLat,
        end_lng: endLng,
        disaster_type: "general",
        // Can be updated based on context
        route_data: JSON.stringify(route.coordinates),
        distance: route.distance,
        duration: route.duration,
        travel_mode: travelMode
      };
      yield this.offlineStorage.saveRoute(offlineRoute);
    });
  }
  /**
   * Generate offline route - NO ROUTING, just show distance and direction
   */
  generateOfflineRoute(startLat, startLng, endLat, endLng, travelMode) {
    console.log("\u26A0\uFE0F No routing available in offline mode - showing distance only");
    return null;
  }
  /**
   * Calculate distance between two points using Haversine formula
   */
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371;
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }
  toRadians(degrees) {
    return degrees * (Math.PI / 180);
  }
  /**
   * Estimate travel duration based on distance and mode
   */
  estimateDuration(distanceMeters, travelMode) {
    const distanceKm = distanceMeters / 1e3;
    const speeds = {
      walking: 5,
      cycling: 15,
      driving: 40
    };
    const speed = speeds[travelMode] || speeds.walking;
    return distanceKm / speed * 3600;
  }
  /**
   * Add route to Leaflet map
   */
  addRouteToMap(map, route, color = "#007bff", weight = 4) {
    const polyline2 = L.polyline(route.coordinates, {
      color,
      weight,
      opacity: route.isOffline ? 0.7 : 1,
      dashArray: route.isOffline ? "10, 5" : void 0
      // Dashed line for offline routes
    }).addTo(map);
    const distanceKm = (route.distance / 1e3).toFixed(1);
    const durationMin = Math.round(route.duration / 60);
    const routeType = route.isOffline ? "Offline Route" : "Online Route";
    polyline2.bindPopup(`
      <div class="route-popup">
        <strong>${routeType}</strong><br>
        Distance: ${distanceKm} km<br>
        Duration: ${durationMin} min<br>
        Mode: ${route.travelMode}
      </div>
    `);
    return polyline2;
  }
  /**
   * Pre-cache routes for common evacuation centers
   */
  preCacheRoutes(_0, _1, _2) {
    return __async(this, arguments, function* (userLat, userLng, evacuationCenters, travelModes = ["walking", "cycling"]) {
      if (!this.offlineStorage.isOnline()) {
        console.log("\u26A0\uFE0F Cannot pre-cache routes while offline");
        return;
      }
      console.log("\u{1F504} Pre-caching routes for evacuation centers...");
      let cachedCount = 0;
      for (const center of evacuationCenters) {
        for (const mode of travelModes) {
          try {
            yield this.getRoute(userLat, userLng, center.latitude, center.longitude, mode);
            cachedCount++;
            yield new Promise((resolve) => setTimeout(resolve, 500));
          } catch (error) {
            console.warn(`Failed to cache route to ${center.name}:`, error);
          }
        }
      }
      console.log(`\u2705 Pre-cached ${cachedCount} routes`);
    });
  }
  static {
    this.\u0275fac = function OfflineRoutingService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OfflineRoutingService)(\u0275\u0275inject(HttpClient), \u0275\u0275inject(OfflineStorageService), \u0275\u0275inject(MapboxRoutingService));
    };
  }
  static {
    this.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _OfflineRoutingService, factory: _OfflineRoutingService.\u0275fac, providedIn: "root" });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OfflineRoutingService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }, { type: OfflineStorageService }, { type: MapboxRoutingService }], null);
})();

// src/app/components/offline-banner.component.ts
function OfflineBannerComponent_ion_button_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-button", 9);
    \u0275\u0275listener("click", function OfflineBannerComponent_ion_button_9_Template_ion_button_click_0_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.enableOfflineMode());
    });
    \u0275\u0275text(1, " Continue Offline ");
    \u0275\u0275elementEnd();
  }
}
function OfflineBannerComponent_ion_button_10_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-button", 9);
    \u0275\u0275listener("click", function OfflineBannerComponent_ion_button_10_Template_ion_button_click_0_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.syncData());
    });
    \u0275\u0275element(1, "ion-icon", 10);
    \u0275\u0275text(2, " Sync ");
    \u0275\u0275elementEnd();
  }
}
function OfflineBannerComponent_ion_button_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-button", 9);
    \u0275\u0275listener("click", function OfflineBannerComponent_ion_button_11_Template_ion_button_click_0_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.prepareOfflineData());
    });
    \u0275\u0275element(1, "ion-icon", 11);
    \u0275\u0275text(2, " Prepare ");
    \u0275\u0275elementEnd();
  }
}
function OfflineBannerComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 12);
    \u0275\u0275element(1, "ion-progress-bar", 13);
    \u0275\u0275elementStart(2, "div", 14);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("value", ctx_r1.preparationProgress);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.preparationStatus);
  }
}
var OfflineBannerComponent = class _OfflineBannerComponent {
  constructor(offlineStorage, offlineMap, offlineRouting, alertCtrl, loadingCtrl, toastCtrl) {
    this.offlineStorage = offlineStorage;
    this.offlineMap = offlineMap;
    this.offlineRouting = offlineRouting;
    this.alertCtrl = alertCtrl;
    this.loadingCtrl = loadingCtrl;
    this.toastCtrl = toastCtrl;
    this.offlineModeEnabled = new EventEmitter();
    this.dataSynced = new EventEmitter();
    this.isOnline = navigator.onLine;
    this.isOfflineMode = false;
    this.hasOfflineData = false;
    this.isPreparingData = false;
    this.preparationProgress = 0;
    this.preparationStatus = "";
    this.lastSyncTime = null;
  }
  ngOnInit() {
    return __async(this, null, function* () {
      this.onlineListener = () => {
        this.isOnline = true;
        this.checkDataStatus();
      };
      this.offlineListener = () => {
        this.isOnline = false;
        this.checkDataStatus();
      };
      window.addEventListener("online", this.onlineListener);
      window.addEventListener("offline", this.offlineListener);
      yield this.checkDataStatus();
    });
  }
  ngOnDestroy() {
    if (this.onlineListener) {
      window.removeEventListener("online", this.onlineListener);
    }
    if (this.offlineListener) {
      window.removeEventListener("offline", this.offlineListener);
    }
  }
  checkDataStatus() {
    return __async(this, null, function* () {
      this.isOfflineMode = this.offlineStorage.isOfflineMode();
      this.hasOfflineData = yield this.offlineStorage.isDataAvailable();
      this.lastSyncTime = this.offlineStorage.getLastSyncTime();
    });
  }
  getBannerClass() {
    if (this.isPreparingData)
      return "preparing";
    if (!this.isOnline)
      return "offline";
    if (this.isOnline && !this.hasOfflineData)
      return "warning";
    return "online";
  }
  getBannerIcon() {
    if (this.isPreparingData)
      return "download-outline";
    if (!this.isOnline)
      return "wifi-outline";
    if (this.isOnline && !this.hasOfflineData)
      return "warning-outline";
    return "checkmark-circle-outline";
  }
  getBannerTitle() {
    if (this.isPreparingData)
      return "Preparing Offline Data";
    if (!this.isOnline && this.hasOfflineData)
      return "Offline Mode Available";
    if (!this.isOnline && !this.hasOfflineData)
      return "No Internet Connection";
    if (this.isOnline && !this.hasOfflineData)
      return "Offline Data Not Ready";
    return "Connected & Ready";
  }
  getBannerSubtitle() {
    if (this.isPreparingData)
      return this.preparationStatus;
    if (!this.isOnline && this.hasOfflineData)
      return "Emergency data is available offline";
    if (!this.isOnline && !this.hasOfflineData)
      return "Limited functionality available";
    if (this.isOnline && !this.hasOfflineData)
      return "Prepare offline data for emergencies";
    if (this.lastSyncTime) {
      const syncDate = new Date(this.lastSyncTime);
      return `Last synced: ${syncDate.toLocaleDateString()}`;
    }
    return "All systems operational";
  }
  showOfflineButton() {
    return !this.isOnline && !this.isOfflineMode && this.hasOfflineData;
  }
  showSyncButton() {
    return this.isOnline && this.hasOfflineData && !this.isPreparingData;
  }
  showPrepareButton() {
    return this.isOnline && !this.hasOfflineData && !this.isPreparingData;
  }
  enableOfflineMode() {
    return __async(this, null, function* () {
      const alert = yield this.alertCtrl.create({
        header: "Enable Offline Mode",
        message: "Switch to offline mode to access cached evacuation data and maps?",
        buttons: [
          {
            text: "Cancel",
            role: "cancel"
          },
          {
            text: "Continue Offline",
            handler: () => {
              this.offlineStorage.setOfflineMode(true);
              this.isOfflineMode = true;
              this.offlineModeEnabled.emit();
              this.showToast("Offline mode enabled. Using cached data.", "success");
            }
          }
        ]
      });
      yield alert.present();
    });
  }
  syncData() {
    return __async(this, null, function* () {
      const loading = yield this.loadingCtrl.create({
        message: "Syncing evacuation data..."
      });
      yield loading.present();
      try {
        const success = yield this.offlineStorage.syncEvacuationCenters();
        yield loading.dismiss();
        if (success) {
          this.dataSynced.emit();
          this.checkDataStatus();
          this.showToast("Data synced successfully", "success");
        } else {
          this.showToast("Sync failed. Please try again.", "danger");
        }
      } catch (error) {
        yield loading.dismiss();
        this.showToast("Sync error. Check your connection.", "danger");
      }
    });
  }
  prepareOfflineData() {
    return __async(this, null, function* () {
      const alert = yield this.alertCtrl.create({
        header: "Prepare Offline Data",
        message: "Download evacuation centers and map data for offline use? This may take a few minutes and use mobile data.",
        buttons: [
          {
            text: "Cancel",
            role: "cancel"
          },
          {
            text: "Download",
            handler: () => this.startDataPreparation()
          }
        ]
      });
      yield alert.present();
    });
  }
  startDataPreparation() {
    return __async(this, null, function* () {
      this.isPreparingData = true;
      this.preparationProgress = 0;
      try {
        this.preparationStatus = "Downloading evacuation centers...";
        const syncSuccess = yield this.offlineStorage.syncEvacuationCenters();
        this.preparationProgress = 0.3;
        if (!syncSuccess) {
          throw new Error("Failed to sync evacuation centers");
        }
        this.preparationStatus = "Getting your location...";
        const position = yield Geolocation.getCurrentPosition({
          enableHighAccuracy: true,
          timeout: 1e4
        });
        this.preparationProgress = 0.4;
        const userLat = position.coords.latitude;
        const userLng = position.coords.longitude;
        this.preparationStatus = "Downloading map tiles...";
        yield this.offlineMap.preloadMapTiles(
          userLat,
          userLng,
          25,
          // 25km radius
          (current, total) => {
            const mapProgress = 0.4 + current / total * 0.4;
            this.preparationProgress = mapProgress;
            this.preparationStatus = `Downloading map tiles... ${current}/${total}`;
          }
        );
        this.preparationStatus = "Pre-caching routes...";
        const centers = yield this.offlineStorage.getEvacuationCenters();
        yield this.offlineRouting.preCacheRoutes(userLat, userLng, centers.slice(0, 10));
        this.preparationProgress = 1;
        this.preparationStatus = "Preparation complete!";
        yield this.checkDataStatus();
        setTimeout(() => {
          this.isPreparingData = false;
          this.showToast("Offline data prepared successfully!", "success");
        }, 1e3);
      } catch (error) {
        console.error("Data preparation failed:", error);
        this.isPreparingData = false;
        this.showToast("Failed to prepare offline data. Please try again.", "danger");
      }
    });
  }
  showToast(message, color) {
    return __async(this, null, function* () {
      const toast = yield this.toastCtrl.create({
        message,
        duration: 3e3,
        color,
        position: "bottom"
      });
      yield toast.present();
    });
  }
  static {
    this.\u0275fac = function OfflineBannerComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OfflineBannerComponent)(\u0275\u0275directiveInject(OfflineStorageService), \u0275\u0275directiveInject(OfflineMapService), \u0275\u0275directiveInject(OfflineRoutingService), \u0275\u0275directiveInject(AlertController), \u0275\u0275directiveInject(LoadingController), \u0275\u0275directiveInject(ToastController));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _OfflineBannerComponent, selectors: [["app-offline-banner"]], outputs: { offlineModeEnabled: "offlineModeEnabled", dataSynced: "dataSynced" }, decls: 13, vars: 8, consts: [[1, "offline-banner", 3, "ngClass"], [1, "banner-content"], [1, "banner-icon", 3, "name"], [1, "banner-text"], [1, "banner-title"], [1, "banner-subtitle"], [1, "banner-actions"], ["fill", "clear", "size", "small", "color", "light", 3, "click", 4, "ngIf"], ["class", "preparation-progress", 4, "ngIf"], ["fill", "clear", "size", "small", "color", "light", 3, "click"], ["name", "sync-outline"], ["name", "download-outline"], [1, "preparation-progress"], [3, "value"], [1, "progress-text"]], template: function OfflineBannerComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "div", 0)(1, "div", 1);
        \u0275\u0275element(2, "ion-icon", 2);
        \u0275\u0275elementStart(3, "div", 3)(4, "div", 4);
        \u0275\u0275text(5);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(6, "div", 5);
        \u0275\u0275text(7);
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(8, "div", 6);
        \u0275\u0275template(9, OfflineBannerComponent_ion_button_9_Template, 2, 0, "ion-button", 7)(10, OfflineBannerComponent_ion_button_10_Template, 3, 0, "ion-button", 7)(11, OfflineBannerComponent_ion_button_11_Template, 3, 0, "ion-button", 7);
        \u0275\u0275elementEnd()();
        \u0275\u0275template(12, OfflineBannerComponent_div_12_Template, 4, 2, "div", 8);
        \u0275\u0275elementEnd();
      }
      if (rf & 2) {
        \u0275\u0275property("ngClass", ctx.getBannerClass());
        \u0275\u0275advance(2);
        \u0275\u0275property("name", ctx.getBannerIcon());
        \u0275\u0275advance(3);
        \u0275\u0275textInterpolate(ctx.getBannerTitle());
        \u0275\u0275advance(2);
        \u0275\u0275textInterpolate(ctx.getBannerSubtitle());
        \u0275\u0275advance(2);
        \u0275\u0275property("ngIf", ctx.showOfflineButton());
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.showSyncButton());
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.showPrepareButton());
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.isPreparingData);
      }
    }, dependencies: [IonicModule, IonButton, IonIcon, IonProgressBar, CommonModule, NgClass, NgIf], styles: ["\n\n.offline-banner[_ngcontent-%COMP%] {\n  padding: 12px 16px;\n  margin: 8px 16px;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n.offline-banner.online[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #28a745,\n      #20c997);\n  color: white;\n}\n.offline-banner.offline[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #dc3545,\n      #fd7e14);\n  color: white;\n}\n.offline-banner.preparing[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #007bff,\n      #6610f2);\n  color: white;\n}\n.offline-banner.warning[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #ffc107,\n      #fd7e14);\n  color: #212529;\n}\n.banner-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n.banner-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n  flex-shrink: 0;\n}\n.banner-text[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.banner-title[_ngcontent-%COMP%] {\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 2px;\n}\n.banner-subtitle[_ngcontent-%COMP%] {\n  font-size: 12px;\n  opacity: 0.9;\n}\n.banner-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 8px;\n}\n.preparation-progress[_ngcontent-%COMP%] {\n  margin-top: 12px;\n}\n.progress-text[_ngcontent-%COMP%] {\n  font-size: 12px;\n  text-align: center;\n  margin-top: 4px;\n  opacity: 0.9;\n}\nion-progress-bar[_ngcontent-%COMP%] {\n  height: 4px;\n  border-radius: 2px;\n}\n/*# sourceMappingURL=offline-banner.component.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OfflineBannerComponent, [{
    type: Component,
    args: [{ selector: "app-offline-banner", template: `
    <div class="offline-banner" [ngClass]="getBannerClass()">
      <div class="banner-content">
        <ion-icon [name]="getBannerIcon()" class="banner-icon"></ion-icon>
        <div class="banner-text">
          <div class="banner-title">{{ getBannerTitle() }}</div>
          <div class="banner-subtitle">{{ getBannerSubtitle() }}</div>
        </div>
        <div class="banner-actions">
          <ion-button 
            *ngIf="showOfflineButton()" 
            fill="clear" 
            size="small" 
            color="light"
            (click)="enableOfflineMode()">
            Continue Offline
          </ion-button>
          <ion-button 
            *ngIf="showSyncButton()" 
            fill="clear" 
            size="small" 
            color="light"
            (click)="syncData()">
            <ion-icon name="sync-outline"></ion-icon>
            Sync
          </ion-button>
          <ion-button 
            *ngIf="showPrepareButton()" 
            fill="clear" 
            size="small" 
            color="light"
            (click)="prepareOfflineData()">
            <ion-icon name="download-outline"></ion-icon>
            Prepare
          </ion-button>
        </div>
      </div>
      
      <!-- Progress bar for data preparation -->
      <div *ngIf="isPreparingData" class="preparation-progress">
        <ion-progress-bar [value]="preparationProgress"></ion-progress-bar>
        <div class="progress-text">{{ preparationStatus }}</div>
      </div>
    </div>
  `, standalone: true, imports: [IonicModule, CommonModule], styles: ["/* angular:styles/component:scss;18ee9a1c867cd86999fa83369685740e89a7a05a5d70f49f62f0a64091e9dcab;C:/Users/<USER>/Lastna/LastProject.1/mobile_ionic/src/app/components/offline-banner.component.ts */\n.offline-banner {\n  padding: 12px 16px;\n  margin: 8px 16px;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n.offline-banner.online {\n  background:\n    linear-gradient(\n      135deg,\n      #28a745,\n      #20c997);\n  color: white;\n}\n.offline-banner.offline {\n  background:\n    linear-gradient(\n      135deg,\n      #dc3545,\n      #fd7e14);\n  color: white;\n}\n.offline-banner.preparing {\n  background:\n    linear-gradient(\n      135deg,\n      #007bff,\n      #6610f2);\n  color: white;\n}\n.offline-banner.warning {\n  background:\n    linear-gradient(\n      135deg,\n      #ffc107,\n      #fd7e14);\n  color: #212529;\n}\n.banner-content {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n.banner-icon {\n  font-size: 24px;\n  flex-shrink: 0;\n}\n.banner-text {\n  flex: 1;\n}\n.banner-title {\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 2px;\n}\n.banner-subtitle {\n  font-size: 12px;\n  opacity: 0.9;\n}\n.banner-actions {\n  display: flex;\n  gap: 8px;\n}\n.preparation-progress {\n  margin-top: 12px;\n}\n.progress-text {\n  font-size: 12px;\n  text-align: center;\n  margin-top: 4px;\n  opacity: 0.9;\n}\nion-progress-bar {\n  height: 4px;\n  border-radius: 2px;\n}\n/*# sourceMappingURL=offline-banner.component.css.map */\n"] }]
  }], () => [{ type: OfflineStorageService }, { type: OfflineMapService }, { type: OfflineRoutingService }, { type: AlertController }, { type: LoadingController }, { type: ToastController }], { offlineModeEnabled: [{
    type: Output
  }], dataSynced: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(OfflineBannerComponent, { className: "OfflineBannerComponent", filePath: "src/app/components/offline-banner.component.ts", lineNumber: 134 });
})();

// src/app/pages/home/<USER>
function HomePage_ion_badge_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-badge", 21);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.unreadNotificationCount > 99 ? "99+" : ctx_r0.unreadNotificationCount, " ");
  }
}
var HomePage = class _HomePage {
  constructor(router, toastCtrl, fcmService, http, offlineStorage) {
    this.router = router;
    this.toastCtrl = toastCtrl;
    this.fcmService = fcmService;
    this.http = http;
    this.offlineStorage = offlineStorage;
    this.isOffline = false;
    this.unreadNotificationCount = 0;
    this.notificationSubscription = null;
    this.pollSubscription = null;
  }
  ngOnInit() {
    const savedOfflineStatus = localStorage.getItem("isOffline");
    this.isOffline = savedOfflineStatus === "true";
    this.loadUnreadCount();
    this.pollSubscription = interval(3e4).subscribe(() => {
      this.loadUnreadCount();
    });
    this.notificationSubscription = this.fcmService.notifications$.subscribe(() => {
      this.loadUnreadCount();
    });
  }
  ngOnDestroy() {
    if (this.notificationSubscription) {
      this.notificationSubscription.unsubscribe();
    }
    if (this.pollSubscription) {
      this.pollSubscription.unsubscribe();
    }
  }
  toggleStatus() {
    this.isOffline = !this.isOffline;
    localStorage.setItem("isOffline", String(this.isOffline));
  }
  openDisasterMap(disasterType) {
    console.log(`\u{1F3E0} HOME: Opening disaster-specific map for: ${disasterType}`);
    let displayName = disasterType;
    let route = "";
    if (disasterType === "earthquake") {
      displayName = "Earthquake";
      route = "/earthquake-map";
    } else if (disasterType === "typhoon") {
      displayName = "Typhoon";
      route = "/typhoon-map";
    } else if (disasterType === "flashflood") {
      displayName = "Flash Flood";
      route = "/flood-map";
    }
    console.log(`\u{1F3E0} HOME: Navigating to ${route} for ${displayName}`);
    this.toastCtrl.create({
      message: `\u{1F5FA}\uFE0F Opening ${displayName} evacuation centers...`,
      duration: 2e3,
      color: "primary"
    }).then((toast) => toast.present());
    this.router.navigate([route]);
  }
  viewMap() {
    console.log(`\u{1F3E0} HOME: Opening complete evacuation centers map`);
    this.toastCtrl.create({
      message: "\u{1F5FA}\uFE0F Opening complete evacuation centers map...",
      duration: 2e3,
      color: "secondary"
    }).then((toast) => toast.present());
    this.router.navigate(["/all-maps"]);
  }
  loadUnreadCount() {
    return __async(this, null, function* () {
      try {
        const response = yield this.http.get(`${environment.apiUrl}/notifications/unread-count`).toPromise();
        if (response) {
          this.unreadNotificationCount = response.unread_count;
        }
      } catch (error) {
        console.error("Error loading unread notification count:", error);
      }
    });
  }
  openNotifications() {
    this.router.navigate(["/notifications"]);
  }
  openDataDebug() {
    console.log("\u{1F41B} Opening data debug page");
    this.router.navigate(["/data-debug"]);
  }
  // Offline banner event handlers
  onOfflineModeEnabled() {
    console.log("\u{1F504} Offline mode enabled from banner");
    this.isOffline = true;
    this.showToast("Offline mode enabled. Using cached data.", "success");
  }
  onDataSynced() {
    console.log("\u{1F504} Data synced from banner");
    this.showToast("Evacuation data updated successfully", "success");
  }
  showToast(message, color) {
    return __async(this, null, function* () {
      const toast = yield this.toastCtrl.create({
        message,
        duration: 3e3,
        color,
        position: "bottom"
      });
      yield toast.present();
    });
  }
  static {
    this.\u0275fac = function HomePage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _HomePage)(\u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(ToastController), \u0275\u0275directiveInject(FcmService), \u0275\u0275directiveInject(HttpClient), \u0275\u0275directiveInject(OfflineStorageService));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _HomePage, selectors: [["app-home"]], decls: 40, vars: 3, consts: [[3, "translucent"], ["slot", "end"], [1, "notification-button", 3, "click"], ["name", "notifications-outline"], ["class", "notification-badge", 4, "ngIf"], [3, "offlineModeEnabled", "dataSynced"], [1, "ion-padding"], [1, "disaster-container"], [1, "home-title"], ["src", "assets/ALERTO.png", "alt", "App Logo", 1, "home-logo"], [2, "font-size", "22px"], [2, "font-size", "35px", "color", "#1565c0", "margin-top", "0px"], [1, "top-disaster"], [1, "disaster", "earthquake", 3, "click"], ["src", "assets/earthquake.png", "alt", "Earthquake"], [1, "disaster", "typhoon", 3, "click"], ["src", "assets/typhoon.png", "alt", "Typhoon"], [1, "disaster", "flood", 3, "click"], ["src", "assets/flood.png", "alt", "Flood"], ["expand", "block", 1, "view-map", 2, "margin-top", "24px", "width", "80%", "height", "45px", "--border-radius", "25px", 3, "click", "disabled"], ["name", "map", "slot", "start"], [1, "notification-badge"]], template: function HomePage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header", 0)(1, "ion-toolbar")(2, "ion-title");
        \u0275\u0275text(3, " Alerto ");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-buttons", 1)(5, "ion-button", 2);
        \u0275\u0275listener("click", function HomePage_Template_ion_button_click_5_listener() {
          return ctx.openNotifications();
        });
        \u0275\u0275element(6, "ion-icon", 3);
        \u0275\u0275template(7, HomePage_ion_badge_7_Template, 2, 1, "ion-badge", 4);
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(8, "ion-content")(9, "app-offline-banner", 5);
        \u0275\u0275listener("offlineModeEnabled", function HomePage_Template_app_offline_banner_offlineModeEnabled_9_listener() {
          return ctx.onOfflineModeEnabled();
        })("dataSynced", function HomePage_Template_app_offline_banner_dataSynced_9_listener() {
          return ctx.onDataSynced();
        });
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(10, "div", 6)(11, "div", 7)(12, "div", 8);
        \u0275\u0275element(13, "img", 9);
        \u0275\u0275elementStart(14, "div", 10);
        \u0275\u0275text(15, "Hi, Welcome to ");
        \u0275\u0275elementStart(16, "p", 11);
        \u0275\u0275text(17, "Safe Area!");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(18, "div", 12)(19, "ion-card", 13);
        \u0275\u0275listener("click", function HomePage_Template_ion_card_click_19_listener() {
          return ctx.openDisasterMap("earthquake");
        });
        \u0275\u0275elementStart(20, "ion-card-content");
        \u0275\u0275element(21, "img", 14);
        \u0275\u0275elementStart(22, "ion-text")(23, "u");
        \u0275\u0275text(24, "Earthquake");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(25, "ion-card", 15);
        \u0275\u0275listener("click", function HomePage_Template_ion_card_click_25_listener() {
          return ctx.openDisasterMap("typhoon");
        });
        \u0275\u0275elementStart(26, "ion-card-content");
        \u0275\u0275element(27, "img", 16);
        \u0275\u0275elementStart(28, "ion-text")(29, "u");
        \u0275\u0275text(30, "Typhoon");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(31, "ion-card", 17);
        \u0275\u0275listener("click", function HomePage_Template_ion_card_click_31_listener() {
          return ctx.openDisasterMap("flashflood");
        });
        \u0275\u0275elementStart(32, "ion-card-content");
        \u0275\u0275element(33, "img", 18);
        \u0275\u0275elementStart(34, "ion-text")(35, "u");
        \u0275\u0275text(36, "Flash Flood");
        \u0275\u0275elementEnd()()()()();
        \u0275\u0275elementStart(37, "ion-button", 19);
        \u0275\u0275listener("click", function HomePage_Template_ion_button_click_37_listener() {
          return ctx.viewMap();
        });
        \u0275\u0275element(38, "ion-icon", 20);
        \u0275\u0275text(39, " See the Whole Map ");
        \u0275\u0275elementEnd()()()();
      }
      if (rf & 2) {
        \u0275\u0275property("translucent", true);
        \u0275\u0275advance(7);
        \u0275\u0275property("ngIf", ctx.unreadNotificationCount > 0);
        \u0275\u0275advance(30);
        \u0275\u0275property("disabled", ctx.isOffline);
      }
    }, dependencies: [IonicModule, IonBadge, IonButton, IonButtons, IonCard, IonCardContent, IonContent, IonHeader, IonIcon, IonText, IonTitle, IonToolbar, CommonModule, NgIf, OfflineBannerComponent], styles: ['\n\n.status-text[_ngcontent-%COMP%] {\n  margin-left: 8px;\n}\nion-header[_ngcontent-%COMP%], \nion-title[_ngcontent-%COMP%] {\n  text-align: center;\n  font-family:\n    "Poppins",\n    Arial,\n    sans-serif;\n  font-size: 2rem;\n  font-weight: 700;\n  letter-spacing: 1px;\n  text-shadow: 1px 2px 4px #ccc;\n}\n.disaster-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 24px;\n  margin: 32px 0 0 0;\n}\n.disaster[_ngcontent-%COMP%] {\n  margin: 0;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n.disaster[_ngcontent-%COMP%]:hover {\n  transform: scale(1.05);\n}\n.disaster[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 16px;\n}\n.disaster[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  margin-bottom: 8px;\n}\n.earthquake[_ngcontent-%COMP%] {\n  --background: #ffcc80;\n}\n.typhoon[_ngcontent-%COMP%] {\n  --background: #c5e1a5;\n  size: 100px;\n  width: 105px;\n  height: 120px;\n}\n.flood[_ngcontent-%COMP%] {\n  --background: #81d4fa;\n}\n.view-map[_ngcontent-%COMP%] {\n  margin-top: 24px;\n  --background: #00bfff;\n}\n.view-map[_ngcontent-%COMP%]:hover {\n  --background: #0090cc;\n}\n.view-map[disabled][_ngcontent-%COMP%] {\n  --background: #999;\n}\n.top-disaster[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  gap: 16px;\n  align-items: center;\n}\n.home-logo[_ngcontent-%COMP%] {\n  width: 150px;\n  height: 150px;\n}\n.home-title[_ngcontent-%COMP%] {\n  padding-top: 105px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 30px;\n  font-weight: 700;\n  letter-spacing: 1px;\n  text-shadow: 1px 2px 4px #ccc;\n}\n.notifications-section[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  border-top: 1px solid var(--ion-color-light);\n  padding-top: 10px;\n}\nion-item-divider[_ngcontent-%COMP%] {\n  --background: transparent;\n  --color: var(--ion-color-primary);\n  font-weight: bold;\n  font-size: 1.1rem;\n  letter-spacing: 0.5px;\n  margin-bottom: 8px;\n}\n.notification-button[_ngcontent-%COMP%] {\n  position: relative;\n}\n.notification-badge[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  background: #e41e3f;\n  color: white;\n  font-size: 10px;\n  font-weight: 600;\n  min-width: 16px;\n  height: 16px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n/*# sourceMappingURL=home.page.css.map */'] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(HomePage, [{
    type: Component,
    args: [{ selector: "app-home", standalone: true, imports: [IonicModule, CommonModule, OfflineBannerComponent], template: `\r
<ion-header [translucent]="true">\r
  <ion-toolbar>\r
    <ion-title>\r
      Alerto\r
    </ion-title>\r
    <ion-buttons slot="end">\r
      <ion-button (click)="openNotifications()" class="notification-button">\r
        <ion-icon name="notifications-outline"></ion-icon>\r
        <ion-badge *ngIf="unreadNotificationCount > 0" class="notification-badge">\r
          {{ unreadNotificationCount > 99 ? '99+' : unreadNotificationCount }}\r
        </ion-badge>\r
      </ion-button>\r
    </ion-buttons>\r
  </ion-toolbar>\r
</ion-header>\r
\r
<ion-content>\r
  <!-- Offline Banner -->\r
  <app-offline-banner\r
    (offlineModeEnabled)="onOfflineModeEnabled()"\r
    (dataSynced)="onDataSynced()">\r
  </app-offline-banner>\r
\r
  <div class="ion-padding">\r
    <div class="disaster-container">\r
      <div class="home-title"><img src="assets/ALERTO.png" alt="App Logo" class="home-logo"/> <div style="font-size: 22px;">Hi, Welcome to <p style="font-size: 35px; color: #1565c0; margin-top: 0px;">Safe Area!</p></div></div>\r
      <div class="top-disaster">\r
\r
      <ion-card class="disaster earthquake" (click)="openDisasterMap('earthquake')">\r
        <ion-card-content>\r
          <img src="assets/earthquake.png" alt="Earthquake">\r
          <ion-text><u>Earthquake</u></ion-text>\r
        </ion-card-content>\r
      </ion-card>\r
\r
      <ion-card class="disaster typhoon" (click)="openDisasterMap('typhoon')">\r
        <ion-card-content>\r
          <img src="assets/typhoon.png" alt="Typhoon">\r
          <ion-text><u>Typhoon</u></ion-text>\r
        </ion-card-content>\r
      </ion-card>\r
\r
      <ion-card class="disaster flood" (click)="openDisasterMap('flashflood')">\r
        <ion-card-content>\r
          <img src="assets/flood.png" alt="Flood">\r
          <ion-text><u>Flash Flood</u></ion-text>\r
        </ion-card-content>\r
      </ion-card>\r
    </div>\r
\r
    <ion-button expand="block" class="view-map" (click)="viewMap()" [disabled]="isOffline" style="margin-top: 24px; width: 80%; height: 45px; --border-radius: 25px;">\r
      <ion-icon name="map" slot="start"></ion-icon>\r
      See the Whole Map\r
    </ion-button>\r
\r
    <!-- Debug Button (temporary) - HIDDEN -->\r
    <!--\r
    <ion-button expand="block" fill="outline" color="warning" (click)="openDataDebug()" style="margin-top: 12px; width: 80%; height: 40px; --border-radius: 20px;">\r
      <ion-icon name="bug" slot="start"></ion-icon>\r
      Debug Data\r
    </ion-button>\r
    -->\r
\r
  </div>\r
</div>\r
\r
\r
`, styles: ['/* src/app/pages/home/<USER>/\n.status-text {\n  margin-left: 8px;\n}\nion-header,\nion-title {\n  text-align: center;\n  font-family:\n    "Poppins",\n    Arial,\n    sans-serif;\n  font-size: 2rem;\n  font-weight: 700;\n  letter-spacing: 1px;\n  text-shadow: 1px 2px 4px #ccc;\n}\n.disaster-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 24px;\n  margin: 32px 0 0 0;\n}\n.disaster {\n  margin: 0;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n.disaster:hover {\n  transform: scale(1.05);\n}\n.disaster ion-card-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  padding: 16px;\n}\n.disaster img {\n  width: 60px;\n  height: 60px;\n  margin-bottom: 8px;\n}\n.earthquake {\n  --background: #ffcc80;\n}\n.typhoon {\n  --background: #c5e1a5;\n  size: 100px;\n  width: 105px;\n  height: 120px;\n}\n.flood {\n  --background: #81d4fa;\n}\n.view-map {\n  margin-top: 24px;\n  --background: #00bfff;\n}\n.view-map:hover {\n  --background: #0090cc;\n}\n.view-map[disabled] {\n  --background: #999;\n}\n.top-disaster {\n  display: flex;\n  justify-content: space-between;\n  gap: 16px;\n  align-items: center;\n}\n.home-logo {\n  width: 150px;\n  height: 150px;\n}\n.home-title {\n  padding-top: 105px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 30px;\n  font-weight: 700;\n  letter-spacing: 1px;\n  text-shadow: 1px 2px 4px #ccc;\n}\n.notifications-section {\n  margin-top: 20px;\n  border-top: 1px solid var(--ion-color-light);\n  padding-top: 10px;\n}\nion-item-divider {\n  --background: transparent;\n  --color: var(--ion-color-primary);\n  font-weight: bold;\n  font-size: 1.1rem;\n  letter-spacing: 0.5px;\n  margin-bottom: 8px;\n}\n.notification-button {\n  position: relative;\n}\n.notification-badge {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  background: #e41e3f;\n  color: white;\n  font-size: 10px;\n  font-weight: 600;\n  min-width: 16px;\n  height: 16px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n/*# sourceMappingURL=home.page.css.map */\n'] }]
  }], () => [{ type: Router }, { type: ToastController }, { type: FcmService }, { type: HttpClient }, { type: OfflineStorageService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(HomePage, { className: "HomePage", filePath: "src/app/pages/home/<USER>", lineNumber: 19 });
})();
export {
  HomePage
};
//# sourceMappingURL=home.page-ORCXRYNN.js.map

{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-segment-view.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement } from './index-527b9e34.js';\nconst segmentViewIosCss = \":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}\";\nconst IonSegmentViewIosStyle0 = segmentViewIosCss;\nconst segmentViewMdCss = \":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}\";\nconst IonSegmentViewMdStyle0 = segmentViewMdCss;\nconst SegmentView = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionSegmentViewScroll = createEvent(this, \"ionSegmentViewScroll\", 7);\n    this.scrollEndTimeout = null;\n    this.isTouching = false;\n    this.disabled = false;\n    this.isManualScroll = undefined;\n  }\n  handleScroll(ev) {\n    var _a;\n    const {\n      scrollLeft,\n      scrollWidth,\n      clientWidth\n    } = ev.target;\n    const scrollRatio = scrollLeft / (scrollWidth - clientWidth);\n    this.ionSegmentViewScroll.emit({\n      scrollRatio,\n      isManualScroll: (_a = this.isManualScroll) !== null && _a !== void 0 ? _a : true\n    });\n    // Reset the timeout to check for scroll end\n    this.resetScrollEndTimeout();\n  }\n  /**\n   * Handle touch start event to know when the user is actively dragging the segment view.\n   */\n  handleScrollStart() {\n    if (this.scrollEndTimeout) {\n      clearTimeout(this.scrollEndTimeout);\n      this.scrollEndTimeout = null;\n    }\n    this.isTouching = true;\n  }\n  /**\n   * Handle touch end event to know when the user is no longer dragging the segment view.\n   */\n  handleTouchEnd() {\n    this.isTouching = false;\n  }\n  /**\n   * Reset the scroll end detection timer. This is called on every scroll event.\n   */\n  resetScrollEndTimeout() {\n    if (this.scrollEndTimeout) {\n      clearTimeout(this.scrollEndTimeout);\n      this.scrollEndTimeout = null;\n    }\n    this.scrollEndTimeout = setTimeout(() => {\n      this.checkForScrollEnd();\n    },\n    // Setting this to a lower value may result in inconsistencies in behavior\n    // across browsers (particularly Firefox).\n    // Ideally, all of this logic is removed once the scroll end event is\n    // supported on all browsers (https://caniuse.com/?search=scrollend)\n    100);\n  }\n  /**\n   * Check if the scroll has ended and the user is not actively touching.\n   * If the conditions are met (active content is enabled and no active touch),\n   * reset the scroll position and emit the scroll end event.\n   */\n  checkForScrollEnd() {\n    // Only emit scroll end event if the active content is not disabled and\n    // the user is not touching the segment view\n    if (!this.isTouching) {\n      this.isManualScroll = undefined;\n    }\n  }\n  /**\n   * @internal\n   *\n   * This method is used to programmatically set the displayed segment content\n   * in the segment view. Calling this method will update the `value` of the\n   * corresponding segment button.\n   *\n   * @param id: The id of the segment content to display.\n   * @param smoothScroll: Whether to animate the scroll transition.\n   */\n  async setContent(id, smoothScroll = true) {\n    const contents = this.getSegmentContents();\n    const index = contents.findIndex(content => content.id === id);\n    if (index === -1) return;\n    this.isManualScroll = false;\n    this.resetScrollEndTimeout();\n    const contentWidth = this.el.offsetWidth;\n    this.el.scrollTo({\n      top: 0,\n      left: index * contentWidth,\n      behavior: smoothScroll ? 'smooth' : 'instant'\n    });\n  }\n  getSegmentContents() {\n    return Array.from(this.el.querySelectorAll('ion-segment-content'));\n  }\n  render() {\n    const {\n      disabled,\n      isManualScroll\n    } = this;\n    return h(Host, {\n      key: 'fa528d2d9ae0f00fc3067defe2a047dce77c814a',\n      class: {\n        'segment-view-disabled': disabled,\n        'segment-view-scroll-disabled': isManualScroll === false\n      }\n    }, h(\"slot\", {\n      key: '74dc8b4d073caeff1bab272d11b9ea3e1a215954'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nSegmentView.style = {\n  ios: IonSegmentViewIosStyle0,\n  md: IonSegmentViewMdStyle0\n};\nexport { SegmentView as ion_segment_view };"], "mappings": ";;;;;;;;;;;;AAIA,IAAM,oBAAoB;AAC1B,IAAM,0BAA0B;AAChC,IAAM,mBAAmB;AACzB,IAAM,yBAAyB;AAC/B,IAAM,cAAc,MAAM;AAAA,EACxB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,uBAAuB,YAAY,MAAM,wBAAwB,CAAC;AACvE,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,aAAa,IAAI;AACf,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,GAAG;AACP,UAAM,cAAc,cAAc,cAAc;AAChD,SAAK,qBAAqB,KAAK;AAAA,MAC7B;AAAA,MACA,iBAAiB,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AAAA,IAC9E,CAAC;AAED,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAClB,QAAI,KAAK,kBAAkB;AACzB,mBAAa,KAAK,gBAAgB;AAClC,WAAK,mBAAmB;AAAA,IAC1B;AACA,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB;AACtB,QAAI,KAAK,kBAAkB;AACzB,mBAAa,KAAK,gBAAgB;AAClC,WAAK,mBAAmB;AAAA,IAC1B;AACA,SAAK,mBAAmB;AAAA,MAAW,MAAM;AACvC,aAAK,kBAAkB;AAAA,MACzB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA;AAAA,IAAG;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAGlB,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWM,WAAW,IAAI,eAAe,MAAM;AAAA;AACxC,YAAM,WAAW,KAAK,mBAAmB;AACzC,YAAM,QAAQ,SAAS,UAAU,aAAW,QAAQ,OAAO,EAAE;AAC7D,UAAI,UAAU,GAAI;AAClB,WAAK,iBAAiB;AACtB,WAAK,sBAAsB;AAC3B,YAAM,eAAe,KAAK,GAAG;AAC7B,WAAK,GAAG,SAAS;AAAA,QACf,KAAK;AAAA,QACL,MAAM,QAAQ;AAAA,QACd,UAAU,eAAe,WAAW;AAAA,MACtC,CAAC;AAAA,IACH;AAAA;AAAA,EACA,qBAAqB;AACnB,WAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,qBAAqB,CAAC;AAAA,EACnE;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO;AAAA,QACL,yBAAyB;AAAA,QACzB,gCAAgC,mBAAmB;AAAA,MACrD;AAAA,IACF,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,YAAY,QAAQ;AAAA,EAClB,KAAK;AAAA,EACL,IAAI;AACN;", "names": [], "x_google_ignoreList": [0]}
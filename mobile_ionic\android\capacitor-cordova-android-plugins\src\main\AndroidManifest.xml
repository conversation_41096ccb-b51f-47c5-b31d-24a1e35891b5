<?xml version='1.0' encoding='utf-8'?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:amazon="http://schemas.amazon.com/apk/res/android">
<application  android:usesCleartextTraffic="true">
<activity android:name="com.gae.scaffolder.plugin.FCMPluginActivity" android:launchMode="singleTop">
  <intent-filter>
    <action android:name="FCM_PLUGIN_ACTIVITY"/>
    <category android:name="android.intent.category.DEFAULT"/>
  </intent-filter>
</activity>
<service android:name="com.gae.scaffolder.plugin.MyFirebaseMessagingService" android:stopWithTask="false">
  <intent-filter>
    <action android:name="com.google.firebase.MESSAGING_EVENT"/>
  </intent-filter>
</service>
<meta-data android:name="com.google.firebase.messaging.default_notification_icon" android:resource="@mipmap/ic_launcher"/>
</application>

</manifest>
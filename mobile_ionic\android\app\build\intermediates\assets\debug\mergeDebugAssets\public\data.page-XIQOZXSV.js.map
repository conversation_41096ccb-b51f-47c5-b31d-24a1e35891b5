{"version": 3, "sources": ["src/app/services/mobile-user.service.ts", "src/app/pages/data/data.page.ts", "src/app/pages/data/data.page.html"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from '../../environments/environment';\r\n\r\ninterface UserData {\r\n  full_name: string;\r\n  mobile_number: string;\r\n  age: number;\r\n  gender: string;\r\n  address: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class MobileUserService {\r\n  private apiUrl = `${environment.apiUrl}/mobile-users`;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  saveUserData(data: UserData): Observable<any> {\r\n    return this.http.post(this.apiUrl, data);\r\n  }\r\n\r\n  createUser(userData: any): Observable<any> {\r\n    return this.http.post(this.apiUrl, userData);\r\n  }\r\n}\r\n", "import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { Router } from '@angular/router';\r\nimport { MobileUserService } from '../../services/mobile-user.service';\r\n\r\n@Component({\r\n  selector: 'app-data',\r\n  templateUrl: './data.page.html',\r\n  styleUrls: ['./data.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule]\r\n})\r\nexport class DataPage {\r\n  userData = {\r\n    full_name: '',\r\n    mobile_number: '',\r\n    age: '',\r\n    gender: '',\r\n    address: ''\r\n  };\r\n\r\n  // Philippines mobile number format: +63 + 10 digits (total 11 digits)\r\n  acceptedTerms = false;\r\n  showError = false;\r\n  errorMessage = '';\r\n  isTermsModalOpen = false;\r\n\r\n  constructor(private router: Router, private mobileUserService: MobileUserService) {}\r\n\r\n  /**\r\n   * Open Terms and Conditions modal\r\n   */\r\n  openTermsModal() {\r\n    this.isTermsModalOpen = true;\r\n  }\r\n\r\n  /**\r\n   * Close Terms and Conditions modal\r\n   */\r\n  closeTermsModal() {\r\n    this.isTermsModalOpen = false;\r\n  }\r\n\r\n  /**\r\n   * Accept terms from modal and close it\r\n   */\r\n  acceptTerms() {\r\n    this.acceptedTerms = true;\r\n    this.closeTermsModal();\r\n  }\r\n\r\n  /**\r\n   * Get current date for terms display\r\n   */\r\n  getCurrentDate(): string {\r\n    return new Date().toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    });\r\n  }\r\n\r\n\r\n\r\n  /**\r\n   * Handle mobile number input and format validation\r\n   * Philippines format: +63 + 10 digits (total 11 digits)\r\n   */\r\n  onMobileNumberInput(event: any) {\r\n    let value = event.target.value;\r\n\r\n    // Remove any non-numeric characters\r\n    value = value.replace(/\\D/g, '');\r\n\r\n    // Limit to exactly 10 digits (after +63)\r\n    if (value.length > 10) {\r\n      value = value.substring(0, 10);\r\n    }\r\n\r\n    // For Philippine mobile numbers, ensure it starts with 9\r\n    if (value.length > 0 && !value.startsWith('9')) {\r\n      // If user types a number that doesn't start with 9, prepend 9\r\n      if (value.length === 1 && value !== '9') {\r\n        value = '9' + value;\r\n      }\r\n    }\r\n\r\n    // Update the model\r\n    this.userData.mobile_number = value;\r\n\r\n    // Clear any previous error when user starts typing\r\n    if (this.showError && this.errorMessage.includes('Mobile number')) {\r\n      this.showError = false;\r\n      this.errorMessage = '';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get the complete mobile number with country code\r\n   * Returns format: +63-************\r\n   */\r\n  getCompletePhoneNumber(): string {\r\n    if (!this.userData.mobile_number) return '';\r\n    return `+63${this.userData.mobile_number}`;\r\n  }\r\n\r\n  /**\r\n   * Validate Philippine mobile number format\r\n   * Must be exactly 10 digits starting with 9 (after +63)\r\n   * Total: +63 + 10 digits = 11 digits\r\n   */\r\n  isValidPhilippineMobile(): boolean {\r\n    const mobileNumber = this.userData.mobile_number;\r\n\r\n    // Must be exactly 10 digits starting with 9\r\n    return /^9[0-9]{9}$/.test(mobileNumber) && mobileNumber.length === 10;\r\n  }\r\n\r\n  onSave() {\r\n    console.log(this.userData); // Debug\r\n\r\n    const requiredFields = ['full_name', 'mobile_number', 'age', 'gender', 'address'];\r\n    for (const field of requiredFields) {\r\n      const value = (this.userData as any)[field];\r\n      if (value === undefined || value === null || value.toString().trim() === '') {\r\n        this.showError = true;\r\n        this.errorMessage = 'All fields are required.';\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Validate Philippine mobile number format\r\n    if (!this.isValidPhilippineMobile()) {\r\n      this.showError = true;\r\n      this.errorMessage = 'Mobile number must be exactly 10 digits starting with 9 (e.g., +63-************).';\r\n      return;\r\n    }\r\n\r\n    if (isNaN(Number(this.userData.age)) || Number(this.userData.age) <= 0) {\r\n      this.showError = true;\r\n      this.errorMessage = 'Age must be a positive number.';\r\n      return;\r\n    }\r\n\r\n    if (!this.acceptedTerms) {\r\n      this.showError = true;\r\n      this.errorMessage = 'You must accept the Terms and Conditions.';\r\n      return;\r\n    }\r\n\r\n    this.mobileUserService.createUser({\r\n      full_name: this.userData.full_name,\r\n      mobile_number: this.userData.mobile_number,\r\n      age: Number(this.userData.age),\r\n      gender: this.userData.gender,\r\n      address: this.userData.address\r\n    }).subscribe({\r\n      next: () => {\r\n        const userData = {\r\n          full_name: this.userData.full_name,\r\n          mobile_number: this.userData.mobile_number,\r\n          age: this.userData.age,\r\n          gender: this.userData.gender,\r\n          address: this.userData.address,\r\n        };\r\n        localStorage.setItem('userData', JSON.stringify(userData));\r\n        localStorage.setItem('onboardingComplete', 'true');\r\n        this.router.navigate(['/tabs/home']);\r\n      },\r\n      error: (err: any) => {\r\n        this.showError = true;\r\n        this.errorMessage = err.error?.message || 'Failed to save user.';\r\n      }\r\n    });\r\n  }\r\n\r\n}\r\n\r\n", "\r\n<ion-content class=\"ion-padding\">\r\n  <div class=\"profile-container\">\r\n\r\n    <div class=\"header-logo\">\r\n      <div class=\"home-title\"><img src=\"assets/ALERTO.png\" alt=\"App Logo\" class=\"home-logo\"/> Hi, Welcome to Safe Area!</div>\r\n    </div>\r\n    <form (ngSubmit)=\"onSave()\">\r\n      <ion-item>\r\n        <ion-label position=\"floating\" style=\"font-size: 15px;\">Full Name:</ion-label>\r\n        <ion-input type=\"text\" [(ngModel)]=\"userData.full_name\" name=\"full_name\" style=\"font-size: 20px; padding-bottom: 10px;\" required></ion-input>\r\n      </ion-item>\r\n\r\n      <ion-item>\r\n        <ion-label position=\"floating\" style=\"font-size: 15px;\">Mobile Number:</ion-label>\r\n        <div style=\"display: flex; align-items: center; width: 100%;\">\r\n          <span style=\"font-size: 20px; font-weight: bold; margin-right: 8px; color: #333; min-width: 40px;\">+63</span>\r\n          <ion-input\r\n            type=\"tel\"\r\n            [(ngModel)]=\"userData.mobile_number\"\r\n            name=\"mobile_number\"\r\n            style=\"font-size: 20px; padding-bottom: 10px; flex: 1;\"\r\n            placeholder=\"************\"\r\n            maxlength=\"10\"\r\n            (ionInput)=\"onMobileNumberInput($event)\"\r\n            required>\r\n          </ion-input>\r\n        </div>\r\n\r\n      </ion-item>\r\n\r\n      <ion-item>\r\n        <ion-label position=\"floating\"  style=\"font-size: 15px;\">Age:</ion-label>\r\n        <ion-input type=\"number\" [(ngModel)]=\"userData.age\" name=\"age\" style=\"font-size: 20px; padding-bottom: 10px; \"required></ion-input>\r\n      </ion-item>\r\n\r\n      <ion-item>\r\n        <ion-label position=\"floating\" >Gender <ion-select [(ngModel)]=\"userData.gender\" name=\"gender\" required>\r\n          <ion-select-option value=\"Male\">Male</ion-select-option>\r\n          <ion-select-option value=\"Female\">Female</ion-select-option>\r\n          <ion-select-option value=\"Other\">Other</ion-select-option>\r\n        </ion-select></ion-label>\r\n\r\n      </ion-item>\r\n\r\n      <ion-item>\r\n        <ion-label position=\"floating\" style=\"font-size: 15px;\">Address:</ion-label>\r\n        <ion-input type=\"text\" [(ngModel)]=\"userData.address\" name=\"address\" style=\"font-size: 20px; padding-bottom: 10px;\" required></ion-input>\r\n      </ion-item>\r\n\r\n      <ion-text color=\"danger\" *ngIf=\"showError\" class=\"error-message\">\r\n        <p>{{ errorMessage }}</p>\r\n      </ion-text>\r\n\r\n      <div class=\"terms-checkbox\">\r\n        <ion-checkbox slot=\"start\" [(ngModel)]=\"acceptedTerms\" name=\"acceptedTerms\" required></ion-checkbox>\r\n        <label>I accept the <a (click)=\"openTermsModal()\" style=\"color: #3880ff; text-decoration: underline; cursor: pointer;\">Terms and Conditions</a></label>\r\n      </div>\r\n\r\n      <ion-button expand=\"block\" type=\"submit\" class=\"ion-margin-top\">\r\n        Save\r\n      </ion-button>\r\n    </form>\r\n  </div>\r\n</ion-content>\r\n\r\n<!-- Terms and Conditions Modal -->\r\n<ion-modal #termsModal [isOpen]=\"isTermsModalOpen\" (willDismiss)=\"closeTermsModal()\">\r\n  <ng-template>\r\n    <ion-header>\r\n      <ion-toolbar>\r\n        <ion-title>Terms and Conditions</ion-title>\r\n        <ion-buttons slot=\"end\">\r\n          <ion-button (click)=\"closeTermsModal()\">\r\n            <ion-icon name=\"close\"></ion-icon>\r\n          </ion-button>\r\n        </ion-buttons>\r\n      </ion-toolbar>\r\n    </ion-header>\r\n\r\n    <ion-content class=\"ion-padding\">\r\n      <div class=\"terms-content\">\r\n        <h2>Alerto - Emergency Evacuation App</h2>\r\n        <h3>Terms and Conditions of Use</h3>\r\n\r\n        <p><strong>Last Updated:</strong> {{ getCurrentDate() }}</p>\r\n\r\n        <h4>1. Acceptance of Terms</h4>\r\n        <p>By using the Alerto emergency evacuation application, you agree to be bound by these Terms and Conditions. If you do not agree to these terms, please do not use this application.</p>\r\n\r\n        <h4>2. Purpose of the Application</h4>\r\n        <p>Alerto is designed to provide emergency evacuation information and guidance during natural disasters including earthquakes, typhoons, and flash floods in the Philippines. The app provides location-based evacuation center recommendations and emergency notifications.</p>\r\n\r\n        <h4>3. User Responsibilities</h4>\r\n        <p>• Provide accurate personal information including contact details</p>\r\n        <p>• Keep your mobile number updated for emergency notifications</p>\r\n        <p>• Use the app responsibly during emergency situations</p>\r\n        <p>• Follow official emergency protocols and local authority instructions</p>\r\n\r\n        <h4>4. Data Collection and Privacy</h4>\r\n        <p>We collect and store your personal information including name, mobile number, age, gender, and address to provide personalized emergency services. Your location data may be accessed to provide relevant evacuation center recommendations.</p>\r\n\r\n        <h4>5. Emergency Notifications</h4>\r\n        <p>By using this app, you consent to receive emergency push notifications on your device. These notifications are critical for your safety during disaster events.</p>\r\n\r\n        <h4>6. Limitation of Liability</h4>\r\n        <p>While we strive to provide accurate and timely information, Alerto and its developers are not liable for any damages or losses resulting from the use of this application. Always follow official emergency protocols and local authority guidance.</p>\r\n\r\n        <h4>7. Service Availability</h4>\r\n        <p>We cannot guarantee uninterrupted service availability, especially during extreme weather conditions or network outages that may occur during disasters.</p>\r\n\r\n        <h4>8. Updates and Modifications</h4>\r\n        <p>These terms may be updated periodically. Continued use of the application constitutes acceptance of any modifications.</p>\r\n\r\n        <h4>9. Contact Information</h4>\r\n        <p>For questions about these terms or the application, please contact our support team through the app's feedback feature.</p>\r\n\r\n        <p><strong>By clicking \"I accept\" below, you acknowledge that you have read, understood, and agree to be bound by these Terms and Conditions.</strong></p>\r\n      </div>\r\n\r\n      <div class=\"modal-buttons\">\r\n        <ion-button expand=\"block\" fill=\"outline\" (click)=\"closeTermsModal()\">\r\n          Close\r\n        </ion-button>\r\n        <ion-button expand=\"block\" (click)=\"acceptTerms()\">\r\n          I Accept\r\n        </ion-button>\r\n      </div>\r\n    </ion-content>\r\n  </ng-template>\r\n</ion-modal>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBM,IAAO,oBAAP,MAAO,mBAAiB;EAG5B,YAAoB,MAAgB;AAAhB,SAAA,OAAA;AAFZ,SAAA,SAAS,GAAG,YAAY,MAAM;EAEC;EAEvC,aAAa,MAAc;AACzB,WAAO,KAAK,KAAK,KAAK,KAAK,QAAQ,IAAI;EACzC;EAEA,WAAW,UAAa;AACtB,WAAO,KAAK,KAAK,KAAK,KAAK,QAAQ,QAAQ;EAC7C;;;uCAXW,oBAAiB,mBAAA,UAAA,CAAA;IAAA;EAAA;;4EAAjB,oBAAiB,SAAjB,mBAAiB,WAAA,YAFhB,OAAM,CAAA;EAAA;;;sEAEP,mBAAiB,CAAA;UAH7B;WAAW;MACV,YAAY;KACb;;;;;;;AEmCK,IAAA,yBAAA,GAAA,YAAA,EAAA,EAAiE,GAAA,GAAA;AAC5D,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA,EAAI;;;;AAAtB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA;;;;;;AAkBP,IAAA,yBAAA,GAAA,YAAA,EAAY,GAAA,aAAA,EACG,GAAA,WAAA;AACA,IAAA,iBAAA,GAAA,sBAAA;AAAoB,IAAA,uBAAA;AAC/B,IAAA,yBAAA,GAAA,eAAA,EAAA,EAAwB,GAAA,cAAA,EAAA;AACV,IAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,CAAiB;IAAA,CAAA;AACpC,IAAA,oBAAA,GAAA,YAAA,EAAA;AACF,IAAA,uBAAA,EAAa,EACD,EACF;AAGhB,IAAA,yBAAA,GAAA,eAAA,CAAA,EAAiC,GAAA,OAAA,EAAA,EACJ,GAAA,IAAA;AACrB,IAAA,iBAAA,IAAA,mCAAA;AAAiC,IAAA,uBAAA;AACrC,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,6BAAA;AAA2B,IAAA,uBAAA;AAE/B,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAAsB,IAAA,uBAAA;AAExD,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,wBAAA;AAAsB,IAAA,uBAAA;AAC1B,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,oLAAA;AAAkL,IAAA,uBAAA;AAErL,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,+BAAA;AAA6B,IAAA,uBAAA;AACjC,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,2QAAA;AAAyQ,IAAA,uBAAA;AAE5Q,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,0BAAA;AAAwB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,wEAAA;AAAiE,IAAA,uBAAA;AACpE,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,oEAAA;AAA6D,IAAA,uBAAA;AAChE,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,4DAAA;AAAqD,IAAA,uBAAA;AACxD,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,6EAAA;AAAsE,IAAA,uBAAA;AAEzE,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,gCAAA;AAA8B,IAAA,uBAAA;AAClC,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,8OAAA;AAA4O,IAAA,uBAAA;AAE/O,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,4BAAA;AAA0B,IAAA,uBAAA;AAC9B,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,iKAAA;AAA+J,IAAA,uBAAA;AAElK,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,4BAAA;AAA0B,IAAA,uBAAA;AAC9B,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,qPAAA;AAAmP,IAAA,uBAAA;AAEtP,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,yBAAA;AAAuB,IAAA,uBAAA;AAC3B,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,0JAAA;AAAwJ,IAAA,uBAAA;AAE3J,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,8BAAA;AAA4B,IAAA,uBAAA;AAChC,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,wHAAA;AAAsH,IAAA,uBAAA;AAEzH,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,wBAAA;AAAsB,IAAA,uBAAA;AAC1B,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,yHAAA;AAAuH,IAAA,uBAAA;AAE1H,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,oIAAA;AAAkI,IAAA,uBAAA,EAAS,EAAI;AAG5J,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,cAAA,EAAA;AACiB,IAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,CAAiB;IAAA,CAAA;AAClE,IAAA,iBAAA,IAAA,SAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,cAAA,EAAA;AAA2B,IAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,CAAa;IAAA,CAAA;AAC/C,IAAA,iBAAA,IAAA,YAAA;AACF,IAAA,uBAAA,EAAa,EACT;;;;AA1C8B,IAAA,oBAAA,EAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,eAAA,GAAA,EAAA;;;ADvEpC,IAAO,WAAP,MAAO,UAAQ;EAenB,YAAoB,QAAwB,mBAAoC;AAA5D,SAAA,SAAA;AAAwB,SAAA,oBAAA;AAd5C,SAAA,WAAW;MACT,WAAW;MACX,eAAe;MACf,KAAK;MACL,QAAQ;MACR,SAAS;;AAIX,SAAA,gBAAgB;AAChB,SAAA,YAAY;AACZ,SAAA,eAAe;AACf,SAAA,mBAAmB;EAEgE;;;;EAKnF,iBAAc;AACZ,SAAK,mBAAmB;EAC1B;;;;EAKA,kBAAe;AACb,SAAK,mBAAmB;EAC1B;;;;EAKA,cAAW;AACT,SAAK,gBAAgB;AACrB,SAAK,gBAAe;EACtB;;;;EAKA,iBAAc;AACZ,YAAO,oBAAI,KAAI,GAAG,mBAAmB,SAAS;MAC5C,MAAM;MACN,OAAO;MACP,KAAK;KACN;EACH;;;;;EAQA,oBAAoB,OAAU;AAC5B,QAAI,QAAQ,MAAM,OAAO;AAGzB,YAAQ,MAAM,QAAQ,OAAO,EAAE;AAG/B,QAAI,MAAM,SAAS,IAAI;AACrB,cAAQ,MAAM,UAAU,GAAG,EAAE;IAC/B;AAGA,QAAI,MAAM,SAAS,KAAK,CAAC,MAAM,WAAW,GAAG,GAAG;AAE9C,UAAI,MAAM,WAAW,KAAK,UAAU,KAAK;AACvC,gBAAQ,MAAM;MAChB;IACF;AAGA,SAAK,SAAS,gBAAgB;AAG9B,QAAI,KAAK,aAAa,KAAK,aAAa,SAAS,eAAe,GAAG;AACjE,WAAK,YAAY;AACjB,WAAK,eAAe;IACtB;EACF;;;;;EAMA,yBAAsB;AACpB,QAAI,CAAC,KAAK,SAAS;AAAe,aAAO;AACzC,WAAO,MAAM,KAAK,SAAS,aAAa;EAC1C;;;;;;EAOA,0BAAuB;AACrB,UAAM,eAAe,KAAK,SAAS;AAGnC,WAAO,cAAc,KAAK,YAAY,KAAK,aAAa,WAAW;EACrE;EAEA,SAAM;AACJ,YAAQ,IAAI,KAAK,QAAQ;AAEzB,UAAM,iBAAiB,CAAC,aAAa,iBAAiB,OAAO,UAAU,SAAS;AAChF,eAAW,SAAS,gBAAgB;AAClC,YAAM,QAAS,KAAK,SAAiB,KAAK;AAC1C,UAAI,UAAU,UAAa,UAAU,QAAQ,MAAM,SAAQ,EAAG,KAAI,MAAO,IAAI;AAC3E,aAAK,YAAY;AACjB,aAAK,eAAe;AACpB;MACF;IACF;AAGA,QAAI,CAAC,KAAK,wBAAuB,GAAI;AACnC,WAAK,YAAY;AACjB,WAAK,eAAe;AACpB;IACF;AAEA,QAAI,MAAM,OAAO,KAAK,SAAS,GAAG,CAAC,KAAK,OAAO,KAAK,SAAS,GAAG,KAAK,GAAG;AACtE,WAAK,YAAY;AACjB,WAAK,eAAe;AACpB;IACF;AAEA,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,YAAY;AACjB,WAAK,eAAe;AACpB;IACF;AAEA,SAAK,kBAAkB,WAAW;MAChC,WAAW,KAAK,SAAS;MACzB,eAAe,KAAK,SAAS;MAC7B,KAAK,OAAO,KAAK,SAAS,GAAG;MAC7B,QAAQ,KAAK,SAAS;MACtB,SAAS,KAAK,SAAS;KACxB,EAAE,UAAU;MACX,MAAM,MAAK;AACT,cAAM,WAAW;UACf,WAAW,KAAK,SAAS;UACzB,eAAe,KAAK,SAAS;UAC7B,KAAK,KAAK,SAAS;UACnB,QAAQ,KAAK,SAAS;UACtB,SAAS,KAAK,SAAS;;AAEzB,qBAAa,QAAQ,YAAY,KAAK,UAAU,QAAQ,CAAC;AACzD,qBAAa,QAAQ,sBAAsB,MAAM;AACjD,aAAK,OAAO,SAAS,CAAC,YAAY,CAAC;MACrC;MACA,OAAO,CAAC,QAAY;AAClB,aAAK,YAAY;AACjB,aAAK,eAAe,IAAI,OAAO,WAAW;MAC5C;KACD;EACH;;;uCAlKW,WAAQ,4BAAA,MAAA,GAAA,4BAAA,iBAAA,CAAA;IAAA;EAAA;;yEAAR,WAAQ,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,cAAA,EAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,OAAA,qBAAA,OAAA,YAAA,GAAA,WAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,YAAA,YAAA,GAAA,aAAA,MAAA,GAAA,CAAA,QAAA,QAAA,QAAA,aAAA,YAAA,IAAA,GAAA,aAAA,QAAA,kBAAA,QAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,WAAA,QAAA,eAAA,UAAA,SAAA,MAAA,GAAA,CAAA,GAAA,aAAA,QAAA,eAAA,QAAA,gBAAA,OAAA,SAAA,QAAA,aAAA,MAAA,GAAA,CAAA,QAAA,OAAA,QAAA,iBAAA,eAAA,gBAAA,aAAA,MAAA,YAAA,IAAA,GAAA,aAAA,QAAA,kBAAA,QAAA,QAAA,KAAA,GAAA,iBAAA,YAAA,SAAA,GAAA,CAAA,QAAA,UAAA,QAAA,OAAA,YAAA,IAAA,GAAA,aAAA,QAAA,kBAAA,QAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,YAAA,UAAA,GAAA,CAAA,QAAA,UAAA,YAAA,IAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,QAAA,QAAA,QAAA,WAAA,YAAA,IAAA,GAAA,aAAA,QAAA,kBAAA,QAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,SAAA,UAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,SAAA,QAAA,iBAAA,YAAA,IAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,SAAA,WAAA,mBAAA,aAAA,UAAA,WAAA,GAAA,OAAA,GAAA,CAAA,UAAA,SAAA,QAAA,UAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,QAAA,GAAA,CAAA,SAAA,UAAA,GAAA,eAAA,GAAA,CAAA,QAAA,KAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,OAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,UAAA,SAAA,QAAA,WAAA,GAAA,OAAA,GAAA,CAAA,UAAA,SAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,kBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;;ACbrB,QAAA,yBAAA,GAAA,eAAA,CAAA,EAAiC,GAAA,OAAA,CAAA,EACA,GAAA,OAAA,CAAA,EAEJ,GAAA,OAAA,CAAA;AACC,QAAA,oBAAA,GAAA,OAAA,CAAA;AAAgE,QAAA,iBAAA,GAAA,4BAAA;AAAyB,QAAA,uBAAA,EAAM;AAEzH,QAAA,yBAAA,GAAA,QAAA,CAAA;AAAM,QAAA,qBAAA,YAAA,SAAA,6CAAA;AAAA,UAAA,wBAAA,GAAA;AAAA,iBAAA,sBAAY,IAAA,OAAA,CAAQ;QAAA,CAAA;AACxB,QAAA,yBAAA,GAAA,UAAA,EAAU,GAAA,aAAA,CAAA;AACgD,QAAA,iBAAA,GAAA,YAAA;AAAU,QAAA,uBAAA;AAClE,QAAA,yBAAA,IAAA,aAAA,CAAA;AAAuB,QAAA,2BAAA,iBAAA,SAAA,sDAAA,QAAA;AAAA,UAAA,wBAAA,GAAA;AAAA,UAAA,6BAAA,IAAA,SAAA,WAAA,MAAA,MAAA,IAAA,SAAA,YAAA;AAAA,iBAAA,sBAAA,MAAA;QAAA,CAAA;AAA0G,QAAA,uBAAA,EAAY;AAG/I,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACgD,QAAA,iBAAA,IAAA,gBAAA;AAAc,QAAA,uBAAA;AACtE,QAAA,yBAAA,IAAA,OAAA,CAAA,EAA8D,IAAA,QAAA,EAAA;AACuC,QAAA,iBAAA,IAAA,KAAA;AAAG,QAAA,uBAAA;AACtG,QAAA,yBAAA,IAAA,aAAA,EAAA;AAEE,QAAA,2BAAA,iBAAA,SAAA,sDAAA,QAAA;AAAA,UAAA,wBAAA,GAAA;AAAA,UAAA,6BAAA,IAAA,SAAA,eAAA,MAAA,MAAA,IAAA,SAAA,gBAAA;AAAA,iBAAA,sBAAA,MAAA;QAAA,CAAA;AAKA,QAAA,qBAAA,YAAA,SAAA,iDAAA,QAAA;AAAA,UAAA,wBAAA,GAAA;AAAA,iBAAA,sBAAY,IAAA,oBAAA,MAAA,CAA2B;QAAA,CAAA;AAEzC,QAAA,uBAAA,EAAY,EACR;AAIR,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACiD,QAAA,iBAAA,IAAA,MAAA;AAAI,QAAA,uBAAA;AAC7D,QAAA,yBAAA,IAAA,aAAA,EAAA;AAAyB,QAAA,2BAAA,iBAAA,SAAA,sDAAA,QAAA;AAAA,UAAA,wBAAA,GAAA;AAAA,UAAA,6BAAA,IAAA,SAAA,KAAA,MAAA,MAAA,IAAA,SAAA,MAAA;AAAA,iBAAA,sBAAA,MAAA;QAAA,CAAA;AAA8F,QAAA,uBAAA,EAAY;AAGrI,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,EAAA;AACwB,QAAA,iBAAA,IAAA,SAAA;AAAO,QAAA,yBAAA,IAAA,cAAA,EAAA;AAAY,QAAA,2BAAA,iBAAA,SAAA,uDAAA,QAAA;AAAA,UAAA,wBAAA,GAAA;AAAA,UAAA,6BAAA,IAAA,SAAA,QAAA,MAAA,MAAA,IAAA,SAAA,SAAA;AAAA,iBAAA,sBAAA,MAAA;QAAA,CAAA;AACjD,QAAA,yBAAA,IAAA,qBAAA,EAAA;AAAgC,QAAA,iBAAA,IAAA,MAAA;AAAI,QAAA,uBAAA;AACpC,QAAA,yBAAA,IAAA,qBAAA,EAAA;AAAkC,QAAA,iBAAA,IAAA,QAAA;AAAM,QAAA,uBAAA;AACxC,QAAA,yBAAA,IAAA,qBAAA,EAAA;AAAiC,QAAA,iBAAA,IAAA,OAAA;AAAK,QAAA,uBAAA,EAAoB,EAC/C,EAAY;AAI3B,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,aAAA,CAAA;AACgD,QAAA,iBAAA,IAAA,UAAA;AAAQ,QAAA,uBAAA;AAChE,QAAA,yBAAA,IAAA,aAAA,EAAA;AAAuB,QAAA,2BAAA,iBAAA,SAAA,sDAAA,QAAA;AAAA,UAAA,wBAAA,GAAA;AAAA,UAAA,6BAAA,IAAA,SAAA,SAAA,MAAA,MAAA,IAAA,SAAA,UAAA;AAAA,iBAAA,sBAAA,MAAA;QAAA,CAAA;AAAsG,QAAA,uBAAA,EAAY;AAG3I,QAAA,qBAAA,IAAA,+BAAA,GAAA,GAAA,YAAA,EAAA;AAIA,QAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,gBAAA,EAAA;AACC,QAAA,2BAAA,iBAAA,SAAA,yDAAA,QAAA;AAAA,UAAA,wBAAA,GAAA;AAAA,UAAA,6BAAA,IAAA,eAAA,MAAA,MAAA,IAAA,gBAAA;AAAA,iBAAA,sBAAA,MAAA;QAAA,CAAA;AAA0D,QAAA,uBAAA;AACrF,QAAA,yBAAA,IAAA,OAAA;AAAO,QAAA,iBAAA,IAAA,eAAA;AAAa,QAAA,yBAAA,IAAA,KAAA,EAAA;AAAG,QAAA,qBAAA,SAAA,SAAA,wCAAA;AAAA,UAAA,wBAAA,GAAA;AAAA,iBAAA,sBAAS,IAAA,eAAA,CAAgB;QAAA,CAAA;AAAuE,QAAA,iBAAA,IAAA,sBAAA;AAAoB,QAAA,uBAAA,EAAI,EAAQ;AAGzJ,QAAA,yBAAA,IAAA,cAAA,EAAA;AACE,QAAA,iBAAA,IAAA,QAAA;AACF,QAAA,uBAAA,EAAa,EACR,EACH;AAIR,QAAA,yBAAA,IAAA,aAAA,IAAA,CAAA;AAAmD,QAAA,qBAAA,eAAA,SAAA,sDAAA;AAAA,UAAA,wBAAA,GAAA;AAAA,iBAAA,sBAAe,IAAA,gBAAA,CAAiB;QAAA,CAAA;AACjF,QAAA,qBAAA,IAAA,kCAAA,IAAA,GAAA,aAAA;AA8DF,QAAA,uBAAA;;;AAxH+B,QAAA,oBAAA,EAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,SAAA,SAAA;AASnB,QAAA,oBAAA,CAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,SAAA,aAAA;AAcqB,QAAA,oBAAA,CAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,SAAA,GAAA;AAI0B,QAAA,oBAAA,CAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,SAAA,MAAA;AAU5B,QAAA,oBAAA,EAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,SAAA,OAAA;AAGC,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,SAAA;AAKG,QAAA,oBAAA,CAAA;AAAA,QAAA,2BAAA,WAAA,IAAA,aAAA;AAYZ,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,UAAA,IAAA,gBAAA;;sBDvDX,aAAW,WAAA,YAAA,aAAA,YAAA,WAAA,SAAA,UAAA,SAAA,UAAA,WAAA,iBAAA,SAAA,UAAA,YAAA,UAAA,+BAAA,+BAAA,8BAAA,4BAAE,cAAY,MAAE,aAAW,oBAAA,iBAAA,sBAAA,mBAAA,oBAAA,SAAA,MAAA,GAAA,QAAA,CAAA,wxGAAA,EAAA,CAAA;EAAA;;;sEAErC,UAAQ,CAAA;UAPpB;uBACW,YAAU,YAGR,MAAI,SACP,CAAC,aAAa,cAAc,WAAW,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,0nFAAA,EAAA,CAAA;;;;6EAEtC,UAAQ,EAAA,WAAA,YAAA,UAAA,mCAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
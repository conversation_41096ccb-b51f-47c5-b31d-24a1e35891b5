{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-button_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { j as getAssetPath, r as registerInstance, c as createEvent, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { m as hasShadowDom, i as inheritAriaAttributes } from './helpers-d94bc8ad.js';\nimport { p as printIonWarning } from './index-cfd9c1f2.js';\nimport { o as openURL, c as createColorClasses$1, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode$1 } from './ionic-global-b26f573e.js';\nlet CACHED_MAP;\nconst getIconMap = () => {\n  if (typeof window === 'undefined') {\n    return new Map();\n  } else {\n    if (!CACHED_MAP) {\n      const win = window;\n      win.Ionicons = win.Ionicons || {};\n      CACHED_MAP = win.Ionicons.map = win.Ionicons.map || new Map();\n    }\n    return CACHED_MAP;\n  }\n};\nconst getUrl = i => {\n  let url = getSrc(i.src);\n  if (url) {\n    return url;\n  }\n  url = getName(i.name, i.icon, i.mode, i.ios, i.md);\n  if (url) {\n    return getNamedUrl(url, i);\n  }\n  if (i.icon) {\n    url = getSrc(i.icon);\n    if (url) {\n      return url;\n    }\n    url = getSrc(i.icon[i.mode]);\n    if (url) {\n      return url;\n    }\n  }\n  return null;\n};\nconst getNamedUrl = (iconName, iconEl) => {\n  const url = getIconMap().get(iconName);\n  if (url) {\n    return url;\n  }\n  try {\n    return getAssetPath(`svg/${iconName}.svg`);\n  } catch (e) {\n    /**\n     * In the custom elements build version of ionicons, referencing an icon\n     * by name will throw an invalid URL error because the asset path is not defined.\n     * This catches that error and logs something that is more developer-friendly.\n     * We also include a reference to the ion-icon element so developers can\n     * figure out which instance of ion-icon needs to be updated.\n     */\n    console.warn(`[Ionicons Warning]: Could not load icon with name \"${iconName}\". Ensure that the icon is registered using addIcons or that the icon SVG data is passed directly to the icon component.`, iconEl);\n  }\n};\nconst getName = (iconName, icon, mode, ios, md) => {\n  // default to \"md\" if somehow the mode wasn't set\n  mode = (mode && toLower(mode)) === 'ios' ? 'ios' : 'md';\n  // if an icon was passed in using the ios or md attributes\n  // set the iconName to whatever was passed in\n  if (ios && mode === 'ios') {\n    iconName = toLower(ios);\n  } else if (md && mode === 'md') {\n    iconName = toLower(md);\n  } else {\n    if (!iconName && icon && !isSrc(icon)) {\n      iconName = icon;\n    }\n    if (isStr(iconName)) {\n      iconName = toLower(iconName);\n    }\n  }\n  if (!isStr(iconName) || iconName.trim() === '') {\n    return null;\n  }\n  // only allow alpha characters and dash\n  const invalidChars = iconName.replace(/[a-z]|-|\\d/gi, '');\n  if (invalidChars !== '') {\n    return null;\n  }\n  return iconName;\n};\nconst getSrc = src => {\n  if (isStr(src)) {\n    src = src.trim();\n    if (isSrc(src)) {\n      return src;\n    }\n  }\n  return null;\n};\nconst isSrc = str => str.length > 0 && /(\\/|\\.)/.test(str);\nconst isStr = val => typeof val === 'string';\nconst toLower = val => val.toLowerCase();\n/**\n * Elements inside of web components sometimes need to inherit global attributes\n * set on the host. For example, the inner input in `ion-input` should inherit\n * the `title` attribute that developers set directly on `ion-input`. This\n * helper function should be called in componentWillLoad and assigned to a variable\n * that is later used in the render function.\n *\n * This does not need to be reactive as changing attributes on the host element\n * does not trigger a re-render.\n */\nconst inheritAttributes = (el, attributes = []) => {\n  const attributeObject = {};\n  attributes.forEach(attr => {\n    if (el.hasAttribute(attr)) {\n      const value = el.getAttribute(attr);\n      if (value !== null) {\n        attributeObject[attr] = el.getAttribute(attr);\n      }\n      el.removeAttribute(attr);\n    }\n  });\n  return attributeObject;\n};\n/**\n * Returns `true` if the document or host element\n * has a `dir` set to `rtl`. The host value will always\n * take priority over the root document value.\n */\nconst isRTL = hostEl => {\n  if (hostEl) {\n    if (hostEl.dir !== '') {\n      return hostEl.dir.toLowerCase() === 'rtl';\n    }\n  }\n  return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === 'rtl';\n};\nconst buttonIosCss = \":host{--overflow:hidden;--ripple-color:currentColor;--border-width:initial;--border-color:initial;--border-style:initial;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--box-shadow:none;display:inline-block;width:auto;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;white-space:normal;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;vertical-align:top;vertical-align:-webkit-baseline-middle;-webkit-font-kerning:none;font-kerning:none}:host(.button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.button-solid){--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff)}:host(.button-outline){--border-color:var(--ion-color-primary, #0054e9);--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-clear){--border-width:0;--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-block){display:block}:host(.button-block) .button-native{margin-left:0;margin-right:0;width:100%;clear:both;contain:content}:host(.button-block) .button-native::after{clear:both}:host(.button-full){display:block}:host(.button-full) .button-native{margin-left:0;margin-right:0;width:100%;contain:content}:host(.button-full:not(.button-round)) .button-native{border-radius:0;border-right-width:0;border-left-width:0}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);line-height:1;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:layout style;cursor:pointer;opacity:var(--opacity);overflow:var(--overflow);z-index:0;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-native::-moz-focus-inner{border:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted(ion-icon){font-size:1.35em;pointer-events:none}::slotted(ion-icon[slot=start]){-webkit-margin-start:-0.3em;margin-inline-start:-0.3em;-webkit-margin-end:0.3em;margin-inline-end:0.3em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=end]){-webkit-margin-start:0.3em;margin-inline-start:0.3em;-webkit-margin-end:-0.2em;margin-inline-end:-0.2em;margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){:host(:hover){color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-activated){color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.button-solid.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.button-outline.ion-color) .button-native{border-color:var(--ion-color-base);background:transparent;color:var(--ion-color-base)}:host(.button-clear.ion-color) .button-native{background:transparent;color:var(--ion-color-base)}:host(.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{color:var(--ion-toolbar-color, var(--color))}:host(.button-outline.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{border-color:var(--ion-toolbar-color, var(--color, var(--border-color)))}:host(.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--background));color:var(--ion-toolbar-background, var(--color))}:host{--border-radius:14px;--padding-top:13px;--padding-bottom:13px;--padding-start:1em;--padding-end:1em;--transition:background-color, opacity 100ms linear;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:4px;margin-bottom:4px;min-height:3.1em;font-size:min(1rem, 48px);font-weight:500;letter-spacing:0}:host(.button-solid){--background-activated:var(--ion-color-primary-shade, #004acd);--background-focused:var(--ion-color-primary-shade, #004acd);--background-hover:var(--ion-color-primary-tint, #1a65eb);--background-activated-opacity:1;--background-focused-opacity:1;--background-hover-opacity:1}:host(.button-outline){--border-radius:14px;--border-width:1px;--border-style:solid;--background-activated:var(--ion-color-primary, #0054e9);--background-focused:var(--ion-color-primary, #0054e9);--background-hover:transparent;--background-focused-opacity:.1;--color-activated:var(--ion-color-primary-contrast, #fff)}:host(.button-clear){--background-activated:transparent;--background-activated-opacity:0;--background-focused:var(--ion-color-primary, #0054e9);--background-hover:transparent;--background-focused-opacity:.1;font-size:min(1.0625rem, 51px);font-weight:normal}:host(.in-buttons){font-size:clamp(17px, 1.0625rem, 21.08px);font-weight:400}:host(.button-large){--border-radius:16px;--padding-top:17px;--padding-start:1em;--padding-end:1em;--padding-bottom:17px;min-height:3.1em;font-size:min(1.25rem, 60px)}:host(.button-small){--border-radius:6px;--padding-top:4px;--padding-start:0.9em;--padding-end:0.9em;--padding-bottom:4px;min-height:2.1em;font-size:min(0.8125rem, 39px)}:host(.button-round){--border-radius:999px;--padding-top:0;--padding-start:26px;--padding-end:26px;--padding-bottom:0}:host(.button-strong){font-weight:600}:host(.button-has-icon-only){--padding-top:0;--padding-bottom:var(--padding-top);--padding-end:var(--padding-top);--padding-start:var(--padding-end);min-width:clamp(30px, 2.125em, 60px);min-height:clamp(30px, 2.125em, 60px)}::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.12px, 1.125em, 43.02px)}:host(.button-small.button-has-icon-only){min-width:clamp(23px, 2.16em, 54px);min-height:clamp(23px, 2.16em, 54px)}:host(.button-small) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(12.1394px, 1.308125em, 40.1856px)}:host(.button-large.button-has-icon-only){min-width:clamp(46px, 2.5em, 78px);min-height:clamp(46px, 2.5em, 78px)}:host(.button-large) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.12px, 0.9em, 43.056px)}:host(.button-outline.ion-focused.ion-color) .button-native,:host(.button-clear.ion-focused.ion-color) .button-native{color:var(--ion-color-base)}:host(.button-outline.ion-focused.ion-color) .button-native::after,:host(.button-clear.ion-focused.ion-color) .button-native::after{background:var(--ion-color-base)}:host(.button-solid.ion-color.ion-focused) .button-native::after{background:var(--ion-color-shade)}@media (any-hover: hover){:host(.button-clear:not(.ion-activated):hover),:host(.button-outline:not(.ion-activated):hover){opacity:0.6}:host(.button-clear.ion-color:hover) .button-native,:host(.button-outline.ion-color:hover) .button-native{color:var(--ion-color-base)}:host(.button-clear.ion-color:hover) .button-native::after,:host(.button-outline.ion-color:hover) .button-native::after{background:transparent}:host(.button-solid.ion-color:hover) .button-native::after{background:var(--ion-color-tint)}:host(:hover.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color):not(.ion-activated)) .button-native::after{background:#fff;opacity:0.1}}:host(.button-clear.ion-activated){opacity:0.4}:host(.button-outline.ion-activated.ion-color) .button-native{color:var(--ion-color-contrast)}:host(.button-outline.ion-activated.ion-color) .button-native::after{background:var(--ion-color-base)}:host(.button-solid.ion-color.ion-activated) .button-native::after{background:var(--ion-color-shade)}:host(.button-outline.ion-activated.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--color));color:var(--ion-toolbar-background, var(--background), var(--ion-color-primary-contrast, #fff))}\";\nconst IonButtonIosStyle0 = buttonIosCss;\nconst buttonMdCss = \":host{--overflow:hidden;--ripple-color:currentColor;--border-width:initial;--border-color:initial;--border-style:initial;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--box-shadow:none;display:inline-block;width:auto;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;white-space:normal;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;vertical-align:top;vertical-align:-webkit-baseline-middle;-webkit-font-kerning:none;font-kerning:none}:host(.button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.button-solid){--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff)}:host(.button-outline){--border-color:var(--ion-color-primary, #0054e9);--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-clear){--border-width:0;--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-block){display:block}:host(.button-block) .button-native{margin-left:0;margin-right:0;width:100%;clear:both;contain:content}:host(.button-block) .button-native::after{clear:both}:host(.button-full){display:block}:host(.button-full) .button-native{margin-left:0;margin-right:0;width:100%;contain:content}:host(.button-full:not(.button-round)) .button-native{border-radius:0;border-right-width:0;border-left-width:0}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);line-height:1;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:layout style;cursor:pointer;opacity:var(--opacity);overflow:var(--overflow);z-index:0;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-native::-moz-focus-inner{border:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted(ion-icon){font-size:1.35em;pointer-events:none}::slotted(ion-icon[slot=start]){-webkit-margin-start:-0.3em;margin-inline-start:-0.3em;-webkit-margin-end:0.3em;margin-inline-end:0.3em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=end]){-webkit-margin-start:0.3em;margin-inline-start:0.3em;-webkit-margin-end:-0.2em;margin-inline-end:-0.2em;margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){:host(:hover){color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-activated){color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.button-solid.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.button-outline.ion-color) .button-native{border-color:var(--ion-color-base);background:transparent;color:var(--ion-color-base)}:host(.button-clear.ion-color) .button-native{background:transparent;color:var(--ion-color-base)}:host(.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{color:var(--ion-toolbar-color, var(--color))}:host(.button-outline.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{border-color:var(--ion-toolbar-color, var(--color, var(--border-color)))}:host(.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--background));color:var(--ion-toolbar-background, var(--color))}:host{--border-radius:4px;--padding-top:8px;--padding-bottom:8px;--padding-start:1.1em;--padding-end:1.1em;--transition:box-shadow 280ms cubic-bezier(.4, 0, .2, 1),\\n                background-color 15ms linear,\\n                color 15ms linear;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:4px;margin-bottom:4px;min-height:36px;font-size:0.875rem;font-weight:500;letter-spacing:0.06em;text-transform:uppercase}:host(.button-solid){--background-activated:transparent;--background-hover:var(--ion-color-primary-contrast, #fff);--background-focused:var(--ion-color-primary-contrast, #fff);--background-activated-opacity:0;--background-focused-opacity:.24;--background-hover-opacity:.08;--box-shadow:0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)}:host(.button-solid.ion-activated){--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12)}:host(.button-outline){--border-width:2px;--border-style:solid;--box-shadow:none;--background-activated:transparent;--background-focused:var(--ion-color-primary, #0054e9);--background-hover:var(--ion-color-primary, #0054e9);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04}:host(.button-outline.ion-activated.ion-color) .button-native{background:transparent}:host(.button-clear){--background-activated:transparent;--background-focused:var(--ion-color-primary, #0054e9);--background-hover:var(--ion-color-primary, #0054e9);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04}:host(.button-round){--border-radius:999px;--padding-top:0;--padding-start:26px;--padding-end:26px;--padding-bottom:0}:host(.button-large){--padding-top:14px;--padding-start:1em;--padding-end:1em;--padding-bottom:14px;min-height:2.8em;font-size:1.25rem}:host(.button-small){--padding-top:4px;--padding-start:0.9em;--padding-end:0.9em;--padding-bottom:4px;min-height:2.1em;font-size:0.8125rem}:host(.button-strong){font-weight:bold}:host(.button-has-icon-only){--padding-top:0;--padding-bottom:var(--padding-top);--padding-end:var(--padding-top);--padding-start:var(--padding-end);min-width:clamp(30px, 2.86em, 60px);min-height:clamp(30px, 2.86em, 60px)}::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.104px, 1.6em, 43.008px)}:host(.button-small.button-has-icon-only){min-width:clamp(23px, 2.16em, 54px);min-height:clamp(23px, 2.16em, 54px)}:host(.button-small) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(13.002px, 1.23125em, 40.385px)}:host(.button-large.button-has-icon-only){min-width:clamp(46px, 2.5em, 78px);min-height:clamp(46px, 2.5em, 78px)}:host(.button-large) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.008px, 1.4em, 43.008px)}:host(.button-solid.ion-color.ion-focused) .button-native::after{background:var(--ion-color-contrast)}:host(.button-clear.ion-color.ion-focused) .button-native::after,:host(.button-outline.ion-color.ion-focused) .button-native::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.button-solid.ion-color:hover) .button-native::after{background:var(--ion-color-contrast)}:host(.button-clear.ion-color:hover) .button-native::after,:host(.button-outline.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}:host(.button-outline.ion-activated.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-background, var(--color));color:var(--ion-toolbar-color, var(--background), var(--ion-color-primary-contrast, #fff))}\";\nconst IonButtonMdStyle0 = buttonMdCss;\nconst Button = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.inItem = false;\n    this.inListHeader = false;\n    this.inToolbar = false;\n    this.formButtonEl = null;\n    this.formEl = null;\n    this.inheritedAttributes = {};\n    this.handleClick = ev => {\n      const {\n        el\n      } = this;\n      if (this.type === 'button') {\n        openURL(this.href, ev, this.routerDirection, this.routerAnimation);\n      } else if (hasShadowDom(el)) {\n        this.submitForm(ev);\n      }\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.slotChanged = () => {\n      /**\n       * Ensures that the 'has-icon-only' class is properly added\n       * or removed from `ion-button` when manipulating the\n       * `icon-only` slot.\n       *\n       * Without this, the 'has-icon-only' class is only checked\n       * or added when `ion-button` component first renders.\n       */\n      this.isCircle = this.hasIconOnly;\n    };\n    this.isCircle = false;\n    this.color = undefined;\n    this.buttonType = 'button';\n    this.disabled = false;\n    this.expand = undefined;\n    this.fill = undefined;\n    this.routerDirection = 'forward';\n    this.routerAnimation = undefined;\n    this.download = undefined;\n    this.href = undefined;\n    this.rel = undefined;\n    this.shape = undefined;\n    this.size = undefined;\n    this.strong = false;\n    this.target = undefined;\n    this.type = 'button';\n    this.form = undefined;\n  }\n  disabledChanged() {\n    const {\n      disabled\n    } = this;\n    if (this.formButtonEl) {\n      this.formButtonEl.disabled = disabled;\n    }\n  }\n  /**\n   * This is responsible for rendering a hidden native\n   * button element inside the associated form. This allows\n   * users to submit a form by pressing \"Enter\" when a text\n   * field inside of the form is focused. The native button\n   * rendered inside of `ion-button` is in the Shadow DOM\n   * and therefore does not participate in form submission\n   * which is why the following code is necessary.\n   */\n  renderHiddenButton() {\n    const formEl = this.formEl = this.findForm();\n    if (formEl) {\n      const {\n        formButtonEl\n      } = this;\n      /**\n       * If the form already has a rendered form button\n       * then do not append a new one again.\n       */\n      if (formButtonEl !== null && formEl.contains(formButtonEl)) {\n        return;\n      }\n      // Create a hidden native button inside of the form\n      const newFormButtonEl = this.formButtonEl = document.createElement('button');\n      newFormButtonEl.type = this.type;\n      newFormButtonEl.style.display = 'none';\n      // Only submit if the button is not disabled.\n      newFormButtonEl.disabled = this.disabled;\n      formEl.appendChild(newFormButtonEl);\n    }\n  }\n  componentWillLoad() {\n    this.inToolbar = !!this.el.closest('ion-buttons');\n    this.inListHeader = !!this.el.closest('ion-list-header');\n    this.inItem = !!this.el.closest('ion-item') || !!this.el.closest('ion-item-divider');\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  get hasIconOnly() {\n    return !!this.el.querySelector('[slot=\"icon-only\"]');\n  }\n  get rippleType() {\n    const hasClearFill = this.fill === undefined || this.fill === 'clear';\n    // If the button is in a toolbar, has a clear fill (which is the default)\n    // and only has an icon we use the unbounded \"circular\" ripple effect\n    if (hasClearFill && this.hasIconOnly && this.inToolbar) {\n      return 'unbounded';\n    }\n    return 'bounded';\n  }\n  /**\n   * Finds the form element based on the provided `form` selector\n   * or element reference provided.\n   */\n  findForm() {\n    const {\n      form\n    } = this;\n    if (form instanceof HTMLFormElement) {\n      return form;\n    }\n    if (typeof form === 'string') {\n      // Check if the string provided is a form id.\n      const el = document.getElementById(form);\n      if (el) {\n        if (el instanceof HTMLFormElement) {\n          return el;\n        } else {\n          /**\n           * The developer specified a string for the form attribute, but the\n           * element with that id is not a form element.\n           */\n          printIonWarning(`[ion-button] - Form with selector: \"#${form}\" could not be found. Verify that the id is attached to a <form> element.`, this.el);\n          return null;\n        }\n      } else {\n        /**\n         * The developer specified a string for the form attribute, but the\n         * element with that id could not be found in the DOM.\n         */\n        printIonWarning(`[ion-button] - Form with selector: \"#${form}\" could not be found. Verify that the id is correct and the form is rendered in the DOM.`, this.el);\n        return null;\n      }\n    }\n    if (form !== undefined) {\n      /**\n       * The developer specified a HTMLElement for the form attribute,\n       * but the element is not a HTMLFormElement.\n       * This will also catch if the developer tries to pass in null\n       * as the form attribute.\n       */\n      printIonWarning(`[ion-button] - The provided \"form\" element is invalid. Verify that the form is a HTMLFormElement and rendered in the DOM.`, this.el);\n      return null;\n    }\n    /**\n     * If the form element is not set, the button may be inside\n     * of a form element. Query the closest form element to the button.\n     */\n    return this.el.closest('form');\n  }\n  submitForm(ev) {\n    // this button wants to specifically submit a form\n    // climb up the dom to see if we're in a <form>\n    // and if so, then use JS to submit it\n    if (this.formEl && this.formButtonEl) {\n      ev.preventDefault();\n      this.formButtonEl.click();\n    }\n  }\n  render() {\n    const mode = getIonMode$1(this);\n    const {\n      buttonType,\n      type,\n      disabled,\n      rel,\n      target,\n      size,\n      href,\n      color,\n      expand,\n      hasIconOnly,\n      shape,\n      strong,\n      inheritedAttributes\n    } = this;\n    const finalSize = size === undefined && this.inItem ? 'small' : size;\n    const TagType = href === undefined ? 'button' : 'a';\n    const attrs = TagType === 'button' ? {\n      type\n    } : {\n      download: this.download,\n      href,\n      rel,\n      target\n    };\n    let fill = this.fill;\n    /**\n     * We check both undefined and null to\n     * work around https://github.com/ionic-team/stencil/issues/3586.\n     */\n    if (fill == null) {\n      fill = this.inToolbar || this.inListHeader ? 'clear' : 'solid';\n    }\n    /**\n     * We call renderHiddenButton in the render function to account\n     * for any properties being set async. For example, changing the\n     * \"type\" prop from \"button\" to \"submit\" after the component has\n     * loaded would warrant the hidden button being added to the\n     * associated form.\n     */\n    {\n      type !== 'button' && this.renderHiddenButton();\n    }\n    return h(Host, {\n      key: 'e213b0bb76b3f90f883b1a0ea463bb86c2df69c3',\n      onClick: this.handleClick,\n      \"aria-disabled\": disabled ? 'true' : null,\n      class: createColorClasses$1(color, {\n        [mode]: true,\n        [buttonType]: true,\n        [`${buttonType}-${expand}`]: expand !== undefined,\n        [`${buttonType}-${finalSize}`]: finalSize !== undefined,\n        [`${buttonType}-${shape}`]: shape !== undefined,\n        [`${buttonType}-${fill}`]: true,\n        [`${buttonType}-strong`]: strong,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'in-buttons': hostContext('ion-buttons', this.el),\n        'button-has-icon-only': hasIconOnly,\n        'button-disabled': disabled,\n        'ion-activatable': true,\n        'ion-focusable': true\n      })\n    }, h(TagType, Object.assign({\n      key: 'b7c2a46fb994024841219316f4089335fa463d84'\n    }, attrs, {\n      class: \"button-native\",\n      part: \"native\",\n      disabled: disabled,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur\n    }, inheritedAttributes), h(\"span\", {\n      key: '3e07a5c9f86836f9fbaefc6c617bdde6eb6f70cd',\n      class: \"button-inner\"\n    }, h(\"slot\", {\n      key: '41c08ae09aed16faaa57707d229ff75d97f0731c',\n      name: \"icon-only\",\n      onSlotchange: this.slotChanged\n    }), h(\"slot\", {\n      key: 'ab8b749e4572884cc04970a7594466b4a24c886e',\n      name: \"start\"\n    }), h(\"slot\", {\n      key: '397f7ff48ea45e82029414be95d29ae86e12c3a9'\n    }), h(\"slot\", {\n      key: '9a5627713002e8be3738d392b616f6e951b70e12',\n      name: \"end\"\n    })), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '105566543ceda102474edab7ca8a44c7e71af589',\n      type: this.rippleType\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nButton.style = {\n  ios: IonButtonIosStyle0,\n  md: IonButtonMdStyle0\n};\nconst validateContent = svgContent => {\n  const div = document.createElement('div');\n  div.innerHTML = svgContent;\n  // setup this way to ensure it works on our buddy IE\n  for (let i = div.childNodes.length - 1; i >= 0; i--) {\n    if (div.childNodes[i].nodeName.toLowerCase() !== 'svg') {\n      div.removeChild(div.childNodes[i]);\n    }\n  }\n  // must only have 1 root element\n  const svgElm = div.firstElementChild;\n  if (svgElm && svgElm.nodeName.toLowerCase() === 'svg') {\n    const svgClass = svgElm.getAttribute('class') || '';\n    svgElm.setAttribute('class', (svgClass + ' s-ion-icon').trim());\n    // root element must be an svg\n    // lets double check we've got valid elements\n    // do not allow scripts\n    if (isValid(svgElm)) {\n      return div.innerHTML;\n    }\n  }\n  return '';\n};\nconst isValid = elm => {\n  if (elm.nodeType === 1) {\n    if (elm.nodeName.toLowerCase() === 'script') {\n      return false;\n    }\n    for (let i = 0; i < elm.attributes.length; i++) {\n      const name = elm.attributes[i].name;\n      if (isStr(name) && name.toLowerCase().indexOf('on') === 0) {\n        return false;\n      }\n    }\n    for (let i = 0; i < elm.childNodes.length; i++) {\n      if (!isValid(elm.childNodes[i])) {\n        return false;\n      }\n    }\n  }\n  return true;\n};\nconst isSvgDataUrl = url => url.startsWith('data:image/svg+xml');\nconst isEncodedDataUrl = url => url.indexOf(';utf8,') !== -1;\nconst ioniconContent = new Map();\nconst requests = new Map();\nlet parser;\nconst getSvgContent = (url, sanitize) => {\n  // see if we already have a request for this url\n  let req = requests.get(url);\n  if (!req) {\n    if (typeof fetch !== 'undefined' && typeof document !== 'undefined') {\n      /**\n       * If the url is a data url of an svg, then try to parse it\n       * with the DOMParser. This works with content security policies enabled.\n       */\n      if (isSvgDataUrl(url) && isEncodedDataUrl(url)) {\n        if (!parser) {\n          /**\n           * Create an instance of the DOM parser. This creates a single\n           * parser instance for the entire app, which is more efficient.\n           */\n          parser = new DOMParser();\n        }\n        const doc = parser.parseFromString(url, 'text/html');\n        const svg = doc.querySelector('svg');\n        if (svg) {\n          ioniconContent.set(url, svg.outerHTML);\n        }\n        return Promise.resolve();\n      } else {\n        // we don't already have a request\n        req = fetch(url).then(rsp => {\n          if (rsp.ok) {\n            return rsp.text().then(svgContent => {\n              if (svgContent && sanitize !== false) {\n                svgContent = validateContent(svgContent);\n              }\n              ioniconContent.set(url, svgContent || '');\n            });\n          }\n          ioniconContent.set(url, '');\n        });\n        // cache for the same requests\n        requests.set(url, req);\n      }\n    } else {\n      // set to empty for ssr scenarios and resolve promise\n      ioniconContent.set(url, '');\n      return Promise.resolve();\n    }\n  }\n  return req;\n};\nconst iconCss = \":host{display:inline-block;width:1em;height:1em;contain:strict;fill:currentColor;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host .ionicon{stroke:currentColor}.ionicon-fill-none{fill:none}.ionicon-stroke-width{stroke-width:32px;stroke-width:var(--ionicon-stroke-width, 32px)}.icon-inner,.ionicon,svg{display:block;height:100%;width:100%}@supports (background: -webkit-named-image(i)){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}@supports not selector(:dir(rtl)) and selector(:host-context([dir='rtl'])){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}:host(.flip-rtl):host-context([dir='rtl']) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}@supports selector(:dir(rtl)){:host(.flip-rtl:dir(rtl)) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}:host(.flip-rtl:dir(ltr)) .icon-inner{-webkit-transform:scaleX(1);transform:scaleX(1)}}:host(.icon-small){font-size:1.125rem !important}:host(.icon-large){font-size:2rem !important}:host(.ion-color){color:var(--ion-color-base) !important}:host(.ion-color-primary){--ion-color-base:var(--ion-color-primary, #3880ff)}:host(.ion-color-secondary){--ion-color-base:var(--ion-color-secondary, #0cd1e8)}:host(.ion-color-tertiary){--ion-color-base:var(--ion-color-tertiary, #f4a942)}:host(.ion-color-success){--ion-color-base:var(--ion-color-success, #10dc60)}:host(.ion-color-warning){--ion-color-base:var(--ion-color-warning, #ffce00)}:host(.ion-color-danger){--ion-color-base:var(--ion-color-danger, #f14141)}:host(.ion-color-light){--ion-color-base:var(--ion-color-light, #f4f5f8)}:host(.ion-color-medium){--ion-color-base:var(--ion-color-medium, #989aa2)}:host(.ion-color-dark){--ion-color-base:var(--ion-color-dark, #222428)}\";\nconst IonIconStyle0 = iconCss;\nconst Icon = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.iconName = null;\n    this.inheritedAttributes = {};\n    this.didLoadIcon = false;\n    this.svgContent = undefined;\n    this.isVisible = false;\n    this.mode = getIonMode();\n    this.color = undefined;\n    this.ios = undefined;\n    this.md = undefined;\n    this.flipRtl = undefined;\n    this.name = undefined;\n    this.src = undefined;\n    this.icon = undefined;\n    this.size = undefined;\n    this.lazy = false;\n    this.sanitize = true;\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n  }\n  connectedCallback() {\n    // purposely do not return the promise here because loading\n    // the svg file should not hold up loading the app\n    // only load the svg if it's visible\n    this.waitUntilVisible(this.el, '50px', () => {\n      this.isVisible = true;\n      this.loadIcon();\n    });\n  }\n  componentDidLoad() {\n    /**\n     * Addresses an Angular issue where property values are assigned after the 'connectedCallback' but prior to the registration of watchers.\n     * This enhancement ensures the loading of an icon when the component has finished rendering and the icon has yet to apply the SVG data.\n     * This modification pertains to the usage of Angular's binding syntax:\n     * `<ion-icon [name]=\"myIconName\"></ion-icon>`\n     */\n    if (!this.didLoadIcon) {\n      this.loadIcon();\n    }\n  }\n  disconnectedCallback() {\n    if (this.io) {\n      this.io.disconnect();\n      this.io = undefined;\n    }\n  }\n  waitUntilVisible(el, rootMargin, cb) {\n    if (this.lazy && typeof window !== 'undefined' && window.IntersectionObserver) {\n      const io = this.io = new window.IntersectionObserver(data => {\n        if (data[0].isIntersecting) {\n          io.disconnect();\n          this.io = undefined;\n          cb();\n        }\n      }, {\n        rootMargin\n      });\n      io.observe(el);\n    } else {\n      // browser doesn't support IntersectionObserver\n      // so just fallback to always show it\n      cb();\n    }\n  }\n  loadIcon() {\n    if (this.isVisible) {\n      const url = getUrl(this);\n      if (url) {\n        if (ioniconContent.has(url)) {\n          // sync if it's already loaded\n          this.svgContent = ioniconContent.get(url);\n        } else {\n          // async if it hasn't been loaded\n          getSvgContent(url, this.sanitize).then(() => this.svgContent = ioniconContent.get(url));\n        }\n        this.didLoadIcon = true;\n      }\n    }\n    this.iconName = getName(this.name, this.icon, this.mode, this.ios, this.md);\n  }\n  render() {\n    const {\n      flipRtl,\n      iconName,\n      inheritedAttributes,\n      el\n    } = this;\n    const mode = this.mode || 'md';\n    // we have designated that arrows & chevrons should automatically flip (unless flip-rtl is set to false) because \"back\" is left in ltr and right in rtl, and \"forward\" is the opposite\n    const shouldAutoFlip = iconName ? (iconName.includes('arrow') || iconName.includes('chevron')) && flipRtl !== false : false;\n    // if shouldBeFlippable is true, the icon should change direction when `dir` changes\n    const shouldBeFlippable = flipRtl || shouldAutoFlip;\n    return h(Host, Object.assign({\n      role: \"img\",\n      class: Object.assign(Object.assign({\n        [mode]: true\n      }, createColorClasses(this.color)), {\n        [`icon-${this.size}`]: !!this.size,\n        'flip-rtl': shouldBeFlippable,\n        'icon-rtl': shouldBeFlippable && isRTL(el)\n      })\n    }, inheritedAttributes), this.svgContent ? h(\"div\", {\n      class: \"icon-inner\",\n      innerHTML: this.svgContent\n    }) : h(\"div\", {\n      class: \"icon-inner\"\n    }));\n  }\n  static get assetsDirs() {\n    return [\"svg\"];\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"name\": [\"loadIcon\"],\n      \"src\": [\"loadIcon\"],\n      \"icon\": [\"loadIcon\"],\n      \"ios\": [\"loadIcon\"],\n      \"md\": [\"loadIcon\"]\n    };\n  }\n};\nconst getIonMode = () => typeof document !== 'undefined' && document.documentElement.getAttribute('mode') || 'md';\nconst createColorClasses = color => {\n  return color ? {\n    'ion-color': true,\n    [`ion-color-${color}`]: true\n  } : null;\n};\nIcon.style = IonIconStyle0;\nexport { Button as ion_button, Icon as ion_icon };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAI;AACJ,IAAM,aAAa,MAAM;AACvB,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO,oBAAI,IAAI;AAAA,EACjB,OAAO;AACL,QAAI,CAAC,YAAY;AACf,YAAM,MAAM;AACZ,UAAI,WAAW,IAAI,YAAY,CAAC;AAChC,mBAAa,IAAI,SAAS,MAAM,IAAI,SAAS,OAAO,oBAAI,IAAI;AAAA,IAC9D;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,SAAS,OAAK;AAClB,MAAI,MAAM,OAAO,EAAE,GAAG;AACtB,MAAI,KAAK;AACP,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE;AACjD,MAAI,KAAK;AACP,WAAO,YAAY,KAAK,CAAC;AAAA,EAC3B;AACA,MAAI,EAAE,MAAM;AACV,UAAM,OAAO,EAAE,IAAI;AACnB,QAAI,KAAK;AACP,aAAO;AAAA,IACT;AACA,UAAM,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC;AAC3B,QAAI,KAAK;AACP,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,cAAc,CAAC,UAAU,WAAW;AACxC,QAAM,MAAM,WAAW,EAAE,IAAI,QAAQ;AACrC,MAAI,KAAK;AACP,WAAO;AAAA,EACT;AACA,MAAI;AACF,WAAO,aAAa,OAAO,QAAQ,MAAM;AAAA,EAC3C,SAAS,GAAG;AAQV,YAAQ,KAAK,sDAAsD,QAAQ,4HAA4H,MAAM;AAAA,EAC/M;AACF;AACA,IAAM,UAAU,CAAC,UAAU,MAAM,MAAM,KAAK,OAAO;AAEjD,UAAQ,QAAQ,QAAQ,IAAI,OAAO,QAAQ,QAAQ;AAGnD,MAAI,OAAO,SAAS,OAAO;AACzB,eAAW,QAAQ,GAAG;AAAA,EACxB,WAAW,MAAM,SAAS,MAAM;AAC9B,eAAW,QAAQ,EAAE;AAAA,EACvB,OAAO;AACL,QAAI,CAAC,YAAY,QAAQ,CAAC,MAAM,IAAI,GAAG;AACrC,iBAAW;AAAA,IACb;AACA,QAAI,MAAM,QAAQ,GAAG;AACnB,iBAAW,QAAQ,QAAQ;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,CAAC,MAAM,QAAQ,KAAK,SAAS,KAAK,MAAM,IAAI;AAC9C,WAAO;AAAA,EACT;AAEA,QAAM,eAAe,SAAS,QAAQ,gBAAgB,EAAE;AACxD,MAAI,iBAAiB,IAAI;AACvB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,SAAS,SAAO;AACpB,MAAI,MAAM,GAAG,GAAG;AACd,UAAM,IAAI,KAAK;AACf,QAAI,MAAM,GAAG,GAAG;AACd,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,QAAQ,SAAO,IAAI,SAAS,KAAK,UAAU,KAAK,GAAG;AACzD,IAAM,QAAQ,SAAO,OAAO,QAAQ;AACpC,IAAM,UAAU,SAAO,IAAI,YAAY;AAWvC,IAAM,oBAAoB,CAAC,IAAI,aAAa,CAAC,MAAM;AACjD,QAAM,kBAAkB,CAAC;AACzB,aAAW,QAAQ,UAAQ;AACzB,QAAI,GAAG,aAAa,IAAI,GAAG;AACzB,YAAM,QAAQ,GAAG,aAAa,IAAI;AAClC,UAAI,UAAU,MAAM;AAClB,wBAAgB,IAAI,IAAI,GAAG,aAAa,IAAI;AAAA,MAC9C;AACA,SAAG,gBAAgB,IAAI;AAAA,IACzB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAMA,IAAM,QAAQ,YAAU;AACtB,MAAI,QAAQ;AACV,QAAI,OAAO,QAAQ,IAAI;AACrB,aAAO,OAAO,IAAI,YAAY,MAAM;AAAA,IACtC;AAAA,EACF;AACA,UAAQ,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,IAAI,YAAY,OAAO;AAC9F;AACA,IAAM,eAAe;AACrB,IAAM,qBAAqB;AAC3B,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,SAAS,MAAM;AAAA,EACnB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,SAAS;AACd,SAAK,sBAAsB,CAAC;AAC5B,SAAK,cAAc,QAAM;AACvB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,KAAK,SAAS,UAAU;AAC1B,gBAAQ,KAAK,MAAM,IAAI,KAAK,iBAAiB,KAAK,eAAe;AAAA,MACnE,WAAW,aAAa,EAAE,GAAG;AAC3B,aAAK,WAAW,EAAE;AAAA,MACpB;AAAA,IACF;AACA,SAAK,UAAU,MAAM;AACnB,WAAK,SAAS,KAAK;AAAA,IACrB;AACA,SAAK,SAAS,MAAM;AAClB,WAAK,QAAQ,KAAK;AAAA,IACpB;AACA,SAAK,cAAc,MAAM;AASvB,WAAK,WAAW,KAAK;AAAA,IACvB;AACA,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,WAAW;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,qBAAqB;AACnB,UAAM,SAAS,KAAK,SAAS,KAAK,SAAS;AAC3C,QAAI,QAAQ;AACV,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AAKJ,UAAI,iBAAiB,QAAQ,OAAO,SAAS,YAAY,GAAG;AAC1D;AAAA,MACF;AAEA,YAAM,kBAAkB,KAAK,eAAe,SAAS,cAAc,QAAQ;AAC3E,sBAAgB,OAAO,KAAK;AAC5B,sBAAgB,MAAM,UAAU;AAEhC,sBAAgB,WAAW,KAAK;AAChC,aAAO,YAAY,eAAe;AAAA,IACpC;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,YAAY,CAAC,CAAC,KAAK,GAAG,QAAQ,aAAa;AAChD,SAAK,eAAe,CAAC,CAAC,KAAK,GAAG,QAAQ,iBAAiB;AACvD,SAAK,SAAS,CAAC,CAAC,KAAK,GAAG,QAAQ,UAAU,KAAK,CAAC,CAAC,KAAK,GAAG,QAAQ,kBAAkB;AACnF,SAAK,sBAAsB,sBAAsB,KAAK,EAAE;AAAA,EAC1D;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,CAAC,CAAC,KAAK,GAAG,cAAc,oBAAoB;AAAA,EACrD;AAAA,EACA,IAAI,aAAa;AACf,UAAM,eAAe,KAAK,SAAS,UAAa,KAAK,SAAS;AAG9D,QAAI,gBAAgB,KAAK,eAAe,KAAK,WAAW;AACtD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,gBAAgB,iBAAiB;AACnC,aAAO;AAAA,IACT;AACA,QAAI,OAAO,SAAS,UAAU;AAE5B,YAAM,KAAK,SAAS,eAAe,IAAI;AACvC,UAAI,IAAI;AACN,YAAI,cAAc,iBAAiB;AACjC,iBAAO;AAAA,QACT,OAAO;AAKL,0BAAgB,wCAAwC,IAAI,6EAA6E,KAAK,EAAE;AAChJ,iBAAO;AAAA,QACT;AAAA,MACF,OAAO;AAKL,wBAAgB,wCAAwC,IAAI,4FAA4F,KAAK,EAAE;AAC/J,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,SAAS,QAAW;AAOtB,sBAAgB,6HAA6H,KAAK,EAAE;AACpJ,aAAO;AAAA,IACT;AAKA,WAAO,KAAK,GAAG,QAAQ,MAAM;AAAA,EAC/B;AAAA,EACA,WAAW,IAAI;AAIb,QAAI,KAAK,UAAU,KAAK,cAAc;AACpC,SAAG,eAAe;AAClB,WAAK,aAAa,MAAM;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAa,IAAI;AAC9B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,YAAY,SAAS,UAAa,KAAK,SAAS,UAAU;AAChE,UAAM,UAAU,SAAS,SAAY,WAAW;AAChD,UAAM,QAAQ,YAAY,WAAW;AAAA,MACnC;AAAA,IACF,IAAI;AAAA,MACF,UAAU,KAAK;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAI,OAAO,KAAK;AAKhB,QAAI,QAAQ,MAAM;AAChB,aAAO,KAAK,aAAa,KAAK,eAAe,UAAU;AAAA,IACzD;AAQA;AACE,eAAS,YAAY,KAAK,mBAAmB;AAAA,IAC/C;AACA,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,SAAS,KAAK;AAAA,MACd,iBAAiB,WAAW,SAAS;AAAA,MACrC,OAAO,mBAAqB,OAAO;AAAA,QACjC,CAAC,IAAI,GAAG;AAAA,QACR,CAAC,UAAU,GAAG;AAAA,QACd,CAAC,GAAG,UAAU,IAAI,MAAM,EAAE,GAAG,WAAW;AAAA,QACxC,CAAC,GAAG,UAAU,IAAI,SAAS,EAAE,GAAG,cAAc;AAAA,QAC9C,CAAC,GAAG,UAAU,IAAI,KAAK,EAAE,GAAG,UAAU;AAAA,QACtC,CAAC,GAAG,UAAU,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3B,CAAC,GAAG,UAAU,SAAS,GAAG;AAAA,QAC1B,cAAc,YAAY,eAAe,KAAK,EAAE;AAAA,QAChD,oBAAoB,YAAY,sBAAsB,KAAK,EAAE;AAAA,QAC7D,cAAc,YAAY,eAAe,KAAK,EAAE;AAAA,QAChD,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,iBAAiB;AAAA,MACnB,CAAC;AAAA,IACH,GAAG,EAAE,SAAS,OAAO,OAAO;AAAA,MAC1B,KAAK;AAAA,IACP,GAAG,OAAO;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,IACf,GAAG,mBAAmB,GAAG,EAAE,QAAQ;AAAA,MACjC,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,MACL,MAAM;AAAA,MACN,cAAc,KAAK;AAAA,IACrB,CAAC,GAAG,EAAE,QAAQ;AAAA,MACZ,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,QAAQ;AAAA,MACZ,KAAK;AAAA,IACP,CAAC,GAAG,EAAE,QAAQ;AAAA,MACZ,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,CAAC,GAAG,SAAS,QAAQ,EAAE,qBAAqB;AAAA,MAC3C,KAAK;AAAA,MACL,MAAM,KAAK;AAAA,IACb,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,YAAY,CAAC,iBAAiB;AAAA,IAChC;AAAA,EACF;AACF;AACA,OAAO,QAAQ;AAAA,EACb,KAAK;AAAA,EACL,IAAI;AACN;AACA,IAAM,kBAAkB,gBAAc;AACpC,QAAM,MAAM,SAAS,cAAc,KAAK;AACxC,MAAI,YAAY;AAEhB,WAAS,IAAI,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AACnD,QAAI,IAAI,WAAW,CAAC,EAAE,SAAS,YAAY,MAAM,OAAO;AACtD,UAAI,YAAY,IAAI,WAAW,CAAC,CAAC;AAAA,IACnC;AAAA,EACF;AAEA,QAAM,SAAS,IAAI;AACnB,MAAI,UAAU,OAAO,SAAS,YAAY,MAAM,OAAO;AACrD,UAAM,WAAW,OAAO,aAAa,OAAO,KAAK;AACjD,WAAO,aAAa,UAAU,WAAW,eAAe,KAAK,CAAC;AAI9D,QAAI,QAAQ,MAAM,GAAG;AACnB,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,UAAU,SAAO;AACrB,MAAI,IAAI,aAAa,GAAG;AACtB,QAAI,IAAI,SAAS,YAAY,MAAM,UAAU;AAC3C,aAAO;AAAA,IACT;AACA,aAAS,IAAI,GAAG,IAAI,IAAI,WAAW,QAAQ,KAAK;AAC9C,YAAM,OAAO,IAAI,WAAW,CAAC,EAAE;AAC/B,UAAI,MAAM,IAAI,KAAK,KAAK,YAAY,EAAE,QAAQ,IAAI,MAAM,GAAG;AACzD,eAAO;AAAA,MACT;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,IAAI,WAAW,QAAQ,KAAK;AAC9C,UAAI,CAAC,QAAQ,IAAI,WAAW,CAAC,CAAC,GAAG;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,eAAe,SAAO,IAAI,WAAW,oBAAoB;AAC/D,IAAM,mBAAmB,SAAO,IAAI,QAAQ,QAAQ,MAAM;AAC1D,IAAM,iBAAiB,oBAAI,IAAI;AAC/B,IAAM,WAAW,oBAAI,IAAI;AACzB,IAAI;AACJ,IAAM,gBAAgB,CAAC,KAAK,aAAa;AAEvC,MAAI,MAAM,SAAS,IAAI,GAAG;AAC1B,MAAI,CAAC,KAAK;AACR,QAAI,OAAO,UAAU,eAAe,OAAO,aAAa,aAAa;AAKnE,UAAI,aAAa,GAAG,KAAK,iBAAiB,GAAG,GAAG;AAC9C,YAAI,CAAC,QAAQ;AAKX,mBAAS,IAAI,UAAU;AAAA,QACzB;AACA,cAAM,MAAM,OAAO,gBAAgB,KAAK,WAAW;AACnD,cAAM,MAAM,IAAI,cAAc,KAAK;AACnC,YAAI,KAAK;AACP,yBAAe,IAAI,KAAK,IAAI,SAAS;AAAA,QACvC;AACA,eAAO,QAAQ,QAAQ;AAAA,MACzB,OAAO;AAEL,cAAM,MAAM,GAAG,EAAE,KAAK,SAAO;AAC3B,cAAI,IAAI,IAAI;AACV,mBAAO,IAAI,KAAK,EAAE,KAAK,gBAAc;AACnC,kBAAI,cAAc,aAAa,OAAO;AACpC,6BAAa,gBAAgB,UAAU;AAAA,cACzC;AACA,6BAAe,IAAI,KAAK,cAAc,EAAE;AAAA,YAC1C,CAAC;AAAA,UACH;AACA,yBAAe,IAAI,KAAK,EAAE;AAAA,QAC5B,CAAC;AAED,iBAAS,IAAI,KAAK,GAAG;AAAA,MACvB;AAAA,IACF,OAAO;AAEL,qBAAe,IAAI,KAAK,EAAE;AAC1B,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,UAAU;AAChB,IAAM,gBAAgB;AACtB,IAAM,OAAO,MAAM;AAAA,EACjB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW;AAChB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,OAAOA,YAAW;AACvB,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,oBAAoB;AAClB,SAAK,sBAAsB,kBAAkB,KAAK,IAAI,CAAC,YAAY,CAAC;AAAA,EACtE;AAAA,EACA,oBAAoB;AAIlB,SAAK,iBAAiB,KAAK,IAAI,QAAQ,MAAM;AAC3C,WAAK,YAAY;AACjB,WAAK,SAAS;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AAOjB,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,IAAI;AACX,WAAK,GAAG,WAAW;AACnB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,iBAAiB,IAAI,YAAY,IAAI;AACnC,QAAI,KAAK,QAAQ,OAAO,WAAW,eAAe,OAAO,sBAAsB;AAC7E,YAAM,KAAK,KAAK,KAAK,IAAI,OAAO,qBAAqB,UAAQ;AAC3D,YAAI,KAAK,CAAC,EAAE,gBAAgB;AAC1B,aAAG,WAAW;AACd,eAAK,KAAK;AACV,aAAG;AAAA,QACL;AAAA,MACF,GAAG;AAAA,QACD;AAAA,MACF,CAAC;AACD,SAAG,QAAQ,EAAE;AAAA,IACf,OAAO;AAGL,SAAG;AAAA,IACL;AAAA,EACF;AAAA,EACA,WAAW;AACT,QAAI,KAAK,WAAW;AAClB,YAAM,MAAM,OAAO,IAAI;AACvB,UAAI,KAAK;AACP,YAAI,eAAe,IAAI,GAAG,GAAG;AAE3B,eAAK,aAAa,eAAe,IAAI,GAAG;AAAA,QAC1C,OAAO;AAEL,wBAAc,KAAK,KAAK,QAAQ,EAAE,KAAK,MAAM,KAAK,aAAa,eAAe,IAAI,GAAG,CAAC;AAAA,QACxF;AACA,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AACA,SAAK,WAAW,QAAQ,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,EAAE;AAAA,EAC5E;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,KAAK,QAAQ;AAE1B,UAAM,iBAAiB,YAAY,SAAS,SAAS,OAAO,KAAK,SAAS,SAAS,SAAS,MAAM,YAAY,QAAQ;AAEtH,UAAM,oBAAoB,WAAW;AACrC,WAAO,EAAE,MAAM,OAAO,OAAO;AAAA,MAC3B,MAAM;AAAA,MACN,OAAO,OAAO,OAAO,OAAO,OAAO;AAAA,QACjC,CAAC,IAAI,GAAG;AAAA,MACV,GAAGC,oBAAmB,KAAK,KAAK,CAAC,GAAG;AAAA,QAClC,CAAC,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK;AAAA,QAC9B,YAAY;AAAA,QACZ,YAAY,qBAAqB,MAAM,EAAE;AAAA,MAC3C,CAAC;AAAA,IACH,GAAG,mBAAmB,GAAG,KAAK,aAAa,EAAE,OAAO;AAAA,MAClD,OAAO;AAAA,MACP,WAAW,KAAK;AAAA,IAClB,CAAC,IAAI,EAAE,OAAO;AAAA,MACZ,OAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,WAAW,aAAa;AACtB,WAAO,CAAC,KAAK;AAAA,EACf;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,QAAQ,CAAC,UAAU;AAAA,MACnB,OAAO,CAAC,UAAU;AAAA,MAClB,QAAQ,CAAC,UAAU;AAAA,MACnB,OAAO,CAAC,UAAU;AAAA,MAClB,MAAM,CAAC,UAAU;AAAA,IACnB;AAAA,EACF;AACF;AACA,IAAMD,cAAa,MAAM,OAAO,aAAa,eAAe,SAAS,gBAAgB,aAAa,MAAM,KAAK;AAC7G,IAAMC,sBAAqB,WAAS;AAClC,SAAO,QAAQ;AAAA,IACb,aAAa;AAAA,IACb,CAAC,aAAa,KAAK,EAAE,GAAG;AAAA,EAC1B,IAAI;AACN;AACA,KAAK,QAAQ;", "names": ["getIonMode", "createColorClasses"], "x_google_ignoreList": [0]}
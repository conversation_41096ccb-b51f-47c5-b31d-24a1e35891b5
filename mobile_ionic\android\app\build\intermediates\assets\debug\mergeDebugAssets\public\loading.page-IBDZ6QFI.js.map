{"version": 3, "sources": ["src/app/pages/loading/loading.page.ts", "src/app/pages/loading/loading.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-loading',\r\n  templateUrl: './loading.page.html',\r\n  styleUrls: ['./loading.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule]\r\n})\r\nexport class LoadingPage implements OnInit {\r\n  isOnline: boolean = false;\r\n\r\n  constructor(private router: Router) {}\r\n\r\n  ngOnInit() {\r\n    // Add debug logging\r\n    console.log('LoadingPage ngOnInit');\r\n    const win = window as any;\r\n    if (win.appDebug) {\r\n      win.appDebug('LoadingPage ngOnInit');\r\n    }\r\n    this.checkInternetConnection();\r\n  }\r\n\r\n  checkInternetConnection() {\r\n    this.isOnline = navigator.onLine;\r\n    // Add debug logging\r\n    console.log('LoadingPage checkInternetConnection, isOnline:', this.isOnline);\r\n    const win = window as any;\r\n    if (win.appDebug) {\r\n      win.appDebug('LoadingPage checkInternetConnection, isOnline: ' + this.isOnline);\r\n    }\r\n\r\n    // Check authentication and onboarding status regardless of internet connection\r\n    const token = localStorage.getItem('token');\r\n    const onboardingComplete = localStorage.getItem('onboardingComplete');\r\n\r\n    console.log('Auth status - Token:', !!token, 'Onboarding complete:', onboardingComplete === 'true', 'Online:', this.isOnline);\r\n\r\n    setTimeout(() => {\r\n      if (token && onboardingComplete === 'true') {\r\n        // User is returning and has completed onboarding - go directly to tabs\r\n        console.log('User is authenticated and onboarding complete - navigating to tabs/home');\r\n        if (win.appDebug) {\r\n          win.appDebug('LoadingPage navigating to tabs/home (authenticated & onboarded)');\r\n        }\r\n        this.router.navigate(['/tabs/home']);\r\n      } else if (token) {\r\n        // User is authenticated but hasn't completed onboarding\r\n        console.log('User is authenticated but onboarding incomplete - navigating to welcome');\r\n        if (win.appDebug) {\r\n          win.appDebug('LoadingPage navigating to welcome (authenticated but not onboarded)');\r\n        }\r\n        this.router.navigate(['/welcome']);\r\n      } else {\r\n        // New or logged out user - go to login\r\n        console.log('User is not authenticated - navigating to login');\r\n        if (win.appDebug) {\r\n          win.appDebug('LoadingPage navigating to login (not authenticated)');\r\n        }\r\n        this.router.navigate(['/login']);\r\n      }\r\n    }, 1000); // Reduced wait time to 1 second for better UX\r\n  }\r\n\r\n  ionViewWillEnter() {\r\n    // Add event listeners for online/offline status\r\n    window.addEventListener('online', this.updateOnlineStatus.bind(this));\r\n    window.addEventListener('offline', this.updateOnlineStatus.bind(this));\r\n  }\r\n\r\n  ionViewWillLeave() {\r\n    // Remove event listeners\r\n    window.removeEventListener('online', this.updateOnlineStatus.bind(this));\r\n    window.removeEventListener('offline', this.updateOnlineStatus.bind(this));\r\n  }\r\n\r\n  private updateOnlineStatus() {\r\n    this.isOnline = navigator.onLine;\r\n    const win = window as any;\r\n    if (win.appDebug) {\r\n      win.appDebug('LoadingPage updateOnlineStatus, isOnline: ' + this.isOnline);\r\n    }\r\n    // Always check connection status, regardless of online/offline state\r\n    this.checkInternetConnection();\r\n  }\r\n}", "<ion-content class=\"ion-padding\">\r\n  <div class=\"loading-container\">\r\n    <div class=\"loader\"></div>\r\n    <h2>Loading...</h2>\r\n    \r\n    <ion-alert\r\n      [isOpen]=\"!isOnline\"\r\n      header=\"No Internet Connection\"\r\n      message=\"Please check your internet connection and try again.\"\r\n      [buttons]=\"['OK']\"\r\n    ></ion-alert>\r\n  </div>\r\n</ion-content> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaM,IAAO,cAAP,MAAO,aAAW;EAGtB,YAAoB,QAAc;AAAd,SAAA,SAAA;AAFpB,SAAA,WAAoB;EAEiB;EAErC,WAAQ;AAEN,YAAQ,IAAI,sBAAsB;AAClC,UAAM,MAAM;AACZ,QAAI,IAAI,UAAU;AAChB,UAAI,SAAS,sBAAsB;IACrC;AACA,SAAK,wBAAuB;EAC9B;EAEA,0BAAuB;AACrB,SAAK,WAAW,UAAU;AAE1B,YAAQ,IAAI,kDAAkD,KAAK,QAAQ;AAC3E,UAAM,MAAM;AACZ,QAAI,IAAI,UAAU;AAChB,UAAI,SAAS,oDAAoD,KAAK,QAAQ;IAChF;AAGA,UAAM,QAAQ,aAAa,QAAQ,OAAO;AAC1C,UAAM,qBAAqB,aAAa,QAAQ,oBAAoB;AAEpE,YAAQ,IAAI,wBAAwB,CAAC,CAAC,OAAO,wBAAwB,uBAAuB,QAAQ,WAAW,KAAK,QAAQ;AAE5H,eAAW,MAAK;AACd,UAAI,SAAS,uBAAuB,QAAQ;AAE1C,gBAAQ,IAAI,yEAAyE;AACrF,YAAI,IAAI,UAAU;AAChB,cAAI,SAAS,iEAAiE;QAChF;AACA,aAAK,OAAO,SAAS,CAAC,YAAY,CAAC;MACrC,WAAW,OAAO;AAEhB,gBAAQ,IAAI,yEAAyE;AACrF,YAAI,IAAI,UAAU;AAChB,cAAI,SAAS,qEAAqE;QACpF;AACA,aAAK,OAAO,SAAS,CAAC,UAAU,CAAC;MACnC,OAAO;AAEL,gBAAQ,IAAI,iDAAiD;AAC7D,YAAI,IAAI,UAAU;AAChB,cAAI,SAAS,qDAAqD;QACpE;AACA,aAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;MACjC;IACF,GAAG,GAAI;EACT;EAEA,mBAAgB;AAEd,WAAO,iBAAiB,UAAU,KAAK,mBAAmB,KAAK,IAAI,CAAC;AACpE,WAAO,iBAAiB,WAAW,KAAK,mBAAmB,KAAK,IAAI,CAAC;EACvE;EAEA,mBAAgB;AAEd,WAAO,oBAAoB,UAAU,KAAK,mBAAmB,KAAK,IAAI,CAAC;AACvE,WAAO,oBAAoB,WAAW,KAAK,mBAAmB,KAAK,IAAI,CAAC;EAC1E;EAEQ,qBAAkB;AACxB,SAAK,WAAW,UAAU;AAC1B,UAAM,MAAM;AACZ,QAAI,IAAI,UAAU;AAChB,UAAI,SAAS,+CAA+C,KAAK,QAAQ;IAC3E;AAEA,SAAK,wBAAuB;EAC9B;;;uCA5EW,cAAW,4BAAA,MAAA,CAAA;IAAA;EAAA;;yEAAX,cAAW,WAAA,CAAA,CAAA,aAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,UAAA,0BAAA,WAAA,wDAAA,GAAA,UAAA,SAAA,CAAA,GAAA,UAAA,SAAA,qBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACbxB,QAAA,yBAAA,GAAA,eAAA,CAAA,EAAiC,GAAA,OAAA,CAAA;AAE7B,QAAA,oBAAA,GAAA,OAAA,CAAA;AACA,QAAA,yBAAA,GAAA,IAAA;AAAI,QAAA,iBAAA,GAAA,YAAA;AAAU,QAAA,uBAAA;AAEd,QAAA,oBAAA,GAAA,aAAA,CAAA;AAMF,QAAA,uBAAA,EAAM;;;AALF,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,UAAA,CAAA,IAAA,QAAA,EAAoB,WAAA,0BAAA,GAAA,GAAA,CAAA;;sBDKd,aAAW,UAAA,YAAE,cAAc,WAAW,GAAA,QAAA,CAAA,6tBAAA,EAAA,CAAA;EAAA;;;sEAErC,aAAW,CAAA;UAPvB;uBACW,eAAa,YAGX,MAAI,SACP,CAAC,aAAa,cAAc,WAAW,GAAC,UAAA;;;;;;;;;;;;kBAAA,QAAA,CAAA,6qBAAA,EAAA,CAAA;;;;6EAEtC,aAAW,EAAA,WAAA,eAAA,UAAA,yCAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
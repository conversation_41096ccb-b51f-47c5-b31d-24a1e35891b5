import{b as p}from"./chunk-XZOVPSKP.js";import{q as l,r as d}from"./chunk-IFNCDCK6.js";import{b as c}from"./chunk-4EI7TLDT.js";import{b as u,f as s,g as h,j as f}from"./chunk-FED6QSGK.js";import{e}from"./chunk-BAKMWPBW.js";import"./chunk-2R6CW7ES.js";var I="",m=I,P="",C=P,v=(()=>{let n=class{constructor(t){u(this,t),this.togglePasswordVisibility=()=>{let{inputElRef:o}=this;o&&(o.type=o.type==="text"?"password":"text")},this.color=void 0,this.showIcon=void 0,this.hideIcon=void 0,this.type="password"}onTypeChange(t){if(t!=="text"&&t!=="password"){e(`[ion-input-password-toggle] - Only inputs of type "text" or "password" are supported. Input of type "${t}" is not compatible.`,this.el);return}}connectedCallback(){let{el:t}=this,o=this.inputElRef=t.closest("ion-input");if(!o){e("[ion-input-password-toggle] - No ancestor ion-input found. This component must be slotted inside of an ion-input.",t);return}this.type=o.type}disconnectedCallback(){this.inputElRef=null}render(){var t,o;let{color:i,type:g}=this,r=c(this),y=(t=this.showIcon)!==null&&t!==void 0?t:l,w=(o=this.hideIcon)!==null&&o!==void 0?o:d,a=g==="text";return s(h,{key:"1a28e078c83e74c72d8bb8189ece93ec2e3fa3d0",class:p(i,{[r]:!0})},s("ion-button",{key:"039d1bab764093bb6fe4a34299b0872abda087fd",mode:r,color:i,fill:"clear",shape:"round","aria-checked":a?"true":"false","aria-label":"show password",role:"switch",type:"button",onPointerDown:b=>{b.preventDefault()},onClick:this.togglePasswordVisibility},s("ion-icon",{key:"26477ee97b808c3d79944bf5e33d4e05f1ae0b3f",slot:"icon-only","aria-hidden":"true",icon:a?w:y})))}get el(){return f(this)}static get watchers(){return{type:["onTypeChange"]}}};return n.style={ios:m,md:C},n})();export{v as ion_input_password_toggle};

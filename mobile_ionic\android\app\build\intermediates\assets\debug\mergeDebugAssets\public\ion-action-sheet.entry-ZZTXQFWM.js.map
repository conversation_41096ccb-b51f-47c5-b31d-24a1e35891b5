{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-action-sheet.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, d as readTask, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { c as createButtonActiveGesture } from './button-active-90f1dbc4.js';\nimport { r as raf } from './helpers-d94bc8ad.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, i as isCancel, f as present, g as dismiss, h as eventMethod, s as safeCall, j as prepareOverlay, k as setOverlayId } from './overlays-d99dcb0a.js';\nimport { g as getClassMap } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport { c as createAnimation } from './animation-8b25e105.js';\nimport './haptic-ac164e4c.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\nimport './index-39782642.js';\nimport './gesture-controller-314a54f6.js';\nimport './index-cfd9c1f2.js';\nimport './hardware-back-button-a7eb8233.js';\nimport './framework-delegate-56b467ad.js';\n\n/**\n * iOS Action Sheet Enter Animation\n */\nconst iosEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.action-sheet-wrapper')).fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n  return baseAnimation.addElement(baseEl).easing('cubic-bezier(.36,.66,.04,1)').duration(400).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Action Sheet Leave Animation\n */\nconst iosLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation.addElement(baseEl.querySelector('.action-sheet-wrapper')).fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n  return baseAnimation.addElement(baseEl).easing('cubic-bezier(.36,.66,.04,1)').duration(450).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * MD Action Sheet Enter Animation\n */\nconst mdEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.action-sheet-wrapper')).fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n  return baseAnimation.addElement(baseEl).easing('cubic-bezier(.36,.66,.04,1)').duration(400).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * MD Action Sheet Leave Animation\n */\nconst mdLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation.addElement(baseEl.querySelector('.action-sheet-wrapper')).fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n  return baseAnimation.addElement(baseEl).easing('cubic-bezier(.36,.66,.04,1)').duration(450).addAnimation([backdropAnimation, wrapperAnimation]);\n};\nconst actionSheetIosCss = \".sc-ion-action-sheet-ios-h{--color:initial;--button-color-activated:var(--button-color);--button-color-focused:var(--button-color);--button-color-hover:var(--button-color);--button-color-selected:var(--button-color);--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--height:auto;--max-height:calc(100% - (var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:block;position:fixed;outline:none;font-family:var(--ion-font-family, inherit);-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-action-sheet-ios-h{display:none}.action-sheet-wrapper.sc-ion-action-sheet-ios{left:0;right:0;bottom:0;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:block;position:absolute;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);z-index:10;pointer-events:none}.action-sheet-button.sc-ion-action-sheet-ios{display:block;position:relative;width:100%;border:0;outline:none;background:var(--button-background);color:var(--button-color);font-family:inherit;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-ios:disabled{color:var(--button-color-disabled);opacity:0.4}.action-sheet-button-inner.sc-ion-action-sheet-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;pointer-events:none;width:100%;height:100%;z-index:1}.action-sheet-container.sc-ion-action-sheet-ios{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;-ms-flex-pack:end;justify-content:flex-end;height:100%;max-height:calc(100vh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)));max-height:calc(100dvh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)))}.action-sheet-group.sc-ion-action-sheet-ios{-ms-flex-negative:2;flex-shrink:2;overscroll-behavior-y:contain;overflow-y:auto;-webkit-overflow-scrolling:touch;pointer-events:all;background:var(--background)}@media (any-pointer: coarse){.action-sheet-group.sc-ion-action-sheet-ios::-webkit-scrollbar{display:none}}.action-sheet-group-cancel.sc-ion-action-sheet-ios{-ms-flex-negative:0;flex-shrink:0;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-ios::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.action-sheet-selected.sc-ion-action-sheet-ios{color:var(--button-color-selected)}.action-sheet-selected.sc-ion-action-sheet-ios::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-button.ion-activated.sc-ion-action-sheet-ios{color:var(--button-color-activated)}.action-sheet-button.ion-activated.sc-ion-action-sheet-ios::after{background:var(--button-background-activated);opacity:var(--button-background-activated-opacity)}.action-sheet-button.ion-focused.sc-ion-action-sheet-ios{color:var(--button-color-focused)}.action-sheet-button.ion-focused.sc-ion-action-sheet-ios::after{background:var(--button-background-focused);opacity:var(--button-background-focused-opacity)}@media (any-hover: hover){.action-sheet-button.sc-ion-action-sheet-ios:not(:disabled):hover{color:var(--button-color-hover)}.action-sheet-button.sc-ion-action-sheet-ios:not(:disabled):hover::after{background:var(--button-background-hover);opacity:var(--button-background-hover-opacity)}}.sc-ion-action-sheet-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--backdrop-opacity:var(--ion-backdrop-opacity, 0.4);--button-background:linear-gradient(0deg, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08), rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08) 50%, transparent 50%) bottom/100% 1px no-repeat transparent;--button-background-activated:var(--ion-text-color, #000);--button-background-activated-opacity:.08;--button-background-hover:currentColor;--button-background-hover-opacity:.04;--button-background-focused:currentColor;--button-background-focused-opacity:.12;--button-background-selected:var(--ion-color-step-150, var(--ion-background-color-step-150, var(--ion-background-color, #fff)));--button-background-selected-opacity:1;--button-color:var(--ion-color-primary, #0054e9);--button-color-disabled:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--color:var(--ion-color-step-400, var(--ion-text-color-step-600, #999999));text-align:center}.action-sheet-wrapper.sc-ion-action-sheet-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:var(--ion-safe-area-top, 0);padding-bottom:var(--ion-safe-area-bottom, 0);-webkit-box-sizing:content-box;box-sizing:content-box}.action-sheet-container.sc-ion-action-sheet-ios{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0}.action-sheet-group.sc-ion-action-sheet-ios{border-radius:13px;margin-bottom:8px}.action-sheet-group.sc-ion-action-sheet-ios:first-child{margin-top:10px}.action-sheet-group.sc-ion-action-sheet-ios:last-child{margin-bottom:10px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-group.sc-ion-action-sheet-ios{background-color:transparent;-webkit-backdrop-filter:saturate(280%) blur(20px);backdrop-filter:saturate(280%) blur(20px)}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-title.sc-ion-action-sheet-ios,.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-button.sc-ion-action-sheet-ios{background-color:transparent;background-image:-webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8))), -webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4)), color-stop(50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4)), color-stop(50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background-image:linear-gradient(0deg, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8), rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%), linear-gradient(0deg, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4), rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4) 50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 50%);background-repeat:no-repeat;background-position:top, bottom;background-size:100% calc(100% - 1px), 100% 1px;-webkit-backdrop-filter:saturate(120%);backdrop-filter:saturate(120%)}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-button.ion-activated.sc-ion-action-sheet-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.7);background-image:none}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-cancel.sc-ion-action-sheet-ios{background:var(--button-background-selected)}}.action-sheet-title.sc-ion-action-sheet-ios{background:-webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08)), color-stop(50%, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08)), color-stop(50%, transparent)) bottom/100% 1px no-repeat transparent;background:linear-gradient(0deg, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08), rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08) 50%, transparent 50%) bottom/100% 1px no-repeat transparent}.action-sheet-title.sc-ion-action-sheet-ios{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:14px;padding-bottom:13px;color:var(--color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-size:max(13px, 0.8125rem);font-weight:400;text-align:center}.action-sheet-title.action-sheet-has-sub-title.sc-ion-action-sheet-ios{font-weight:600}.action-sheet-sub-title.sc-ion-action-sheet-ios{padding-left:0;padding-right:0;padding-top:6px;padding-bottom:0;font-size:max(13px, 0.8125rem);font-weight:400}.action-sheet-button.sc-ion-action-sheet-ios{-webkit-padding-start:14px;padding-inline-start:14px;-webkit-padding-end:14px;padding-inline-end:14px;padding-top:14px;padding-bottom:14px;min-height:56px;font-size:max(20px, 1.25rem);contain:content}.action-sheet-button.sc-ion-action-sheet-ios .action-sheet-icon.sc-ion-action-sheet-ios{-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:max(28px, 1.75rem);pointer-events:none}.action-sheet-button.sc-ion-action-sheet-ios:last-child{background-image:none}.action-sheet-selected.sc-ion-action-sheet-ios{font-weight:bold}.action-sheet-cancel.sc-ion-action-sheet-ios{font-weight:600}.action-sheet-cancel.sc-ion-action-sheet-ios::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-destructive.sc-ion-action-sheet-ios,.action-sheet-destructive.ion-activated.sc-ion-action-sheet-ios,.action-sheet-destructive.ion-focused.sc-ion-action-sheet-ios{color:var(--ion-color-danger, #c5000f)}@media (any-hover: hover){.action-sheet-destructive.sc-ion-action-sheet-ios:hover{color:var(--ion-color-danger, #c5000f)}}\";\nconst IonActionSheetIosStyle0 = actionSheetIosCss;\nconst actionSheetMdCss = \".sc-ion-action-sheet-md-h{--color:initial;--button-color-activated:var(--button-color);--button-color-focused:var(--button-color);--button-color-hover:var(--button-color);--button-color-selected:var(--button-color);--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--height:auto;--max-height:calc(100% - (var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:block;position:fixed;outline:none;font-family:var(--ion-font-family, inherit);-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-action-sheet-md-h{display:none}.action-sheet-wrapper.sc-ion-action-sheet-md{left:0;right:0;bottom:0;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:block;position:absolute;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);z-index:10;pointer-events:none}.action-sheet-button.sc-ion-action-sheet-md{display:block;position:relative;width:100%;border:0;outline:none;background:var(--button-background);color:var(--button-color);font-family:inherit;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-md:disabled{color:var(--button-color-disabled);opacity:0.4}.action-sheet-button-inner.sc-ion-action-sheet-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;pointer-events:none;width:100%;height:100%;z-index:1}.action-sheet-container.sc-ion-action-sheet-md{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;-ms-flex-pack:end;justify-content:flex-end;height:100%;max-height:calc(100vh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)));max-height:calc(100dvh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)))}.action-sheet-group.sc-ion-action-sheet-md{-ms-flex-negative:2;flex-shrink:2;overscroll-behavior-y:contain;overflow-y:auto;-webkit-overflow-scrolling:touch;pointer-events:all;background:var(--background)}@media (any-pointer: coarse){.action-sheet-group.sc-ion-action-sheet-md::-webkit-scrollbar{display:none}}.action-sheet-group-cancel.sc-ion-action-sheet-md{-ms-flex-negative:0;flex-shrink:0;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-md::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.action-sheet-selected.sc-ion-action-sheet-md{color:var(--button-color-selected)}.action-sheet-selected.sc-ion-action-sheet-md::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-button.ion-activated.sc-ion-action-sheet-md{color:var(--button-color-activated)}.action-sheet-button.ion-activated.sc-ion-action-sheet-md::after{background:var(--button-background-activated);opacity:var(--button-background-activated-opacity)}.action-sheet-button.ion-focused.sc-ion-action-sheet-md{color:var(--button-color-focused)}.action-sheet-button.ion-focused.sc-ion-action-sheet-md::after{background:var(--button-background-focused);opacity:var(--button-background-focused-opacity)}@media (any-hover: hover){.action-sheet-button.sc-ion-action-sheet-md:not(:disabled):hover{color:var(--button-color-hover)}.action-sheet-button.sc-ion-action-sheet-md:not(:disabled):hover::after{background:var(--button-background-hover);opacity:var(--button-background-hover-opacity)}}.sc-ion-action-sheet-md-h{--background:var(--ion-overlay-background-color, var(--ion-background-color, #fff));--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);--button-background:transparent;--button-background-selected:currentColor;--button-background-selected-opacity:0;--button-background-activated:transparent;--button-background-activated-opacity:0;--button-background-hover:currentColor;--button-background-hover-opacity:.04;--button-background-focused:currentColor;--button-background-focused-opacity:.12;--button-color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--button-color-disabled:var(--button-color);--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54)}.action-sheet-wrapper.sc-ion-action-sheet-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:var(--ion-safe-area-top, 0);margin-bottom:0}.action-sheet-title.sc-ion-action-sheet-md{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:20px;padding-bottom:17px;min-height:60px;color:var(--color, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54));font-size:1rem;text-align:start}.action-sheet-sub-title.sc-ion-action-sheet-md{padding-left:0;padding-right:0;padding-top:16px;padding-bottom:0;font-size:0.875rem}.action-sheet-group.sc-ion-action-sheet-md:first-child{padding-top:0}.action-sheet-group.sc-ion-action-sheet-md:last-child{padding-bottom:var(--ion-safe-area-bottom)}.action-sheet-button.sc-ion-action-sheet-md{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:12px;padding-bottom:12px;position:relative;min-height:52px;font-size:1rem;text-align:start;contain:content;overflow:hidden}.action-sheet-icon.sc-ion-action-sheet-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:0;margin-bottom:0;color:var(--color);font-size:1.5rem}.action-sheet-button-inner.sc-ion-action-sheet-md{-ms-flex-pack:start;justify-content:flex-start}.action-sheet-selected.sc-ion-action-sheet-md{font-weight:bold}\";\nconst IonActionSheetMdStyle0 = actionSheetMdCss;\nconst ActionSheet = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.didPresent = createEvent(this, \"ionActionSheetDidPresent\", 7);\n    this.willPresent = createEvent(this, \"ionActionSheetWillPresent\", 7);\n    this.willDismiss = createEvent(this, \"ionActionSheetWillDismiss\", 7);\n    this.didDismiss = createEvent(this, \"ionActionSheetDidDismiss\", 7);\n    this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n    this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n    this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n    this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n    this.delegateController = createDelegateController(this);\n    this.lockController = createLockController();\n    this.triggerController = createTriggerController();\n    this.presented = false;\n    this.onBackdropTap = () => {\n      this.dismiss(undefined, BACKDROP);\n    };\n    this.dispatchCancelHandler = ev => {\n      const role = ev.detail.role;\n      if (isCancel(role)) {\n        const cancelButton = this.getButtons().find(b => b.role === 'cancel');\n        this.callButtonHandler(cancelButton);\n      }\n    };\n    this.overlayIndex = undefined;\n    this.delegate = undefined;\n    this.hasController = false;\n    this.keyboardClose = true;\n    this.enterAnimation = undefined;\n    this.leaveAnimation = undefined;\n    this.buttons = [];\n    this.cssClass = undefined;\n    this.backdropDismiss = true;\n    this.header = undefined;\n    this.subHeader = undefined;\n    this.translucent = false;\n    this.animated = true;\n    this.htmlAttributes = undefined;\n    this.isOpen = false;\n    this.trigger = undefined;\n  }\n  onIsOpenChange(newValue, oldValue) {\n    if (newValue === true && oldValue === false) {\n      this.present();\n    } else if (newValue === false && oldValue === true) {\n      this.dismiss();\n    }\n  }\n  triggerChanged() {\n    const {\n      trigger,\n      el,\n      triggerController\n    } = this;\n    if (trigger) {\n      triggerController.addClickListener(el, trigger);\n    }\n  }\n  /**\n   * Present the action sheet overlay after it has been created.\n   */\n  async present() {\n    const unlock = await this.lockController.lock();\n    await this.delegateController.attachViewToDom();\n    await present(this, 'actionSheetEnter', iosEnterAnimation, mdEnterAnimation);\n    unlock();\n  }\n  /**\n   * Dismiss the action sheet overlay after it has been presented.\n   *\n   * @param data Any data to emit in the dismiss events.\n   * @param role The role of the element that is dismissing the action sheet.\n   * This can be useful in a button handler for determining which button was\n   * clicked to dismiss the action sheet.\n   * Some examples include: ``\"cancel\"`, `\"destructive\"`, \"selected\"`, and `\"backdrop\"`.\n   *\n   * This is a no-op if the overlay has not been presented yet. If you want\n   * to remove an overlay from the DOM that was never presented, use the\n   * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n   */\n  async dismiss(data, role) {\n    const unlock = await this.lockController.lock();\n    const dismissed = await dismiss(this, data, role, 'actionSheetLeave', iosLeaveAnimation, mdLeaveAnimation);\n    if (dismissed) {\n      this.delegateController.removeViewFromDom();\n    }\n    unlock();\n    return dismissed;\n  }\n  /**\n   * Returns a promise that resolves when the action sheet did dismiss.\n   */\n  onDidDismiss() {\n    return eventMethod(this.el, 'ionActionSheetDidDismiss');\n  }\n  /**\n   * Returns a promise that resolves when the action sheet will dismiss.\n   *\n   */\n  onWillDismiss() {\n    return eventMethod(this.el, 'ionActionSheetWillDismiss');\n  }\n  async buttonClick(button) {\n    const role = button.role;\n    if (isCancel(role)) {\n      return this.dismiss(button.data, role);\n    }\n    const shouldDismiss = await this.callButtonHandler(button);\n    if (shouldDismiss) {\n      return this.dismiss(button.data, button.role);\n    }\n    return Promise.resolve();\n  }\n  async callButtonHandler(button) {\n    if (button) {\n      // a handler has been provided, execute it\n      // pass the handler the values from the inputs\n      const rtn = await safeCall(button.handler);\n      if (rtn === false) {\n        // if the return value of the handler is false then do not dismiss\n        return false;\n      }\n    }\n    return true;\n  }\n  getButtons() {\n    return this.buttons.map(b => {\n      return typeof b === 'string' ? {\n        text: b\n      } : b;\n    });\n  }\n  connectedCallback() {\n    prepareOverlay(this.el);\n    this.triggerChanged();\n  }\n  disconnectedCallback() {\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n    this.triggerController.removeClickListener();\n  }\n  componentWillLoad() {\n    var _a;\n    if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n      setOverlayId(this.el);\n    }\n  }\n  componentDidLoad() {\n    /**\n     * Only create gesture if:\n     * 1. A gesture does not already exist\n     * 2. App is running in iOS mode\n     * 3. A wrapper ref exists\n     * 4. A group ref exists\n     */\n    const {\n      groupEl,\n      wrapperEl\n    } = this;\n    if (!this.gesture && getIonMode(this) === 'ios' && wrapperEl && groupEl) {\n      readTask(() => {\n        const isScrollable = groupEl.scrollHeight > groupEl.clientHeight;\n        if (!isScrollable) {\n          this.gesture = createButtonActiveGesture(wrapperEl, refEl => refEl.classList.contains('action-sheet-button'));\n          this.gesture.enable(true);\n        }\n      });\n    }\n    /**\n     * If action sheet was rendered with isOpen=\"true\"\n     * then we should open action sheet immediately.\n     */\n    if (this.isOpen === true) {\n      raf(() => this.present());\n    }\n    /**\n     * When binding values in frameworks such as Angular\n     * it is possible for the value to be set after the Web Component\n     * initializes but before the value watcher is set up in Stencil.\n     * As a result, the watcher callback may not be fired.\n     * We work around this by manually calling the watcher\n     * callback when the component has loaded and the watcher\n     * is configured.\n     */\n    this.triggerChanged();\n  }\n  render() {\n    const {\n      header,\n      htmlAttributes,\n      overlayIndex\n    } = this;\n    const mode = getIonMode(this);\n    const allButtons = this.getButtons();\n    const cancelButton = allButtons.find(b => b.role === 'cancel');\n    const buttons = allButtons.filter(b => b.role !== 'cancel');\n    const headerID = `action-sheet-${overlayIndex}-header`;\n    return h(Host, Object.assign({\n      key: '7bbd202ca9e19727e7514abbe073687d982f80c3',\n      role: \"dialog\",\n      \"aria-modal\": \"true\",\n      \"aria-labelledby\": header !== undefined ? headerID : null,\n      tabindex: \"-1\"\n    }, htmlAttributes, {\n      style: {\n        zIndex: `${20000 + this.overlayIndex}`\n      },\n      class: Object.assign(Object.assign({\n        [mode]: true\n      }, getClassMap(this.cssClass)), {\n        'overlay-hidden': true,\n        'action-sheet-translucent': this.translucent\n      }),\n      onIonActionSheetWillDismiss: this.dispatchCancelHandler,\n      onIonBackdropTap: this.onBackdropTap\n    }), h(\"ion-backdrop\", {\n      key: '23344a9221a2e6720d7b9de5249dc37256cafa7b',\n      tappable: this.backdropDismiss\n    }), h(\"div\", {\n      key: 'fbc2ba15549c2ab04e759e82df6e177fd80cc0a6',\n      tabindex: \"0\",\n      \"aria-hidden\": \"true\"\n    }), h(\"div\", {\n      key: '748ee5235d0b4cb26d6f1b7589f77af2e37ad28a',\n      class: \"action-sheet-wrapper ion-overlay-wrapper\",\n      ref: el => this.wrapperEl = el\n    }, h(\"div\", {\n      key: '7ce5fa236cf75e9b1e49c4725c9a811078706554',\n      class: \"action-sheet-container\"\n    }, h(\"div\", {\n      key: 'dc2251f3bcee4a93e3449f09621cbd2b65d329e9',\n      class: \"action-sheet-group\",\n      ref: el => this.groupEl = el\n    }, header !== undefined && h(\"div\", {\n      key: '48d325c8a852f56ed57a9ada1a6709d05ba32ee2',\n      id: headerID,\n      class: {\n        'action-sheet-title': true,\n        'action-sheet-has-sub-title': this.subHeader !== undefined\n      }\n    }, header, this.subHeader && h(\"div\", {\n      key: '66093728052eb67f37a35f3232761ce4a08896f3',\n      class: \"action-sheet-sub-title\"\n    }, this.subHeader)), buttons.map(b => h(\"button\", Object.assign({}, b.htmlAttributes, {\n      type: \"button\",\n      id: b.id,\n      class: buttonClass(b),\n      onClick: () => this.buttonClick(b),\n      disabled: b.disabled\n    }), h(\"span\", {\n      class: \"action-sheet-button-inner\"\n    }, b.icon && h(\"ion-icon\", {\n      icon: b.icon,\n      \"aria-hidden\": \"true\",\n      lazy: false,\n      class: \"action-sheet-icon\"\n    }), b.text), mode === 'md' && h(\"ion-ripple-effect\", null)))), cancelButton && h(\"div\", {\n      key: 'f4eb8e3e2885b85af5080df18d0de0bdd1d719de',\n      class: \"action-sheet-group action-sheet-group-cancel\"\n    }, h(\"button\", Object.assign({\n      key: '169f4eb09255aba85062baad49ceb151239fbfb7'\n    }, cancelButton.htmlAttributes, {\n      type: \"button\",\n      class: buttonClass(cancelButton),\n      onClick: () => this.buttonClick(cancelButton)\n    }), h(\"span\", {\n      key: '25fb8a466dd67ea94c79cfb4f9965527e1ce6d42',\n      class: \"action-sheet-button-inner\"\n    }, cancelButton.icon && h(\"ion-icon\", {\n      key: 'eb5b071e120a2c86afdf967af6a763a43044d1ca',\n      icon: cancelButton.icon,\n      \"aria-hidden\": \"true\",\n      lazy: false,\n      class: \"action-sheet-icon\"\n    }), cancelButton.text), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '452ad7e1052b2c681e2d98de8193949755ad4d54'\n    }))))), h(\"div\", {\n      key: 'e1cecf280c987c050d9445e2c458b903f153089b',\n      tabindex: \"0\",\n      \"aria-hidden\": \"true\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"isOpen\": [\"onIsOpenChange\"],\n      \"trigger\": [\"triggerChanged\"]\n    };\n  }\n};\nconst buttonClass = button => {\n  return Object.assign({\n    'action-sheet-button': true,\n    'ion-activatable': !button.disabled,\n    'ion-focusable': !button.disabled,\n    [`action-sheet-${button.role}`]: button.role !== undefined\n  }, getClassMap(button.cssClass));\n};\nActionSheet.style = {\n  ios: IonActionSheetIosStyle0,\n  md: IonActionSheetMdStyle0\n};\nexport { ActionSheet as ion_action_sheet };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAM,oBAAoB,YAAU;AAClC,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBAAkB,WAAW,OAAO,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,MAAM,yBAAyB,EAAE,aAAa;AAAA,IACjI,kBAAkB;AAAA,EACpB,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC;AACtC,mBAAiB,WAAW,OAAO,cAAc,uBAAuB,CAAC,EAAE,OAAO,aAAa,oBAAoB,gBAAgB;AACnI,SAAO,cAAc,WAAW,MAAM,EAAE,OAAO,6BAA6B,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAChJ;AAKA,IAAM,oBAAoB,YAAU;AAClC,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBAAkB,WAAW,OAAO,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,2BAA2B,CAAC;AACjH,mBAAiB,WAAW,OAAO,cAAc,uBAAuB,CAAC,EAAE,OAAO,aAAa,kBAAkB,kBAAkB;AACnI,SAAO,cAAc,WAAW,MAAM,EAAE,OAAO,6BAA6B,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAChJ;AAKA,IAAM,mBAAmB,YAAU;AACjC,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBAAkB,WAAW,OAAO,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,MAAM,yBAAyB,EAAE,aAAa;AAAA,IACjI,kBAAkB;AAAA,EACpB,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC;AACtC,mBAAiB,WAAW,OAAO,cAAc,uBAAuB,CAAC,EAAE,OAAO,aAAa,oBAAoB,gBAAgB;AACnI,SAAO,cAAc,WAAW,MAAM,EAAE,OAAO,6BAA6B,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAChJ;AAKA,IAAM,mBAAmB,YAAU;AACjC,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBAAkB,WAAW,OAAO,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,2BAA2B,CAAC;AACjH,mBAAiB,WAAW,OAAO,cAAc,uBAAuB,CAAC,EAAE,OAAO,aAAa,kBAAkB,kBAAkB;AACnI,SAAO,cAAc,WAAW,MAAM,EAAE,OAAO,6BAA6B,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAChJ;AACA,IAAM,oBAAoB;AAC1B,IAAM,0BAA0B;AAChC,IAAM,mBAAmB;AACzB,IAAM,yBAAyB;AAC/B,IAAM,cAAc,MAAM;AAAA,EACxB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,aAAa,YAAY,MAAM,4BAA4B,CAAC;AACjE,SAAK,cAAc,YAAY,MAAM,6BAA6B,CAAC;AACnE,SAAK,cAAc,YAAY,MAAM,6BAA6B,CAAC;AACnE,SAAK,aAAa,YAAY,MAAM,4BAA4B,CAAC;AACjE,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,qBAAqB,yBAAyB,IAAI;AACvD,SAAK,iBAAiB,qBAAqB;AAC3C,SAAK,oBAAoB,wBAAwB;AACjD,SAAK,YAAY;AACjB,SAAK,gBAAgB,MAAM;AACzB,WAAK,QAAQ,QAAW,QAAQ;AAAA,IAClC;AACA,SAAK,wBAAwB,QAAM;AACjC,YAAM,OAAO,GAAG,OAAO;AACvB,UAAI,SAAS,IAAI,GAAG;AAClB,cAAM,eAAe,KAAK,WAAW,EAAE,KAAK,OAAK,EAAE,SAAS,QAAQ;AACpE,aAAK,kBAAkB,YAAY;AAAA,MACrC;AAAA,IACF;AACA,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,UAAU,CAAC;AAChB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,eAAe,UAAU,UAAU;AACjC,QAAI,aAAa,QAAQ,aAAa,OAAO;AAC3C,WAAK,QAAQ;AAAA,IACf,WAAW,aAAa,SAAS,aAAa,MAAM;AAClD,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,wBAAkB,iBAAiB,IAAI,OAAO;AAAA,IAChD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIM,UAAU;AAAA;AACd,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,YAAM,KAAK,mBAAmB,gBAAgB;AAC9C,YAAM,QAAQ,MAAM,oBAAoB,mBAAmB,gBAAgB;AAC3E,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcM,QAAQ,MAAM,MAAM;AAAA;AACxB,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,YAAM,YAAY,MAAM,QAAQ,MAAM,MAAM,MAAM,oBAAoB,mBAAmB,gBAAgB;AACzG,UAAI,WAAW;AACb,aAAK,mBAAmB,kBAAkB;AAAA,MAC5C;AACA,aAAO;AACP,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,WAAO,YAAY,KAAK,IAAI,0BAA0B;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,YAAY,KAAK,IAAI,2BAA2B;AAAA,EACzD;AAAA,EACM,YAAY,QAAQ;AAAA;AACxB,YAAM,OAAO,OAAO;AACpB,UAAI,SAAS,IAAI,GAAG;AAClB,eAAO,KAAK,QAAQ,OAAO,MAAM,IAAI;AAAA,MACvC;AACA,YAAM,gBAAgB,MAAM,KAAK,kBAAkB,MAAM;AACzD,UAAI,eAAe;AACjB,eAAO,KAAK,QAAQ,OAAO,MAAM,OAAO,IAAI;AAAA,MAC9C;AACA,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAAA;AAAA,EACM,kBAAkB,QAAQ;AAAA;AAC9B,UAAI,QAAQ;AAGV,cAAM,MAAM,MAAM,SAAS,OAAO,OAAO;AACzC,YAAI,QAAQ,OAAO;AAEjB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACA,aAAa;AACX,WAAO,KAAK,QAAQ,IAAI,OAAK;AAC3B,aAAO,OAAO,MAAM,WAAW;AAAA,QAC7B,MAAM;AAAA,MACR,IAAI;AAAA,IACN,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,mBAAe,KAAK,EAAE;AACtB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACjB;AACA,SAAK,kBAAkB,oBAAoB;AAAA,EAC7C;AAAA,EACA,oBAAoB;AAClB,QAAI;AACJ,QAAI,GAAG,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC5E,mBAAa,KAAK,EAAE;AAAA,IACtB;AAAA,EACF;AAAA,EACA,mBAAmB;AAQjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,KAAK,WAAW,WAAW,IAAI,MAAM,SAAS,aAAa,SAAS;AACvE,eAAS,MAAM;AACb,cAAM,eAAe,QAAQ,eAAe,QAAQ;AACpD,YAAI,CAAC,cAAc;AACjB,eAAK,UAAU,0BAA0B,WAAW,WAAS,MAAM,UAAU,SAAS,qBAAqB,CAAC;AAC5G,eAAK,QAAQ,OAAO,IAAI;AAAA,QAC1B;AAAA,MACF,CAAC;AAAA,IACH;AAKA,QAAI,KAAK,WAAW,MAAM;AACxB,UAAI,MAAM,KAAK,QAAQ,CAAC;AAAA,IAC1B;AAUA,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,aAAa,KAAK,WAAW;AACnC,UAAM,eAAe,WAAW,KAAK,OAAK,EAAE,SAAS,QAAQ;AAC7D,UAAM,UAAU,WAAW,OAAO,OAAK,EAAE,SAAS,QAAQ;AAC1D,UAAM,WAAW,gBAAgB,YAAY;AAC7C,WAAO,EAAE,MAAM,OAAO,OAAO;AAAA,MAC3B,KAAK;AAAA,MACL,MAAM;AAAA,MACN,cAAc;AAAA,MACd,mBAAmB,WAAW,SAAY,WAAW;AAAA,MACrD,UAAU;AAAA,IACZ,GAAG,gBAAgB;AAAA,MACjB,OAAO;AAAA,QACL,QAAQ,GAAG,MAAQ,KAAK,YAAY;AAAA,MACtC;AAAA,MACA,OAAO,OAAO,OAAO,OAAO,OAAO;AAAA,QACjC,CAAC,IAAI,GAAG;AAAA,MACV,GAAG,YAAY,KAAK,QAAQ,CAAC,GAAG;AAAA,QAC9B,kBAAkB;AAAA,QAClB,4BAA4B,KAAK;AAAA,MACnC,CAAC;AAAA,MACD,6BAA6B,KAAK;AAAA,MAClC,kBAAkB,KAAK;AAAA,IACzB,CAAC,GAAG,EAAE,gBAAgB;AAAA,MACpB,KAAK;AAAA,MACL,UAAU,KAAK;AAAA,IACjB,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,KAAK;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,IACjB,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,KAAK;AAAA,MACL,OAAO;AAAA,MACP,KAAK,QAAM,KAAK,YAAY;AAAA,IAC9B,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,KAAK,QAAM,KAAK,UAAU;AAAA,IAC5B,GAAG,WAAW,UAAa,EAAE,OAAO;AAAA,MAClC,KAAK;AAAA,MACL,IAAI;AAAA,MACJ,OAAO;AAAA,QACL,sBAAsB;AAAA,QACtB,8BAA8B,KAAK,cAAc;AAAA,MACnD;AAAA,IACF,GAAG,QAAQ,KAAK,aAAa,EAAE,OAAO;AAAA,MACpC,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,KAAK,SAAS,CAAC,GAAG,QAAQ,IAAI,OAAK,EAAE,UAAU,OAAO,OAAO,CAAC,GAAG,EAAE,gBAAgB;AAAA,MACpF,MAAM;AAAA,MACN,IAAI,EAAE;AAAA,MACN,OAAO,YAAY,CAAC;AAAA,MACpB,SAAS,MAAM,KAAK,YAAY,CAAC;AAAA,MACjC,UAAU,EAAE;AAAA,IACd,CAAC,GAAG,EAAE,QAAQ;AAAA,MACZ,OAAO;AAAA,IACT,GAAG,EAAE,QAAQ,EAAE,YAAY;AAAA,MACzB,MAAM,EAAE;AAAA,MACR,eAAe;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC,GAAG,EAAE,IAAI,GAAG,SAAS,QAAQ,EAAE,qBAAqB,IAAI,CAAC,CAAC,CAAC,GAAG,gBAAgB,EAAE,OAAO;AAAA,MACtF,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,UAAU,OAAO,OAAO;AAAA,MAC3B,KAAK;AAAA,IACP,GAAG,aAAa,gBAAgB;AAAA,MAC9B,MAAM;AAAA,MACN,OAAO,YAAY,YAAY;AAAA,MAC/B,SAAS,MAAM,KAAK,YAAY,YAAY;AAAA,IAC9C,CAAC,GAAG,EAAE,QAAQ;AAAA,MACZ,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,aAAa,QAAQ,EAAE,YAAY;AAAA,MACpC,KAAK;AAAA,MACL,MAAM,aAAa;AAAA,MACnB,eAAe;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC,GAAG,aAAa,IAAI,GAAG,SAAS,QAAQ,EAAE,qBAAqB;AAAA,MAC9D,KAAK;AAAA,IACP,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MACf,KAAK;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,IACjB,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,UAAU,CAAC,gBAAgB;AAAA,MAC3B,WAAW,CAAC,gBAAgB;AAAA,IAC9B;AAAA,EACF;AACF;AACA,IAAM,cAAc,YAAU;AAC5B,SAAO,OAAO,OAAO;AAAA,IACnB,uBAAuB;AAAA,IACvB,mBAAmB,CAAC,OAAO;AAAA,IAC3B,iBAAiB,CAAC,OAAO;AAAA,IACzB,CAAC,gBAAgB,OAAO,IAAI,EAAE,GAAG,OAAO,SAAS;AAAA,EACnD,GAAG,YAAY,OAAO,QAAQ,CAAC;AACjC;AACA,YAAY,QAAQ;AAAA,EAClB,KAAK;AAAA,EACL,IAAI;AACN;", "names": [], "x_google_ignoreList": [0]}
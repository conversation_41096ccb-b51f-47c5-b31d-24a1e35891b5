{"version": 3, "sources": ["src/app/pages/disaster-maps/earthquake-map.page.ts", "src/app/pages/disaster-maps/earthquake-map.page.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IonicModule, LoadingController, ToastController, AlertController } from '@ionic/angular';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Geolocation } from '@capacitor/geolocation';\r\nimport { MapboxRoutingService } from '../../services/mapbox-routing.service';\r\nimport { OfflineStorageService } from '../../services/offline-storage.service';\r\nimport * as L from 'leaflet';\r\n\r\ninterface EvacuationCenter {\r\n  id: number;\r\n  name: string;\r\n  address: string;\r\n  latitude: number;\r\n  longitude: number;\r\n  capacity?: number;\r\n  status?: string;\r\n  disaster_type?: string;\r\n  contact?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-earthquake-map',\r\n  templateUrl: './earthquake-map.page.html',\r\n  styleUrls: ['./earthquake-map.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule]\r\n})\r\nexport class EarthquakeMapPage implements OnInit, AfterViewInit {\r\n  private map!: L.Map;\r\n  private userMarker: L.Marker<any> | null = null;\r\n  private routeLayer: L.LayerGroup | null = null;\r\n  private nearestMarkers: L.Marker[] = [];\r\n\r\n  public evacuationCenters: EvacuationCenter[] = [];\r\n  public userLocation: { lat: number, lng: number } | null = null;\r\n\r\n  // Properties for highlighting new centers\r\n  public newCenterId: string | null = null;\r\n  public highlightCenter: boolean = false;\r\n  public centerLat: number | null = null;\r\n  public centerLng: number | null = null;\r\n\r\n  private loadingCtrl = inject(LoadingController);\r\n  private toastCtrl = inject(ToastController);\r\n  private alertCtrl = inject(AlertController);\r\n  private http = inject(HttpClient);\r\n  private router = inject(Router);\r\n  private route = inject(ActivatedRoute);\r\n  private mapboxRouting = inject(MapboxRoutingService);\r\n  private offlineStorage = inject(OfflineStorageService);\r\n\r\n  ngOnInit() {\r\n    console.log('🟠 EARTHQUAKE MAP: Component initialized...');\r\n    // Don't initialize map here - wait for view to be ready\r\n\r\n    // Check for query parameters to highlight new center\r\n    this.route.queryParams.subscribe((params: any) => {\r\n      if (params['newCenterId']) {\r\n        this.newCenterId = params['newCenterId'];\r\n        this.highlightCenter = params['highlightCenter'] === 'true';\r\n        this.centerLat = params['centerLat'] ? parseFloat(params['centerLat']) : null;\r\n        this.centerLng = params['centerLng'] ? parseFloat(params['centerLng']) : null;\r\n        console.log('🟠 EARTHQUAKE MAP: New center to highlight:', this.newCenterId);\r\n      }\r\n    });\r\n  }\r\n\r\n  async ngAfterViewInit() {\r\n    console.log('🟠 EARTHQUAKE MAP: View initialized, loading map...');\r\n    // Small delay to ensure DOM is fully rendered\r\n    setTimeout(async () => {\r\n      await this.loadEarthquakeMap();\r\n    }, 100);\r\n  }\r\n\r\n  async loadEarthquakeMap() {\r\n    const loading = await this.loadingCtrl.create({\r\n      message: 'Loading earthquake evacuation centers...',\r\n      spinner: 'crescent'\r\n    });\r\n    await loading.present();\r\n\r\n    try {\r\n      // Get user location\r\n      const position = await Geolocation.getCurrentPosition({\r\n        enableHighAccuracy: true,\r\n        timeout: 20000\r\n      });\r\n\r\n      const userLat = position.coords.latitude;\r\n      const userLng = position.coords.longitude;\r\n\r\n      this.userLocation = { lat: userLat, lng: userLng };\r\n\r\n      console.log(`🟠 EARTHQUAKE MAP: User location [${userLat}, ${userLng}]`);\r\n\r\n      // Initialize map\r\n      this.initializeMap(userLat, userLng);\r\n\r\n      // Load ONLY earthquake centers and auto-route\r\n      await this.loadEarthquakeCenters(userLat, userLng);\r\n\r\n      await loading.dismiss();\r\n\r\n      // Show success message\r\n      const toast = await this.toastCtrl.create({\r\n        message: `🟠 Showing ${this.evacuationCenters.length} earthquake evacuation centers`,\r\n        duration: 3000,\r\n        color: 'warning',\r\n        position: 'top'\r\n      });\r\n      await toast.present();\r\n\r\n    } catch (error) {\r\n      await loading.dismiss();\r\n      console.error('🟠 EARTHQUAKE MAP: Error loading map', error);\r\n\r\n      const alert = await this.alertCtrl.create({\r\n        header: 'Location Error',\r\n        message: 'Unable to get your location. Please enable GPS and try again.',\r\n        buttons: [\r\n          {\r\n            text: 'Retry',\r\n            handler: () => this.loadEarthquakeMap()\r\n          },\r\n          {\r\n            text: 'Go Back',\r\n            handler: () => this.router.navigate(['/tabs/home'])\r\n          }\r\n        ]\r\n      });\r\n      await alert.present();\r\n    }\r\n  }\r\n\r\n  initializeMap(lat: number, lng: number) {\r\n    console.log(`🟠 EARTHQUAKE MAP: Initializing map at [${lat}, ${lng}]`);\r\n\r\n    // Check if container exists\r\n    const container = document.getElementById('earthquake-map');\r\n    if (!container) {\r\n      console.error('🟠 EARTHQUAKE MAP: Container #earthquake-map not found!');\r\n      throw new Error('Map container not found. Please ensure the view is properly loaded.');\r\n    }\r\n\r\n    if (this.map) {\r\n      this.map.remove();\r\n    }\r\n\r\n    this.map = L.map('earthquake-map').setView([lat, lng], 13);\r\n\r\n    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {\r\n      attribution: 'OpenStreetMap contributors'\r\n    }).addTo(this.map);\r\n\r\n    // Add user marker\r\n    this.userMarker = L.marker([lat, lng], {\r\n      icon: L.icon({\r\n        iconUrl: 'assets/Location.png',\r\n        iconSize: [30, 30],\r\n        iconAnchor: [15, 30]\r\n      })\r\n    }).addTo(this.map);\r\n\r\n    this.userMarker.bindPopup('📍 You are here!').openPopup();\r\n  }\r\n\r\n  async loadEarthquakeCenters(userLat: number, userLng: number) {\r\n    try {\r\n      console.log('🟠 EARTHQUAKE MAP: Fetching earthquake centers...');\r\n\r\n      let allCenters: EvacuationCenter[] = [];\r\n\r\n      // Check if offline mode is enabled or if we're offline\r\n      if (this.offlineStorage.isOfflineMode() || !navigator.onLine) {\r\n        console.log('🔄 Loading earthquake centers from offline storage');\r\n        allCenters = await this.offlineStorage.getEvacuationCenters();\r\n        console.log('📱 OFFLINE DATA:', allCenters);\r\n\r\n        if (allCenters.length === 0) {\r\n          console.warn('⚠️ No cached evacuation centers found');\r\n          const alert = await this.alertCtrl.create({\r\n            header: 'No Offline Data',\r\n            message: 'No offline evacuation data available. Please sync data when online.',\r\n            buttons: ['OK']\r\n          });\r\n          await alert.present();\r\n          return;\r\n        }\r\n      } else {\r\n        // Try to get data from API when online\r\n        try {\r\n          allCenters = await firstValueFrom(\r\n            this.http.get<EvacuationCenter[]>(`${environment.apiUrl}/evacuation-centers`)\r\n          );\r\n          console.log('🟠 EARTHQUAKE MAP: Total centers received from API:', allCenters?.length || 0);\r\n        } catch (apiError) {\r\n          console.error('❌ API failed, falling back to offline data:', apiError);\r\n          allCenters = await this.offlineStorage.getEvacuationCenters();\r\n\r\n          if (allCenters.length === 0) {\r\n            const alert = await this.alertCtrl.create({\r\n              header: 'Connection Error',\r\n              message: 'Cannot connect to server and no offline data available. Please check your connection or sync data when online.',\r\n              buttons: ['OK']\r\n            });\r\n            await alert.present();\r\n            return;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Filter for EARTHQUAKE ONLY\r\n      this.evacuationCenters = allCenters.filter(center =>\r\n        center.disaster_type === 'Earthquake'\r\n      );\r\n\r\n      console.log(`🟠 EARTHQUAKE MAP: Filtered to ${this.evacuationCenters.length} earthquake centers`);\r\n\r\n      if (this.evacuationCenters.length === 0) {\r\n        const alert = await this.alertCtrl.create({\r\n          header: 'No Earthquake Centers',\r\n          message: 'No earthquake evacuation centers found in the data.',\r\n          buttons: ['OK']\r\n        });\r\n        await alert.present();\r\n        return;\r\n      }\r\n\r\n      // Add markers and routes\r\n      await this.addMarkersAndRoutes(userLat, userLng);\r\n\r\n    } catch (error) {\r\n      console.error('🟠 EARTHQUAKE MAP: Error loading centers', error);\r\n\r\n      // Try to load from offline storage as last resort\r\n      try {\r\n        console.log('🔄 Last resort: trying offline storage...');\r\n        const offlineCenters = await this.offlineStorage.getEvacuationCenters();\r\n        this.evacuationCenters = offlineCenters.filter(center =>\r\n          center.disaster_type === 'Earthquake'\r\n        );\r\n\r\n        if (this.evacuationCenters.length > 0) {\r\n          console.log(`🟠 Loaded ${this.evacuationCenters.length} earthquake centers from offline storage`);\r\n          // Continue with adding markers...\r\n          await this.addMarkersAndRoutes(userLat, userLng);\r\n          return;\r\n        }\r\n      } catch (offlineError) {\r\n        console.error('❌ Offline storage also failed:', offlineError);\r\n      }\r\n\r\n      const toast = await this.toastCtrl.create({\r\n        message: 'Error loading earthquake centers. Please check your connection or sync offline data.',\r\n        duration: 4000,\r\n        color: 'danger'\r\n      });\r\n      await toast.present();\r\n    }\r\n  }\r\n\r\n  // Add markers and routes to map\r\n  async addMarkersAndRoutes(userLat: number, userLng: number) {\r\n    // Check if we're in offline mode\r\n    const isOfflineMode = this.offlineStorage.isOfflineMode() || !navigator.onLine;\r\n\r\n    // Add earthquake markers (orange)\r\n    this.evacuationCenters.forEach(center => {\r\n      const lat = Number(center.latitude);\r\n      const lng = Number(center.longitude);\r\n\r\n      if (!isNaN(lat) && !isNaN(lng)) {\r\n        const marker = L.marker([lat, lng], {\r\n          icon: L.icon({\r\n            iconUrl: 'assets/forEarthquake.png',\r\n            iconSize: [40, 40],\r\n            iconAnchor: [20, 40],\r\n            popupAnchor: [0, -40]\r\n          })\r\n        });\r\n\r\n        const distance = this.calculateDistance(userLat, userLng, lat, lng);\r\n\r\n        // Make marker clickable with transportation options (only if online)\r\n        marker.on('click', () => {\r\n          if (isOfflineMode) {\r\n            this.showOfflineMarkerInfo(center, distance);\r\n          } else {\r\n            this.showTransportationOptions(center);\r\n          }\r\n        });\r\n\r\n        // Check if this is the new center to highlight\r\n        const isNewCenter = this.newCenterId && center.id.toString() === this.newCenterId;\r\n\r\n        // Create popup content based on online/offline status\r\n        const offlineIndicator = isOfflineMode ? '<p><em>📱 Offline Mode - Limited functionality</em></p>' : '<p><em>Click marker for route options</em></p>';\r\n\r\n        marker.bindPopup(`\r\n          <div class=\"evacuation-popup\">\r\n            <h3>🟠 ${center.name} ${isNewCenter ? '⭐ NEW!' : ''}</h3>\r\n            <p><strong>Type:</strong> Earthquake Center</p>\r\n            <p><strong>Distance:</strong> ${(distance / 1000).toFixed(2)} km</p>\r\n            <p><strong>Capacity:</strong> ${center.capacity || 'N/A'}</p>\r\n            ${offlineIndicator}\r\n            ${isNewCenter ? '<p><strong>🆕 Recently Added!</strong></p>' : ''}\r\n          </div>\r\n        `);\r\n\r\n        // If this is the new center, open its popup and center map on it\r\n        if (isNewCenter) {\r\n          marker.openPopup();\r\n          this.map.setView([lat, lng], 15); // Zoom in on the new center\r\n\r\n          // Show a toast notification\r\n          this.toastCtrl.create({\r\n            message: `🆕 New earthquake evacuation center: ${center.name}`,\r\n            duration: 5000,\r\n            color: 'warning',\r\n            position: 'top'\r\n          }).then(toast => toast.present());\r\n        }\r\n\r\n        marker.addTo(this.map);\r\n        console.log(`🟠 Added earthquake marker: ${center.name}`);\r\n      }\r\n    });\r\n\r\n    // Only auto-route if online\r\n    if (!isOfflineMode) {\r\n      console.log('🟠 Online mode: Auto-routing to 2 nearest earthquake centers...');\r\n      await this.routeToTwoNearestCenters();\r\n    } else {\r\n      console.log('🟠 Offline mode: Showing markers only (no routing)');\r\n    }\r\n\r\n    // Fit map to show all earthquake centers\r\n    if (this.evacuationCenters.length > 0) {\r\n      const bounds = L.latLngBounds([]);\r\n      bounds.extend([userLat, userLng]);\r\n\r\n      this.evacuationCenters.forEach(center => {\r\n        bounds.extend([Number(center.latitude), Number(center.longitude)]);\r\n      });\r\n\r\n      this.map.fitBounds(bounds, { padding: [50, 50] });\r\n    }\r\n  }\r\n\r\n  calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {\r\n    const R = 6371e3; // meters\r\n    const φ1 = lat1 * Math.PI / 180;\r\n    const φ2 = lat2 * Math.PI / 180;\r\n    const Δφ = (lat2 - lat1) * Math.PI / 180;\r\n    const Δλ = (lon2 - lon1) * Math.PI / 180;\r\n\r\n    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +\r\n              Math.cos(φ1) * Math.cos(φ2) *\r\n              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);\r\n\r\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\r\n    return R * c;\r\n  }\r\n\r\n  // Auto-route to 2 nearest earthquake centers\r\n  async routeToTwoNearestCenters() {\r\n    if (!this.userLocation || this.evacuationCenters.length === 0) {\r\n      console.log('🟠 EARTHQUAKE MAP: No user location or evacuation centers available');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      console.log('🟠 EARTHQUAKE MAP: Finding 2 nearest earthquake centers...');\r\n\r\n      // Find 2 nearest centers\r\n      const nearestCenters = this.getTwoNearestCenters(\r\n        this.userLocation.lat,\r\n        this.userLocation.lng\r\n      );\r\n\r\n      if (nearestCenters.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // Clear previous routes\r\n      this.clearRoutes();\r\n\r\n      // Calculate and display routes with earthquake color (orange)\r\n      await this.calculateRoutes(nearestCenters);\r\n\r\n    } catch (error) {\r\n      console.error('🟠 EARTHQUAKE MAP: Error calculating routes', error);\r\n    }\r\n  }\r\n\r\n  // Get 2 nearest evacuation centers\r\n  getTwoNearestCenters(userLat: number, userLng: number): EvacuationCenter[] {\r\n    const centersWithDistance = this.evacuationCenters.map(center => ({\r\n      ...center,\r\n      distance: this.calculateDistance(\r\n        userLat, userLng,\r\n        Number(center.latitude), Number(center.longitude)\r\n      )\r\n    }));\r\n\r\n    // Sort by distance and take first 2\r\n    return centersWithDistance\r\n      .sort((a, b) => a.distance - b.distance)\r\n      .slice(0, 2);\r\n  }\r\n\r\n  // Calculate routes to nearest centers with earthquake color\r\n  async calculateRoutes(centers: EvacuationCenter[]) {\r\n    if (!this.userLocation) return;\r\n\r\n    this.routeLayer = L.layerGroup().addTo(this.map);\r\n\r\n    for (let i = 0; i < centers.length; i++) {\r\n      const center = centers[i];\r\n      const lat = Number(center.latitude);\r\n      const lng = Number(center.longitude);\r\n\r\n      if (!isNaN(lat) && !isNaN(lng)) {\r\n        try {\r\n          const mapboxProfile = this.mapboxRouting.convertTravelModeToProfile('walking');\r\n\r\n          const routeData = await this.mapboxRouting.getDirections(\r\n            this.userLocation.lng, this.userLocation.lat,\r\n            lng, lat,\r\n            mapboxProfile,\r\n            {\r\n              geometries: 'geojson',\r\n              overview: 'simplified',\r\n              steps: false\r\n            }\r\n          );\r\n\r\n          if (routeData && routeData.routes && routeData.routes.length > 0) {\r\n            const route = routeData.routes[0];\r\n\r\n            // Use earthquake color (orange)\r\n            const routeColor = '#ff9500';\r\n\r\n            // Draw route\r\n            const routeLine = L.polyline(\r\n              route.geometry.coordinates.map((coord: number[]) => [coord[1], coord[0]]),\r\n              {\r\n                color: routeColor,\r\n                weight: 4,\r\n                opacity: 0.8,\r\n                dashArray: i === 0 ? undefined : '10, 10' // Solid for first, dashed for second\r\n              }\r\n            );\r\n\r\n            routeLine.addTo(this.routeLayer);\r\n\r\n            console.log(`🟠 Route ${i + 1}: ${(route.distance/1000).toFixed(2)}km, ${(route.duration/60).toFixed(0)}min`);\r\n          }\r\n        } catch (error) {\r\n          console.error(`🟠 Error calculating route to center ${i + 1}:`, error);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // Clear previous routes\r\n  clearRoutes() {\r\n    if (this.routeLayer) {\r\n      this.map.removeLayer(this.routeLayer);\r\n      this.routeLayer = null;\r\n    }\r\n\r\n    this.nearestMarkers.forEach(marker => {\r\n      this.map.removeLayer(marker);\r\n    });\r\n    this.nearestMarkers = [];\r\n  }\r\n\r\n  // Show offline marker information when clicked in offline mode\r\n  async showOfflineMarkerInfo(center: EvacuationCenter, distance: number) {\r\n    const alert = await this.alertCtrl.create({\r\n      header: `📱 ${center.name}`,\r\n      message: `\r\n        <div style=\"text-align: left;\">\r\n          <p><strong>Type:</strong> Earthquake Center</p>\r\n          <p><strong>Distance:</strong> ${(distance / 1000).toFixed(2)} km</p>\r\n          <p><strong>Address:</strong> ${center.address || 'N/A'}</p>\r\n          <p><strong>Capacity:</strong> ${center.capacity || 'N/A'}</p>\r\n          <p><strong>Status:</strong> ${center.status || 'N/A'}</p>\r\n          <br>\r\n          <p><em>📱 Offline Mode: Routing not available. Use external navigation apps for directions.</em></p>\r\n        </div>\r\n      `,\r\n      buttons: [\r\n        {\r\n          text: 'Open in Maps',\r\n          handler: () => {\r\n            this.openInExternalMaps(center);\r\n          }\r\n        },\r\n        {\r\n          text: 'Close',\r\n          role: 'cancel'\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  // Open evacuation center in external maps app\r\n  async openInExternalMaps(center: EvacuationCenter) {\r\n    const lat = Number(center.latitude);\r\n    const lng = Number(center.longitude);\r\n\r\n    // Create maps URL that works on both Android and iOS\r\n    const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&travelmode=walking`;\r\n\r\n    try {\r\n      window.open(mapsUrl, '_system');\r\n    } catch (error) {\r\n      console.error('Error opening external maps:', error);\r\n      const toast = await this.toastCtrl.create({\r\n        message: 'Could not open external maps app',\r\n        duration: 3000,\r\n        color: 'danger'\r\n      });\r\n      await toast.present();\r\n    }\r\n  }\r\n\r\n  // Show transportation options when marker is clicked (online mode)\r\n  async showTransportationOptions(center: EvacuationCenter) {\r\n    // Check if we're offline before showing transportation options\r\n    const isOfflineMode = this.offlineStorage.isOfflineMode() || !navigator.onLine;\r\n\r\n    if (isOfflineMode) {\r\n      const distance = this.calculateDistance(\r\n        this.userLocation?.lat || 0,\r\n        this.userLocation?.lng || 0,\r\n        Number(center.latitude),\r\n        Number(center.longitude)\r\n      );\r\n      await this.showOfflineMarkerInfo(center, distance);\r\n      return;\r\n    }\r\n\r\n    const alert = await this.alertCtrl.create({\r\n      header: `Route to ${center.name}`,\r\n      message: 'Choose your transportation mode:',\r\n      buttons: [\r\n        {\r\n          text: '🚶‍♂️ Walk',\r\n          handler: () => {\r\n            this.routeToCenter(center, 'walking');\r\n          }\r\n        },\r\n        {\r\n          text: '🚴‍♂️ Cycle',\r\n          handler: () => {\r\n            this.routeToCenter(center, 'cycling');\r\n          }\r\n        },\r\n        {\r\n          text: '🚗 Drive',\r\n          handler: () => {\r\n            this.routeToCenter(center, 'driving');\r\n          }\r\n        },\r\n        {\r\n          text: 'Cancel',\r\n          role: 'cancel'\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  // Route to specific center with chosen transportation mode\r\n  async routeToCenter(center: EvacuationCenter, travelMode: 'walking' | 'cycling' | 'driving') {\r\n    if (!this.userLocation) return;\r\n\r\n    // Check if we're offline\r\n    const isOfflineMode = this.offlineStorage.isOfflineMode() || !navigator.onLine;\r\n\r\n    if (isOfflineMode) {\r\n      console.log('🟠 Offline mode: Cannot calculate routes');\r\n      const toast = await this.toastCtrl.create({\r\n        message: '📱 Offline mode: Routing not available. Use external navigation apps.',\r\n        duration: 4000,\r\n        color: 'warning'\r\n      });\r\n      await toast.present();\r\n\r\n      // Offer to open in external maps\r\n      await this.openInExternalMaps(center);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Clear existing routes\r\n      this.clearRoutes();\r\n\r\n      const lat = Number(center.latitude);\r\n      const lng = Number(center.longitude);\r\n\r\n      if (!isNaN(lat) && !isNaN(lng)) {\r\n        const mapboxProfile = this.mapboxRouting.convertTravelModeToProfile(travelMode);\r\n\r\n        const routeData = await this.mapboxRouting.getDirections(\r\n          this.userLocation.lng, this.userLocation.lat,\r\n          lng, lat,\r\n          mapboxProfile,\r\n          {\r\n            geometries: 'geojson',\r\n            overview: 'full',\r\n            steps: false\r\n          }\r\n        );\r\n\r\n        if (routeData && routeData.routes && routeData.routes.length > 0) {\r\n          const route = routeData.routes[0];\r\n\r\n          // Use earthquake color (orange)\r\n          const routeColor = '#ff9500';\r\n\r\n          this.routeLayer = L.layerGroup().addTo(this.map);\r\n\r\n          // Draw route\r\n          const routeLine = L.polyline(\r\n            route.geometry.coordinates.map((coord: number[]) => [coord[1], coord[0]]),\r\n            {\r\n              color: routeColor,\r\n              weight: 5,\r\n              opacity: 0.8\r\n            }\r\n          );\r\n\r\n          routeLine.addTo(this.routeLayer);\r\n\r\n          // Show route info\r\n          const toast = await this.toastCtrl.create({\r\n            message: `🟠 Route: ${(route.distance/1000).toFixed(2)}km, ${(route.duration/60).toFixed(0)}min via ${travelMode}`,\r\n            duration: 4000,\r\n            color: 'warning'\r\n          });\r\n          await toast.present();\r\n\r\n          // Fit map to route\r\n          this.map.fitBounds(routeLine.getBounds(), { padding: [50, 50] });\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('🟠 Error routing to center:', error);\r\n\r\n      const toast = await this.toastCtrl.create({\r\n        message: 'Error calculating route. Please try again.',\r\n        duration: 3000,\r\n        color: 'danger'\r\n      });\r\n      await toast.present();\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigate(['/tabs/home']);\r\n  }\r\n\r\n  ionViewWillLeave() {\r\n    this.clearRoutes();\r\n    if (this.map) {\r\n      this.map.remove();\r\n    }\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"warning\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-button (click)=\"goBack()\">\r\n        <ion-icon name=\"chevron-back-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n    <ion-title>🟠 Earthquake Evacuation Centers</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div id=\"earthquake-map\" style=\"height: 100%; width: 100%;\"></div>\r\n  \r\n  <!-- Floating info card -->\r\n  <div class=\"floating-info\">\r\n    <ion-card>\r\n      <ion-card-content>\r\n        <div class=\"info-row\">\r\n          <ion-icon name=\"warning\" color=\"warning\"></ion-icon>\r\n          <span>Earthquake Centers: {{ evacuationCenters.length }}</span>\r\n        </div>\r\n        <div class=\"info-text\">\r\n          Showing evacuation centers specifically for earthquake disasters\r\n        </div>\r\n      </ion-card-content>\r\n    </ion-card>\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,QAAmB;AAqBb,IAAO,oBAAP,MAAO,mBAAiB;EAP9B,cAAA;AASU,SAAA,aAAmC;AACnC,SAAA,aAAkC;AAClC,SAAA,iBAA6B,CAAA;AAE9B,SAAA,oBAAwC,CAAA;AACxC,SAAA,eAAoD;AAGpD,SAAA,cAA6B;AAC7B,SAAA,kBAA2B;AAC3B,SAAA,YAA2B;AAC3B,SAAA,YAA2B;AAE1B,SAAA,cAAc,OAAO,iBAAiB;AACtC,SAAA,YAAY,OAAO,eAAe;AAClC,SAAA,YAAY,OAAO,eAAe;AAClC,SAAA,OAAO,OAAO,UAAU;AACxB,SAAA,SAAS,OAAO,MAAM;AACtB,SAAA,QAAQ,OAAO,cAAc;AAC7B,SAAA,gBAAgB,OAAO,oBAAoB;AAC3C,SAAA,iBAAiB,OAAO,qBAAqB;;EAErD,WAAQ;AACN,YAAQ,IAAI,oDAA6C;AAIzD,SAAK,MAAM,YAAY,UAAU,CAAC,WAAe;AAC/C,UAAI,OAAO,aAAa,GAAG;AACzB,aAAK,cAAc,OAAO,aAAa;AACvC,aAAK,kBAAkB,OAAO,iBAAiB,MAAM;AACrD,aAAK,YAAY,OAAO,WAAW,IAAI,WAAW,OAAO,WAAW,CAAC,IAAI;AACzE,aAAK,YAAY,OAAO,WAAW,IAAI,WAAW,OAAO,WAAW,CAAC,IAAI;AACzE,gBAAQ,IAAI,sDAA+C,KAAK,WAAW;MAC7E;IACF,CAAC;EACH;EAEM,kBAAe;;AACnB,cAAQ,IAAI,4DAAqD;AAEjE,iBAAW,MAAW;AACpB,cAAM,KAAK,kBAAiB;MAC9B,IAAG,GAAG;IACR;;EAEM,oBAAiB;;AACrB,YAAM,UAAU,MAAM,KAAK,YAAY,OAAO;QAC5C,SAAS;QACT,SAAS;OACV;AACD,YAAM,QAAQ,QAAO;AAErB,UAAI;AAEF,cAAM,WAAW,MAAM,YAAY,mBAAmB;UACpD,oBAAoB;UACpB,SAAS;SACV;AAED,cAAM,UAAU,SAAS,OAAO;AAChC,cAAM,UAAU,SAAS,OAAO;AAEhC,aAAK,eAAe,EAAE,KAAK,SAAS,KAAK,QAAO;AAEhD,gBAAQ,IAAI,4CAAqC,OAAO,KAAK,OAAO,GAAG;AAGvE,aAAK,cAAc,SAAS,OAAO;AAGnC,cAAM,KAAK,sBAAsB,SAAS,OAAO;AAEjD,cAAM,QAAQ,QAAO;AAGrB,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS,qBAAc,KAAK,kBAAkB,MAAM;UACpD,UAAU;UACV,OAAO;UACP,UAAU;SACX;AACD,cAAM,MAAM,QAAO;MAErB,SAAS,OAAO;AACd,cAAM,QAAQ,QAAO;AACrB,gBAAQ,MAAM,+CAAwC,KAAK;AAE3D,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,QAAQ;UACR,SAAS;UACT,SAAS;YACP;cACE,MAAM;cACN,SAAS,MAAM,KAAK,kBAAiB;;YAEvC;cACE,MAAM;cACN,SAAS,MAAM,KAAK,OAAO,SAAS,CAAC,YAAY,CAAC;;;SAGvD;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;EAEA,cAAc,KAAa,KAAW;AACpC,YAAQ,IAAI,kDAA2C,GAAG,KAAK,GAAG,GAAG;AAGrE,UAAM,YAAY,SAAS,eAAe,gBAAgB;AAC1D,QAAI,CAAC,WAAW;AACd,cAAQ,MAAM,gEAAyD;AACvE,YAAM,IAAI,MAAM,qEAAqE;IACvF;AAEA,QAAI,KAAK,KAAK;AACZ,WAAK,IAAI,OAAM;IACjB;AAEA,SAAK,MAAQ,MAAI,gBAAgB,EAAE,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE;AAEzD,IAAE,YAAU,sDAAsD;MAChE,aAAa;KACd,EAAE,MAAM,KAAK,GAAG;AAGjB,SAAK,aAAe,SAAO,CAAC,KAAK,GAAG,GAAG;MACrC,MAAQ,OAAK;QACX,SAAS;QACT,UAAU,CAAC,IAAI,EAAE;QACjB,YAAY,CAAC,IAAI,EAAE;OACpB;KACF,EAAE,MAAM,KAAK,GAAG;AAEjB,SAAK,WAAW,UAAU,yBAAkB,EAAE,UAAS;EACzD;EAEM,sBAAsB,SAAiB,SAAe;;AAC1D,UAAI;AACF,gBAAQ,IAAI,0DAAmD;AAE/D,YAAI,aAAiC,CAAA;AAGrC,YAAI,KAAK,eAAe,cAAa,KAAM,CAAC,UAAU,QAAQ;AAC5D,kBAAQ,IAAI,2DAAoD;AAChE,uBAAa,MAAM,KAAK,eAAe,qBAAoB;AAC3D,kBAAQ,IAAI,2BAAoB,UAAU;AAE1C,cAAI,WAAW,WAAW,GAAG;AAC3B,oBAAQ,KAAK,iDAAuC;AACpD,kBAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;cACxC,QAAQ;cACR,SAAS;cACT,SAAS,CAAC,IAAI;aACf;AACD,kBAAM,MAAM,QAAO;AACnB;UACF;QACF,OAAO;AAEL,cAAI;AACF,yBAAa,MAAM,eACjB,KAAK,KAAK,IAAwB,GAAG,YAAY,MAAM,qBAAqB,CAAC;AAE/E,oBAAQ,IAAI,8DAAuD,YAAY,UAAU,CAAC;UAC5F,SAAS,UAAU;AACjB,oBAAQ,MAAM,oDAA+C,QAAQ;AACrE,yBAAa,MAAM,KAAK,eAAe,qBAAoB;AAE3D,gBAAI,WAAW,WAAW,GAAG;AAC3B,oBAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;gBACxC,QAAQ;gBACR,SAAS;gBACT,SAAS,CAAC,IAAI;eACf;AACD,oBAAM,MAAM,QAAO;AACnB;YACF;UACF;QACF;AAGA,aAAK,oBAAoB,WAAW,OAAO,YACzC,OAAO,kBAAkB,YAAY;AAGvC,gBAAQ,IAAI,yCAAkC,KAAK,kBAAkB,MAAM,qBAAqB;AAEhG,YAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,gBAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;YACxC,QAAQ;YACR,SAAS;YACT,SAAS,CAAC,IAAI;WACf;AACD,gBAAM,MAAM,QAAO;AACnB;QACF;AAGA,cAAM,KAAK,oBAAoB,SAAS,OAAO;MAEjD,SAAS,OAAO;AACd,gBAAQ,MAAM,mDAA4C,KAAK;AAG/D,YAAI;AACF,kBAAQ,IAAI,kDAA2C;AACvD,gBAAM,iBAAiB,MAAM,KAAK,eAAe,qBAAoB;AACrE,eAAK,oBAAoB,eAAe,OAAO,YAC7C,OAAO,kBAAkB,YAAY;AAGvC,cAAI,KAAK,kBAAkB,SAAS,GAAG;AACrC,oBAAQ,IAAI,oBAAa,KAAK,kBAAkB,MAAM,0CAA0C;AAEhG,kBAAM,KAAK,oBAAoB,SAAS,OAAO;AAC/C;UACF;QACF,SAAS,cAAc;AACrB,kBAAQ,MAAM,uCAAkC,YAAY;QAC9D;AAEA,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS;UACT,UAAU;UACV,OAAO;SACR;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;;EAGM,oBAAoB,SAAiB,SAAe;;AAExD,YAAM,gBAAgB,KAAK,eAAe,cAAa,KAAM,CAAC,UAAU;AAGxE,WAAK,kBAAkB,QAAQ,YAAS;AACtC,cAAM,MAAM,OAAO,OAAO,QAAQ;AAClC,cAAM,MAAM,OAAO,OAAO,SAAS;AAEnC,YAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;AAC9B,gBAAMA,UAAW,SAAO,CAAC,KAAK,GAAG,GAAG;YAClC,MAAQ,OAAK;cACX,SAAS;cACT,UAAU,CAAC,IAAI,EAAE;cACjB,YAAY,CAAC,IAAI,EAAE;cACnB,aAAa,CAAC,GAAG,GAAG;aACrB;WACF;AAED,gBAAM,WAAW,KAAK,kBAAkB,SAAS,SAAS,KAAK,GAAG;AAGlE,UAAAA,QAAO,GAAG,SAAS,MAAK;AACtB,gBAAI,eAAe;AACjB,mBAAK,sBAAsB,QAAQ,QAAQ;YAC7C,OAAO;AACL,mBAAK,0BAA0B,MAAM;YACvC;UACF,CAAC;AAGD,gBAAM,cAAc,KAAK,eAAe,OAAO,GAAG,SAAQ,MAAO,KAAK;AAGtE,gBAAM,mBAAmB,gBAAgB,mEAA4D;AAErG,UAAAA,QAAO,UAAU;;4BAEJ,OAAO,IAAI,IAAI,cAAc,gBAAW,EAAE;;6CAElB,WAAW,KAAM,QAAQ,CAAC,CAAC;4CAC5B,OAAO,YAAY,KAAK;cACtD,gBAAgB;cAChB,cAAc,sDAA+C,EAAE;;SAEpE;AAGD,cAAI,aAAa;AACf,YAAAA,QAAO,UAAS;AAChB,iBAAK,IAAI,QAAQ,CAAC,KAAK,GAAG,GAAG,EAAE;AAG/B,iBAAK,UAAU,OAAO;cACpB,SAAS,+CAAwC,OAAO,IAAI;cAC5D,UAAU;cACV,OAAO;cACP,UAAU;aACX,EAAE,KAAK,WAAS,MAAM,QAAO,CAAE;UAClC;AAEA,UAAAA,QAAO,MAAM,KAAK,GAAG;AACrB,kBAAQ,IAAI,sCAA+B,OAAO,IAAI,EAAE;QAC1D;MACF,CAAC;AAGD,UAAI,CAAC,eAAe;AAClB,gBAAQ,IAAI,wEAAiE;AAC7E,cAAM,KAAK,yBAAwB;MACrC,OAAO;AACL,gBAAQ,IAAI,2DAAoD;MAClE;AAGA,UAAI,KAAK,kBAAkB,SAAS,GAAG;AACrC,cAAM,SAAW,eAAa,CAAA,CAAE;AAChC,eAAO,OAAO,CAAC,SAAS,OAAO,CAAC;AAEhC,aAAK,kBAAkB,QAAQ,YAAS;AACtC,iBAAO,OAAO,CAAC,OAAO,OAAO,QAAQ,GAAG,OAAO,OAAO,SAAS,CAAC,CAAC;QACnE,CAAC;AAED,aAAK,IAAI,UAAU,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,EAAC,CAAE;MAClD;IACF;;EAEA,kBAAkB,MAAc,MAAc,MAAc,MAAY;AACtE,UAAM,IAAI;AACV,UAAM,UAAK,OAAO,KAAK,KAAK;AAC5B,UAAM,UAAK,OAAO,KAAK,KAAK;AAC5B,UAAM,gBAAM,OAAO,QAAQ,KAAK,KAAK;AACrC,UAAM,gBAAM,OAAO,QAAQ,KAAK,KAAK;AAErC,UAAM,IAAI,KAAK,IAAI,eAAK,CAAC,IAAI,KAAK,IAAI,eAAK,CAAC,IAClC,KAAK,IAAI,OAAE,IAAI,KAAK,IAAI,OAAE,IAC1B,KAAK,IAAI,eAAK,CAAC,IAAI,KAAK,IAAI,eAAK,CAAC;AAE5C,UAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AACvD,WAAO,IAAI;EACb;;EAGM,2BAAwB;;AAC5B,UAAI,CAAC,KAAK,gBAAgB,KAAK,kBAAkB,WAAW,GAAG;AAC7D,gBAAQ,IAAI,4EAAqE;AACjF;MACF;AAEA,UAAI;AACF,gBAAQ,IAAI,mEAA4D;AAGxE,cAAM,iBAAiB,KAAK,qBAC1B,KAAK,aAAa,KAClB,KAAK,aAAa,GAAG;AAGvB,YAAI,eAAe,WAAW,GAAG;AAC/B;QACF;AAGA,aAAK,YAAW;AAGhB,cAAM,KAAK,gBAAgB,cAAc;MAE3C,SAAS,OAAO;AACd,gBAAQ,MAAM,sDAA+C,KAAK;MACpE;IACF;;;EAGA,qBAAqB,SAAiB,SAAe;AACnD,UAAM,sBAAsB,KAAK,kBAAkB,IAAI,YAAW,iCAC7D,SAD6D;MAEhE,UAAU,KAAK,kBACb,SAAS,SACT,OAAO,OAAO,QAAQ,GAAG,OAAO,OAAO,SAAS,CAAC;MAEnD;AAGF,WAAO,oBACJ,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE,QAAQ,EACtC,MAAM,GAAG,CAAC;EACf;;EAGM,gBAAgB,SAA2B;;AAC/C,UAAI,CAAC,KAAK;AAAc;AAExB,WAAK,aAAe,aAAU,EAAG,MAAM,KAAK,GAAG;AAE/C,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAM,SAAS,QAAQ,CAAC;AACxB,cAAM,MAAM,OAAO,OAAO,QAAQ;AAClC,cAAM,MAAM,OAAO,OAAO,SAAS;AAEnC,YAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;AAC9B,cAAI;AACF,kBAAM,gBAAgB,KAAK,cAAc,2BAA2B,SAAS;AAE7E,kBAAM,YAAY,MAAM,KAAK,cAAc,cACzC,KAAK,aAAa,KAAK,KAAK,aAAa,KACzC,KAAK,KACL,eACA;cACE,YAAY;cACZ,UAAU;cACV,OAAO;aACR;AAGH,gBAAI,aAAa,UAAU,UAAU,UAAU,OAAO,SAAS,GAAG;AAChE,oBAAM,QAAQ,UAAU,OAAO,CAAC;AAGhC,oBAAM,aAAa;AAGnB,oBAAM,YAAc,WAClB,MAAM,SAAS,YAAY,IAAI,CAAC,UAAoB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GACxE;gBACE,OAAO;gBACP,QAAQ;gBACR,SAAS;gBACT,WAAW,MAAM,IAAI,SAAY;;eAClC;AAGH,wBAAU,MAAM,KAAK,UAAU;AAE/B,sBAAQ,IAAI,mBAAY,IAAI,CAAC,MAAM,MAAM,WAAS,KAAM,QAAQ,CAAC,CAAC,QAAQ,MAAM,WAAS,IAAI,QAAQ,CAAC,CAAC,KAAK;YAC9G;UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,+CAAwC,IAAI,CAAC,KAAK,KAAK;UACvE;QACF;MACF;IACF;;;EAGA,cAAW;AACT,QAAI,KAAK,YAAY;AACnB,WAAK,IAAI,YAAY,KAAK,UAAU;AACpC,WAAK,aAAa;IACpB;AAEA,SAAK,eAAe,QAAQ,CAAAA,YAAS;AACnC,WAAK,IAAI,YAAYA,OAAM;IAC7B,CAAC;AACD,SAAK,iBAAiB,CAAA;EACxB;;EAGM,sBAAsB,QAA0B,UAAgB;;AACpE,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC,QAAQ,aAAM,OAAO,IAAI;QACzB,SAAS;;;2CAG4B,WAAW,KAAM,QAAQ,CAAC,CAAC;yCAC7B,OAAO,WAAW,KAAK;0CACtB,OAAO,YAAY,KAAK;wCAC1B,OAAO,UAAU,KAAK;;;;;QAKxD,SAAS;UACP;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,mBAAmB,MAAM;YAChC;;UAEF;YACE,MAAM;YACN,MAAM;;;OAGX;AAED,YAAM,MAAM,QAAO;IACrB;;;EAGM,mBAAmB,QAAwB;;AAC/C,YAAM,MAAM,OAAO,OAAO,QAAQ;AAClC,YAAM,MAAM,OAAO,OAAO,SAAS;AAGnC,YAAM,UAAU,sDAAsD,GAAG,IAAI,GAAG;AAEhF,UAAI;AACF,eAAO,KAAK,SAAS,SAAS;MAChC,SAAS,OAAO;AACd,gBAAQ,MAAM,gCAAgC,KAAK;AACnD,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS;UACT,UAAU;UACV,OAAO;SACR;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;;EAGM,0BAA0B,QAAwB;;AAEtD,YAAM,gBAAgB,KAAK,eAAe,cAAa,KAAM,CAAC,UAAU;AAExE,UAAI,eAAe;AACjB,cAAM,WAAW,KAAK,kBACpB,KAAK,cAAc,OAAO,GAC1B,KAAK,cAAc,OAAO,GAC1B,OAAO,OAAO,QAAQ,GACtB,OAAO,OAAO,SAAS,CAAC;AAE1B,cAAM,KAAK,sBAAsB,QAAQ,QAAQ;AACjD;MACF;AAEA,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC,QAAQ,YAAY,OAAO,IAAI;QAC/B,SAAS;QACT,SAAS;UACP;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,cAAc,QAAQ,SAAS;YACtC;;UAEF;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,cAAc,QAAQ,SAAS;YACtC;;UAEF;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,cAAc,QAAQ,SAAS;YACtC;;UAEF;YACE,MAAM;YACN,MAAM;;;OAGX;AAED,YAAM,MAAM,QAAO;IACrB;;;EAGM,cAAc,QAA0B,YAA6C;;AACzF,UAAI,CAAC,KAAK;AAAc;AAGxB,YAAM,gBAAgB,KAAK,eAAe,cAAa,KAAM,CAAC,UAAU;AAExE,UAAI,eAAe;AACjB,gBAAQ,IAAI,iDAA0C;AACtD,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS;UACT,UAAU;UACV,OAAO;SACR;AACD,cAAM,MAAM,QAAO;AAGnB,cAAM,KAAK,mBAAmB,MAAM;AACpC;MACF;AAEA,UAAI;AAEF,aAAK,YAAW;AAEhB,cAAM,MAAM,OAAO,OAAO,QAAQ;AAClC,cAAM,MAAM,OAAO,OAAO,SAAS;AAEnC,YAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG;AAC9B,gBAAM,gBAAgB,KAAK,cAAc,2BAA2B,UAAU;AAE9E,gBAAM,YAAY,MAAM,KAAK,cAAc,cACzC,KAAK,aAAa,KAAK,KAAK,aAAa,KACzC,KAAK,KACL,eACA;YACE,YAAY;YACZ,UAAU;YACV,OAAO;WACR;AAGH,cAAI,aAAa,UAAU,UAAU,UAAU,OAAO,SAAS,GAAG;AAChE,kBAAM,QAAQ,UAAU,OAAO,CAAC;AAGhC,kBAAM,aAAa;AAEnB,iBAAK,aAAe,aAAU,EAAG,MAAM,KAAK,GAAG;AAG/C,kBAAM,YAAc,WAClB,MAAM,SAAS,YAAY,IAAI,CAAC,UAAoB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GACxE;cACE,OAAO;cACP,QAAQ;cACR,SAAS;aACV;AAGH,sBAAU,MAAM,KAAK,UAAU;AAG/B,kBAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;cACxC,SAAS,qBAAc,MAAM,WAAS,KAAM,QAAQ,CAAC,CAAC,QAAQ,MAAM,WAAS,IAAI,QAAQ,CAAC,CAAC,WAAW,UAAU;cAChH,UAAU;cACV,OAAO;aACR;AACD,kBAAM,MAAM,QAAO;AAGnB,iBAAK,IAAI,UAAU,UAAU,UAAS,GAAI,EAAE,SAAS,CAAC,IAAI,EAAE,EAAC,CAAE;UACjE;QACF;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,sCAA+B,KAAK;AAElD,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,SAAS;UACT,UAAU;UACV,OAAO;SACR;AACD,cAAM,MAAM,QAAO;MACrB;IACF;;EAEA,SAAM;AACJ,SAAK,OAAO,SAAS,CAAC,YAAY,CAAC;EACrC;EAEA,mBAAgB;AACd,SAAK,YAAW;AAChB,QAAI,KAAK,KAAK;AACZ,WAAK,IAAI,OAAM;IACjB;EACF;;;uCAxoBW,oBAAiB;IAAA;EAAA;;yEAAjB,oBAAiB,WAAA,CAAA,CAAA,oBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,QAAA,OAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,sBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,MAAA,kBAAA,GAAA,UAAA,QAAA,SAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,QAAA,WAAA,SAAA,SAAA,GAAA,CAAA,GAAA,WAAA,CAAA,GAAA,UAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;AC/B9B,QAAA,yBAAA,GAAA,cAAA,CAAA,EAAiC,GAAA,eAAA,CAAA,EACF,GAAA,eAAA,CAAA,EACD,GAAA,cAAA,CAAA;AACZ,QAAA,qBAAA,SAAA,SAAA,yDAAA;AAAA,iBAAS,IAAA,OAAA;QAAQ,CAAA;AAC3B,QAAA,oBAAA,GAAA,YAAA,CAAA;AACF,QAAA,uBAAA,EAAa;AAEf,QAAA,yBAAA,GAAA,WAAA;AAAW,QAAA,iBAAA,GAAA,yCAAA;AAAgC,QAAA,uBAAA,EAAY,EAC3C;AAGhB,QAAA,yBAAA,GAAA,eAAA,CAAA;AACE,QAAA,oBAAA,GAAA,OAAA,CAAA;AAGA,QAAA,yBAAA,GAAA,OAAA,CAAA,EAA2B,IAAA,UAAA,EACf,IAAA,kBAAA,EACU,IAAA,OAAA,CAAA;AAEd,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,yBAAA,IAAA,MAAA;AAAM,QAAA,iBAAA,EAAA;AAAkD,QAAA,uBAAA,EAAO;AAEjE,QAAA,yBAAA,IAAA,OAAA,EAAA;AACE,QAAA,iBAAA,IAAA,oEAAA;AACF,QAAA,uBAAA,EAAM,EACW,EACV,EACP;;;AA3BI,QAAA,qBAAA,eAAA,IAAA;AAWC,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,cAAA,IAAA;AASG,QAAA,oBAAA,CAAA;AAAA,QAAA,6BAAA,wBAAA,IAAA,kBAAA,QAAA,EAAA;;sBDSJ,aAAW,WAAA,YAAA,SAAA,gBAAA,YAAA,WAAA,SAAA,UAAA,YAAE,YAAY,GAAA,QAAA,CAAA,+6DAAA,EAAA,CAAA;EAAA;;;sEAExB,mBAAiB,CAAA;UAP7B;uBACW,sBAAoB,YAGlB,MAAI,SACP,CAAC,aAAa,YAAY,GAAC,UAAA,u+BAAA,QAAA,CAAA,48CAAA,EAAA,CAAA;;;;6EAEzB,mBAAiB,EAAA,WAAA,qBAAA,UAAA,sDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": ["marker"]}
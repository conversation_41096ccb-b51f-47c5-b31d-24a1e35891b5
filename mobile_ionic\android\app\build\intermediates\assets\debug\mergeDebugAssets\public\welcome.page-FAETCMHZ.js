import {
  CommonModule,
  Component,
  FormsModule,
  IonButton,
  IonContent,
  IonicModule,
  Router,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵtext
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import "./chunk-UL2P3LPA.js";

// src/app/pages/welcome/welcome.page.ts
var WelcomePage = class _WelcomePage {
  constructor(router) {
    this.router = router;
  }
  getStarted() {
    this.router.navigate(["/data"]);
  }
  static {
    this.\u0275fac = function WelcomePage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _WelcomePage)(\u0275\u0275directiveInject(Router));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _WelcomePage, selectors: [["app-welcome"]], decls: 9, vars: 0, consts: [[1, "ion-padding", "welcome-bg"], [1, "welcome-wrapper"], ["src", "assets/ALERTO.png", "alt", "App Logo", 1, "welcome-logo"], [1, "welcome-title"], [2, "color", "#ff0000", "font-weight", "700", "font-size", "35px"], ["expand", "block", 1, "welcome-btn", 3, "click"]], template: function WelcomePage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-content", 0)(1, "div", 1);
        \u0275\u0275element(2, "img", 2);
        \u0275\u0275elementStart(3, "h4", 3);
        \u0275\u0275text(4, "Welcome To ");
        \u0275\u0275elementStart(5, "strong", 4);
        \u0275\u0275text(6, "Alerto!");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(7, "ion-button", 5);
        \u0275\u0275listener("click", function WelcomePage_Template_ion_button_click_7_listener() {
          return ctx.getStarted();
        });
        \u0275\u0275text(8, "Get Started");
        \u0275\u0275elementEnd()()();
      }
    }, dependencies: [IonicModule, IonButton, IonContent, CommonModule, FormsModule], styles: ["\n\n.welcome-wrapper[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 80vh;\n  width: 100%;\n  text-align: center;\n  align-items: center;\n  padding-top: 60px;\n}\n.welcome-logo[_ngcontent-%COMP%] {\n  width: 400px;\n  height: 400px;\n  align-items: center;\n  margin: 20px auto;\n}\n.welcome-title[_ngcontent-%COMP%] {\n  font-size: 25px;\n  font-weight: 700;\n}\n.welcome-desc[_ngcontent-%COMP%] {\n  color: #888;\n}\n.welcome-btn[_ngcontent-%COMP%] {\n  --border-radius: 25px;\n  font-size: 1.1rem;\n  width: 100%;\n  height: 48px;\n}\n.welcome-link[_ngcontent-%COMP%] {\n  font-size: 1rem;\n}\n.welcome-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  font-weight: 600;\n}\n/*# sourceMappingURL=welcome.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(WelcomePage, [{
    type: Component,
    args: [{ selector: "app-welcome", standalone: true, imports: [IonicModule, CommonModule, FormsModule], template: '<ion-content class="ion-padding welcome-bg">\r\n  <div class="welcome-wrapper">\r\n    <img src="assets/ALERTO.png" alt="App Logo" class="welcome-logo" />\r\n    <h4 class="welcome-title">Welcome To <strong style="color: #ff0000; font-weight: 700; font-size: 35px;">Alerto!</strong></h4>\r\n    <ion-button expand="block" class="welcome-btn" (click)="getStarted()">Get Started</ion-button>\r\n    \r\n  </div>\r\n</ion-content>', styles: ["/* src/app/pages/welcome/welcome.page.scss */\n.welcome-wrapper {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 80vh;\n  width: 100%;\n  text-align: center;\n  align-items: center;\n  padding-top: 60px;\n}\n.welcome-logo {\n  width: 400px;\n  height: 400px;\n  align-items: center;\n  margin: 20px auto;\n}\n.welcome-title {\n  font-size: 25px;\n  font-weight: 700;\n}\n.welcome-desc {\n  color: #888;\n}\n.welcome-btn {\n  --border-radius: 25px;\n  font-size: 1.1rem;\n  width: 100%;\n  height: 48px;\n}\n.welcome-link {\n  font-size: 1rem;\n}\n.welcome-link a {\n  font-weight: 600;\n}\n/*# sourceMappingURL=welcome.page.css.map */\n"] }]
  }], () => [{ type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(WelcomePage, { className: "WelcomePage", filePath: "src/app/pages/welcome/welcome.page.ts", lineNumber: 14 });
})();
export {
  WelcomePage
};
//# sourceMappingURL=welcome.page-FAETCMHZ.js.map

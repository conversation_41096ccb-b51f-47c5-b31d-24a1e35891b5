{"version": 3, "sources": ["src/app/pages/notifications/notifications.page.ts", "src/app/pages/notifications/notifications.page.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { Router } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { environment } from '../../../environments/environment';\r\nimport { Subscription } from 'rxjs';\r\nimport { FcmService, FCMNotification } from '../../services/fcm.service';\r\n\r\nexport interface AppNotification {\r\n  id: number;\r\n  type: 'evacuation_center_added' | 'emergency_alert' | 'system_update' | 'general';\r\n  title: string;\r\n  message: string;\r\n  data?: any;\r\n  read: boolean;\r\n  created_at: string;\r\n  updated_at: string;\r\n  reactions?: number;\r\n  user_id?: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-notifications',\r\n  templateUrl: './notifications.page.html',\r\n  styleUrls: ['./notifications.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule]\r\n})\r\nexport class NotificationsPage implements OnInit, OnDestroy {\r\n  notifications: AppNotification[] = [];\r\n  filteredNotifications: AppNotification[] = [];\r\n  activeTab: 'all' | 'unread' = 'all';\r\n  unreadCount = 0;\r\n  isLoading = false;\r\n  hasMoreNotifications = false;\r\n  currentPage = 1;\r\n  private notificationSubscription: Subscription | null = null;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private http: HttpClient,\r\n    private fcmService: FcmService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadNotifications();\r\n    this.subscribeToNewNotifications();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.notificationSubscription) {\r\n      this.notificationSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  async loadNotifications() {\r\n    this.isLoading = true;\r\n    try {\r\n      const response = await this.http.get<{\r\n        notifications: AppNotification[],\r\n        unread_count: number,\r\n        has_more: boolean\r\n      }>(`${environment.apiUrl}/notifications?page=${this.currentPage}`).toPromise();\r\n\r\n      if (response) {\r\n        if (this.currentPage === 1) {\r\n          this.notifications = response.notifications;\r\n        } else {\r\n          this.notifications.push(...response.notifications);\r\n        }\r\n        this.unreadCount = response.unread_count;\r\n        this.hasMoreNotifications = response.has_more;\r\n        this.filterNotifications();\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading notifications:', error);\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  subscribeToNewNotifications() {\r\n    this.notificationSubscription = this.fcmService.notifications$.subscribe(\r\n      (fcmNotification: FCMNotification) => {\r\n        // Convert FCM notification to app notification format\r\n        const appNotification: AppNotification = {\r\n          id: Date.now(), // Temporary ID\r\n          type: this.mapFCMTypeToAppType(fcmNotification.category),\r\n          title: fcmNotification.title,\r\n          message: fcmNotification.body,\r\n          data: fcmNotification,\r\n          read: false,\r\n          created_at: new Date().toISOString(),\r\n          updated_at: new Date().toISOString()\r\n        };\r\n\r\n        // Add to the beginning of the list\r\n        this.notifications.unshift(appNotification);\r\n        this.unreadCount++;\r\n        this.filterNotifications();\r\n      }\r\n    );\r\n  }\r\n\r\n  mapFCMTypeToAppType(category?: string): AppNotification['type'] {\r\n    switch (category?.toLowerCase()) {\r\n      case 'evacuation':\r\n      case 'evacuation_center':\r\n        return 'evacuation_center_added';\r\n      case 'emergency':\r\n      case 'earthquake':\r\n      case 'typhoon':\r\n      case 'flood':\r\n        return 'emergency_alert';\r\n      case 'system':\r\n        return 'system_update';\r\n      default:\r\n        return 'general';\r\n    }\r\n  }\r\n\r\n  setActiveTab(tab: 'all' | 'unread') {\r\n    this.activeTab = tab;\r\n    this.filterNotifications();\r\n  }\r\n\r\n  filterNotifications() {\r\n    if (this.activeTab === 'unread') {\r\n      this.filteredNotifications = this.notifications.filter(n => !n.read);\r\n    } else {\r\n      this.filteredNotifications = this.notifications;\r\n    }\r\n  }\r\n\r\n  async onNotificationClick(notification: AppNotification) {\r\n    // Mark as read if unread\r\n    if (!notification.read) {\r\n      await this.markAsRead(notification);\r\n    }\r\n\r\n    // Navigate based on notification type\r\n    switch (notification.type) {\r\n      case 'evacuation_center_added':\r\n        this.router.navigate(['/tabs/map'], { \r\n          queryParams: { \r\n            disasterType: 'all',\r\n            showNewCenters: true \r\n          } \r\n        });\r\n        break;\r\n      case 'emergency_alert':\r\n        const disasterType = this.extractDisasterType(notification);\r\n        this.router.navigate(['/tabs/map'], { \r\n          queryParams: { \r\n            disasterType: disasterType \r\n          } \r\n        });\r\n        break;\r\n      default:\r\n        // Handle other notification types\r\n        break;\r\n    }\r\n  }\r\n\r\n  extractDisasterType(notification: AppNotification): string {\r\n    const message = notification.message.toLowerCase();\r\n    if (message.includes('earthquake')) return 'Earthquake';\r\n    if (message.includes('typhoon')) return 'Typhoon';\r\n    if (message.includes('flood')) return 'Flood';\r\n    return 'all';\r\n  }\r\n\r\n  async markAsRead(notification: AppNotification) {\r\n    try {\r\n      await this.http.put(`${environment.apiUrl}/notifications/${notification.id}/read`, {}).toPromise();\r\n      notification.read = true;\r\n      this.unreadCount = Math.max(0, this.unreadCount - 1);\r\n      this.filterNotifications();\r\n    } catch (error) {\r\n      console.error('Error marking notification as read:', error);\r\n    }\r\n  }\r\n\r\n  async markAllAsRead() {\r\n    try {\r\n      await this.http.put(`${environment.apiUrl}/notifications/mark-all-read`, {}).toPromise();\r\n      this.notifications.forEach(n => n.read = true);\r\n      this.unreadCount = 0;\r\n      this.filterNotifications();\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n    }\r\n  }\r\n\r\n  loadMoreNotifications() {\r\n    if (!this.isLoading && this.hasMoreNotifications) {\r\n      this.currentPage++;\r\n      this.loadNotifications();\r\n    }\r\n  }\r\n\r\n  seeAllNotifications() {\r\n    this.setActiveTab('all');\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigate(['/tabs/home']);\r\n  }\r\n\r\n  trackByNotificationId(index: number, notification: AppNotification): number {\r\n    return notification.id;\r\n  }\r\n\r\n  getNotificationIcon(notification: AppNotification): string {\r\n    switch (notification.type) {\r\n      case 'evacuation_center_added':\r\n        return 'assets/evacuation-center-icon.png';\r\n      case 'emergency_alert':\r\n        return 'assets/emergency-icon.png';\r\n      case 'system_update':\r\n        return 'assets/system-icon.png';\r\n      default:\r\n        return 'assets/alerto_icon.png';\r\n    }\r\n  }\r\n\r\n  getBadgeIcon(notification: AppNotification): string {\r\n    switch (notification.type) {\r\n      case 'evacuation_center_added':\r\n        return 'add-circle';\r\n      case 'emergency_alert':\r\n        return 'warning';\r\n      case 'system_update':\r\n        return 'settings';\r\n      default:\r\n        return 'notifications';\r\n    }\r\n  }\r\n\r\n  getIconBadgeClass(notification: AppNotification): string {\r\n    switch (notification.type) {\r\n      case 'evacuation_center_added':\r\n        return 'badge-success';\r\n      case 'emergency_alert':\r\n        return 'badge-danger';\r\n      case 'system_update':\r\n        return 'badge-info';\r\n      default:\r\n        return 'badge-primary';\r\n    }\r\n  }\r\n\r\n  getNotificationTitle(notification: AppNotification): string {\r\n    switch (notification.type) {\r\n      case 'evacuation_center_added':\r\n        return 'New evacuation center added.';\r\n      case 'emergency_alert':\r\n        return notification.title;\r\n      default:\r\n        return notification.title;\r\n    }\r\n  }\r\n\r\n  getNotificationDescription(notification: AppNotification): string {\r\n    return notification.message;\r\n  }\r\n\r\n  getTimeAgo(dateString: string): string {\r\n    const date = new Date(dateString);\r\n    const now = new Date();\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) return `${diffInSeconds}s`;\r\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;\r\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;\r\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;\r\n    return `${Math.floor(diffInSeconds / 604800)}w`;\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar>\r\n    <ion-buttons slot=\"start\">\r\n      <ion-button (click)=\"goBack()\">\r\n        <ion-icon name=\"chevron-back-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n    <ion-title>Notifications</ion-title>\r\n    <ion-buttons slot=\"end\">\r\n      <ion-button (click)=\"markAllAsRead()\" [disabled]=\"unreadCount === 0\">\r\n        <ion-icon name=\"checkmark-done-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <!-- Header with tabs -->\r\n  <div class=\"notification-header\">\r\n    <div class=\"notification-tabs\">\r\n      <button \r\n        class=\"tab-button\" \r\n        [class.active]=\"activeTab === 'all'\"\r\n        (click)=\"setActiveTab('all')\">\r\n        All\r\n      </button>\r\n      <button \r\n        class=\"tab-button\" \r\n        [class.active]=\"activeTab === 'unread'\"\r\n        (click)=\"setActiveTab('unread')\">\r\n        Unread\r\n        <span class=\"unread-badge\" *ngIf=\"unreadCount > 0\">{{ unreadCount }}</span>\r\n      </button>\r\n    </div>\r\n    \r\n    <div class=\"section-header\" *ngIf=\"filteredNotifications.length > 0\">\r\n      <span class=\"section-title\">Earlier</span>\r\n      <button class=\"see-all-btn\" (click)=\"seeAllNotifications()\">See all</button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Notifications List -->\r\n  <div class=\"notifications-container\">\r\n    <!-- No notifications message -->\r\n    <div class=\"no-notifications\" *ngIf=\"filteredNotifications.length === 0\">\r\n      <ion-icon name=\"notifications-outline\" class=\"no-notifications-icon\"></ion-icon>\r\n      <h3>No notifications yet</h3>\r\n      <p>When you receive notifications, they'll appear here.</p>\r\n    </div>\r\n\r\n    <!-- Notifications -->\r\n    <div class=\"notification-item\" \r\n         *ngFor=\"let notification of filteredNotifications; trackBy: trackByNotificationId\"\r\n         [class.unread]=\"!notification.read\"\r\n         (click)=\"onNotificationClick(notification)\">\r\n      \r\n      <!-- Notification Icon -->\r\n      <div class=\"notification-icon\">\r\n        <img [src]=\"getNotificationIcon(notification)\" [alt]=\"notification.type\" class=\"icon-image\">\r\n        <div class=\"icon-badge\" [ngClass]=\"getIconBadgeClass(notification)\">\r\n          <ion-icon [name]=\"getBadgeIcon(notification)\"></ion-icon>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Notification Content -->\r\n      <div class=\"notification-content\">\r\n        <div class=\"notification-text\">\r\n          <span class=\"notification-title\">{{ getNotificationTitle(notification) }}</span>\r\n          <span class=\"notification-description\">{{ getNotificationDescription(notification) }}</span>\r\n        </div>\r\n        <div class=\"notification-meta\">\r\n          <span class=\"notification-time\">{{ getTimeAgo(notification.created_at) }}</span>\r\n          <span class=\"notification-reactions\" *ngIf=\"notification.reactions\">\r\n            {{ notification.reactions }} Reactions\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Unread indicator -->\r\n      <div class=\"unread-indicator\" *ngIf=\"!notification.read\"></div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Load more button -->\r\n  <div class=\"load-more-container\" *ngIf=\"hasMoreNotifications\">\r\n    <ion-button fill=\"clear\" (click)=\"loadMoreNotifications()\" [disabled]=\"isLoading\">\r\n      <ion-spinner *ngIf=\"isLoading\" name=\"crescent\"></ion-spinner>\r\n      <span *ngIf=\"!isLoading\">Load More</span>\r\n    </ion-button>\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+BQ,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAmD,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA;;;;AAAjB,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,WAAA;;;;;;AAIvD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAqE,GAAA,QAAA,EAAA;AACvC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;AACnC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA4B,IAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,oBAAA,CAAqB;IAAA,CAAA;AAAE,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA,EAAS;;;;;AAO9E,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,sBAAA;AAAoB,IAAA,uBAAA;AACxB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,sDAAA;AAAoD,IAAA,uBAAA,EAAI;;;;;AAyBvD,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,gBAAA,WAAA,aAAA;;;;;AAMN,IAAA,oBAAA,GAAA,OAAA,EAAA;;;;;;AA5BF,IAAA,yBAAA,GAAA,OAAA,EAAA;AAGK,IAAA,qBAAA,SAAA,SAAA,yDAAA;AAAA,YAAA,kBAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,oBAAA,eAAA,CAAiC;IAAA,CAAA;AAG7C,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA;AACF,IAAA,uBAAA,EAAM;AAIR,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkC,GAAA,OAAA,EAAA,EACD,GAAA,QAAA,EAAA;AACI,IAAA,iBAAA,CAAA;AAAwC,IAAA,uBAAA;AACzE,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAuC,IAAA,iBAAA,EAAA;AAA8C,IAAA,uBAAA,EAAO;AAE9F,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA+B,IAAA,QAAA,EAAA;AACG,IAAA,iBAAA,EAAA;AAAyC,IAAA,uBAAA;AACzE,IAAA,qBAAA,IAAA,2CAAA,GAAA,GAAA,QAAA,EAAA;AAGF,IAAA,uBAAA,EAAM;AAIR,IAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA;;;;;AA3BK,IAAA,sBAAA,UAAA,CAAA,gBAAA,IAAA;AAKI,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,OAAA,OAAA,oBAAA,eAAA,GAAA,uBAAA,EAAyC,OAAA,gBAAA,IAAA;AACtB,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,kBAAA,eAAA,CAAA;AACZ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,aAAA,eAAA,CAAA;AAOuB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,qBAAA,eAAA,CAAA;AACM,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,2BAAA,eAAA,CAAA;AAGP,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,gBAAA,UAAA,CAAA;AACM,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,gBAAA,SAAA;AAOX,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,gBAAA,IAAA;;;;;AAO/B,IAAA,oBAAA,GAAA,eAAA,EAAA;;;;;AACA,IAAA,yBAAA,GAAA,MAAA;AAAyB,IAAA,iBAAA,GAAA,WAAA;AAAS,IAAA,uBAAA;;;;;;AAHtC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8D,GAAA,cAAA,EAAA;AACnC,IAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,sBAAA,CAAuB;IAAA,CAAA;AACvD,IAAA,qBAAA,GAAA,iDAAA,GAAA,GAAA,eAAA,EAAA,EAA+C,GAAA,0CAAA,GAAA,GAAA,QAAA,EAAA;AAEjD,IAAA,uBAAA,EAAa;;;;AAH8C,IAAA,oBAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,SAAA;AAC3C,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,SAAA;AACP,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,SAAA;;;ADzDP,IAAO,oBAAP,MAAO,mBAAiB;EAU5B,YACU,QACA,MACA,YAAsB;AAFtB,SAAA,SAAA;AACA,SAAA,OAAA;AACA,SAAA,aAAA;AAZV,SAAA,gBAAmC,CAAA;AACnC,SAAA,wBAA2C,CAAA;AAC3C,SAAA,YAA8B;AAC9B,SAAA,cAAc;AACd,SAAA,YAAY;AACZ,SAAA,uBAAuB;AACvB,SAAA,cAAc;AACN,SAAA,2BAAgD;EAMrD;EAEH,WAAQ;AACN,SAAK,kBAAiB;AACtB,SAAK,4BAA2B;EAClC;EAEA,cAAW;AACT,QAAI,KAAK,0BAA0B;AACjC,WAAK,yBAAyB,YAAW;IAC3C;EACF;EAEM,oBAAiB;;AACrB,WAAK,YAAY;AACjB,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,KAAK,IAI9B,GAAG,YAAY,MAAM,uBAAuB,KAAK,WAAW,EAAE,EAAE,UAAS;AAE5E,YAAI,UAAU;AACZ,cAAI,KAAK,gBAAgB,GAAG;AAC1B,iBAAK,gBAAgB,SAAS;UAChC,OAAO;AACL,iBAAK,cAAc,KAAK,GAAG,SAAS,aAAa;UACnD;AACA,eAAK,cAAc,SAAS;AAC5B,eAAK,uBAAuB,SAAS;AACrC,eAAK,oBAAmB;QAC1B;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,gCAAgC,KAAK;MACrD;AACE,aAAK,YAAY;MACnB;IACF;;EAEA,8BAA2B;AACzB,SAAK,2BAA2B,KAAK,WAAW,eAAe,UAC7D,CAAC,oBAAoC;AAEnC,YAAM,kBAAmC;QACvC,IAAI,KAAK,IAAG;;QACZ,MAAM,KAAK,oBAAoB,gBAAgB,QAAQ;QACvD,OAAO,gBAAgB;QACvB,SAAS,gBAAgB;QACzB,MAAM;QACN,MAAM;QACN,aAAY,oBAAI,KAAI,GAAG,YAAW;QAClC,aAAY,oBAAI,KAAI,GAAG,YAAW;;AAIpC,WAAK,cAAc,QAAQ,eAAe;AAC1C,WAAK;AACL,WAAK,oBAAmB;IAC1B,CAAC;EAEL;EAEA,oBAAoB,UAAiB;AACnC,YAAQ,UAAU,YAAW,GAAI;MAC/B,KAAK;MACL,KAAK;AACH,eAAO;MACT,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT;AACE,eAAO;IACX;EACF;EAEA,aAAa,KAAqB;AAChC,SAAK,YAAY;AACjB,SAAK,oBAAmB;EAC1B;EAEA,sBAAmB;AACjB,QAAI,KAAK,cAAc,UAAU;AAC/B,WAAK,wBAAwB,KAAK,cAAc,OAAO,OAAK,CAAC,EAAE,IAAI;IACrE,OAAO;AACL,WAAK,wBAAwB,KAAK;IACpC;EACF;EAEM,oBAAoB,cAA6B;;AAErD,UAAI,CAAC,aAAa,MAAM;AACtB,cAAM,KAAK,WAAW,YAAY;MACpC;AAGA,cAAQ,aAAa,MAAM;QACzB,KAAK;AACH,eAAK,OAAO,SAAS,CAAC,WAAW,GAAG;YAClC,aAAa;cACX,cAAc;cACd,gBAAgB;;WAEnB;AACD;QACF,KAAK;AACH,gBAAM,eAAe,KAAK,oBAAoB,YAAY;AAC1D,eAAK,OAAO,SAAS,CAAC,WAAW,GAAG;YAClC,aAAa;cACX;;WAEH;AACD;QACF;AAEE;MACJ;IACF;;EAEA,oBAAoB,cAA6B;AAC/C,UAAM,UAAU,aAAa,QAAQ,YAAW;AAChD,QAAI,QAAQ,SAAS,YAAY;AAAG,aAAO;AAC3C,QAAI,QAAQ,SAAS,SAAS;AAAG,aAAO;AACxC,QAAI,QAAQ,SAAS,OAAO;AAAG,aAAO;AACtC,WAAO;EACT;EAEM,WAAW,cAA6B;;AAC5C,UAAI;AACF,cAAM,KAAK,KAAK,IAAI,GAAG,YAAY,MAAM,kBAAkB,aAAa,EAAE,SAAS,CAAA,CAAE,EAAE,UAAS;AAChG,qBAAa,OAAO;AACpB,aAAK,cAAc,KAAK,IAAI,GAAG,KAAK,cAAc,CAAC;AACnD,aAAK,oBAAmB;MAC1B,SAAS,OAAO;AACd,gBAAQ,MAAM,uCAAuC,KAAK;MAC5D;IACF;;EAEM,gBAAa;;AACjB,UAAI;AACF,cAAM,KAAK,KAAK,IAAI,GAAG,YAAY,MAAM,gCAAgC,CAAA,CAAE,EAAE,UAAS;AACtF,aAAK,cAAc,QAAQ,OAAK,EAAE,OAAO,IAAI;AAC7C,aAAK,cAAc;AACnB,aAAK,oBAAmB;MAC1B,SAAS,OAAO;AACd,gBAAQ,MAAM,4CAA4C,KAAK;MACjE;IACF;;EAEA,wBAAqB;AACnB,QAAI,CAAC,KAAK,aAAa,KAAK,sBAAsB;AAChD,WAAK;AACL,WAAK,kBAAiB;IACxB;EACF;EAEA,sBAAmB;AACjB,SAAK,aAAa,KAAK;EACzB;EAEA,SAAM;AACJ,SAAK,OAAO,SAAS,CAAC,YAAY,CAAC;EACrC;EAEA,sBAAsB,OAAe,cAA6B;AAChE,WAAO,aAAa;EACtB;EAEA,oBAAoB,cAA6B;AAC/C,YAAQ,aAAa,MAAM;MACzB,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT;AACE,eAAO;IACX;EACF;EAEA,aAAa,cAA6B;AACxC,YAAQ,aAAa,MAAM;MACzB,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT;AACE,eAAO;IACX;EACF;EAEA,kBAAkB,cAA6B;AAC7C,YAAQ,aAAa,MAAM;MACzB,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT;AACE,eAAO;IACX;EACF;EAEA,qBAAqB,cAA6B;AAChD,YAAQ,aAAa,MAAM;MACzB,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO,aAAa;MACtB;AACE,eAAO,aAAa;IACxB;EACF;EAEA,2BAA2B,cAA6B;AACtD,WAAO,aAAa;EACtB;EAEA,WAAW,YAAkB;AAC3B,UAAM,OAAO,IAAI,KAAK,UAAU;AAChC,UAAM,MAAM,oBAAI,KAAI;AACpB,UAAM,gBAAgB,KAAK,OAAO,IAAI,QAAO,IAAK,KAAK,QAAO,KAAM,GAAI;AAExE,QAAI,gBAAgB;AAAI,aAAO,GAAG,aAAa;AAC/C,QAAI,gBAAgB;AAAM,aAAO,GAAG,KAAK,MAAM,gBAAgB,EAAE,CAAC;AAClE,QAAI,gBAAgB;AAAO,aAAO,GAAG,KAAK,MAAM,gBAAgB,IAAI,CAAC;AACrE,QAAI,gBAAgB;AAAQ,aAAO,GAAG,KAAK,MAAM,gBAAgB,KAAK,CAAC;AACvE,WAAO,GAAG,KAAK,MAAM,gBAAgB,MAAM,CAAC;EAC9C;;;uCAzPW,oBAAiB,4BAAA,MAAA,GAAA,4BAAA,UAAA,GAAA,4BAAA,UAAA,CAAA;IAAA;EAAA;;yEAAjB,oBAAiB,WAAA,CAAA,CAAA,mBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,OAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,sBAAA,GAAA,CAAA,QAAA,KAAA,GAAA,CAAA,GAAA,SAAA,UAAA,GAAA,CAAA,QAAA,wBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,OAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,yBAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,UAAA,SAAA,GAAA,SAAA,WAAA,cAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,QAAA,yBAAA,GAAA,uBAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,OAAA,KAAA,GAAA,CAAA,GAAA,cAAA,GAAA,SAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,0BAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,SAAA,0BAAA,GAAA,MAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,wBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,QAAA,SAAA,GAAA,SAAA,UAAA,GAAA,CAAA,QAAA,YAAA,GAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,UAAA,CAAA,GAAA,UAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;AC9B9B,QAAA,yBAAA,GAAA,cAAA,CAAA,EAAiC,GAAA,aAAA,EAClB,GAAA,eAAA,CAAA,EACe,GAAA,cAAA,CAAA;AACZ,QAAA,qBAAA,SAAA,SAAA,yDAAA;AAAA,iBAAS,IAAA,OAAA;QAAQ,CAAA;AAC3B,QAAA,oBAAA,GAAA,YAAA,CAAA;AACF,QAAA,uBAAA,EAAa;AAEf,QAAA,yBAAA,GAAA,WAAA;AAAW,QAAA,iBAAA,GAAA,eAAA;AAAa,QAAA,uBAAA;AACxB,QAAA,yBAAA,GAAA,eAAA,CAAA,EAAwB,GAAA,cAAA,CAAA;AACV,QAAA,qBAAA,SAAA,SAAA,yDAAA;AAAA,iBAAS,IAAA,cAAA;QAAe,CAAA;AAClC,QAAA,oBAAA,GAAA,YAAA,CAAA;AACF,QAAA,uBAAA,EAAa,EACD,EACF;AAGhB,QAAA,yBAAA,IAAA,eAAA,CAAA,EAAiC,IAAA,OAAA,CAAA,EAEE,IAAA,OAAA,CAAA,EACA,IAAA,UAAA,EAAA;AAI3B,QAAA,qBAAA,SAAA,SAAA,sDAAA;AAAA,iBAAS,IAAA,aAAa,KAAK;QAAC,CAAA;AAC5B,QAAA,iBAAA,IAAA,OAAA;AACF,QAAA,uBAAA;AACA,QAAA,yBAAA,IAAA,UAAA,EAAA;AAGE,QAAA,qBAAA,SAAA,SAAA,sDAAA;AAAA,iBAAS,IAAA,aAAa,QAAQ;QAAC,CAAA;AAC/B,QAAA,iBAAA,IAAA,UAAA;AACA,QAAA,qBAAA,IAAA,oCAAA,GAAA,GAAA,QAAA,EAAA;AACF,QAAA,uBAAA,EAAS;AAGX,QAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,EAAA;AAIF,QAAA,uBAAA;AAGA,QAAA,yBAAA,IAAA,OAAA,EAAA;AAEE,QAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,EAAA,EAAyE,IAAA,mCAAA,IAAA,IAAA,OAAA,EAAA;AAqC3E,QAAA,uBAAA;AAGA,QAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,EAAA;AAMF,QAAA,uBAAA;;;AA1FY,QAAA,qBAAA,eAAA,IAAA;AASgC,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,YAAA,IAAA,gBAAA,CAAA;AAO/B,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,cAAA,IAAA;AAML,QAAA,oBAAA,CAAA;AAAA,QAAA,sBAAA,UAAA,IAAA,cAAA,KAAA;AAMA,QAAA,oBAAA,CAAA;AAAA,QAAA,sBAAA,UAAA,IAAA,cAAA,QAAA;AAG4B,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,cAAA,CAAA;AAIH,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,sBAAA,SAAA,CAAA;AASE,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,sBAAA,WAAA,CAAA;AAQD,QAAA,oBAAA;AAAA,QAAA,qBAAA,WAAA,IAAA,qBAAA,EAA0B,gBAAA,IAAA,qBAAA;AAgCxB,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,oBAAA;;sBDxDxB,aAAW,WAAA,YAAA,YAAA,WAAA,SAAA,YAAA,UAAA,YAAE,cAAY,SAAA,SAAA,MAAE,WAAW,GAAA,QAAA,CAAA,osMAAA,EAAA,CAAA;EAAA;;;sEAErC,mBAAiB,CAAA;UAP7B;uBACW,qBAAmB,YAGjB,MAAI,SACP,CAAC,aAAa,cAAc,WAAW,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,kkKAAA,EAAA,CAAA;;;;6EAEtC,mBAAiB,EAAA,WAAA,qBAAA,UAAA,qDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
import {
  AuthService
} from "./chunk-XKXQPGS3.js";
import "./chunk-FKALCVFZ.js";
import {
  FcmService
} from "./chunk-RDFT5QPW.js";
import {
  environment
} from "./chunk-I7MI46CM.js";
import "./chunk-TAZAZ6IP.js";
import {
  AlertController,
  CommonModule,
  Component,
  FormsModule,
  HttpClient,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardSubtitle,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonSpinner,
  IonTitle,
  IonToolbar,
  IonicModule,
  NgControlStatus,
  NgForOf,
  NgIf,
  NgModel,
  Platform,
  TextValueAccessorDirective,
  ToastController,
  firstValueFrom,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import {
  __async
} from "./chunk-UL2P3LPA.js";

// src/app/pages/login-debug/login-debug.page.ts
function LoginDebugPage_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10);
    \u0275\u0275element(1, "ion-spinner");
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Running diagnostics...");
    \u0275\u0275elementEnd()();
  }
}
function LoginDebugPage_ion_card_14_ion_item_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-item", 12);
    \u0275\u0275listener("click", function LoginDebugPage_ion_card_14_ion_item_7_Template_ion_item_click_0_listener() {
      const diagnostic_r2 = \u0275\u0275restoreView(_r1).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.showDetails(diagnostic_r2));
    });
    \u0275\u0275element(1, "ion-icon", 13);
    \u0275\u0275elementStart(2, "ion-label")(3, "h3");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()();
    \u0275\u0275element(7, "ion-icon", 14);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const diagnostic_r2 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("name", ctx_r2.getStatusIcon(diagnostic_r2.status))("color", ctx_r2.getStatusColor(diagnostic_r2.status));
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(diagnostic_r2.test);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(diagnostic_r2.message);
  }
}
function LoginDebugPage_ion_card_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-card")(1, "ion-card-header")(2, "ion-card-title");
    \u0275\u0275text(3, "Diagnostic Results");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "ion-card-subtitle");
    \u0275\u0275text(5, "Mobile login troubleshooting");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "ion-card-content");
    \u0275\u0275template(7, LoginDebugPage_ion_card_14_ion_item_7_Template, 8, 4, "ion-item", 11);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(7);
    \u0275\u0275property("ngForOf", ctx_r2.diagnostics);
  }
}
function LoginDebugPage_ion_card_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "ion-card")(1, "ion-card-header")(2, "ion-card-title");
    \u0275\u0275text(3, "Test Credentials");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "ion-card-subtitle");
    \u0275\u0275text(5, "Credentials used for API testing");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "ion-card-content")(7, "ion-item")(8, "ion-label", 15);
    \u0275\u0275text(9, "Test Email");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "ion-input", 16);
    \u0275\u0275twoWayListener("ngModelChange", function LoginDebugPage_ion_card_15_Template_ion_input_ngModelChange_10_listener($event) {
      \u0275\u0275restoreView(_r4);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.testCredentials.email, $event) || (ctx_r2.testCredentials.email = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(11, "ion-item")(12, "ion-label", 15);
    \u0275\u0275text(13, "Test Password");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "ion-input", 17);
    \u0275\u0275twoWayListener("ngModelChange", function LoginDebugPage_ion_card_15_Template_ion_input_ngModelChange_14_listener($event) {
      \u0275\u0275restoreView(_r4);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.testCredentials.password, $event) || (ctx_r2.testCredentials.password = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "p", 18);
    \u0275\u0275element(16, "ion-icon", 19);
    \u0275\u0275text(17, " These credentials are used only for testing API connectivity. A 401 error is expected and indicates the API is working correctly. ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(10);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.testCredentials.email);
    \u0275\u0275advance(4);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.testCredentials.password);
  }
}
function LoginDebugPage_ion_card_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-card")(1, "ion-card-header")(2, "ion-card-title");
    \u0275\u0275text(3, "Environment Configuration");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "ion-card-subtitle");
    \u0275\u0275text(5, "Current app configuration");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "ion-card-content")(7, "ion-item")(8, "ion-label")(9, "h3");
    \u0275\u0275text(10, "API URL");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "p");
    \u0275\u0275text(12);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(13, "ion-item")(14, "ion-label")(15, "h3");
    \u0275\u0275text(16, "Production Mode");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "p");
    \u0275\u0275text(18);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(19, "ion-item")(20, "ion-label")(21, "h3");
    \u0275\u0275text(22, "Platform");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(23, "p");
    \u0275\u0275text(24);
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(12);
    \u0275\u0275textInterpolate(ctx_r2.getApiUrl());
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(ctx_r2.isProduction() ? "Yes" : "No");
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(ctx_r2.getPlatformInfo());
  }
}
function LoginDebugPage_ion_card_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ion-card")(1, "ion-card-header")(2, "ion-card-title");
    \u0275\u0275text(3, "Common Mobile Login Issues");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "ion-card-subtitle");
    \u0275\u0275text(5, "Troubleshooting guide");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "ion-card-content")(7, "div", 20)(8, "h4");
    \u0275\u0275text(9, "\u{1F310} Network Connectivity");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "p");
    \u0275\u0275text(11, "Ensure your mobile device and computer are on the same WiFi network.");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(12, "div", 20)(13, "h4");
    \u0275\u0275text(14, "\u{1F525} Firewall/Security");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "p");
    \u0275\u0275text(16, "Check if your computer's firewall is blocking port 8000.");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(17, "div", 20)(18, "h4");
    \u0275\u0275text(19, "\u{1F5A5}\uFE0F Backend Server");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "p");
    \u0275\u0275text(21, "Verify the Laravel backend is running on your computer.");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(22, "div", 20)(23, "h4");
    \u0275\u0275text(24, "\u{1F4F1} CORS Configuration");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "p");
    \u0275\u0275text(26, "Ensure CORS allows requests from mobile devices.");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(27, "div", 20)(28, "h4");
    \u0275\u0275text(29, "\u{1F527} IP Address");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(30, "p");
    \u0275\u0275text(31, "Verify the IP address in environment.ts matches your computer's IP.");
    \u0275\u0275elementEnd()()()();
  }
}
var LoginDebugPage = class _LoginDebugPage {
  constructor(http, platform, alertCtrl, toastCtrl, authService, fcmService) {
    this.http = http;
    this.platform = platform;
    this.alertCtrl = alertCtrl;
    this.toastCtrl = toastCtrl;
    this.authService = authService;
    this.fcmService = fcmService;
    this.diagnostics = [];
    this.isRunning = false;
    this.testCredentials = {
      email: "<EMAIL>",
      password: "password123"
    };
  }
  ngOnInit() {
    this.runDiagnostics();
  }
  runDiagnostics() {
    return __async(this, null, function* () {
      this.isRunning = true;
      this.diagnostics = [];
      yield this.testPlatform();
      yield this.testNetworkConnectivity();
      yield this.testBackendConnectivity();
      yield this.testApiEndpoints();
      yield this.testCorsConfiguration();
      yield this.testFcmToken();
      yield this.testLocalStorage();
      yield this.testLoginApi();
      this.isRunning = false;
      yield this.showDiagnosticSummary();
    });
  }
  testPlatform() {
    return __async(this, null, function* () {
      const result = {
        test: "Platform Detection",
        status: "pending",
        message: "Detecting platform..."
      };
      this.diagnostics.push(result);
      try {
        const platformInfo = {
          isMobile: this.platform.is("mobile"),
          isAndroid: this.platform.is("android"),
          isIOS: this.platform.is("ios"),
          isCordova: this.platform.is("cordova"),
          isCapacitor: this.platform.is("capacitor"),
          isBrowser: !this.platform.is("cordova") && !this.platform.is("capacitor"),
          userAgent: navigator.userAgent
        };
        result.status = "success";
        result.message = `Platform: ${this.platform.is("android") ? "Android" : this.platform.is("ios") ? "iOS" : "Browser"}`;
        result.details = platformInfo;
      } catch (error) {
        result.status = "error";
        result.message = `Platform detection failed: ${error}`;
      }
    });
  }
  testNetworkConnectivity() {
    return __async(this, null, function* () {
      const result = {
        test: "Network Connectivity",
        status: "pending",
        message: "Testing network connectivity..."
      };
      this.diagnostics.push(result);
      try {
        const isOnline = navigator.onLine;
        if (isOnline) {
          const response = yield firstValueFrom(this.http.get("https://httpbin.org/get"));
          result.status = "success";
          result.message = "Network connectivity: Online";
          result.details = { online: isOnline, externalTest: "success" };
        } else {
          result.status = "error";
          result.message = "Network connectivity: Offline";
          result.details = { online: isOnline };
        }
      } catch (error) {
        result.status = "warning";
        result.message = `Network test failed: ${error}`;
        result.details = { online: navigator.onLine, error };
      }
    });
  }
  testBackendConnectivity() {
    return __async(this, null, function* () {
      const result = {
        test: "Backend Connectivity",
        status: "pending",
        message: "Testing backend server connectivity..."
      };
      this.diagnostics.push(result);
      try {
        console.log("Testing backend at:", environment.apiUrl);
        const testUrl = environment.apiUrl.replace("/api", "/up");
        const response = yield firstValueFrom(this.http.get(testUrl));
        result.status = "success";
        result.message = `Backend server accessible at ${environment.apiUrl}`;
        result.details = { url: testUrl, response };
      } catch (error) {
        result.status = "error";
        result.message = `Backend server not accessible: ${error.status || "Network error"}`;
        result.details = {
          url: environment.apiUrl,
          error: error.message || error,
          status: error.status,
          statusText: error.statusText
        };
      }
    });
  }
  testApiEndpoints() {
    return __async(this, null, function* () {
      const result = {
        test: "API Endpoints",
        status: "pending",
        message: "Testing API endpoints..."
      };
      this.diagnostics.push(result);
      try {
        const centersResponse = yield firstValueFrom(this.http.get(`${environment.apiUrl}/evacuation-centers`));
        result.status = "success";
        result.message = "API endpoints accessible";
        result.details = {
          evacuationCenters: Array.isArray(centersResponse) ? centersResponse.length : "Invalid response"
        };
      } catch (error) {
        result.status = "error";
        result.message = `API endpoints not accessible: ${error.status || "Network error"}`;
        result.details = { error: error.message || error };
      }
    });
  }
  testCorsConfiguration() {
    return __async(this, null, function* () {
      const result = {
        test: "CORS Configuration",
        status: "pending",
        message: "Testing CORS configuration..."
      };
      this.diagnostics.push(result);
      try {
        const response = yield firstValueFrom(this.http.options(`${environment.apiUrl}/test`, {
          headers: { "Content-Type": "application/json" }
        }));
        result.status = "success";
        result.message = "CORS configuration working";
        result.details = { response };
      } catch (error) {
        if (error.status === 0) {
          result.status = "error";
          result.message = "CORS error: Request blocked by browser";
        } else {
          result.status = "warning";
          result.message = `CORS test inconclusive: ${error.status}`;
        }
        result.details = { error };
      }
    });
  }
  testFcmToken() {
    return __async(this, null, function* () {
      const result = {
        test: "FCM Token Generation",
        status: "pending",
        message: "Testing FCM token generation..."
      };
      this.diagnostics.push(result);
      try {
        const token = yield this.fcmService.getToken();
        if (token) {
          result.status = "success";
          result.message = "FCM token generated successfully";
          result.details = { tokenLength: token.length, tokenPreview: token.substring(0, 20) + "..." };
        } else {
          result.status = "warning";
          result.message = "FCM token generation returned empty";
          result.details = { token };
        }
      } catch (error) {
        result.status = "error";
        result.message = `FCM token generation failed: ${error}`;
        result.details = { error };
      }
    });
  }
  testLocalStorage() {
    return __async(this, null, function* () {
      const result = {
        test: "LocalStorage Functionality",
        status: "pending",
        message: "Testing localStorage functionality..."
      };
      this.diagnostics.push(result);
      try {
        const testKey = "diagnostic_test";
        const testValue = "test_value_" + Date.now();
        localStorage.setItem(testKey, testValue);
        const retrievedValue = localStorage.getItem(testKey);
        localStorage.removeItem(testKey);
        if (retrievedValue === testValue) {
          result.status = "success";
          result.message = "LocalStorage working correctly";
          result.details = { test: "passed" };
        } else {
          result.status = "error";
          result.message = "LocalStorage read/write failed";
          result.details = { expected: testValue, actual: retrievedValue };
        }
      } catch (error) {
        result.status = "error";
        result.message = `LocalStorage error: ${error}`;
        result.details = { error };
      }
    });
  }
  testLoginApi() {
    return __async(this, null, function* () {
      const result = {
        test: "Login API Call",
        status: "pending",
        message: "Testing login API call..."
      };
      this.diagnostics.push(result);
      try {
        const response = yield firstValueFrom(this.http.post(`${environment.apiUrl}/auth/login`, this.testCredentials));
        result.status = "success";
        result.message = "Login API call successful (unexpected!)";
        result.details = { response };
      } catch (error) {
        if (error.status === 401) {
          result.status = "success";
          result.message = "Login API reachable (401 expected for test credentials)";
          result.details = { status: error.status, message: "API working correctly" };
        } else if (error.status === 0) {
          result.status = "error";
          result.message = "Login API not reachable (network error)";
          result.details = { error: "Network connectivity issue" };
        } else {
          result.status = "warning";
          result.message = `Login API returned unexpected status: ${error.status}`;
          result.details = { status: error.status, error: error.message };
        }
      }
    });
  }
  showDiagnosticSummary() {
    return __async(this, null, function* () {
      const successCount = this.diagnostics.filter((d) => d.status === "success").length;
      const errorCount = this.diagnostics.filter((d) => d.status === "error").length;
      const warningCount = this.diagnostics.filter((d) => d.status === "warning").length;
      let message = `Diagnostics Complete:
\u2705 ${successCount} passed
\u26A0\uFE0F ${warningCount} warnings
\u274C ${errorCount} failed`;
      if (errorCount > 0) {
        message += "\n\nCheck the failed tests to identify login issues.";
      }
      const alert = yield this.alertCtrl.create({
        header: "Diagnostic Results",
        message,
        buttons: ["OK"]
      });
      yield alert.present();
    });
  }
  getStatusIcon(status) {
    switch (status) {
      case "success":
        return "checkmark-circle";
      case "error":
        return "close-circle";
      case "warning":
        return "warning";
      case "pending":
        return "time";
      default:
        return "help-circle";
    }
  }
  getStatusColor(status) {
    switch (status) {
      case "success":
        return "success";
      case "error":
        return "danger";
      case "warning":
        return "warning";
      case "pending":
        return "medium";
      default:
        return "medium";
    }
  }
  showDetails(diagnostic) {
    return __async(this, null, function* () {
      const alert = yield this.alertCtrl.create({
        header: diagnostic.test,
        message: `Status: ${diagnostic.message}

Details: ${JSON.stringify(diagnostic.details, null, 2)}`,
        buttons: ["OK"]
      });
      yield alert.present();
    });
  }
  getApiUrl() {
    return environment.apiUrl;
  }
  isProduction() {
    return environment.production;
  }
  getPlatformInfo() {
    if (this.platform.is("android"))
      return "Android";
    if (this.platform.is("ios"))
      return "iOS";
    if (this.platform.is("capacitor"))
      return "Capacitor";
    if (this.platform.is("cordova"))
      return "Cordova";
    return "Browser";
  }
  static {
    this.\u0275fac = function LoginDebugPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _LoginDebugPage)(\u0275\u0275directiveInject(HttpClient), \u0275\u0275directiveInject(Platform), \u0275\u0275directiveInject(AlertController), \u0275\u0275directiveInject(ToastController), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(FcmService));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _LoginDebugPage, selectors: [["app-login-debug"]], decls: 18, vars: 8, consts: [[3, "translucent"], ["slot", "end"], [3, "click", "disabled"], ["name", "refresh"], [3, "fullscreen"], ["collapse", "condense"], ["size", "large"], [1, "diagnostic-container"], ["class", "loading-container", 4, "ngIf"], [4, "ngIf"], [1, "loading-container"], ["button", "", 3, "click", 4, "ngFor", "ngForOf"], ["button", "", 3, "click"], ["slot", "start", 3, "name", "color"], ["name", "chevron-forward", "slot", "end"], ["position", "floating"], ["type", "email", 3, "ngModelChange", "ngModel"], ["type", "password", 3, "ngModelChange", "ngModel"], [1, "note"], ["name", "information-circle", "color", "primary"], [1, "issue-item"]], template: function LoginDebugPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header", 0)(1, "ion-toolbar")(2, "ion-title");
        \u0275\u0275text(3, "Login Diagnostics");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-buttons", 1)(5, "ion-button", 2);
        \u0275\u0275listener("click", function LoginDebugPage_Template_ion_button_click_5_listener() {
          return ctx.runDiagnostics();
        });
        \u0275\u0275element(6, "ion-icon", 3);
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(7, "ion-content", 4)(8, "ion-header", 5)(9, "ion-toolbar")(10, "ion-title", 6);
        \u0275\u0275text(11, "Login Debug");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(12, "div", 7);
        \u0275\u0275template(13, LoginDebugPage_div_13_Template, 4, 0, "div", 8)(14, LoginDebugPage_ion_card_14_Template, 8, 1, "ion-card", 9)(15, LoginDebugPage_ion_card_15_Template, 18, 2, "ion-card", 9)(16, LoginDebugPage_ion_card_16_Template, 25, 3, "ion-card", 9)(17, LoginDebugPage_ion_card_17_Template, 32, 0, "ion-card", 9);
        \u0275\u0275elementEnd()();
      }
      if (rf & 2) {
        \u0275\u0275property("translucent", true);
        \u0275\u0275advance(5);
        \u0275\u0275property("disabled", ctx.isRunning);
        \u0275\u0275advance(2);
        \u0275\u0275property("fullscreen", true);
        \u0275\u0275advance(6);
        \u0275\u0275property("ngIf", ctx.isRunning);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isRunning && ctx.diagnostics.length > 0);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isRunning);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isRunning);
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", !ctx.isRunning);
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonCard, IonCardContent, IonCardHeader, IonCardSubtitle, IonCardTitle, IonContent, IonHeader, IonIcon, IonInput, IonItem, IonLabel, IonSpinner, IonTitle, IonToolbar, TextValueAccessorDirective, CommonModule, NgForOf, NgIf, FormsModule, NgControlStatus, NgModel], styles: ["\n\n.debug-container[_ngcontent-%COMP%] {\n  max-width: 600px;\n  margin: 0 auto;\n  padding: 16px;\n}\n.diagnostic-item[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n  border-radius: 8px;\n  overflow: hidden;\n}\n.diagnostic-header[_ngcontent-%COMP%] {\n  padding: 12px 16px;\n  font-weight: 600;\n  color: white;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n.diagnostic-header.pending[_ngcontent-%COMP%] {\n  background-color: var(--ion-color-warning);\n}\n.diagnostic-header.success[_ngcontent-%COMP%] {\n  background-color: var(--ion-color-success);\n}\n.diagnostic-header.error[_ngcontent-%COMP%] {\n  background-color: var(--ion-color-danger);\n}\n.diagnostic-header.warning[_ngcontent-%COMP%] {\n  background-color: var(--ion-color-warning);\n}\n.diagnostic-content[_ngcontent-%COMP%] {\n  padding: 16px;\n  background-color: var(--ion-color-light);\n  border-left: 4px solid var(--ion-color-medium);\n}\n.diagnostic-content.pending[_ngcontent-%COMP%] {\n  border-left-color: var(--ion-color-warning);\n}\n.diagnostic-content.success[_ngcontent-%COMP%] {\n  border-left-color: var(--ion-color-success);\n}\n.diagnostic-content.error[_ngcontent-%COMP%] {\n  border-left-color: var(--ion-color-danger);\n}\n.diagnostic-content.warning[_ngcontent-%COMP%] {\n  border-left-color: var(--ion-color-warning);\n}\n.diagnostic-message[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n  margin-bottom: 8px;\n}\n.diagnostic-details[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: var(--ion-color-medium);\n  font-family: monospace;\n  background-color: var(--ion-color-step-50);\n  padding: 8px;\n  border-radius: 4px;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n.test-credentials[_ngcontent-%COMP%] {\n  background-color: var(--ion-color-step-100);\n  padding: 12px;\n  border-radius: 8px;\n  margin-bottom: 16px;\n}\n.test-credentials[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0 0 8px 0;\n  color: var(--ion-color-primary);\n}\n.test-credentials[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 4px 0;\n  font-size: 0.9rem;\n  color: var(--ion-color-medium);\n}\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n}\n.action-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  flex: 1;\n}\nion-card[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\nion-card-title[_ngcontent-%COMP%] {\n  color: var(--ion-color-primary);\n}\n.status-icon[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n}\n.loading-spinner[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n}\n.error-text[_ngcontent-%COMP%] {\n  color: var(--ion-color-danger);\n}\n.success-text[_ngcontent-%COMP%] {\n  color: var(--ion-color-success);\n}\n.warning-text[_ngcontent-%COMP%] {\n  color: var(--ion-color-warning);\n}\n/*# sourceMappingURL=login-debug.page.css.map */", "\n\n.diagnostic-container[_ngcontent-%COMP%] {\n  padding: 16px;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px;\n  text-align: center;\n}\nion-card[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\nion-item[_ngcontent-%COMP%] {\n  --padding-start: 0;\n  --inner-padding-end: 0;\n}\n.note[_ngcontent-%COMP%] {\n  margin-top: 16px;\n  padding: 12px;\n  background: #f0f8ff;\n  border-radius: 8px;\n  font-size: 14px;\n  color: #666;\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n}\n.issue-item[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #eee;\n}\n.issue-item[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n  margin-bottom: 0;\n  padding-bottom: 0;\n}\n.issue-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 600;\n}\n.issue-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 14px;\n  color: #666;\n  line-height: 1.4;\n}\n/*# sourceMappingURL=login-debug.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoginDebugPage, [{
    type: Component,
    args: [{ selector: "app-login-debug", standalone: true, imports: [IonicModule, CommonModule, FormsModule], template: `<ion-header [translucent]="true">\r
  <ion-toolbar>\r
    <ion-title>Login Diagnostics</ion-title>\r
    <ion-buttons slot="end">\r
      <ion-button (click)="runDiagnostics()" [disabled]="isRunning">\r
        <ion-icon name="refresh"></ion-icon>\r
      </ion-button>\r
    </ion-buttons>\r
  </ion-toolbar>\r
</ion-header>\r
\r
<ion-content [fullscreen]="true">\r
  <ion-header collapse="condense">\r
    <ion-toolbar>\r
      <ion-title size="large">Login Debug</ion-title>\r
    </ion-toolbar>\r
  </ion-header>\r
\r
  <div class="diagnostic-container">\r
    \r
    <!-- Running State -->\r
    <div *ngIf="isRunning" class="loading-container">\r
      <ion-spinner></ion-spinner>\r
      <p>Running diagnostics...</p>\r
    </div>\r
\r
    <!-- Diagnostic Results -->\r
    <ion-card *ngIf="!isRunning && diagnostics.length > 0">\r
      <ion-card-header>\r
        <ion-card-title>Diagnostic Results</ion-card-title>\r
        <ion-card-subtitle>Mobile login troubleshooting</ion-card-subtitle>\r
      </ion-card-header>\r
      <ion-card-content>\r
        \r
        <ion-item \r
          *ngFor="let diagnostic of diagnostics" \r
          (click)="showDetails(diagnostic)"\r
          button>\r
          <ion-icon \r
            [name]="getStatusIcon(diagnostic.status)" \r
            [color]="getStatusColor(diagnostic.status)"\r
            slot="start">\r
          </ion-icon>\r
          <ion-label>\r
            <h3>{{ diagnostic.test }}</h3>\r
            <p>{{ diagnostic.message }}</p>\r
          </ion-label>\r
          <ion-icon name="chevron-forward" slot="end"></ion-icon>\r
        </ion-item>\r
\r
      </ion-card-content>\r
    </ion-card>\r
\r
    <!-- Test Credentials -->\r
    <ion-card *ngIf="!isRunning">\r
      <ion-card-header>\r
        <ion-card-title>Test Credentials</ion-card-title>\r
        <ion-card-subtitle>Credentials used for API testing</ion-card-subtitle>\r
      </ion-card-header>\r
      <ion-card-content>\r
        \r
        <ion-item>\r
          <ion-label position="floating">Test Email</ion-label>\r
          <ion-input [(ngModel)]="testCredentials.email" type="email"></ion-input>\r
        </ion-item>\r
\r
        <ion-item>\r
          <ion-label position="floating">Test Password</ion-label>\r
          <ion-input [(ngModel)]="testCredentials.password" type="password"></ion-input>\r
        </ion-item>\r
\r
        <p class="note">\r
          <ion-icon name="information-circle" color="primary"></ion-icon>\r
          These credentials are used only for testing API connectivity. \r
          A 401 error is expected and indicates the API is working correctly.\r
        </p>\r
\r
      </ion-card-content>\r
    </ion-card>\r
\r
    <!-- Environment Info -->\r
    <ion-card *ngIf="!isRunning">\r
      <ion-card-header>\r
        <ion-card-title>Environment Configuration</ion-card-title>\r
        <ion-card-subtitle>Current app configuration</ion-card-subtitle>\r
      </ion-card-header>\r
      <ion-card-content>\r
        \r
        <ion-item>\r
          <ion-label>\r
            <h3>API URL</h3>\r
            <p>{{ getApiUrl() }}</p>\r
          </ion-label>\r
        </ion-item>\r
\r
        <ion-item>\r
          <ion-label>\r
            <h3>Production Mode</h3>\r
            <p>{{ isProduction() ? 'Yes' : 'No' }}</p>\r
          </ion-label>\r
        </ion-item>\r
\r
        <ion-item>\r
          <ion-label>\r
            <h3>Platform</h3>\r
            <p>{{ getPlatformInfo() }}</p>\r
          </ion-label>\r
        </ion-item>\r
\r
      </ion-card-content>\r
    </ion-card>\r
\r
    <!-- Common Issues -->\r
    <ion-card *ngIf="!isRunning">\r
      <ion-card-header>\r
        <ion-card-title>Common Mobile Login Issues</ion-card-title>\r
        <ion-card-subtitle>Troubleshooting guide</ion-card-subtitle>\r
      </ion-card-header>\r
      <ion-card-content>\r
        \r
        <div class="issue-item">\r
          <h4>\u{1F310} Network Connectivity</h4>\r
          <p>Ensure your mobile device and computer are on the same WiFi network.</p>\r
        </div>\r
\r
        <div class="issue-item">\r
          <h4>\u{1F525} Firewall/Security</h4>\r
          <p>Check if your computer's firewall is blocking port 8000.</p>\r
        </div>\r
\r
        <div class="issue-item">\r
          <h4>\u{1F5A5}\uFE0F Backend Server</h4>\r
          <p>Verify the Laravel backend is running on your computer.</p>\r
        </div>\r
\r
        <div class="issue-item">\r
          <h4>\u{1F4F1} CORS Configuration</h4>\r
          <p>Ensure CORS allows requests from mobile devices.</p>\r
        </div>\r
\r
        <div class="issue-item">\r
          <h4>\u{1F527} IP Address</h4>\r
          <p>Verify the IP address in environment.ts matches your computer's IP.</p>\r
        </div>\r
\r
      </ion-card-content>\r
    </ion-card>\r
\r
  </div>\r
</ion-content>\r
\r
<style>\r
.diagnostic-container {\r
  padding: 16px;\r
}\r
\r
.loading-container {\r
  display: flex;\r
  flex-direction: column;\r
  align-items: center;\r
  justify-content: center;\r
  padding: 40px;\r
  text-align: center;\r
}\r
\r
ion-card {\r
  margin-bottom: 16px;\r
}\r
\r
ion-item {\r
  --padding-start: 0;\r
  --inner-padding-end: 0;\r
}\r
\r
.note {\r
  margin-top: 16px;\r
  padding: 12px;\r
  background: #f0f8ff;\r
  border-radius: 8px;\r
  font-size: 14px;\r
  color: #666;\r
  display: flex;\r
  align-items: flex-start;\r
  gap: 8px;\r
}\r
\r
.issue-item {\r
  margin-bottom: 16px;\r
  padding-bottom: 16px;\r
  border-bottom: 1px solid #eee;\r
}\r
\r
.issue-item:last-child {\r
  border-bottom: none;\r
  margin-bottom: 0;\r
  padding-bottom: 0;\r
}\r
\r
.issue-item h4 {\r
  margin: 0 0 8px 0;\r
  font-size: 16px;\r
  font-weight: 600;\r
}\r
\r
.issue-item p {\r
  margin: 0;\r
  font-size: 14px;\r
  color: #666;\r
  line-height: 1.4;\r
}\r
</style>\r
`, styles: ["/* src/app/pages/login-debug/login-debug.page.scss */\n.debug-container {\n  max-width: 600px;\n  margin: 0 auto;\n  padding: 16px;\n}\n.diagnostic-item {\n  margin-bottom: 16px;\n  border-radius: 8px;\n  overflow: hidden;\n}\n.diagnostic-header {\n  padding: 12px 16px;\n  font-weight: 600;\n  color: white;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n.diagnostic-header.pending {\n  background-color: var(--ion-color-warning);\n}\n.diagnostic-header.success {\n  background-color: var(--ion-color-success);\n}\n.diagnostic-header.error {\n  background-color: var(--ion-color-danger);\n}\n.diagnostic-header.warning {\n  background-color: var(--ion-color-warning);\n}\n.diagnostic-content {\n  padding: 16px;\n  background-color: var(--ion-color-light);\n  border-left: 4px solid var(--ion-color-medium);\n}\n.diagnostic-content.pending {\n  border-left-color: var(--ion-color-warning);\n}\n.diagnostic-content.success {\n  border-left-color: var(--ion-color-success);\n}\n.diagnostic-content.error {\n  border-left-color: var(--ion-color-danger);\n}\n.diagnostic-content.warning {\n  border-left-color: var(--ion-color-warning);\n}\n.diagnostic-message {\n  font-size: 0.9rem;\n  margin-bottom: 8px;\n}\n.diagnostic-details {\n  font-size: 0.8rem;\n  color: var(--ion-color-medium);\n  font-family: monospace;\n  background-color: var(--ion-color-step-50);\n  padding: 8px;\n  border-radius: 4px;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n.test-credentials {\n  background-color: var(--ion-color-step-100);\n  padding: 12px;\n  border-radius: 8px;\n  margin-bottom: 16px;\n}\n.test-credentials h3 {\n  margin: 0 0 8px 0;\n  color: var(--ion-color-primary);\n}\n.test-credentials p {\n  margin: 4px 0;\n  font-size: 0.9rem;\n  color: var(--ion-color-medium);\n}\n.action-buttons {\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n}\n.action-buttons ion-button {\n  flex: 1;\n}\nion-card {\n  margin-bottom: 16px;\n}\nion-card-title {\n  color: var(--ion-color-primary);\n}\n.status-icon {\n  font-size: 1.2rem;\n}\n.loading-spinner {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 20px;\n}\n.error-text {\n  color: var(--ion-color-danger);\n}\n.success-text {\n  color: var(--ion-color-success);\n}\n.warning-text {\n  color: var(--ion-color-warning);\n}\n/*# sourceMappingURL=login-debug.page.css.map */\n", "/* angular:styles/component:css;0cef3647d88feffcfbeaf1f8f72a10f6d92eb30c850ecd5e878404a7d1893168;C:\\Users\\<USER>\\Lastna\\LastProject.1\\mobile_ionic\\src\\app\\pages\\login-debug\\login-debug.page.html */\n.diagnostic-container {\n  padding: 16px;\n}\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px;\n  text-align: center;\n}\nion-card {\n  margin-bottom: 16px;\n}\nion-item {\n  --padding-start: 0;\n  --inner-padding-end: 0;\n}\n.note {\n  margin-top: 16px;\n  padding: 12px;\n  background: #f0f8ff;\n  border-radius: 8px;\n  font-size: 14px;\n  color: #666;\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n}\n.issue-item {\n  margin-bottom: 16px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #eee;\n}\n.issue-item:last-child {\n  border-bottom: none;\n  margin-bottom: 0;\n  padding-bottom: 0;\n}\n.issue-item h4 {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 600;\n}\n.issue-item p {\n  margin: 0;\n  font-size: 14px;\n  color: #666;\n  line-height: 1.4;\n}\n/*# sourceMappingURL=login-debug.page.css.map */\n"] }]
  }], () => [{ type: HttpClient }, { type: Platform }, { type: AlertController }, { type: ToastController }, { type: AuthService }, { type: FcmService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(LoginDebugPage, { className: "LoginDebugPage", filePath: "src/app/pages/login-debug/login-debug.page.ts", lineNumber: 25 });
})();
export {
  LoginDebugPage
};
//# sourceMappingURL=login-debug.page-3NQWYJGH.js.map

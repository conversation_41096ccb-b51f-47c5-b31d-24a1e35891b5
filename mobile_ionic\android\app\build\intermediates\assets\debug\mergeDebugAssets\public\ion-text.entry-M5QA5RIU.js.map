{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-text.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host } from './index-527b9e34.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport './index-cfd9c1f2.js';\nconst textCss = \":host(.ion-color){color:var(--ion-color-base)}\";\nconst IonTextStyle0 = textCss;\nconst Text = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '0c2546ea3f24b0a6bfd606199441d0a4edfa4ca1',\n      class: createColorClasses(this.color, {\n        [mode]: true\n      })\n    }, h(\"slot\", {\n      key: 'b7623ccb06f9461090a1f33e9f85886c7a4d5eff'\n    }));\n  }\n};\nText.style = IonTextStyle0;\nexport { Text as ion_text };"], "mappings": ";;;;;;;;;;;;;;;AAOA,IAAM,UAAU;AAChB,IAAM,gBAAgB;AACtB,IAAM,OAAO,MAAM;AAAA,EACjB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS;AACP,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,OAAO,mBAAmB,KAAK,OAAO;AAAA,QACpC,CAAC,IAAI,GAAG;AAAA,MACV,CAAC;AAAA,IACH,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC;AAAA,EACJ;AACF;AACA,KAAK,QAAQ;", "names": [], "x_google_ignoreList": [0]}
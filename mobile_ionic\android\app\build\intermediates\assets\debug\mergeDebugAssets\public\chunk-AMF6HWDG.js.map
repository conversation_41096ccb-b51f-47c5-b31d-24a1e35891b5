{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/helpers-d94bc8ad.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as printIonError } from './index-cfd9c1f2.js';\nconst transitionEndAsync = (el, expectedDuration = 0) => {\n  return new Promise(resolve => {\n    transitionEnd(el, expectedDuration, resolve);\n  });\n};\n/**\n * Allows developer to wait for a transition\n * to finish and fallback to a timer if the\n * transition is cancelled or otherwise\n * never finishes. Also see transitionEndAsync\n * which is an await-able version of this.\n */\nconst transitionEnd = (el, expectedDuration = 0, callback) => {\n  let unRegTrans;\n  let animationTimeout;\n  const opts = {\n    passive: true\n  };\n  const ANIMATION_FALLBACK_TIMEOUT = 500;\n  const unregister = () => {\n    if (unRegTrans) {\n      unRegTrans();\n    }\n  };\n  const onTransitionEnd = ev => {\n    if (ev === undefined || el === ev.target) {\n      unregister();\n      callback(ev);\n    }\n  };\n  if (el) {\n    el.addEventListener('webkitTransitionEnd', onTransitionEnd, opts);\n    el.addEventListener('transitionend', onTransitionEnd, opts);\n    animationTimeout = setTimeout(onTransitionEnd, expectedDuration + ANIMATION_FALLBACK_TIMEOUT);\n    unRegTrans = () => {\n      if (animationTimeout !== undefined) {\n        clearTimeout(animationTimeout);\n        animationTimeout = undefined;\n      }\n      el.removeEventListener('webkitTransitionEnd', onTransitionEnd, opts);\n      el.removeEventListener('transitionend', onTransitionEnd, opts);\n    };\n  }\n  return unregister;\n};\n/**\n * Waits for a component to be ready for\n * both custom element and non-custom element builds.\n * If non-custom element build, el.componentOnReady\n * will be used.\n * For custom element builds, we wait a frame\n * so that the inner contents of the component\n * have a chance to render.\n *\n * Use this utility rather than calling\n * el.componentOnReady yourself.\n */\nconst componentOnReady = (el, callback) => {\n  if (el.componentOnReady) {\n    // eslint-disable-next-line custom-rules/no-component-on-ready-method\n    el.componentOnReady().then(resolvedEl => callback(resolvedEl));\n  } else {\n    raf(() => callback(el));\n  }\n};\n/**\n * This functions checks if a Stencil component is using\n * the lazy loaded build of Stencil. Returns `true` if\n * the component is lazy loaded. Returns `false` otherwise.\n */\nconst hasLazyBuild = stencilEl => {\n  return stencilEl.componentOnReady !== undefined;\n};\n/**\n * Elements inside of web components sometimes need to inherit global attributes\n * set on the host. For example, the inner input in `ion-input` should inherit\n * the `title` attribute that developers set directly on `ion-input`. This\n * helper function should be called in componentWillLoad and assigned to a variable\n * that is later used in the render function.\n *\n * This does not need to be reactive as changing attributes on the host element\n * does not trigger a re-render.\n */\nconst inheritAttributes = (el, attributes = []) => {\n  const attributeObject = {};\n  attributes.forEach(attr => {\n    if (el.hasAttribute(attr)) {\n      const value = el.getAttribute(attr);\n      if (value !== null) {\n        attributeObject[attr] = el.getAttribute(attr);\n      }\n      el.removeAttribute(attr);\n    }\n  });\n  return attributeObject;\n};\n/**\n * List of available ARIA attributes + `role`.\n * Removed deprecated attributes.\n * https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes\n */\nconst ariaAttributes = ['role', 'aria-activedescendant', 'aria-atomic', 'aria-autocomplete', 'aria-braillelabel', 'aria-brailleroledescription', 'aria-busy', 'aria-checked', 'aria-colcount', 'aria-colindex', 'aria-colindextext', 'aria-colspan', 'aria-controls', 'aria-current', 'aria-describedby', 'aria-description', 'aria-details', 'aria-disabled', 'aria-errormessage', 'aria-expanded', 'aria-flowto', 'aria-haspopup', 'aria-hidden', 'aria-invalid', 'aria-keyshortcuts', 'aria-label', 'aria-labelledby', 'aria-level', 'aria-live', 'aria-multiline', 'aria-multiselectable', 'aria-orientation', 'aria-owns', 'aria-placeholder', 'aria-posinset', 'aria-pressed', 'aria-readonly', 'aria-relevant', 'aria-required', 'aria-roledescription', 'aria-rowcount', 'aria-rowindex', 'aria-rowindextext', 'aria-rowspan', 'aria-selected', 'aria-setsize', 'aria-sort', 'aria-valuemax', 'aria-valuemin', 'aria-valuenow', 'aria-valuetext'];\n/**\n * Returns an array of aria attributes that should be copied from\n * the shadow host element to a target within the light DOM.\n * @param el The element that the attributes should be copied from.\n * @param ignoreList The list of aria-attributes to ignore reflecting and removing from the host.\n * Use this in instances where we manually specify aria attributes on the `<Host>` element.\n */\nconst inheritAriaAttributes = (el, ignoreList) => {\n  let attributesToInherit = ariaAttributes;\n  if (ignoreList && ignoreList.length > 0) {\n    attributesToInherit = attributesToInherit.filter(attr => !ignoreList.includes(attr));\n  }\n  return inheritAttributes(el, attributesToInherit);\n};\nconst addEventListener = (el, eventName, callback, opts) => {\n  return el.addEventListener(eventName, callback, opts);\n};\nconst removeEventListener = (el, eventName, callback, opts) => {\n  return el.removeEventListener(eventName, callback, opts);\n};\n/**\n * Gets the root context of a shadow dom element\n * On newer browsers this will be the shadowRoot,\n * but for older browser this may just be the\n * element itself.\n *\n * Useful for whenever you need to explicitly\n * do \"myElement.shadowRoot!.querySelector(...)\".\n */\nconst getElementRoot = (el, fallback = el) => {\n  return el.shadowRoot || fallback;\n};\n/**\n * Patched version of requestAnimationFrame that avoids ngzone\n * Use only when you know ngzone should not run\n */\nconst raf = h => {\n  if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n    return __zone_symbol__requestAnimationFrame(h);\n  }\n  if (typeof requestAnimationFrame === 'function') {\n    return requestAnimationFrame(h);\n  }\n  return setTimeout(h);\n};\nconst hasShadowDom = el => {\n  return !!el.shadowRoot && !!el.attachShadow;\n};\nconst focusVisibleElement = el => {\n  el.focus();\n  /**\n   * When programmatically focusing an element,\n   * the focus-visible utility will not run because\n   * it is expecting a keyboard event to have triggered this;\n   * however, there are times when we need to manually control\n   * this behavior so we call the `setFocus` method on ion-app\n   * which will let us explicitly set the elements to focus.\n   */\n  if (el.classList.contains('ion-focusable')) {\n    const app = el.closest('ion-app');\n    if (app) {\n      app.setFocus([el]);\n    }\n  }\n};\n/**\n * This method is used to add a hidden input to a host element that contains\n * a Shadow DOM. It does not add the input inside of the Shadow root which\n * allows it to be picked up inside of forms. It should contain the same\n * values as the host element.\n *\n * @param always Add a hidden input even if the container does not use Shadow\n * @param container The element where the input will be added\n * @param name The name of the input\n * @param value The value of the input\n * @param disabled If true, the input is disabled\n */\nconst renderHiddenInput = (always, container, name, value, disabled) => {\n  if (always || hasShadowDom(container)) {\n    let input = container.querySelector('input.aux-input');\n    if (!input) {\n      input = container.ownerDocument.createElement('input');\n      input.type = 'hidden';\n      input.classList.add('aux-input');\n      container.appendChild(input);\n    }\n    input.disabled = disabled;\n    input.name = name;\n    input.value = value || '';\n  }\n};\nconst clamp = (min, n, max) => {\n  return Math.max(min, Math.min(n, max));\n};\nconst assert = (actual, reason) => {\n  if (!actual) {\n    const message = 'ASSERT: ' + reason;\n    printIonError(message);\n    debugger; // eslint-disable-line\n    throw new Error(message);\n  }\n};\nconst pointerCoord = ev => {\n  // get X coordinates for either a mouse click\n  // or a touch depending on the given event\n  if (ev) {\n    const changedTouches = ev.changedTouches;\n    if (changedTouches && changedTouches.length > 0) {\n      const touch = changedTouches[0];\n      return {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n    }\n    if (ev.pageX !== undefined) {\n      return {\n        x: ev.pageX,\n        y: ev.pageY\n      };\n    }\n  }\n  return {\n    x: 0,\n    y: 0\n  };\n};\n/**\n * @hidden\n * Given a side, return if it should be on the end\n * based on the value of dir\n * @param side the side\n * @param isRTL whether the application dir is rtl\n */\nconst isEndSide = side => {\n  const isRTL = document.dir === 'rtl';\n  switch (side) {\n    case 'start':\n      return isRTL;\n    case 'end':\n      return !isRTL;\n    default:\n      throw new Error(`\"${side}\" is not a valid value for [side]. Use \"start\" or \"end\" instead.`);\n  }\n};\nconst debounceEvent = (event, wait) => {\n  const original = event._original || event;\n  return {\n    _original: event,\n    emit: debounce(original.emit.bind(original), wait)\n  };\n};\nconst debounce = (func, wait = 0) => {\n  let timer;\n  return (...args) => {\n    clearTimeout(timer);\n    timer = setTimeout(func, wait, ...args);\n  };\n};\n/**\n * Check whether the two string maps are shallow equal.\n *\n * undefined is treated as an empty map.\n *\n * @returns whether the keys are the same and the values are shallow equal.\n */\nconst shallowEqualStringMap = (map1, map2) => {\n  map1 !== null && map1 !== void 0 ? map1 : map1 = {};\n  map2 !== null && map2 !== void 0 ? map2 : map2 = {};\n  if (map1 === map2) {\n    return true;\n  }\n  const keys1 = Object.keys(map1);\n  if (keys1.length !== Object.keys(map2).length) {\n    return false;\n  }\n  for (const k1 of keys1) {\n    if (!(k1 in map2)) {\n      return false;\n    }\n    if (map1[k1] !== map2[k1]) {\n      return false;\n    }\n  }\n  return true;\n};\n/**\n * Checks input for usable number. Not NaN and not Infinite.\n */\nconst isSafeNumber = input => {\n  return typeof input === 'number' && !isNaN(input) && isFinite(input);\n};\nexport { addEventListener as a, removeEventListener as b, componentOnReady as c, renderHiddenInput as d, debounceEvent as e, focusVisibleElement as f, getElementRoot as g, inheritAttributes as h, inheritAriaAttributes as i, clamp as j, hasLazyBuild as k, isSafeNumber as l, hasShadowDom as m, assert as n, isEndSide as o, debounce as p, pointerCoord as q, raf as r, shallowEqualStringMap as s, transitionEndAsync as t };"], "mappings": ";;;;;AAIA,IAAM,qBAAqB,CAAC,IAAI,mBAAmB,MAAM;AACvD,SAAO,IAAI,QAAQ,aAAW;AAC5B,kBAAc,IAAI,kBAAkB,OAAO;AAAA,EAC7C,CAAC;AACH;AAQA,IAAM,gBAAgB,CAAC,IAAI,mBAAmB,GAAG,aAAa;AAC5D,MAAI;AACJ,MAAI;AACJ,QAAM,OAAO;AAAA,IACX,SAAS;AAAA,EACX;AACA,QAAM,6BAA6B;AACnC,QAAM,aAAa,MAAM;AACvB,QAAI,YAAY;AACd,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,kBAAkB,QAAM;AAC5B,QAAI,OAAO,UAAa,OAAO,GAAG,QAAQ;AACxC,iBAAW;AACX,eAAS,EAAE;AAAA,IACb;AAAA,EACF;AACA,MAAI,IAAI;AACN,OAAG,iBAAiB,uBAAuB,iBAAiB,IAAI;AAChE,OAAG,iBAAiB,iBAAiB,iBAAiB,IAAI;AAC1D,uBAAmB,WAAW,iBAAiB,mBAAmB,0BAA0B;AAC5F,iBAAa,MAAM;AACjB,UAAI,qBAAqB,QAAW;AAClC,qBAAa,gBAAgB;AAC7B,2BAAmB;AAAA,MACrB;AACA,SAAG,oBAAoB,uBAAuB,iBAAiB,IAAI;AACnE,SAAG,oBAAoB,iBAAiB,iBAAiB,IAAI;AAAA,IAC/D;AAAA,EACF;AACA,SAAO;AACT;AAaA,IAAM,mBAAmB,CAAC,IAAI,aAAa;AACzC,MAAI,GAAG,kBAAkB;AAEvB,OAAG,iBAAiB,EAAE,KAAK,gBAAc,SAAS,UAAU,CAAC;AAAA,EAC/D,OAAO;AACL,QAAI,MAAM,SAAS,EAAE,CAAC;AAAA,EACxB;AACF;AAMA,IAAM,eAAe,eAAa;AAChC,SAAO,UAAU,qBAAqB;AACxC;AAWA,IAAM,oBAAoB,CAAC,IAAI,aAAa,CAAC,MAAM;AACjD,QAAM,kBAAkB,CAAC;AACzB,aAAW,QAAQ,UAAQ;AACzB,QAAI,GAAG,aAAa,IAAI,GAAG;AACzB,YAAM,QAAQ,GAAG,aAAa,IAAI;AAClC,UAAI,UAAU,MAAM;AAClB,wBAAgB,IAAI,IAAI,GAAG,aAAa,IAAI;AAAA,MAC9C;AACA,SAAG,gBAAgB,IAAI;AAAA,IACzB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAMA,IAAM,iBAAiB,CAAC,QAAQ,yBAAyB,eAAe,qBAAqB,qBAAqB,+BAA+B,aAAa,gBAAgB,iBAAiB,iBAAiB,qBAAqB,gBAAgB,iBAAiB,gBAAgB,oBAAoB,oBAAoB,gBAAgB,iBAAiB,qBAAqB,iBAAiB,eAAe,iBAAiB,eAAe,gBAAgB,qBAAqB,cAAc,mBAAmB,cAAc,aAAa,kBAAkB,wBAAwB,oBAAoB,aAAa,oBAAoB,iBAAiB,gBAAgB,iBAAiB,iBAAiB,iBAAiB,wBAAwB,iBAAiB,iBAAiB,qBAAqB,gBAAgB,iBAAiB,gBAAgB,aAAa,iBAAiB,iBAAiB,iBAAiB,gBAAgB;AAQx5B,IAAM,wBAAwB,CAAC,IAAI,eAAe;AAChD,MAAI,sBAAsB;AAC1B,MAAI,cAAc,WAAW,SAAS,GAAG;AACvC,0BAAsB,oBAAoB,OAAO,UAAQ,CAAC,WAAW,SAAS,IAAI,CAAC;AAAA,EACrF;AACA,SAAO,kBAAkB,IAAI,mBAAmB;AAClD;AACA,IAAM,mBAAmB,CAAC,IAAI,WAAW,UAAU,SAAS;AAC1D,SAAO,GAAG,iBAAiB,WAAW,UAAU,IAAI;AACtD;AACA,IAAM,sBAAsB,CAAC,IAAI,WAAW,UAAU,SAAS;AAC7D,SAAO,GAAG,oBAAoB,WAAW,UAAU,IAAI;AACzD;AAUA,IAAM,iBAAiB,CAAC,IAAI,WAAW,OAAO;AAC5C,SAAO,GAAG,cAAc;AAC1B;AAKA,IAAM,MAAM,OAAK;AACf,MAAI,OAAO,yCAAyC,YAAY;AAC9D,WAAO,qCAAqC,CAAC;AAAA,EAC/C;AACA,MAAI,OAAO,0BAA0B,YAAY;AAC/C,WAAO,sBAAsB,CAAC;AAAA,EAChC;AACA,SAAO,WAAW,CAAC;AACrB;AACA,IAAM,eAAe,QAAM;AACzB,SAAO,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG;AACjC;AACA,IAAM,sBAAsB,QAAM;AAChC,KAAG,MAAM;AAST,MAAI,GAAG,UAAU,SAAS,eAAe,GAAG;AAC1C,UAAM,MAAM,GAAG,QAAQ,SAAS;AAChC,QAAI,KAAK;AACP,UAAI,SAAS,CAAC,EAAE,CAAC;AAAA,IACnB;AAAA,EACF;AACF;AAaA,IAAM,oBAAoB,CAAC,QAAQ,WAAW,MAAM,OAAO,aAAa;AACtE,MAAI,UAAU,aAAa,SAAS,GAAG;AACrC,QAAI,QAAQ,UAAU,cAAc,iBAAiB;AACrD,QAAI,CAAC,OAAO;AACV,cAAQ,UAAU,cAAc,cAAc,OAAO;AACrD,YAAM,OAAO;AACb,YAAM,UAAU,IAAI,WAAW;AAC/B,gBAAU,YAAY,KAAK;AAAA,IAC7B;AACA,UAAM,WAAW;AACjB,UAAM,OAAO;AACb,UAAM,QAAQ,SAAS;AAAA,EACzB;AACF;AACA,IAAM,QAAQ,CAAC,KAAK,GAAG,QAAQ;AAC7B,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,GAAG,CAAC;AACvC;AACA,IAAM,SAAS,CAAC,QAAQ,WAAW;AACjC,MAAI,CAAC,QAAQ;AACX,UAAM,UAAU,aAAa;AAC7B,kBAAc,OAAO;AACrB;AACA,UAAM,IAAI,MAAM,OAAO;AAAA,EACzB;AACF;AACA,IAAM,eAAe,QAAM;AAGzB,MAAI,IAAI;AACN,UAAM,iBAAiB,GAAG;AAC1B,QAAI,kBAAkB,eAAe,SAAS,GAAG;AAC/C,YAAM,QAAQ,eAAe,CAAC;AAC9B,aAAO;AAAA,QACL,GAAG,MAAM;AAAA,QACT,GAAG,MAAM;AAAA,MACX;AAAA,IACF;AACA,QAAI,GAAG,UAAU,QAAW;AAC1B,aAAO;AAAA,QACL,GAAG,GAAG;AAAA,QACN,GAAG,GAAG;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAQA,IAAM,YAAY,UAAQ;AACxB,QAAM,QAAQ,SAAS,QAAQ;AAC/B,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO,CAAC;AAAA,IACV;AACE,YAAM,IAAI,MAAM,IAAI,IAAI,kEAAkE;AAAA,EAC9F;AACF;AACA,IAAM,gBAAgB,CAAC,OAAO,SAAS;AACrC,QAAM,WAAW,MAAM,aAAa;AACpC,SAAO;AAAA,IACL,WAAW;AAAA,IACX,MAAM,SAAS,SAAS,KAAK,KAAK,QAAQ,GAAG,IAAI;AAAA,EACnD;AACF;AACA,IAAM,WAAW,CAAC,MAAM,OAAO,MAAM;AACnC,MAAI;AACJ,SAAO,IAAI,SAAS;AAClB,iBAAa,KAAK;AAClB,YAAQ,WAAW,MAAM,MAAM,GAAG,IAAI;AAAA,EACxC;AACF;AAQA,IAAM,wBAAwB,CAAC,MAAM,SAAS;AAC5C,WAAS,QAAQ,SAAS,SAAS,OAAO,OAAO,CAAC;AAClD,WAAS,QAAQ,SAAS,SAAS,OAAO,OAAO,CAAC;AAClD,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,WAAO;AAAA,EACT;AACA,aAAW,MAAM,OAAO;AACtB,QAAI,EAAE,MAAM,OAAO;AACjB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,EAAE,MAAM,KAAK,EAAE,GAAG;AACzB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAIA,IAAM,eAAe,WAAS;AAC5B,SAAO,OAAO,UAAU,YAAY,CAAC,MAAM,KAAK,KAAK,SAAS,KAAK;AACrE;", "names": [], "x_google_ignoreList": [0]}
{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/lock-controller-316928be.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Creates a lock controller.\n *\n * Claiming a lock means that nothing else can acquire the lock until it is released.\n * This can momentarily prevent execution of code that needs to wait for the earlier code to finish.\n * For example, this can be used to prevent multiple transitions from occurring at the same time.\n */\nconst createLockController = () => {\n  let waitPromise;\n  /**\n   * When lock() is called, the lock is claimed.\n   * Once a lock has been claimed, it cannot be claimed again until it is released.\n   * When this function gets resolved, the lock is released, allowing it to be claimed again.\n   *\n   * @example ```tsx\n   * const unlock = await this.lockController.lock();\n   * // do other stuff\n   * unlock();\n   * ```\n   */\n  const lock = async () => {\n    const p = waitPromise;\n    let resolve;\n    waitPromise = new Promise(r => resolve = r);\n    if (p !== undefined) {\n      await p;\n    }\n    return resolve;\n  };\n  return {\n    lock\n  };\n};\nexport { createLockController as c };"], "mappings": ";;;;;AAUA,IAAM,uBAAuB,MAAM;AACjC,MAAI;AAYJ,QAAM,OAAO,MAAY;AACvB,UAAM,IAAI;AACV,QAAI;AACJ,kBAAc,IAAI,QAAQ,OAAK,UAAU,CAAC;AAC1C,QAAI,MAAM,QAAW;AACnB,YAAM;AAAA,IACR;AACA,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,EACF;AACF;", "names": [], "x_google_ignoreList": [0]}
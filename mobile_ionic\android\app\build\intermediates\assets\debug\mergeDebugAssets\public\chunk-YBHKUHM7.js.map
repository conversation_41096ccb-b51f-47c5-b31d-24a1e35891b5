{"version": 3, "sources": ["src/app/services/offline-map.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport * as L from 'leaflet';\r\nimport { OfflineStorageService } from './offline-storage.service';\r\n\r\ninterface TileCoordinate {\r\n  z: number;\r\n  x: number;\r\n  y: number;\r\n}\r\n\r\ninterface CachedTile {\r\n  z: number;\r\n  x: number;\r\n  y: number;\r\n  tile_data: string; // Base64 encoded image data\r\n  created_at: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class OfflineMapService {\r\n  private readonly TILE_CACHE_SIZE = 1000; // Maximum number of tiles to cache\r\n  private readonly CACHE_EXPIRY_DAYS = 30; // Tiles expire after 30 days\r\n  private readonly PHILIPPINES_BOUNDS = {\r\n    north: 21.0,\r\n    south: 4.5,\r\n    east: 127.0,\r\n    west: 116.0\r\n  };\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private offlineStorage: OfflineStorageService\r\n  ) {}\r\n\r\n  /**\r\n   * Create an offline-capable tile layer for Leaflet\r\n   */\r\n  createOfflineTileLayer(): <PERSON><PERSON>TileLayer {\r\n    const offlineLayer = L.tileLayer('', {\r\n      attribution: '© OpenStreetMap contributors (Offline Mode)',\r\n      maxZoom: 18,\r\n      minZoom: 8\r\n    });\r\n\r\n    // Override the createTile method to use cached tiles\r\n    (offlineLayer as any).createTile = (coords: TileCoordinate, done: Function) => {\r\n      const tile = document.createElement('img');\r\n\r\n      this.getTileFromCache(coords.z, coords.x, coords.y)\r\n        .then(cachedTile => {\r\n          if (cachedTile) {\r\n            tile.src = `data:image/png;base64,${cachedTile.tile_data}`;\r\n            done(null, tile);\r\n          } else {\r\n            // Show placeholder for missing tiles\r\n            tile.src = this.createPlaceholderTile(coords);\r\n            done(null, tile);\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error('Error loading cached tile:', error);\r\n          tile.src = this.createPlaceholderTile(coords);\r\n          done(null, tile);\r\n        });\r\n\r\n      return tile;\r\n    };\r\n\r\n    return offlineLayer;\r\n  }\r\n\r\n  /**\r\n   * Pre-cache map tiles for Philippines region at multiple zoom levels\r\n   */\r\n  async preloadMapTiles(\r\n    centerLat: number,\r\n    centerLng: number,\r\n    radiusKm: number = 50,\r\n    onProgress?: (progress: number, total: number) => void\r\n  ): Promise<void> {\r\n    console.log('🗺️ Starting map tile preload...');\r\n\r\n    const zoomLevels = [10, 11, 12, 13, 14, 15]; // Focus on useful zoom levels\r\n    let totalTiles = 0;\r\n    let processedTiles = 0;\r\n\r\n    // Calculate total tiles to download\r\n    for (const zoom of zoomLevels) {\r\n      const bounds = this.calculateTileBounds(centerLat, centerLng, radiusKm, zoom);\r\n      totalTiles += (bounds.maxX - bounds.minX + 1) * (bounds.maxY - bounds.minY + 1);\r\n    }\r\n\r\n    console.log(`📊 Total tiles to download: ${totalTiles}`);\r\n\r\n    for (const zoom of zoomLevels) {\r\n      const bounds = this.calculateTileBounds(centerLat, centerLng, radiusKm, zoom);\r\n\r\n      for (let x = bounds.minX; x <= bounds.maxX; x++) {\r\n        for (let y = bounds.minY; y <= bounds.maxY; y++) {\r\n          try {\r\n            await this.downloadAndCacheTile(zoom, x, y);\r\n            processedTiles++;\r\n\r\n            if (onProgress) {\r\n              onProgress(processedTiles, totalTiles);\r\n            }\r\n\r\n            // Add small delay to prevent overwhelming the server\r\n            await this.delay(100);\r\n          } catch (error) {\r\n            console.warn(`Failed to cache tile ${zoom}/${x}/${y}:`, error);\r\n            processedTiles++;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log('✅ Map tile preload completed');\r\n  }\r\n\r\n  /**\r\n   * Download and cache a single tile\r\n   */\r\n  private async downloadAndCacheTile(z: number, x: number, y: number): Promise<void> {\r\n    // Check if tile already exists and is not expired\r\n    const existingTile = await this.getTileFromCache(z, x, y);\r\n    if (existingTile && !this.isTileExpired(existingTile.created_at)) {\r\n      return; // Tile is already cached and fresh\r\n    }\r\n\r\n    const tileUrl = `https://tile.openstreetmap.org/${z}/${x}/${y}.png`;\r\n\r\n    try {\r\n      const response = await this.http.get(tileUrl, { responseType: 'blob' }).toPromise();\r\n      if (response) {\r\n        const base64Data = await this.blobToBase64(response);\r\n        await this.saveTileToCache(z, x, y, base64Data);\r\n      }\r\n    } catch (error) {\r\n      throw new Error(`Failed to download tile: ${error}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get tile from cache\r\n   */\r\n  private async getTileFromCache(z: number, x: number, y: number): Promise<CachedTile | null> {\r\n    const cachedTile = await this.offlineStorage.getMapTile(z, x, y);\r\n    if (cachedTile) {\r\n      return {\r\n        z: cachedTile.z,\r\n        x: cachedTile.x,\r\n        y: cachedTile.y,\r\n        tile_data: cachedTile.tile_data,\r\n        created_at: cachedTile.created_at\r\n      };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Save tile to cache\r\n   */\r\n  private async saveTileToCache(z: number, x: number, y: number, tileData: string): Promise<void> {\r\n    await this.offlineStorage.saveMapTile(z, x, y, tileData);\r\n  }\r\n\r\n  /**\r\n   * Calculate tile bounds for a given center point and radius\r\n   */\r\n  private calculateTileBounds(lat: number, lng: number, radiusKm: number, zoom: number) {\r\n    const latRad = lat * Math.PI / 180;\r\n    const n = Math.pow(2, zoom);\r\n\r\n    // Convert radius to degrees (approximate)\r\n    const latDelta = radiusKm / 111; // 1 degree ≈ 111 km\r\n    const lngDelta = radiusKm / (111 * Math.cos(latRad));\r\n\r\n    const minLat = Math.max(lat - latDelta, this.PHILIPPINES_BOUNDS.south);\r\n    const maxLat = Math.min(lat + latDelta, this.PHILIPPINES_BOUNDS.north);\r\n    const minLng = Math.max(lng - lngDelta, this.PHILIPPINES_BOUNDS.west);\r\n    const maxLng = Math.min(lng + lngDelta, this.PHILIPPINES_BOUNDS.east);\r\n\r\n    return {\r\n      minX: Math.floor((minLng + 180) / 360 * n),\r\n      maxX: Math.floor((maxLng + 180) / 360 * n),\r\n      minY: Math.floor((1 - Math.log(Math.tan(maxLat * Math.PI / 180) + 1 / Math.cos(maxLat * Math.PI / 180)) / Math.PI) / 2 * n),\r\n      maxY: Math.floor((1 - Math.log(Math.tan(minLat * Math.PI / 180) + 1 / Math.cos(minLat * Math.PI / 180)) / Math.PI) / 2 * n)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a placeholder tile for missing tiles\r\n   */\r\n  private createPlaceholderTile(coords: TileCoordinate): string {\r\n    const canvas = document.createElement('canvas');\r\n    canvas.width = 256;\r\n    canvas.height = 256;\r\n    const ctx = canvas.getContext('2d');\r\n\r\n    if (ctx) {\r\n      // Gray background\r\n      ctx.fillStyle = '#f0f0f0';\r\n      ctx.fillRect(0, 0, 256, 256);\r\n\r\n      // Border\r\n      ctx.strokeStyle = '#ccc';\r\n      ctx.strokeRect(0, 0, 256, 256);\r\n\r\n      // Text\r\n      ctx.fillStyle = '#999';\r\n      ctx.font = '12px Arial';\r\n      ctx.textAlign = 'center';\r\n      ctx.fillText('Offline Mode', 128, 120);\r\n      ctx.fillText(`${coords.z}/${coords.x}/${coords.y}`, 128, 140);\r\n    }\r\n\r\n    return canvas.toDataURL();\r\n  }\r\n\r\n  /**\r\n   * Check if a tile is expired\r\n   */\r\n  private isTileExpired(createdAt: string): boolean {\r\n    const created = new Date(createdAt);\r\n    const now = new Date();\r\n    const diffDays = (now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24);\r\n    return diffDays > this.CACHE_EXPIRY_DAYS;\r\n  }\r\n\r\n  /**\r\n   * Convert blob to base64\r\n   */\r\n  private blobToBase64(blob: Blob): Promise<string> {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        const result = reader.result as string;\r\n        resolve(result.split(',')[1]); // Remove data:image/png;base64, prefix\r\n      };\r\n      reader.onerror = reject;\r\n      reader.readAsDataURL(blob);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Utility delay function\r\n   */\r\n  private delay(ms: number): Promise<void> {\r\n    return new Promise(resolve => setTimeout(resolve, ms));\r\n  }\r\n\r\n  /**\r\n   * Clean up old tiles to manage storage space\r\n   */\r\n  async cleanupOldTiles(): Promise<void> {\r\n    console.log('🧹 Cleaning up old map tiles...');\r\n    // The cleanup is now handled by the OfflineStorageService\r\n    // when saving new tiles (automatic cache size management)\r\n    console.log('✅ Tile cleanup completed');\r\n  }\r\n\r\n  /**\r\n   * Get cache statistics\r\n   */\r\n  async getCacheStats(): Promise<{ tileCount: number; sizeEstimate: string }> {\r\n    const storageInfo = this.offlineStorage.getStorageInfo();\r\n\r\n    // Estimate tile count (rough calculation)\r\n    const avgTileSize = 15000; // Average tile size in bytes\r\n    const tileCount = Math.floor(storageInfo.used / avgTileSize);\r\n\r\n    const sizeEstimate = this.formatBytes(storageInfo.used);\r\n\r\n    return { tileCount, sizeEstimate };\r\n  }\r\n\r\n  private formatBytes(bytes: number): string {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA,QAAmB;AAoBb,IAAO,oBAAP,MAAO,mBAAiB;EAU5B,YACU,MACA,gBAAqC;AADrC,SAAA,OAAA;AACA,SAAA,iBAAA;AAXO,SAAA,kBAAkB;AAClB,SAAA,oBAAoB;AACpB,SAAA,qBAAqB;MACpC,OAAO;MACP,OAAO;MACP,MAAM;MACN,MAAM;;EAML;;;;EAKH,yBAAsB;AACpB,UAAM,eAAiB,YAAU,IAAI;MACnC,aAAa;MACb,SAAS;MACT,SAAS;KACV;AAGA,iBAAqB,aAAa,CAAC,QAAwB,SAAkB;AAC5E,YAAM,OAAO,SAAS,cAAc,KAAK;AAEzC,WAAK,iBAAiB,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC,EAC/C,KAAK,gBAAa;AACjB,YAAI,YAAY;AACd,eAAK,MAAM,yBAAyB,WAAW,SAAS;AACxD,eAAK,MAAM,IAAI;QACjB,OAAO;AAEL,eAAK,MAAM,KAAK,sBAAsB,MAAM;AAC5C,eAAK,MAAM,IAAI;QACjB;MACF,CAAC,EACA,MAAM,WAAQ;AACb,gBAAQ,MAAM,8BAA8B,KAAK;AACjD,aAAK,MAAM,KAAK,sBAAsB,MAAM;AAC5C,aAAK,MAAM,IAAI;MACjB,CAAC;AAEH,aAAO;IACT;AAEA,WAAO;EACT;;;;EAKM,gBACJ,WACA,WACA,WAAmB,IACnB,YAAsD;;AAEtD,cAAQ,IAAI,8CAAkC;AAE9C,YAAM,aAAa,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAC1C,UAAI,aAAa;AACjB,UAAI,iBAAiB;AAGrB,iBAAW,QAAQ,YAAY;AAC7B,cAAM,SAAS,KAAK,oBAAoB,WAAW,WAAW,UAAU,IAAI;AAC5E,uBAAe,OAAO,OAAO,OAAO,OAAO,MAAM,OAAO,OAAO,OAAO,OAAO;MAC/E;AAEA,cAAQ,IAAI,sCAA+B,UAAU,EAAE;AAEvD,iBAAW,QAAQ,YAAY;AAC7B,cAAM,SAAS,KAAK,oBAAoB,WAAW,WAAW,UAAU,IAAI;AAE5E,iBAAS,IAAI,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK;AAC/C,mBAAS,IAAI,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK;AAC/C,gBAAI;AACF,oBAAM,KAAK,qBAAqB,MAAM,GAAG,CAAC;AAC1C;AAEA,kBAAI,YAAY;AACd,2BAAW,gBAAgB,UAAU;cACvC;AAGA,oBAAM,KAAK,MAAM,GAAG;YACtB,SAAS,OAAO;AACd,sBAAQ,KAAK,wBAAwB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK;AAC7D;YACF;UACF;QACF;MACF;AAEA,cAAQ,IAAI,mCAA8B;IAC5C;;;;;EAKc,qBAAqB,GAAW,GAAW,GAAS;;AAEhE,YAAM,eAAe,MAAM,KAAK,iBAAiB,GAAG,GAAG,CAAC;AACxD,UAAI,gBAAgB,CAAC,KAAK,cAAc,aAAa,UAAU,GAAG;AAChE;MACF;AAEA,YAAM,UAAU,kCAAkC,CAAC,IAAI,CAAC,IAAI,CAAC;AAE7D,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,KAAK,IAAI,SAAS,EAAE,cAAc,OAAM,CAAE,EAAE,UAAS;AACjF,YAAI,UAAU;AACZ,gBAAM,aAAa,MAAM,KAAK,aAAa,QAAQ;AACnD,gBAAM,KAAK,gBAAgB,GAAG,GAAG,GAAG,UAAU;QAChD;MACF,SAAS,OAAO;AACd,cAAM,IAAI,MAAM,4BAA4B,KAAK,EAAE;MACrD;IACF;;;;;EAKc,iBAAiB,GAAW,GAAW,GAAS;;AAC5D,YAAM,aAAa,MAAM,KAAK,eAAe,WAAW,GAAG,GAAG,CAAC;AAC/D,UAAI,YAAY;AACd,eAAO;UACL,GAAG,WAAW;UACd,GAAG,WAAW;UACd,GAAG,WAAW;UACd,WAAW,WAAW;UACtB,YAAY,WAAW;;MAE3B;AACA,aAAO;IACT;;;;;EAKc,gBAAgB,GAAW,GAAW,GAAW,UAAgB;;AAC7E,YAAM,KAAK,eAAe,YAAY,GAAG,GAAG,GAAG,QAAQ;IACzD;;;;;EAKQ,oBAAoB,KAAa,KAAa,UAAkB,MAAY;AAClF,UAAM,SAAS,MAAM,KAAK,KAAK;AAC/B,UAAM,IAAI,KAAK,IAAI,GAAG,IAAI;AAG1B,UAAM,WAAW,WAAW;AAC5B,UAAM,WAAW,YAAY,MAAM,KAAK,IAAI,MAAM;AAElD,UAAM,SAAS,KAAK,IAAI,MAAM,UAAU,KAAK,mBAAmB,KAAK;AACrE,UAAM,SAAS,KAAK,IAAI,MAAM,UAAU,KAAK,mBAAmB,KAAK;AACrE,UAAM,SAAS,KAAK,IAAI,MAAM,UAAU,KAAK,mBAAmB,IAAI;AACpE,UAAM,SAAS,KAAK,IAAI,MAAM,UAAU,KAAK,mBAAmB,IAAI;AAEpE,WAAO;MACL,MAAM,KAAK,OAAO,SAAS,OAAO,MAAM,CAAC;MACzC,MAAM,KAAK,OAAO,SAAS,OAAO,MAAM,CAAC;MACzC,MAAM,KAAK,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,SAAS,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,SAAS,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC;MAC1H,MAAM,KAAK,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,SAAS,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,SAAS,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC;;EAE9H;;;;EAKQ,sBAAsB,QAAsB;AAClD,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,QAAQ;AACf,WAAO,SAAS;AAChB,UAAM,MAAM,OAAO,WAAW,IAAI;AAElC,QAAI,KAAK;AAEP,UAAI,YAAY;AAChB,UAAI,SAAS,GAAG,GAAG,KAAK,GAAG;AAG3B,UAAI,cAAc;AAClB,UAAI,WAAW,GAAG,GAAG,KAAK,GAAG;AAG7B,UAAI,YAAY;AAChB,UAAI,OAAO;AACX,UAAI,YAAY;AAChB,UAAI,SAAS,gBAAgB,KAAK,GAAG;AACrC,UAAI,SAAS,GAAG,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,KAAK,GAAG;IAC9D;AAEA,WAAO,OAAO,UAAS;EACzB;;;;EAKQ,cAAc,WAAiB;AACrC,UAAM,UAAU,IAAI,KAAK,SAAS;AAClC,UAAM,MAAM,oBAAI,KAAI;AACpB,UAAM,YAAY,IAAI,QAAO,IAAK,QAAQ,QAAO,MAAO,MAAO,KAAK,KAAK;AACzE,WAAO,WAAW,KAAK;EACzB;;;;EAKQ,aAAa,MAAU;AAC7B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,YAAM,SAAS,IAAI,WAAU;AAC7B,aAAO,SAAS,MAAK;AACnB,cAAM,SAAS,OAAO;AACtB,gBAAQ,OAAO,MAAM,GAAG,EAAE,CAAC,CAAC;MAC9B;AACA,aAAO,UAAU;AACjB,aAAO,cAAc,IAAI;IAC3B,CAAC;EACH;;;;EAKQ,MAAM,IAAU;AACtB,WAAO,IAAI,QAAQ,aAAW,WAAW,SAAS,EAAE,CAAC;EACvD;;;;EAKM,kBAAe;;AACnB,cAAQ,IAAI,wCAAiC;AAG7C,cAAQ,IAAI,+BAA0B;IACxC;;;;;EAKM,gBAAa;;AACjB,YAAM,cAAc,KAAK,eAAe,eAAc;AAGtD,YAAM,cAAc;AACpB,YAAM,YAAY,KAAK,MAAM,YAAY,OAAO,WAAW;AAE3D,YAAM,eAAe,KAAK,YAAY,YAAY,IAAI;AAEtD,aAAO,EAAE,WAAW,aAAY;IAClC;;EAEQ,YAAY,OAAa;AAC/B,QAAI,UAAU;AAAG,aAAO;AACxB,UAAM,IAAI;AACV,UAAM,QAAQ,CAAC,SAAS,MAAM,MAAM,IAAI;AACxC,UAAM,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC;AAClD,WAAO,YAAY,QAAQ,KAAK,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC;EACxE;;;uCAxQW,oBAAiB,mBAAA,UAAA,GAAA,mBAAA,qBAAA,CAAA;IAAA;EAAA;;4EAAjB,oBAAiB,SAAjB,mBAAiB,WAAA,YAFhB,OAAM,CAAA;EAAA;;;sEAEP,mBAAiB,CAAA;UAH7B;WAAW;MACV,YAAY;KACb;;;", "names": []}
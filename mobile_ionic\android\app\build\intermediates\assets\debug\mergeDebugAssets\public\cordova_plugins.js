
  cordova.define('cordova/plugin_list', function(require, exports, module) {
    module.exports = [
      {
          "id": "cordova-plugin-device.device",
          "file": "plugins/cordova-plugin-device/www/device.js",
          "pluginId": "cordova-plugin-device",
        "clobbers": [
          "device"
        ]
        },
      {
          "id": "cordova-plugin-fcm-with-dependecy-updated.FCMPlugin",
          "file": "plugins/cordova-plugin-fcm-with-dependecy-updated/www/FCMPlugin.js",
          "pluginId": "cordova-plugin-fcm-with-dependecy-updated",
        "clobbers": [
          "FCM"
        ]
        }
    ];
    module.exports.metadata =
    // TOP OF METADATA
    {
      "cordova-plugin-device": "3.0.0",
      "cordova-plugin-fcm-with-dependecy-updated": "7.8.0"
    };
    // BOTTOM OF METADATA
    });
    
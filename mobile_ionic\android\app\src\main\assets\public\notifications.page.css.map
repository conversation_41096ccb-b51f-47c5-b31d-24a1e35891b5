{"version": 3, "sources": ["src/app/pages/notifications/notifications.page.scss"], "sourcesContent": ["ion-content {\r\n  --background: #f0f2f5;\r\n}\r\n\r\n.notification-header {\r\n  background: white;\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e4e6ea;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 10;\r\n}\r\n\r\n.notification-tabs {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.tab-button {\r\n  background: none;\r\n  border: none;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #65676b;\r\n  padding: 8px 12px;\r\n  border-radius: 20px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  position: relative;\r\n\r\n  &.active {\r\n    color: #1877f2;\r\n    background: #e7f3ff;\r\n  }\r\n\r\n  &:hover {\r\n    background: #f2f3f4;\r\n  }\r\n}\r\n\r\n.unread-badge {\r\n  background: #e41e3f;\r\n  color: white;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  padding: 2px 6px;\r\n  border-radius: 10px;\r\n  margin-left: 6px;\r\n  min-width: 18px;\r\n  text-align: center;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-title {\r\n  font-size: 17px;\r\n  font-weight: 600;\r\n  color: #050505;\r\n}\r\n\r\n.see-all-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #1877f2;\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  padding: 4px 8px;\r\n  border-radius: 6px;\r\n\r\n  &:hover {\r\n    background: #f2f3f4;\r\n  }\r\n}\r\n\r\n.notifications-container {\r\n  padding: 0;\r\n}\r\n\r\n.no-notifications {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: #65676b;\r\n\r\n  .no-notifications-icon {\r\n    font-size: 64px;\r\n    color: #bcc0c4;\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  h3 {\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    margin: 0 0 8px 0;\r\n    color: #050505;\r\n  }\r\n\r\n  p {\r\n    font-size: 15px;\r\n    margin: 0;\r\n    line-height: 1.4;\r\n  }\r\n}\r\n\r\n.notification-item {\r\n  background: white;\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #e4e6ea;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 12px;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s ease;\r\n  position: relative;\r\n\r\n  &:hover {\r\n    background: #f7f8fa;\r\n  }\r\n\r\n  &.unread {\r\n    background: #f0f8ff;\r\n    \r\n    &:hover {\r\n      background: #e7f3ff;\r\n    }\r\n  }\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.notification-icon {\r\n  position: relative;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.icon-image {\r\n  width: 56px;\r\n  height: 56px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #e4e6ea;\r\n}\r\n\r\n.icon-badge {\r\n  position: absolute;\r\n  bottom: -2px;\r\n  right: -2px;\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 2px solid white;\r\n  font-size: 12px;\r\n\r\n  &.badge-success {\r\n    background: #42b883;\r\n    color: white;\r\n  }\r\n\r\n  &.badge-danger {\r\n    background: #e41e3f;\r\n    color: white;\r\n  }\r\n\r\n  &.badge-info {\r\n    background: #1877f2;\r\n    color: white;\r\n  }\r\n\r\n  &.badge-primary {\r\n    background: #03b2dd;\r\n    color: white;\r\n  }\r\n\r\n  ion-icon {\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.notification-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.notification-text {\r\n  margin-bottom: 4px;\r\n  line-height: 1.3;\r\n}\r\n\r\n.notification-title {\r\n  color: #050505;\r\n  font-size: 15px;\r\n  font-weight: 400;\r\n  display: block;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.notification-description {\r\n  color: #65676b;\r\n  font-size: 13px;\r\n  display: block;\r\n  line-height: 1.4;\r\n}\r\n\r\n.notification-meta {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 13px;\r\n  color: #65676b;\r\n}\r\n\r\n.notification-time {\r\n  font-weight: 500;\r\n}\r\n\r\n.notification-reactions {\r\n  &::before {\r\n    content: \"•\";\r\n    margin-right: 8px;\r\n  }\r\n}\r\n\r\n.unread-indicator {\r\n  position: absolute;\r\n  top: 50%;\r\n  right: 16px;\r\n  transform: translateY(-50%);\r\n  width: 8px;\r\n  height: 8px;\r\n  background: #1877f2;\r\n  border-radius: 50%;\r\n}\r\n\r\n.load-more-container {\r\n  padding: 20px;\r\n  text-align: center;\r\n  background: white;\r\n  border-top: 1px solid #e4e6ea;\r\n\r\n  ion-button {\r\n    --color: #1877f2;\r\n    font-weight: 600;\r\n  }\r\n}\r\n\r\n// Dark mode support\r\n@media (prefers-color-scheme: dark) {\r\n  ion-content {\r\n    --background: #18191a;\r\n  }\r\n\r\n  .notification-header {\r\n    background: #242526;\r\n    border-bottom-color: #3a3b3c;\r\n  }\r\n\r\n  .tab-button {\r\n    color: #b0b3b8;\r\n\r\n    &.active {\r\n      color: #2d88ff;\r\n      background: #263951;\r\n    }\r\n\r\n    &:hover {\r\n      background: #3a3b3c;\r\n    }\r\n  }\r\n\r\n  .section-title {\r\n    color: #e4e6ea;\r\n  }\r\n\r\n  .see-all-btn {\r\n    color: #2d88ff;\r\n\r\n    &:hover {\r\n      background: #3a3b3c;\r\n    }\r\n  }\r\n\r\n  .notification-item {\r\n    background: #242526;\r\n    border-bottom-color: #3a3b3c;\r\n\r\n    &:hover {\r\n      background: #3a3b3c;\r\n    }\r\n\r\n    &.unread {\r\n      background: #263951;\r\n      \r\n      &:hover {\r\n        background: #2d4373;\r\n      }\r\n    }\r\n  }\r\n\r\n  .notification-title {\r\n    color: #e4e6ea;\r\n  }\r\n\r\n  .notification-description,\r\n  .notification-meta {\r\n    color: #b0b3b8;\r\n  }\r\n\r\n  .no-notifications {\r\n    color: #b0b3b8;\r\n\r\n    h3 {\r\n      color: #e4e6ea;\r\n    }\r\n  }\r\n\r\n  .load-more-container {\r\n    background: #242526;\r\n    border-top-color: #3a3b3c;\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA;AACE,gBAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,iBAAA,IAAA,MAAA;AACA,YAAA;AACA,OAAA;AACA,WAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CAAA;AACE,cAAA;AACA,UAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,UAAA;AACA,cAAA,IAAA,KAAA;AACA,YAAA;;AAEA,CAZF,UAYE,CAAA;AACE,SAAA;AACA,cAAA;;AAGF,CAjBF,UAiBE;AACE,cAAA;;AAIJ,CAAA;AACE,cAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,eAAA;AACA,aAAA;AACA,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAAA;AACE,cAAA;AACA,UAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;AACA,UAAA;AACA,WAAA,IAAA;AACA,iBAAA;;AAEA,CAVF,WAUE;AACE,cAAA;;AAIJ,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA,KAAA;AACA,SAAA;;AAEA,CALF,iBAKE,CAAA;AACE,aAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CAXF,iBAWE;AACE,aAAA;AACA,eAAA;AACA,UAAA,EAAA,EAAA,IAAA;AACA,SAAA;;AAGF,CAlBF,iBAkBE;AACE,aAAA;AACA,UAAA;AACA,eAAA;;AAIJ,CAAA;AACE,cAAA;AACA,WAAA,KAAA;AACA,iBAAA,IAAA,MAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA;AACA,cAAA,iBAAA,KAAA;AACA,YAAA;;AAEA,CAXF,iBAWE;AACE,cAAA;;AAGF,CAfF,iBAeE,CAAA;AACE,cAAA;;AAEA,CAlBJ,iBAkBI,CAHF,MAGE;AACE,cAAA;;AAIJ,CAvBF,iBAuBE;AACE,iBAAA;;AAIJ,CAAA;AACE,YAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA;AACA,cAAA;AACA,UAAA,IAAA,MAAA;;AAGF,CAAA;AACE,YAAA;AACA,UAAA;AACA,SAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA,IAAA,MAAA;AACA,aAAA;;AAEA,CAbF,UAaE,CAAA;AACE,cAAA;AACA,SAAA;;AAGF,CAlBF,UAkBE,CAAA;AACE,cAAA;AACA,SAAA;;AAGF,CAvBF,UAuBE,CAAA;AACE,cAAA;AACA,SAAA;;AAGF,CA5BF,UA4BE,CAAA;AACE,cAAA;AACA,SAAA;;AAGF,CAjCF,WAiCE;AACE,aAAA;;AAIJ,CAAA;AACE,QAAA;AACA,aAAA;;AAGF,CAAA;AACE,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,eAAA;AACA,WAAA;AACA,iBAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,WAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,aAAA;AACA,SAAA;;AAGF,CAAA;AACE,eAAA;;AAIA,CAAA,sBAAA;AACE,WAAA;AACA,gBAAA;;AAIJ,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,aAAA,WAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,cAAA;AACA,cAAA;AACA,cAAA,IAAA,MAAA;;AAEA,CANF,oBAME;AACE,WAAA;AACA,eAAA;;AAKJ,OAAA,CAAA,oBAAA,EAAA;AACE;AACE,kBAAA;;AAGF,GAjQF;AAkQI,gBAAA;AACA,yBAAA;;AAGF,GAvPF;AAwPI,WAAA;;AAEA,GA1PJ,UA0PI,CA9OF;AA+OI,WAAA;AACA,gBAAA;;AAGF,GA/PJ,UA+PI;AACE,gBAAA;;AAIJ,GA5NF;AA6NI,WAAA;;AAGF,GA1NF;AA2NI,WAAA;;AAEA,GA7NJ,WA6NI;AACE,gBAAA;;AAIJ,GAtLF;AAuLI,gBAAA;AACA,yBAAA;;AAEA,GA1LJ,iBA0LI;AACE,gBAAA;;AAGF,GA9LJ,iBA8LI,CA/KF;AAgLI,gBAAA;;AAEA,GAjMN,iBAiMM,CAlLJ,MAkLI;AACE,gBAAA;;AAKN,GA9GF;AA+GI,WAAA;;AAGF,GA1GF;EA0GE,CAnGF;AAqGI,WAAA;;AAGF,GAzOF;AA0OI,WAAA;;AAEA,GA5OJ,iBA4OI;AACE,WAAA;;AAIJ,GAlFF;AAmFI,gBAAA;AACA,sBAAA;;;", "names": []}
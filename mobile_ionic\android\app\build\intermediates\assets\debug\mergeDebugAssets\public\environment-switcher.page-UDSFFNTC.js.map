{"version": 3, "sources": ["src/app/pages/environment-switcher/environment-switcher.page.ts", "src/app/pages/environment-switcher/environment-switcher.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { IonicModule, AlertController, LoadingController } from '@ionic/angular';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { EnvironmentSwitcherService, ApiEndpoint } from '../../services/environment-switcher.service';\r\n\r\n@Component({\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule],\r\n  selector: 'app-environment-switcher',\r\n  templateUrl: './environment-switcher.page.html',\r\n  styleUrls: ['./environment-switcher.page.scss']\r\n})\r\nexport class EnvironmentSwitcherPage implements OnInit {\r\n  endpoints: (ApiEndpoint & { testResult?: any })[] = [];\r\n  currentApiUrl = '';\r\n  isLoading = false;\r\n\r\n  constructor(\r\n    private envSwitcher: EnvironmentSwitcherService,\r\n    private alertController: AlertController,\r\n    private loadingController: LoadingController,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadEndpoints();\r\n    this.currentApiUrl = this.envSwitcher.getCurrentApiUrl();\r\n  }\r\n\r\n  loadEndpoints() {\r\n    this.endpoints = this.envSwitcher.getApiEndpoints();\r\n  }\r\n\r\n  async selectEndpoint(endpoint: ApiEndpoint) {\r\n    const alert = await this.alertController.create({\r\n      header: 'Switch API Endpoint',\r\n      message: `Switch to ${endpoint.name}?\\n\\n${endpoint.description}`,\r\n      buttons: [\r\n        {\r\n          text: 'Cancel',\r\n          role: 'cancel'\r\n        },\r\n        {\r\n          text: 'Switch',\r\n          handler: () => {\r\n            this.envSwitcher.setApiUrl(endpoint.url);\r\n            this.currentApiUrl = endpoint.url;\r\n            this.loadEndpoints();\r\n            this.presentSuccessAlert('API endpoint switched successfully!');\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async testEndpoint(endpoint: ApiEndpoint) {\r\n    const loading = await this.loadingController.create({\r\n      message: `Testing ${endpoint.name}...`,\r\n      duration: 10000\r\n    });\r\n    await loading.present();\r\n\r\n    try {\r\n      const result = await this.envSwitcher.testEndpoint(endpoint.url);\r\n      \r\n      // Update the endpoint with test result\r\n      const index = this.endpoints.findIndex(e => e.url === endpoint.url);\r\n      if (index !== -1) {\r\n        this.endpoints[index].testResult = result;\r\n      }\r\n\r\n      await loading.dismiss();\r\n\r\n      // Show result\r\n      const alert = await this.alertController.create({\r\n        header: 'Connection Test',\r\n        message: `${endpoint.name}\\n\\n${result.message}`,\r\n        buttons: ['OK']\r\n      });\r\n      await alert.present();\r\n\r\n    } catch (error) {\r\n      await loading.dismiss();\r\n      this.presentErrorAlert('Test failed', 'Unable to test endpoint');\r\n    }\r\n  }\r\n\r\n  async testAllEndpoints() {\r\n    const loading = await this.loadingController.create({\r\n      message: 'Testing all endpoints...',\r\n      duration: 30000\r\n    });\r\n    await loading.present();\r\n\r\n    try {\r\n      const results = await this.envSwitcher.testAllEndpoints();\r\n      this.endpoints = results;\r\n      await loading.dismiss();\r\n\r\n      // Find working endpoints\r\n      const workingEndpoints = results.filter(r => r.testResult.success);\r\n      \r\n      if (workingEndpoints.length > 0) {\r\n        const message = `Found ${workingEndpoints.length} working endpoint(s):\\n\\n` +\r\n          workingEndpoints.map(e => `✅ ${e.name}`).join('\\n');\r\n        \r\n        this.presentSuccessAlert(message);\r\n      } else {\r\n        this.presentErrorAlert('No Working Endpoints', 'All endpoints failed connectivity test');\r\n      }\r\n\r\n    } catch (error) {\r\n      await loading.dismiss();\r\n      this.presentErrorAlert('Test Failed', 'Unable to test endpoints');\r\n    }\r\n  }\r\n\r\n  async autoDetect() {\r\n    const loading = await this.loadingController.create({\r\n      message: 'Auto-detecting best endpoint...',\r\n      duration: 30000\r\n    });\r\n    await loading.present();\r\n\r\n    try {\r\n      const bestEndpoint = await this.envSwitcher.autoDetectBestEndpoint();\r\n      await loading.dismiss();\r\n\r\n      if (bestEndpoint) {\r\n        this.currentApiUrl = bestEndpoint.url;\r\n        this.loadEndpoints();\r\n        this.presentSuccessAlert(`Auto-detected and switched to: ${bestEndpoint.name}`);\r\n      } else {\r\n        this.presentErrorAlert('Auto-Detection Failed', 'No working endpoints found');\r\n      }\r\n\r\n    } catch (error) {\r\n      await loading.dismiss();\r\n      this.presentErrorAlert('Auto-Detection Failed', 'Unable to detect working endpoint');\r\n    }\r\n  }\r\n\r\n  getStatusIcon(endpoint: ApiEndpoint & { testResult?: any }): string {\r\n    if (!endpoint.testResult) return 'help-circle-outline';\r\n    return endpoint.testResult.success ? 'checkmark-circle' : 'close-circle';\r\n  }\r\n\r\n  getStatusColor(endpoint: ApiEndpoint & { testResult?: any }): string {\r\n    if (!endpoint.testResult) return 'medium';\r\n    return endpoint.testResult.success ? 'success' : 'danger';\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  private async presentSuccessAlert(message: string) {\r\n    const alert = await this.alertController.create({\r\n      header: 'Success',\r\n      message,\r\n      buttons: ['OK']\r\n    });\r\n    await alert.present();\r\n  }\r\n\r\n  private async presentErrorAlert(header: string, message: string) {\r\n    const alert = await this.alertController.create({\r\n      header,\r\n      message,\r\n      buttons: ['OK']\r\n    });\r\n    await alert.present();\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar>\r\n    <ion-buttons slot=\"start\">\r\n      <ion-button (click)=\"goBack()\">\r\n        <ion-icon name=\"chevron-back-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n    <ion-title>API Endpoint Switcher</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\" class=\"ion-padding\">\r\n  <div class=\"switcher-container\">\r\n    \r\n    <!-- Current Status -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Current API Endpoint</ion-card-title>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <p><strong>URL:</strong> {{ currentApiUrl }}</p>\r\n        <ion-button expand=\"block\" fill=\"outline\" (click)=\"autoDetect()\">\r\n          <ion-icon name=\"search-outline\" slot=\"start\"></ion-icon>\r\n          Auto-Detect Best Endpoint\r\n        </ion-button>\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Quick Actions -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Quick Actions</ion-card-title>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <ion-button expand=\"block\" (click)=\"testAllEndpoints()\">\r\n          <ion-icon name=\"flash-outline\" slot=\"start\"></ion-icon>\r\n          Test All Endpoints\r\n        </ion-button>\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Available Endpoints -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Available Endpoints</ion-card-title>\r\n        <ion-card-subtitle>Select an endpoint to switch to</ion-card-subtitle>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        \r\n        <ion-list>\r\n          <ion-item \r\n            *ngFor=\"let endpoint of endpoints\" \r\n            [class.active-endpoint]=\"endpoint.isActive\"\r\n            button\r\n          >\r\n            <ion-icon \r\n              [name]=\"getStatusIcon(endpoint)\" \r\n              [color]=\"getStatusColor(endpoint)\"\r\n              slot=\"start\">\r\n            </ion-icon>\r\n            \r\n            <ion-label>\r\n              <h2>{{ endpoint.name }}</h2>\r\n              <p>{{ endpoint.description }}</p>\r\n              <p class=\"endpoint-url\">{{ endpoint.url }}</p>\r\n              <p *ngIf=\"endpoint.testResult\" \r\n                 [class.success-message]=\"endpoint.testResult.success\"\r\n                 [class.error-message]=\"!endpoint.testResult.success\">\r\n                {{ endpoint.testResult.message }}\r\n              </p>\r\n            </ion-label>\r\n\r\n            <ion-buttons slot=\"end\">\r\n              <ion-button \r\n                fill=\"clear\" \r\n                (click)=\"testEndpoint(endpoint); $event.stopPropagation()\">\r\n                <ion-icon name=\"refresh-outline\"></ion-icon>\r\n              </ion-button>\r\n              \r\n              <ion-button \r\n                *ngIf=\"!endpoint.isActive\"\r\n                fill=\"clear\" \r\n                (click)=\"selectEndpoint(endpoint); $event.stopPropagation()\">\r\n                <ion-icon name=\"checkmark-outline\"></ion-icon>\r\n              </ion-button>\r\n              \r\n              <ion-badge *ngIf=\"endpoint.isActive\" color=\"primary\">\r\n                Active\r\n              </ion-badge>\r\n            </ion-buttons>\r\n          </ion-item>\r\n        </ion-list>\r\n\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <!-- Help Section -->\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Troubleshooting Tips</ion-card-title>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <ion-list>\r\n          <ion-item>\r\n            <ion-icon name=\"wifi-outline\" slot=\"start\" color=\"primary\"></ion-icon>\r\n            <ion-label>\r\n              <h3>ngrok (Recommended)</h3>\r\n              <p>Most reliable for device testing. Works from anywhere.</p>\r\n            </ion-label>\r\n          </ion-item>\r\n          \r\n          <ion-item>\r\n            <ion-icon name=\"home-outline\" slot=\"start\" color=\"warning\"></ion-icon>\r\n            <ion-label>\r\n              <h3>Local IP</h3>\r\n              <p>Requires same WiFi network and firewall configuration.</p>\r\n            </ion-label>\r\n          </ion-item>\r\n          \r\n          <ion-item>\r\n            <ion-icon name=\"desktop-outline\" slot=\"start\" color=\"medium\"></ion-icon>\r\n            <ion-label>\r\n              <h3>Localhost</h3>\r\n              <p>Only works in web browser, not on mobile devices.</p>\r\n            </ion-label>\r\n          </ion-item>\r\n        </ion-list>\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiEc,IAAA,yBAAA,GAAA,GAAA;AAGE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAHG,IAAA,sBAAA,mBAAA,YAAA,WAAA,OAAA,EAAqD,iBAAA,CAAA,YAAA,WAAA,OAAA;AAEtD,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,YAAA,WAAA,SAAA,GAAA;;;;;;AAWF,IAAA,yBAAA,GAAA,cAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,uFAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,cAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAS,aAAA,eAAA,WAAA;AAAwB,aAAA,sBAAE,OAAA,gBAAA,CAAwB;IAAA,CAAA;AAC3D,IAAA,oBAAA,GAAA,YAAA,EAAA;AACF,IAAA,uBAAA;;;;;AAEA,IAAA,yBAAA,GAAA,aAAA,EAAA;AACE,IAAA,iBAAA,GAAA,UAAA;AACF,IAAA,uBAAA;;;;;;AAtCJ,IAAA,yBAAA,GAAA,YAAA,EAAA;AAKE,IAAA,oBAAA,GAAA,YAAA,EAAA;AAMA,IAAA,yBAAA,GAAA,WAAA,EAAW,GAAA,IAAA;AACL,IAAA,iBAAA,CAAA;AAAmB,IAAA,uBAAA;AACvB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA;AAC7B,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAwB,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA;AAC1C,IAAA,qBAAA,GAAA,kDAAA,GAAA,GAAA,KAAA,EAAA;AAKF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,eAAA,EAAA,EAAwB,IAAA,cAAA,EAAA;AAGpB,IAAA,qBAAA,SAAA,SAAA,0EAAA,QAAA;AAAA,YAAA,cAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAS,aAAA,aAAA,WAAA;AAAsB,aAAA,sBAAE,OAAA,gBAAA,CAAwB;IAAA,CAAA;AACzD,IAAA,oBAAA,IAAA,YAAA,EAAA;AACF,IAAA,uBAAA;AAEA,IAAA,qBAAA,IAAA,4DAAA,GAAA,GAAA,cAAA,EAAA,EAG+D,IAAA,2DAAA,GAAA,GAAA,aAAA,EAAA;AAOjE,IAAA,uBAAA,EAAc;;;;;AArCd,IAAA,sBAAA,mBAAA,YAAA,QAAA;AAIE,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,WAAA,CAAA,EAAgC,SAAA,OAAA,eAAA,WAAA,CAAA;AAM5B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,IAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,WAAA;AACqB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,GAAA;AACpB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,YAAA,UAAA;AAeD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,YAAA,QAAA;AAMS,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,YAAA,QAAA;;;ADzEpB,IAAO,0BAAP,MAAO,yBAAuB;EAKlC,YACU,aACA,iBACA,mBACA,QAAc;AAHd,SAAA,cAAA;AACA,SAAA,kBAAA;AACA,SAAA,oBAAA;AACA,SAAA,SAAA;AARV,SAAA,YAAoD,CAAA;AACpD,SAAA,gBAAgB;AAChB,SAAA,YAAY;EAOT;EAEH,WAAQ;AACN,SAAK,cAAa;AAClB,SAAK,gBAAgB,KAAK,YAAY,iBAAgB;EACxD;EAEA,gBAAa;AACX,SAAK,YAAY,KAAK,YAAY,gBAAe;EACnD;EAEM,eAAe,UAAqB;;AACxC,YAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;QAC9C,QAAQ;QACR,SAAS,aAAa,SAAS,IAAI;;EAAQ,SAAS,WAAW;QAC/D,SAAS;UACP;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,YAAY,UAAU,SAAS,GAAG;AACvC,mBAAK,gBAAgB,SAAS;AAC9B,mBAAK,cAAa;AAClB,mBAAK,oBAAoB,qCAAqC;YAChE;;;OAGL;AAED,YAAM,MAAM,QAAO;IACrB;;EAEM,aAAa,UAAqB;;AACtC,YAAM,UAAU,MAAM,KAAK,kBAAkB,OAAO;QAClD,SAAS,WAAW,SAAS,IAAI;QACjC,UAAU;OACX;AACD,YAAM,QAAQ,QAAO;AAErB,UAAI;AACF,cAAM,SAAS,MAAM,KAAK,YAAY,aAAa,SAAS,GAAG;AAG/D,cAAM,QAAQ,KAAK,UAAU,UAAU,OAAK,EAAE,QAAQ,SAAS,GAAG;AAClE,YAAI,UAAU,IAAI;AAChB,eAAK,UAAU,KAAK,EAAE,aAAa;QACrC;AAEA,cAAM,QAAQ,QAAO;AAGrB,cAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;UAC9C,QAAQ;UACR,SAAS,GAAG,SAAS,IAAI;;EAAO,OAAO,OAAO;UAC9C,SAAS,CAAC,IAAI;SACf;AACD,cAAM,MAAM,QAAO;MAErB,SAAS,OAAO;AACd,cAAM,QAAQ,QAAO;AACrB,aAAK,kBAAkB,eAAe,yBAAyB;MACjE;IACF;;EAEM,mBAAgB;;AACpB,YAAM,UAAU,MAAM,KAAK,kBAAkB,OAAO;QAClD,SAAS;QACT,UAAU;OACX;AACD,YAAM,QAAQ,QAAO;AAErB,UAAI;AACF,cAAM,UAAU,MAAM,KAAK,YAAY,iBAAgB;AACvD,aAAK,YAAY;AACjB,cAAM,QAAQ,QAAO;AAGrB,cAAM,mBAAmB,QAAQ,OAAO,OAAK,EAAE,WAAW,OAAO;AAEjE,YAAI,iBAAiB,SAAS,GAAG;AAC/B,gBAAM,UAAU,SAAS,iBAAiB,MAAM;;IAC9C,iBAAiB,IAAI,OAAK,UAAK,EAAE,IAAI,EAAE,EAAE,KAAK,IAAI;AAEpD,eAAK,oBAAoB,OAAO;QAClC,OAAO;AACL,eAAK,kBAAkB,wBAAwB,wCAAwC;QACzF;MAEF,SAAS,OAAO;AACd,cAAM,QAAQ,QAAO;AACrB,aAAK,kBAAkB,eAAe,0BAA0B;MAClE;IACF;;EAEM,aAAU;;AACd,YAAM,UAAU,MAAM,KAAK,kBAAkB,OAAO;QAClD,SAAS;QACT,UAAU;OACX;AACD,YAAM,QAAQ,QAAO;AAErB,UAAI;AACF,cAAM,eAAe,MAAM,KAAK,YAAY,uBAAsB;AAClE,cAAM,QAAQ,QAAO;AAErB,YAAI,cAAc;AAChB,eAAK,gBAAgB,aAAa;AAClC,eAAK,cAAa;AAClB,eAAK,oBAAoB,kCAAkC,aAAa,IAAI,EAAE;QAChF,OAAO;AACL,eAAK,kBAAkB,yBAAyB,4BAA4B;QAC9E;MAEF,SAAS,OAAO;AACd,cAAM,QAAQ,QAAO;AACrB,aAAK,kBAAkB,yBAAyB,mCAAmC;MACrF;IACF;;EAEA,cAAc,UAA4C;AACxD,QAAI,CAAC,SAAS;AAAY,aAAO;AACjC,WAAO,SAAS,WAAW,UAAU,qBAAqB;EAC5D;EAEA,eAAe,UAA4C;AACzD,QAAI,CAAC,SAAS;AAAY,aAAO;AACjC,WAAO,SAAS,WAAW,UAAU,YAAY;EACnD;EAEA,SAAM;AACJ,SAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;EACjC;EAEc,oBAAoB,SAAe;;AAC/C,YAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;QAC9C,QAAQ;QACR;QACA,SAAS,CAAC,IAAI;OACf;AACD,YAAM,MAAM,QAAO;IACrB;;EAEc,kBAAkB,QAAgB,SAAe;;AAC7D,YAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;QAC9C;QACA;QACA,SAAS,CAAC,IAAI;OACf;AACD,YAAM,MAAM,QAAO;IACrB;;;;uCAlKW,0BAAuB,4BAAA,0BAAA,GAAA,4BAAA,eAAA,GAAA,4BAAA,iBAAA,GAAA,4BAAA,MAAA,CAAA;IAAA;EAAA;;yEAAvB,0BAAuB,WAAA,CAAA,CAAA,0BAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,OAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,sBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,YAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,UAAA,SAAA,QAAA,WAAA,GAAA,OAAA,GAAA,CAAA,QAAA,kBAAA,QAAA,OAAA,GAAA,CAAA,UAAA,SAAA,GAAA,OAAA,GAAA,CAAA,QAAA,iBAAA,QAAA,OAAA,GAAA,CAAA,UAAA,IAAA,GAAA,mBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,QAAA,gBAAA,QAAA,SAAA,SAAA,SAAA,GAAA,CAAA,QAAA,gBAAA,QAAA,SAAA,SAAA,SAAA,GAAA,CAAA,QAAA,mBAAA,QAAA,SAAA,SAAA,QAAA,GAAA,CAAA,UAAA,EAAA,GAAA,CAAA,QAAA,SAAA,GAAA,QAAA,OAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,mBAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,QAAA,KAAA,GAAA,CAAA,QAAA,SAAA,GAAA,OAAA,GAAA,CAAA,QAAA,iBAAA,GAAA,CAAA,QAAA,SAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,QAAA,mBAAA,GAAA,CAAA,SAAA,SAAA,CAAA,GAAA,UAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACbpC,QAAA,yBAAA,GAAA,cAAA,CAAA,EAAiC,GAAA,aAAA,EAClB,GAAA,eAAA,CAAA,EACe,GAAA,cAAA,CAAA;AACZ,QAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,iBAAS,IAAA,OAAA;QAAQ,CAAA;AAC3B,QAAA,oBAAA,GAAA,YAAA,CAAA;AACF,QAAA,uBAAA,EAAa;AAEf,QAAA,yBAAA,GAAA,WAAA;AAAW,QAAA,iBAAA,GAAA,uBAAA;AAAqB,QAAA,uBAAA,EAAY,EAChC;AAGhB,QAAA,yBAAA,GAAA,eAAA,CAAA,EAAqD,GAAA,OAAA,CAAA,EACnB,GAAA,UAAA,EAGpB,IAAA,iBAAA,EACS,IAAA,gBAAA;AACC,QAAA,iBAAA,IAAA,sBAAA;AAAoB,QAAA,uBAAA,EAAiB;AAEvD,QAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,GAAA,EACb,IAAA,QAAA;AAAQ,QAAA,iBAAA,IAAA,MAAA;AAAI,QAAA,uBAAA;AAAU,QAAA,iBAAA,EAAA;AAAmB,QAAA,uBAAA;AAC5C,QAAA,yBAAA,IAAA,cAAA,CAAA;AAA0C,QAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,iBAAS,IAAA,WAAA;QAAY,CAAA;AAC7D,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,iBAAA,IAAA,6BAAA;AACF,QAAA,uBAAA,EAAa,EACI;AAIrB,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,iBAAA,EACS,IAAA,gBAAA;AACC,QAAA,iBAAA,IAAA,eAAA;AAAa,QAAA,uBAAA,EAAiB;AAEhD,QAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,cAAA,CAAA;AACW,QAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,iBAAS,IAAA,iBAAA;QAAkB,CAAA;AACpD,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,iBAAA,IAAA,sBAAA;AACF,QAAA,uBAAA,EAAa,EACI;AAIrB,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,iBAAA,EACS,IAAA,gBAAA;AACC,QAAA,iBAAA,IAAA,qBAAA;AAAmB,QAAA,uBAAA;AACnC,QAAA,yBAAA,IAAA,mBAAA;AAAmB,QAAA,iBAAA,IAAA,iCAAA;AAA+B,QAAA,uBAAA,EAAoB;AAExE,QAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA;AAGd,QAAA,qBAAA,IAAA,8CAAA,IAAA,IAAA,YAAA,EAAA;AAyCF,QAAA,uBAAA,EAAW,EAEM;AAIrB,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,iBAAA,EACS,IAAA,gBAAA;AACC,QAAA,iBAAA,IAAA,sBAAA;AAAoB,QAAA,uBAAA,EAAiB;AAEvD,QAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA,EACN,IAAA,UAAA;AAEN,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,QAAA,iBAAA,IAAA,qBAAA;AAAmB,QAAA,uBAAA;AACvB,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,wDAAA;AAAsD,QAAA,uBAAA,EAAI,EACnD;AAGd,QAAA,yBAAA,IAAA,UAAA;AACE,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,QAAA,iBAAA,IAAA,UAAA;AAAQ,QAAA,uBAAA;AACZ,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,wDAAA;AAAsD,QAAA,uBAAA,EAAI,EACnD;AAGd,QAAA,yBAAA,IAAA,UAAA;AACE,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,yBAAA,IAAA,WAAA,EAAW,IAAA,IAAA;AACL,QAAA,iBAAA,IAAA,WAAA;AAAS,QAAA,uBAAA;AACb,QAAA,yBAAA,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,mDAAA;AAAiD,QAAA,uBAAA,EAAI,EAC9C,EACH,EACF,EACM,EACV,EAEP;;;AAlII,QAAA,qBAAA,eAAA,IAAA;AAWC,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,cAAA,IAAA;AASoB,QAAA,oBAAA,EAAA;AAAA,QAAA,6BAAA,KAAA,IAAA,eAAA,EAAA;AA+BA,QAAA,oBAAA,EAAA;AAAA,QAAA,qBAAA,WAAA,IAAA,SAAA;;sBD3CrB,aAAW,UAAA,WAAA,YAAA,SAAA,gBAAA,eAAA,iBAAA,cAAA,YAAA,WAAA,SAAA,SAAA,UAAA,SAAA,UAAA,YAAE,cAAY,SAAA,IAAA,GAAA,QAAA,CAAA,gnCAAA,EAAA,CAAA;EAAA;;;sEAKxB,yBAAuB,CAAA;UAPnC;yBACa,MAAI,SACP,CAAC,aAAa,YAAY,GAAC,UAC1B,4BAA0B,UAAA,mmJAAA,QAAA,CAAA,i9BAAA,EAAA,CAAA;;;;6EAIzB,yBAAuB,EAAA,WAAA,2BAAA,UAAA,mEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
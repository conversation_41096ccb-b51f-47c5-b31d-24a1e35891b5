import {
  environment
} from "./chunk-I7MI46CM.js";
import {
  registerPlugin
} from "./chunk-TAZAZ6IP.js";
import {
  AlertController,
  HttpClient,
  Injectable,
  Observable,
  Platform,
  Router,
  Subject,
  ToastController,
  __decorate,
  __extends,
  fromEvent,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵgetInheritedFactory,
  ɵɵinject
} from "./chunk-NS3G4TP7.js";
import {
  __async,
  __spreadValues
} from "./chunk-UL2P3LPA.js";

// node_modules/@awesome-cordova-plugins/core/bootstrap.js
function checkReady() {
  if (typeof process === "undefined") {
    var win_1 = typeof window !== "undefined" ? window : {};
    var DEVICE_READY_TIMEOUT_1 = 5e3;
    var before_1 = Date.now();
    var didFireReady_1 = false;
    win_1.document.addEventListener("deviceready", function() {
      console.log("Ionic Native: deviceready event fired after " + (Date.now() - before_1) + " ms");
      didFireReady_1 = true;
    });
    setTimeout(function() {
      if (!didFireReady_1 && win_1.cordova) {
        console.warn("Ionic Native: deviceready did not fire within " + DEVICE_READY_TIMEOUT_1 + "ms. This can happen when plugins are in an inconsistent state. Try removing plugins from plugins/ and reinstalling them.");
      }
    }, DEVICE_READY_TIMEOUT_1);
  }
}

// node_modules/@awesome-cordova-plugins/core/decorators/common.js
var ERR_CORDOVA_NOT_AVAILABLE = {
  error: "cordova_not_available"
};
var ERR_PLUGIN_NOT_INSTALLED = {
  error: "plugin_not_installed"
};
function getPromise(callback) {
  var tryNativePromise = function() {
    if (Promise) {
      return new Promise(function(resolve, reject) {
        callback(resolve, reject);
      });
    } else {
      console.error("No Promise support or polyfill found. To enable Ionic Native support, please add the es6-promise polyfill before this script, or run with a library like Angular or on a recent browser.");
    }
  };
  if (typeof window !== "undefined" && window.angular) {
    var doc = window.document;
    var injector = window.angular.element(doc.querySelector("[ng-app]") || doc.body).injector();
    if (injector) {
      var $q = injector.get("$q");
      return $q(function(resolve, reject) {
        callback(resolve, reject);
      });
    }
    console.warn("Angular 1 was detected but $q couldn't be retrieved. This is usually when the app is not bootstrapped on the html or body tag. Falling back to native promises which won't trigger an automatic digest when promises resolve.");
  }
  return tryNativePromise();
}
function wrapPromise(pluginObj, methodName, args, opts) {
  if (opts === void 0) {
    opts = {};
  }
  var pluginResult, rej;
  var p = getPromise(function(resolve, reject) {
    if (opts.destruct) {
      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, function() {
        var args2 = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          args2[_i] = arguments[_i];
        }
        return resolve(args2);
      }, function() {
        var args2 = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          args2[_i] = arguments[_i];
        }
        return reject(args2);
      });
    } else {
      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, resolve, reject);
    }
    rej = reject;
  });
  if (pluginResult && pluginResult.error) {
    p.catch(function() {
    });
    typeof rej === "function" && rej(pluginResult.error);
  }
  return p;
}
function wrapOtherPromise(pluginObj, methodName, args, opts) {
  if (opts === void 0) {
    opts = {};
  }
  return getPromise(function(resolve, reject) {
    var pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts);
    if (pluginResult) {
      if (pluginResult.error) {
        reject(pluginResult.error);
      } else if (pluginResult.then) {
        pluginResult.then(resolve).catch(reject);
      }
    } else {
      reject({
        error: "unexpected_error"
      });
    }
  });
}
function wrapObservable(pluginObj, methodName, args, opts) {
  if (opts === void 0) {
    opts = {};
  }
  return new Observable(function(observer) {
    var pluginResult;
    if (opts.destruct) {
      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, function() {
        var args2 = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          args2[_i] = arguments[_i];
        }
        return observer.next(args2);
      }, function() {
        var args2 = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          args2[_i] = arguments[_i];
        }
        return observer.error(args2);
      });
    } else {
      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, observer.next.bind(observer), observer.error.bind(observer));
    }
    if (pluginResult && pluginResult.error) {
      observer.error(pluginResult.error);
      observer.complete();
    }
    return function() {
      try {
        if (opts.clearFunction) {
          if (opts.clearWithArgs) {
            return callCordovaPlugin(pluginObj, opts.clearFunction, args, opts, observer.next.bind(observer), observer.error.bind(observer));
          }
          return callCordovaPlugin(pluginObj, opts.clearFunction, []);
        }
      } catch (e) {
        console.warn("Unable to clear the previous observable watch for", pluginObj.constructor.getPluginName(), methodName);
        console.warn(e);
      }
    };
  });
}
function wrapEventObservable(event, element) {
  element = typeof window !== "undefined" && element ? get(window, element) : element || (typeof window !== "undefined" ? window : {});
  return fromEvent(element, event);
}
function checkAvailability(plugin, methodName, pluginName) {
  var pluginRef, pluginPackage;
  if (typeof plugin === "string") {
    pluginRef = plugin;
  } else {
    pluginRef = plugin.constructor.getPluginRef();
    pluginName = plugin.constructor.getPluginName();
    pluginPackage = plugin.constructor.getPluginInstallName();
  }
  var pluginInstance = getPlugin(pluginRef);
  if (!pluginInstance || !!methodName && typeof pluginInstance[methodName] === "undefined") {
    if (typeof window === "undefined" || !window.cordova) {
      cordovaWarn(pluginName, methodName);
      return ERR_CORDOVA_NOT_AVAILABLE;
    }
    pluginWarn(pluginName, pluginPackage, methodName);
    return ERR_PLUGIN_NOT_INSTALLED;
  }
  return true;
}
function setIndex(args, opts, resolve, reject) {
  if (opts === void 0) {
    opts = {};
  }
  if (opts.sync) {
    return args;
  }
  if (opts.callbackOrder === "reverse") {
    args.unshift(reject);
    args.unshift(resolve);
  } else if (opts.callbackStyle === "node") {
    args.push(function(err, result) {
      if (err) {
        reject(err);
      } else {
        resolve(result);
      }
    });
  } else if (opts.callbackStyle === "object" && opts.successName && opts.errorName) {
    var obj = {};
    obj[opts.successName] = resolve;
    obj[opts.errorName] = reject;
    args.push(obj);
  } else if (typeof opts.successIndex !== "undefined" || typeof opts.errorIndex !== "undefined") {
    var setSuccessIndex = function() {
      if (opts.successIndex > args.length) {
        args[opts.successIndex] = resolve;
      } else {
        args.splice(opts.successIndex, 0, resolve);
      }
    };
    var setErrorIndex = function() {
      if (opts.errorIndex > args.length) {
        args[opts.errorIndex] = reject;
      } else {
        args.splice(opts.errorIndex, 0, reject);
      }
    };
    if (opts.successIndex > opts.errorIndex) {
      setErrorIndex();
      setSuccessIndex();
    } else {
      setSuccessIndex();
      setErrorIndex();
    }
  } else {
    args.push(resolve);
    args.push(reject);
  }
  return args;
}
function callCordovaPlugin(pluginObj, methodName, args, opts, resolve, reject) {
  if (opts === void 0) {
    opts = {};
  }
  args = setIndex(args, opts, resolve, reject);
  var availabilityCheck = checkAvailability(pluginObj, methodName);
  if (availabilityCheck === true) {
    var pluginInstance = getPlugin(pluginObj.constructor.getPluginRef());
    return pluginInstance[methodName].apply(pluginInstance, args);
  } else {
    return availabilityCheck;
  }
}
function getPlugin(pluginRef) {
  if (typeof window !== "undefined") {
    return get(window, pluginRef);
  }
  return null;
}
function get(element, path) {
  var paths = path.split(".");
  var obj = element;
  for (var i = 0; i < paths.length; i++) {
    if (!obj) {
      return null;
    }
    obj = obj[paths[i]];
  }
  return obj;
}
function pluginWarn(pluginName, plugin, method) {
  if (method) {
    console.warn("Native: tried calling " + pluginName + "." + method + ", but the " + pluginName + " plugin is not installed.");
  } else {
    console.warn("Native: tried accessing the " + pluginName + " plugin but it's not installed.");
  }
  if (plugin) {
    console.warn("Install the " + pluginName + " plugin: 'ionic cordova plugin add " + plugin + "'");
  }
}
function cordovaWarn(pluginName, method) {
  if (typeof process === "undefined") {
    if (method) {
      console.warn("Native: tried calling " + pluginName + "." + method + ", but Cordova is not available. Make sure to include cordova.js or run in a device/simulator");
    } else {
      console.warn("Native: tried accessing the " + pluginName + " plugin but Cordova is not available. Make sure to include cordova.js or run in a device/simulator");
    }
  }
}
var wrap = function(pluginObj, methodName, opts) {
  if (opts === void 0) {
    opts = {};
  }
  return function() {
    var args = [];
    for (var _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i];
    }
    if (opts.sync) {
      return callCordovaPlugin(pluginObj, methodName, args, opts);
    } else if (opts.observable) {
      return wrapObservable(pluginObj, methodName, args, opts);
    } else if (opts.eventObservable && opts.event) {
      return wrapEventObservable(opts.event, opts.element);
    } else if (opts.otherPromise) {
      return wrapOtherPromise(pluginObj, methodName, args, opts);
    } else {
      return wrapPromise(pluginObj, methodName, args, opts);
    }
  };
};

// node_modules/@awesome-cordova-plugins/core/util.js
function get2(element, path) {
  var paths = path.split(".");
  var obj = element;
  for (var i = 0; i < paths.length; i++) {
    if (!obj) {
      return null;
    }
    obj = obj[paths[i]];
  }
  return obj;
}

// node_modules/@awesome-cordova-plugins/core/awesome-cordova-plugin.js
var AwesomeCordovaNativePlugin = (
  /** @class */
  function() {
    function AwesomeCordovaNativePlugin2() {
    }
    AwesomeCordovaNativePlugin2.installed = function() {
      var isAvailable = checkAvailability(this.pluginRef) === true;
      return isAvailable;
    };
    AwesomeCordovaNativePlugin2.getPlugin = function() {
      if (typeof window !== "undefined") {
        return get2(window, this.pluginRef);
      }
      return null;
    };
    AwesomeCordovaNativePlugin2.getPluginName = function() {
      var pluginName = this.pluginName;
      return pluginName;
    };
    AwesomeCordovaNativePlugin2.getPluginRef = function() {
      var pluginRef = this.pluginRef;
      return pluginRef;
    };
    AwesomeCordovaNativePlugin2.getPluginInstallName = function() {
      var plugin = this.plugin;
      return plugin;
    };
    AwesomeCordovaNativePlugin2.getSupportedPlatforms = function() {
      var platform = this.platforms;
      return platform;
    };
    AwesomeCordovaNativePlugin2.pluginName = "";
    AwesomeCordovaNativePlugin2.pluginRef = "";
    AwesomeCordovaNativePlugin2.plugin = "";
    AwesomeCordovaNativePlugin2.repo = "";
    AwesomeCordovaNativePlugin2.platforms = [];
    AwesomeCordovaNativePlugin2.install = "";
    return AwesomeCordovaNativePlugin2;
  }()
);

// node_modules/@awesome-cordova-plugins/core/decorators/cordova.js
function cordova(pluginObj, methodName, config, args) {
  return wrap(pluginObj, methodName, config).apply(this, args);
}

// node_modules/@awesome-cordova-plugins/core/index.js
checkReady();

// node_modules/@awesome-cordova-plugins/fcm/ngx/index.js
var FCM = (
  /** @class */
  function(_super) {
    __extends(FCM2, _super);
    function FCM2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    FCM2.prototype.getAPNSToken = function() {
      return cordova(this, "getAPNSToken", {}, arguments);
    };
    FCM2.prototype.getToken = function() {
      return cordova(this, "getToken", {}, arguments);
    };
    FCM2.prototype.onTokenRefresh = function() {
      return cordova(this, "onTokenRefresh", {
        "observable": true
      }, arguments);
    };
    FCM2.prototype.subscribeToTopic = function(topic) {
      return cordova(this, "subscribeToTopic", {}, arguments);
    };
    FCM2.prototype.unsubscribeFromTopic = function(topic) {
      return cordova(this, "unsubscribeFromTopic", {}, arguments);
    };
    FCM2.prototype.hasPermission = function() {
      return cordova(this, "hasPermission", {}, arguments);
    };
    FCM2.prototype.onNotification = function() {
      return cordova(this, "onNotification", {
        "observable": true,
        "successIndex": 0,
        "errorIndex": 2
      }, arguments);
    };
    FCM2.prototype.clearAllNotifications = function() {
      return cordova(this, "clearAllNotifications", {}, arguments);
    };
    FCM2.prototype.requestPushPermissionIOS = function(options) {
      return cordova(this, "requestPushPermissionIOS", {}, arguments);
    };
    FCM2.prototype.createNotificationChannelAndroid = function(channelConfig) {
      return cordova(this, "createNotificationChannelAndroid", {}, arguments);
    };
    FCM2.\u0275fac = /* @__PURE__ */ (() => {
      let \u0275FCM_BaseFactory;
      return function FCM_Factory(__ngFactoryType__) {
        return (\u0275FCM_BaseFactory || (\u0275FCM_BaseFactory = \u0275\u0275getInheritedFactory(FCM2)))(__ngFactoryType__ || FCM2);
      };
    })();
    FCM2.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({
      token: FCM2,
      factory: FCM2.\u0275fac
    });
    FCM2.pluginName = "FCM";
    FCM2.plugin = "cordova-plugin-fcm-with-dependecy-updated";
    FCM2.pluginRef = "FCMPlugin";
    FCM2.repo = "https://github.com/andrehtissot/cordova-plugin-fcm-with-dependecy-updated";
    FCM2.platforms = ["Android", "iOS"];
    FCM2 = __decorate([], FCM2);
    return FCM2;
  }(AwesomeCordovaNativePlugin)
);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FCM, [{
    type: Injectable
  }], null, {
    getAPNSToken: [],
    getToken: [],
    onTokenRefresh: [],
    subscribeToTopic: [],
    unsubscribeFromTopic: [],
    hasPermission: [],
    onNotification: [],
    clearAllNotifications: [],
    requestPushPermissionIOS: [],
    createNotificationChannelAndroid: []
  });
})();

// node_modules/@capacitor-firebase/messaging/dist/esm/definitions.js
var Importance;
(function(Importance2) {
  Importance2[Importance2["Min"] = 1] = "Min";
  Importance2[Importance2["Low"] = 2] = "Low";
  Importance2[Importance2["Default"] = 3] = "Default";
  Importance2[Importance2["High"] = 4] = "High";
  Importance2[Importance2["Max"] = 5] = "Max";
})(Importance || (Importance = {}));
var Visibility;
(function(Visibility2) {
  Visibility2[Visibility2["Secret"] = -1] = "Secret";
  Visibility2[Visibility2["Private"] = 0] = "Private";
  Visibility2[Visibility2["Public"] = 1] = "Public";
})(Visibility || (Visibility = {}));

// node_modules/@capacitor-firebase/messaging/dist/esm/index.js
var FirebaseMessaging = registerPlugin("FirebaseMessaging", {
  web: () => import("./web-D3PFM4Z5.js").then((m) => new m.FirebaseMessagingWeb())
});

// src/app/services/fcm.service.ts
var FcmService = class _FcmService {
  constructor(fcm, http, platform, toastCtrl, alertCtrl, router) {
    this.fcm = fcm;
    this.http = http;
    this.platform = platform;
    this.toastCtrl = toastCtrl;
    this.alertCtrl = alertCtrl;
    this.router = router;
    this.notificationSubject = new Subject();
    this.notifications$ = this.notificationSubject.asObservable();
  }
  initPush() {
    return __async(this, null, function* () {
      try {
        if (this.platform.is("cordova") || this.platform.is("capacitor")) {
          console.log("Initializing FCM...");
          if (this.platform.is("android")) {
            yield this.createAndroidNotificationChannels();
          }
          const isGooglePlayAvailable = yield this.checkGooglePlayServices();
          if (!isGooglePlayAvailable) {
            console.warn("Google Play Services not available. FCM may not work properly.");
            this.alertCtrl.create({
              header: "Google Play Services Required",
              message: "This app requires Google Play Services for push notifications. Please install or update Google Play Services and restart the app.",
              buttons: ["OK"]
            }).then((alert) => alert.present());
            localStorage.setItem("google_play_services_missing", "true");
          } else {
            localStorage.removeItem("google_play_services_missing");
          }
          if (this.platform.is("capacitor")) {
            try {
              const permissionResult = yield FirebaseMessaging.requestPermissions();
              console.log("FCM permission result:", permissionResult);
              if (permissionResult.receive === "granted") {
                console.log("FCM permission granted");
                console.log("Device registered with FCM");
              } else {
                console.warn("FCM permission not granted:", permissionResult.receive);
                this.alertCtrl.create({
                  header: "Notification Permission Required",
                  message: "This app requires notification permissions to alert you about emergencies. Please enable notifications for this app in your device settings.",
                  buttons: ["OK"]
                }).then((alert) => alert.present());
              }
            } catch (error) {
              console.error("Error initializing Capacitor Firebase Messaging:", error);
            }
          }
          try {
            const token = yield this.getToken();
            console.log("FCM Token registered:", token.substring(0, 20) + "...");
            this.registerTokenWithBackend(token);
          } catch (error) {
            console.error("Error getting FCM token:", error);
          }
          try {
            if (this.platform.is("cordova")) {
              this.fcm.onTokenRefresh().subscribe({
                next: (token) => {
                  console.log("FCM Token refreshed (Cordova):", token);
                  this.registerTokenWithBackend(token);
                },
                error: (error) => {
                  console.error("Error in token refresh (Cordova):", error);
                }
              });
            }
            if (this.platform.is("capacitor")) {
              FirebaseMessaging.addListener("tokenReceived", (event) => {
                console.log("FCM Token refreshed (Capacitor):", event.token);
                this.registerTokenWithBackend(event.token);
              });
            }
          } catch (error) {
            console.error("Failed to set up token refresh:", error);
          }
          this.setupNotificationListeners();
        } else {
          console.log("FCM not initialized: not running on a device");
        }
      } catch (error) {
        console.error("Error in initPush:", error);
      }
    });
  }
  /**
   * Get the FCM token for the device
   * @returns Promise with the FCM token
   */
  getToken() {
    return __async(this, null, function* () {
      if (this.platform.is("capacitor")) {
        try {
          const result = yield FirebaseMessaging.getToken();
          console.log("Got FCM token from Capacitor Firebase Messaging:", result.token);
          return result.token;
        } catch (capacitorError) {
          console.error("Error getting token from Capacitor Firebase Messaging:", capacitorError);
          try {
            const token = yield this.fcm.getToken();
            console.log("Got FCM token from Cordova FCM plugin:", token);
            return token;
          } catch (cordovaError) {
            console.error("Error getting token from Cordova FCM plugin:", cordovaError);
            throw cordovaError;
          }
        }
      } else if (this.platform.is("cordova")) {
        return this.fcm.getToken();
      } else {
        const mockToken = "browser-mock-token-" + Math.random().toString(36).substring(2, 15);
        console.log("Using mock FCM token for browser:", mockToken);
        return Promise.resolve(mockToken);
      }
    });
  }
  /**
   * Register FCM token with the backend
   * @param token The FCM token to register
   * @param userId Optional user ID to associate with the token
   */
  registerTokenWithBackend(token, userId) {
    const storedToken = localStorage.getItem("fcm_token");
    if (storedToken === token) {
      console.log("Token already registered, skipping registration");
      return;
    }
    let deviceType = "web";
    if (this.platform.is("ios")) {
      deviceType = "ios";
    } else if (this.platform.is("android")) {
      deviceType = "android";
    }
    console.log(`Registering ${deviceType} token with backend...`);
    const payload = {
      token,
      device_type: deviceType,
      project_id: environment.firebase.projectId || "last-5acaf"
      // Use correct project ID
    };
    if (userId) {
      payload.user_id = userId;
      console.log(`Associating token with user ID: ${userId}`);
    } else {
      const authToken = localStorage.getItem("token");
      if (authToken) {
        try {
          const tokenData = this.parseJwt(authToken);
          if (tokenData && tokenData.sub) {
            payload.user_id = tokenData.sub;
            console.log(`Associating token with user ID from JWT: ${tokenData.sub}`);
          }
        } catch (error) {
          console.error("Error parsing JWT token:", error);
        }
      }
    }
    if (token && (environment.firebase.projectId || payload.project_id)) {
      localStorage.setItem("fcm_token", token);
      localStorage.setItem("fcm_token_registering", "true");
      this.http.post(`${environment.apiUrl}/device-token`, payload).subscribe({
        next: (res) => {
          console.log("Token registered with backend:", res);
          localStorage.removeItem("fcm_token_registering");
        },
        error: (err) => {
          console.error("Error registering token:", err);
          localStorage.removeItem("fcm_token_registering");
        }
      });
    } else {
      console.log("Skipping token registration: Missing project ID or token");
      if (token) {
        localStorage.setItem("fcm_token", token);
      }
    }
  }
  /**
   * Parse a JWT token to get the payload
   * @param token The JWT token to parse
   * @returns The decoded token payload
   */
  parseJwt(token) {
    try {
      const base64Url = token.split(".")[1];
      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(atob(base64).split("").map(function(c) {
        return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(""));
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error("Error parsing JWT token:", error);
      return null;
    }
  }
  setupNotificationListeners() {
    this.setupCapacitorNotificationListeners();
    this.setupCordovaNotificationListeners();
  }
  setupCapacitorNotificationListeners() {
    try {
      if (this.platform.is("capacitor")) {
        console.log("Setting up Capacitor Firebase Messaging notification listeners");
        FirebaseMessaging.addListener("notificationReceived", (event) => {
          console.log("Capacitor: Notification received in foreground:", event);
          const notificationData = event.notification.data || {};
          this.processNotification(__spreadValues({
            title: event.notification.title || "",
            body: event.notification.body || "",
            category: notificationData.category || "",
            severity: notificationData.severity || "low",
            wasTapped: false,
            // Foreground notification
            notification_id: notificationData.notification_id || null,
            time: (/* @__PURE__ */ new Date()).toISOString()
          }, Object.keys(notificationData).filter((key) => !["category", "severity", "notification_id"].includes(key)).reduce((obj, key) => {
            obj[key] = notificationData[key];
            return obj;
          }, {})));
        });
        FirebaseMessaging.addListener("notificationActionPerformed", (event) => {
          console.log("Capacitor: Notification tapped:", event);
          const notificationData = event.notification.data || {};
          this.processNotification(__spreadValues({
            title: event.notification.title || "",
            body: event.notification.body || "",
            category: notificationData.category || "",
            severity: notificationData.severity || "low",
            wasTapped: true,
            // Background notification that was tapped
            notification_id: notificationData.notification_id || null,
            time: (/* @__PURE__ */ new Date()).toISOString()
          }, Object.keys(notificationData).filter((key) => !["category", "severity", "notification_id"].includes(key)).reduce((obj, key) => {
            obj[key] = notificationData[key];
            return obj;
          }, {})));
        });
      }
    } catch (error) {
      console.error("Failed to set up Capacitor notification listeners:", error);
    }
  }
  setupCordovaNotificationListeners() {
    try {
      if (this.platform.is("cordova")) {
        console.log("Setting up Cordova FCM notification listeners");
        this.fcm.onNotification().subscribe({
          next: (data) => {
            console.log("Cordova FCM notification received:", data);
            const notificationData = __spreadValues({}, data);
            this.processNotification(__spreadValues({
              title: data["title"] || data["aps"] && data["aps"]["alert"] && data["aps"]["alert"]["title"] || "",
              body: data["body"] || data["aps"] && data["aps"]["alert"] && data["aps"]["alert"]["body"] || data["message"] || "",
              category: data["category"] || "",
              severity: data["severity"] || "low",
              wasTapped: data["wasTapped"] || false,
              notification_id: data["notification_id"] || null,
              time: data["time"] || (/* @__PURE__ */ new Date()).toISOString()
            }, Object.keys(notificationData).filter((key) => !["title", "body", "category", "severity", "wasTapped", "notification_id", "time"].includes(key)).reduce((obj, key) => {
              obj[key] = notificationData[key];
              return obj;
            }, {})));
          },
          error: (error) => {
            console.error("Error in Cordova FCM notification subscription:", error);
          }
        });
      }
    } catch (error) {
      console.error("Failed to set up Cordova FCM notification listeners:", error);
    }
  }
  processNotification(notification) {
    try {
      console.log("Processed notification:", {
        title: notification.title,
        body: notification.body,
        category: notification.category,
        severity: notification.severity,
        wasTapped: notification.wasTapped,
        notification_id: notification.notification_id,
        project_id: environment.firebase.projectId || "new-firebase-project"
      });
      this.notificationSubject.next(notification);
      if (notification.wasTapped) {
        console.log("Notification tapped in background");
        this.handleBackgroundNotification(notification);
      } else {
        console.log("Notification received in foreground");
        this.handleForegroundNotification(notification);
      }
    } catch (error) {
      console.error("Error processing notification:", error, notification);
    }
  }
  handleForegroundNotification(notification) {
    return __async(this, null, function* () {
      try {
        console.log("\u{1F514} Handling foreground notification:", notification);
        this.vibrateDevice();
        this.playNotificationSound();
        yield new Promise((resolve) => setTimeout(resolve, 500));
        yield this.showEmergencyNotificationModal(notification);
      } catch (error) {
        console.error("\u274C Error handling foreground notification:", error);
        yield this.showFallbackToast(notification);
      }
    });
  }
  /**
   * Show custom emergency notification modal with disaster-specific colors
   */
  showEmergencyNotificationModal(notification) {
    return __async(this, null, function* () {
      try {
        console.log("\u{1F4F1} Creating emergency notification modal...", notification);
        yield new Promise((resolve) => setTimeout(resolve, 100));
        const existingAlert = yield this.alertCtrl.getTop();
        if (existingAlert) {
          console.log("\u26A0\uFE0F Alert already present, dismissing first...");
          yield existingAlert.dismiss();
          yield new Promise((resolve) => setTimeout(resolve, 500));
        }
        console.log("\u{1F35E} Showing immediate toast notification as backup...");
        yield this.showFallbackToast(notification);
        const alert = yield this.alertCtrl.create({
          header: notification.title || "EMERGENCY ALERT",
          subHeader: notification.category ? `${notification.category.toUpperCase()} ALERT` : "",
          message: notification.body || "",
          buttons: [
            {
              text: "Go to Safe Area",
              cssClass: "alert-button-primary",
              handler: () => {
                console.log("\u{1F5FA}\uFE0F User tapped Go to Safe Area from modal");
                this.navigateBasedOnNotification(notification);
                return true;
              }
            },
            {
              text: "Dismiss",
              role: "cancel",
              cssClass: "alert-button-secondary",
              handler: () => {
                console.log("\u274C User dismissed notification modal");
                return true;
              }
            }
          ],
          cssClass: `emergency-notification ${notification.category?.toLowerCase() || "general"}-alert`,
          backdropDismiss: false,
          // Prevent accidental dismissal
          keyboardClose: false
        });
        console.log("\u2705 Emergency modal created, presenting...");
        yield alert.present();
        console.log("\u2705 Emergency modal presented successfully");
      } catch (error) {
        console.error("\u274C Error creating emergency modal:", error);
        console.error("\u274C Modal error details:", error?.message, error?.stack);
        yield this.showFallbackToast(notification);
      }
    });
  }
  /**
   * Fallback toast notification when modal fails
   */
  showFallbackToast(notification) {
    return __async(this, null, function* () {
      try {
        console.log("\u{1F35E} Showing emergency toast notification");
        const toast = yield this.toastCtrl.create({
          header: `\u{1F6A8} ${notification.title || "EMERGENCY ALERT"}`,
          message: notification.body || "Emergency notification received",
          duration: 8e3,
          // Longer duration for emergency
          position: "top",
          color: this.getToastColor(notification.category),
          cssClass: "emergency-toast",
          buttons: [
            {
              text: "\u{1F5FA}\uFE0F Go to Safe Area",
              handler: () => {
                console.log("\u{1F5FA}\uFE0F User tapped Go to Safe Area from toast");
                this.navigateBasedOnNotification(notification);
                return true;
              }
            },
            {
              text: "Dismiss",
              role: "cancel",
              handler: () => {
                console.log("\u274C User dismissed toast notification");
                return true;
              }
            }
          ]
        });
        yield toast.present();
        console.log("\u2705 Emergency toast shown successfully");
        this.vibrateDevice();
      } catch (error) {
        console.error("\u274C Even fallback toast failed:", error);
        try {
          const simpleAlert = yield this.alertCtrl.create({
            header: "\u{1F6A8} EMERGENCY ALERT",
            message: `${notification.title}

${notification.body}`,
            buttons: [
              {
                text: "Go to Safe Area",
                handler: () => this.navigateBasedOnNotification(notification)
              },
              "Dismiss"
            ]
          });
          yield simpleAlert.present();
          console.log("\u2705 Simple alert shown as last resort");
        } catch (finalError) {
          console.error("\u274C All notification methods failed:", finalError);
          console.log("\u{1F4E2} EMERGENCY NOTIFICATION (all display methods failed):", notification);
        }
      }
    });
  }
  /**
   * Get toast color based on disaster category
   */
  getToastColor(category) {
    if (!category)
      return "warning";
    switch (category.toLowerCase()) {
      case "earthquake":
        return "warning";
      case "flood":
        return "primary";
      case "typhoon":
        return "success";
      case "fire":
        return "danger";
      default:
        return "warning";
    }
  }
  /**
   * Test method to simulate foreground notification
   */
  simulateForegroundNotification(notification) {
    return __async(this, null, function* () {
      console.log("\u{1F9EA} Simulating foreground notification:", notification);
      yield this.processNotification(notification);
    });
  }
  /**
   * Test method to simulate background notification
   */
  simulateBackgroundNotification(notification) {
    return __async(this, null, function* () {
      console.log("\u{1F9EA} Simulating background notification:", notification);
      notification.wasTapped = true;
      yield this.processNotification(notification);
    });
  }
  /**
   * Get disaster-specific styling
   */
  getDisasterStyle(category) {
    if (!category) {
      return { color: "#666666", icon: "notifications-outline" };
    }
    const type = category.toLowerCase();
    if (type.includes("earthquake") || type.includes("quake")) {
      return { color: "#ffa500", icon: "earth-outline" };
    } else if (type.includes("flood") || type.includes("flash")) {
      return { color: "#0000ff", icon: "water-outline" };
    } else if (type.includes("typhoon") || type.includes("storm") || type.includes("hurricane")) {
      return { color: "#008000", icon: "thunderstorm-outline" };
    } else if (type.includes("fire")) {
      return { color: "#ff0000", icon: "flame-outline" };
    }
    return { color: "#666666", icon: "alert-circle-outline" };
  }
  /**
   * Vibrate the device if the API is available
   */
  vibrateDevice() {
    if ("vibrate" in navigator) {
      navigator.vibrate([1e3, 200, 1e3, 200, 1e3]);
      console.log("Device vibration triggered with strong pattern");
    } else {
      console.log("Vibration API not supported on this device");
    }
  }
  /**
   * Play a notification sound
   */
  playNotificationSound() {
    try {
      const audio = new Audio();
      audio.src = "data:audio/wav;base64,UklGRl9vT19XQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YU...";
      audio.volume = 1;
      audio.play().catch((error) => {
        console.error("Error playing notification sound:", error);
      });
    } catch (error) {
      console.error("Error creating audio element:", error);
    }
  }
  // Removed duplicate function
  handleBackgroundNotification(notification) {
    return __async(this, null, function* () {
      try {
        console.log("\u{1F514} Handling background notification tap:", notification);
        this.vibrateDevice();
        yield new Promise((resolve) => setTimeout(resolve, 1e3));
        yield this.showEmergencyNotificationModal(notification);
      } catch (error) {
        console.error("\u274C Error handling background notification:", error);
        this.navigateBasedOnNotification(notification);
      }
    });
  }
  navigateBasedOnNotification(notification) {
    console.log("\u{1F5FA}\uFE0F Navigating based on notification:", notification);
    if (notification.category === "evacuation_center" || notification["data"] && notification["data"]["type"] === "evacuation_center_added") {
      console.log("\u{1F3E2} New evacuation center notification detected");
      this.handleEvacuationCenterNotification(notification);
      return;
    }
    if (notification.category) {
      const category = notification.category.toLowerCase();
      console.log("\u{1F4CD} Notification category:", category);
      let mappedDisasterType = "";
      switch (category) {
        case "flood":
        case "flashflood":
          mappedDisasterType = "Flood";
          break;
        case "earthquake":
        case "quake":
          mappedDisasterType = "Earthquake";
          break;
        case "typhoon":
        case "storm":
        case "hurricane":
          mappedDisasterType = "Typhoon";
          break;
        case "fire":
          mappedDisasterType = "Fire";
          break;
        default:
          console.warn("Unknown disaster category:", category);
          mappedDisasterType = "all";
          break;
      }
      console.log(`\u{1F5FA}\uFE0F Mapped disaster type: ${category} -> ${mappedDisasterType}`);
      if (mappedDisasterType && mappedDisasterType !== "all") {
        console.log("\u{1F5FA}\uFE0F Navigating to disaster-specific map:", mappedDisasterType);
        let route = "";
        switch (mappedDisasterType.toLowerCase()) {
          case "earthquake":
            route = "/earthquake-map";
            break;
          case "typhoon":
            route = "/typhoon-map";
            break;
          case "flood":
            route = "/flood-map";
            break;
          default:
            route = "/tabs/map";
            break;
        }
        console.log(`\u{1F5FA}\uFE0F Navigating to route: ${route}`);
        this.router.navigate([route]);
      } else {
        console.log("\u{1F3E0} Navigating to home (unknown disaster type)");
        this.router.navigate(["/tabs/home"]);
      }
    } else {
      console.log("\u{1F3E0} Navigating to home (no category)");
      this.router.navigate(["/tabs/home"]);
    }
  }
  handleEvacuationCenterNotification(notification) {
    console.log("\u{1F3E2} Handling evacuation center notification:", notification);
    try {
      let evacuationData = null;
      if (notification["data"]) {
        evacuationData = notification["data"];
      }
      if (evacuationData && evacuationData.evacuation_center_id) {
        const centerId = evacuationData.evacuation_center_id;
        const disasterType = evacuationData.disaster_type;
        const latitude = evacuationData.location?.latitude;
        const longitude = evacuationData.location?.longitude;
        console.log(`\u{1F3E2} Evacuation center details:`, {
          id: centerId,
          disasterType,
          lat: latitude,
          lng: longitude
        });
        let route = "/all-maps";
        if (disasterType) {
          switch (disasterType.toLowerCase()) {
            case "earthquake":
              route = "/earthquake-map";
              break;
            case "typhoon":
              route = "/typhoon-map";
              break;
            case "flood":
            case "flash flood":
              route = "/flood-map";
              break;
            default:
              route = "/all-maps";
              break;
          }
        }
        console.log(`\u{1F5FA}\uFE0F Navigating to ${route} for new evacuation center`);
        this.router.navigate([route], {
          queryParams: {
            newCenterId: centerId,
            highlightCenter: "true",
            centerLat: latitude,
            centerLng: longitude
          }
        });
      } else {
        console.log("\u{1F3E2} No evacuation center data found, navigating to all maps");
        this.router.navigate(["/all-maps"]);
      }
    } catch (error) {
      console.error("\u274C Error handling evacuation center notification:", error);
      this.router.navigate(["/all-maps"]);
    }
  }
  /**
   * Check if Google Play Services is available on the device
   * @returns Promise<boolean> True if Google Play Services is available
   */
  checkGooglePlayServices() {
    return __async(this, null, function* () {
      try {
        if (this.platform.is("capacitor") && this.platform.is("android")) {
          try {
            yield FirebaseMessaging.getToken();
            return true;
          } catch (error) {
            console.error("Error checking Google Play Services:", error);
            const errorMessage = error.message || "";
            if (errorMessage.includes("Google Play Services") || errorMessage.includes("GoogleApiAvailability") || errorMessage.includes("API unavailable")) {
              return false;
            }
            return true;
          }
        }
        return true;
      } catch (error) {
        console.error("Error in checkGooglePlayServices:", error);
        return true;
      }
    });
  }
  /**
   * Create notification channels for Android
   * This is required for notifications to display properly on Android 8.0+
   */
  createAndroidNotificationChannels() {
    return __async(this, null, function* () {
      try {
        if (this.platform.is("android")) {
          console.log("Creating Android notification channels");
          yield this.sendTestChannelNotification("emergency-alerts", "Emergency Alerts", "High priority notifications for emergencies", "high");
          yield this.sendTestChannelNotification("general-notifications", "General Notifications", "Standard notifications", "default");
          console.log("Android notification channels created successfully");
        }
      } catch (error) {
        console.error("Error creating Android notification channels:", error);
      }
    });
  }
  /**
   * Send a test notification to create a channel
   * This is a workaround to create notification channels on Android
   */
  sendTestChannelNotification(channelId, channelName, channelDescription, importance) {
    return __async(this, null, function* () {
      try {
        const payload = {
          notification: {
            title: "Channel Setup",
            body: "Setting up notification channels",
            android: {
              channelId,
              priority: importance === "high" ? "high" : importance === "default" ? "default" : "low",
              sound: importance !== "low",
              vibrate: importance !== "low",
              visibility: "public"
            }
          }
        };
        console.log(`Created notification channel: ${channelId} (${channelName})`);
      } catch (error) {
        console.error(`Error creating notification channel ${channelId}:`, error);
      }
    });
  }
  /**
   * Refresh FCM token and re-register with backend
   * This can be called when the user is having trouble receiving notifications
   * @param userId Optional user ID to associate with the token
   * @returns Promise<boolean> True if refresh was successful
   */
  refreshFCMToken(userId) {
    return __async(this, null, function* () {
      try {
        console.log("Refreshing FCM token...");
        if (this.platform.is("capacitor")) {
          try {
            yield FirebaseMessaging.deleteToken();
            console.log("Existing FCM token deleted");
            const result = yield FirebaseMessaging.getToken();
            console.log("New FCM token obtained:", result.token);
            this.registerTokenWithBackend(result.token, userId);
            return true;
          } catch (error) {
            console.error("Error refreshing Capacitor FCM token:", error);
            return false;
          }
        } else if (this.platform.is("cordova")) {
          try {
            const token = yield this.fcm.getToken();
            console.log("New FCM token obtained from Cordova:", token);
            this.registerTokenWithBackend(token, userId);
            return true;
          } catch (error) {
            console.error("Error refreshing Cordova FCM token:", error);
            return false;
          }
        } else {
          const mockToken = "browser-mock-token-" + Math.random().toString(36).substring(2, 15);
          console.log("New mock FCM token generated:", mockToken);
          this.registerTokenWithBackend(mockToken, userId);
          return true;
        }
      } catch (error) {
        console.error("Error in refreshFCMToken:", error);
        return false;
      }
    });
  }
  static {
    this.\u0275fac = function FcmService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _FcmService)(\u0275\u0275inject(FCM), \u0275\u0275inject(HttpClient), \u0275\u0275inject(Platform), \u0275\u0275inject(ToastController), \u0275\u0275inject(AlertController), \u0275\u0275inject(Router));
    };
  }
  static {
    this.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _FcmService, factory: _FcmService.\u0275fac, providedIn: "root" });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FcmService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: FCM }, { type: HttpClient }, { type: Platform }, { type: ToastController }, { type: AlertController }, { type: Router }], null);
})();

export {
  FCM,
  FcmService
};
//# sourceMappingURL=chunk-RDFT5QPW.js.map

{"version": 3, "sources": ["node_modules/@awesome-cordova-plugins/core/bootstrap.js", "node_modules/@awesome-cordova-plugins/core/decorators/common.js", "node_modules/@awesome-cordova-plugins/core/util.js", "node_modules/@awesome-cordova-plugins/core/awesome-cordova-plugin.js", "node_modules/@awesome-cordova-plugins/core/decorators/cordova.js", "node_modules/@awesome-cordova-plugins/core/index.js", "node_modules/@awesome-cordova-plugins/fcm/ngx/index.js", "node_modules/@capacitor-firebase/messaging/dist/esm/definitions.js", "node_modules/@capacitor-firebase/messaging/dist/esm/index.js", "src/app/services/fcm.service.ts"], "sourcesContent": ["/**\n *\n */\nexport function checkReady() {\n  if (typeof process === 'undefined') {\n    var win_1 = typeof window !== 'undefined' ? window : {};\n    var DEVICE_READY_TIMEOUT_1 = 5000;\n    // To help developers using cordova, we listen for the device ready event and\n    // log an error if it didn't fire in a reasonable amount of time. Generally,\n    // when this happens, developers should remove and reinstall plugins, since\n    // an inconsistent plugin is often the culprit.\n    var before_1 = Date.now();\n    var didFireReady_1 = false;\n    win_1.document.addEventListener('deviceready', function () {\n      console.log(\"Ionic Native: deviceready event fired after \" + (Date.now() - before_1) + \" ms\");\n      didFireReady_1 = true;\n    });\n    setTimeout(function () {\n      if (!didFireReady_1 && win_1.cordova) {\n        console.warn(\"Ionic Native: deviceready did not fire within \" + DEVICE_READY_TIMEOUT_1 + \"ms. This can happen when plugins are in an inconsistent state. Try removing plugins from plugins/ and reinstalling them.\");\n      }\n    }, DEVICE_READY_TIMEOUT_1);\n  }\n}\n", "import { fromEvent, Observable } from 'rxjs';\nexport var ERR_CORDOVA_NOT_AVAILABLE = {\n  error: 'cordova_not_available'\n};\nexport var ERR_PLUGIN_NOT_INSTALLED = {\n  error: 'plugin_not_installed'\n};\n/**\n * @param callback\n */\nexport function getPromise(callback) {\n  var tryNativePromise = function () {\n    if (Promise) {\n      return new Promise(function (resolve, reject) {\n        callback(resolve, reject);\n      });\n    } else {\n      console.error('No Promise support or polyfill found. To enable Ionic Native support, please add the es6-promise polyfill before this script, or run with a library like Angular or on a recent browser.');\n    }\n  };\n  if (typeof window !== 'undefined' && window.angular) {\n    var doc = window.document;\n    var injector = window.angular.element(doc.querySelector('[ng-app]') || doc.body).injector();\n    if (injector) {\n      var $q = injector.get('$q');\n      return $q(function (resolve, reject) {\n        callback(resolve, reject);\n      });\n    }\n    console.warn(\"Angular 1 was detected but $q couldn't be retrieved. This is usually when the app is not bootstrapped on the html or body tag. Falling back to native promises which won't trigger an automatic digest when promises resolve.\");\n  }\n  return tryNativePromise();\n}\n/**\n * @param pluginObj\n * @param methodName\n * @param args\n * @param opts\n */\nexport function wrapPromise(pluginObj, methodName, args, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  var pluginResult, rej;\n  var p = getPromise(function (resolve, reject) {\n    if (opts.destruct) {\n      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return resolve(args);\n      }, function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return reject(args);\n      });\n    } else {\n      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, resolve, reject);\n    }\n    rej = reject;\n  });\n  // Angular throws an error on unhandled rejection, but in this case we have already printed\n  // a warning that Cordova is undefined or the plugin is uninstalled, so there is no reason\n  // to error\n  if (pluginResult && pluginResult.error) {\n    p.catch(function () {});\n    typeof rej === 'function' && rej(pluginResult.error);\n  }\n  return p;\n}\n/**\n * @param pluginObj\n * @param methodName\n * @param args\n * @param opts\n */\nfunction wrapOtherPromise(pluginObj, methodName, args, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  return getPromise(function (resolve, reject) {\n    var pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts);\n    if (pluginResult) {\n      if (pluginResult.error) {\n        reject(pluginResult.error);\n      } else if (pluginResult.then) {\n        pluginResult.then(resolve).catch(reject);\n      }\n    } else {\n      reject({\n        error: 'unexpected_error'\n      });\n    }\n  });\n}\n/**\n * @param pluginObj\n * @param methodName\n * @param args\n * @param opts\n */\nfunction wrapObservable(pluginObj, methodName, args, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  return new Observable(function (observer) {\n    var pluginResult;\n    if (opts.destruct) {\n      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return observer.next(args);\n      }, function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return observer.error(args);\n      });\n    } else {\n      pluginResult = callCordovaPlugin(pluginObj, methodName, args, opts, observer.next.bind(observer), observer.error.bind(observer));\n    }\n    if (pluginResult && pluginResult.error) {\n      observer.error(pluginResult.error);\n      observer.complete();\n    }\n    return function () {\n      try {\n        if (opts.clearFunction) {\n          if (opts.clearWithArgs) {\n            return callCordovaPlugin(pluginObj, opts.clearFunction, args, opts, observer.next.bind(observer), observer.error.bind(observer));\n          }\n          return callCordovaPlugin(pluginObj, opts.clearFunction, []);\n        }\n      } catch (e) {\n        console.warn('Unable to clear the previous observable watch for', pluginObj.constructor.getPluginName(), methodName);\n        console.warn(e);\n      }\n    };\n  });\n}\n/**\n * Wrap the event with an observable\n *\n * @private\n * @param event event name\n * @param element The element to attach the event listener to\n * @returns {Observable}\n */\nfunction wrapEventObservable(event, element) {\n  element = typeof window !== 'undefined' && element ? get(window, element) : element || (typeof window !== 'undefined' ? window : {});\n  return fromEvent(element, event);\n}\n/**\n * @param plugin\n * @param methodName\n * @param pluginName\n */\nexport function checkAvailability(plugin, methodName, pluginName) {\n  var pluginRef, pluginPackage;\n  if (typeof plugin === 'string') {\n    pluginRef = plugin;\n  } else {\n    pluginRef = plugin.constructor.getPluginRef();\n    pluginName = plugin.constructor.getPluginName();\n    pluginPackage = plugin.constructor.getPluginInstallName();\n  }\n  var pluginInstance = getPlugin(pluginRef);\n  if (!pluginInstance || !!methodName && typeof pluginInstance[methodName] === 'undefined') {\n    if (typeof window === 'undefined' || !window.cordova) {\n      cordovaWarn(pluginName, methodName);\n      return ERR_CORDOVA_NOT_AVAILABLE;\n    }\n    pluginWarn(pluginName, pluginPackage, methodName);\n    return ERR_PLUGIN_NOT_INSTALLED;\n  }\n  return true;\n}\n/**\n * Checks if _objectInstance exists and has the method/property\n *\n * @param pluginObj\n * @param methodName\n * @private\n */\nexport function instanceAvailability(pluginObj, methodName) {\n  return pluginObj._objectInstance && (!methodName || typeof pluginObj._objectInstance[methodName] !== 'undefined');\n}\n/**\n * @param args\n * @param opts\n * @param resolve\n * @param reject\n */\nexport function setIndex(args, opts, resolve, reject) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  // ignore resolve and reject in case sync\n  if (opts.sync) {\n    return args;\n  }\n  // If the plugin method expects myMethod(success, err, options)\n  if (opts.callbackOrder === 'reverse') {\n    // Get those arguments in the order [resolve, reject, ...restOfArgs]\n    args.unshift(reject);\n    args.unshift(resolve);\n  } else if (opts.callbackStyle === 'node') {\n    args.push(function (err, result) {\n      if (err) {\n        reject(err);\n      } else {\n        resolve(result);\n      }\n    });\n  } else if (opts.callbackStyle === 'object' && opts.successName && opts.errorName) {\n    var obj = {};\n    obj[opts.successName] = resolve;\n    obj[opts.errorName] = reject;\n    args.push(obj);\n  } else if (typeof opts.successIndex !== 'undefined' || typeof opts.errorIndex !== 'undefined') {\n    var setSuccessIndex = function () {\n      // If we've specified a success/error index\n      if (opts.successIndex > args.length) {\n        args[opts.successIndex] = resolve;\n      } else {\n        args.splice(opts.successIndex, 0, resolve);\n      }\n    };\n    var setErrorIndex = function () {\n      // We don't want that the reject cb gets spliced into the position of an optional argument that has not been\n      // defined and thus causing non expected behavior.\n      if (opts.errorIndex > args.length) {\n        args[opts.errorIndex] = reject; // insert the reject fn at the correct specific index\n      } else {\n        args.splice(opts.errorIndex, 0, reject); // otherwise just splice it into the array\n      }\n    };\n    if (opts.successIndex > opts.errorIndex) {\n      setErrorIndex();\n      setSuccessIndex();\n    } else {\n      setSuccessIndex();\n      setErrorIndex();\n    }\n  } else {\n    // Otherwise, let's tack them on to the end of the argument list\n    // which is 90% of cases\n    args.push(resolve);\n    args.push(reject);\n  }\n  return args;\n}\n/**\n * @param pluginObj\n * @param methodName\n * @param args\n * @param opts\n * @param resolve\n * @param reject\n */\nexport function callCordovaPlugin(pluginObj, methodName, args, opts, resolve, reject) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  // Try to figure out where the success/error callbacks need to be bound\n  // to our promise resolve/reject handlers.\n  args = setIndex(args, opts, resolve, reject);\n  var availabilityCheck = checkAvailability(pluginObj, methodName);\n  if (availabilityCheck === true) {\n    var pluginInstance = getPlugin(pluginObj.constructor.getPluginRef());\n    // eslint-disable-next-line prefer-spread\n    return pluginInstance[methodName].apply(pluginInstance, args);\n  } else {\n    return availabilityCheck;\n  }\n}\n/**\n * @param pluginObj\n * @param methodName\n * @param args\n * @param opts\n * @param resolve\n * @param reject\n */\nexport function callInstance(pluginObj, methodName, args, opts, resolve, reject) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  args = setIndex(args, opts, resolve, reject);\n  if (instanceAvailability(pluginObj, methodName)) {\n    // eslint-disable-next-line prefer-spread\n    return pluginObj._objectInstance[methodName].apply(pluginObj._objectInstance, args);\n  }\n}\n/**\n * @param pluginRef\n */\nexport function getPlugin(pluginRef) {\n  if (typeof window !== 'undefined') {\n    return get(window, pluginRef);\n  }\n  return null;\n}\n/**\n * @param element\n * @param path\n */\nexport function get(element, path) {\n  var paths = path.split('.');\n  var obj = element;\n  for (var i = 0; i < paths.length; i++) {\n    if (!obj) {\n      return null;\n    }\n    obj = obj[paths[i]];\n  }\n  return obj;\n}\n/**\n * @param pluginName\n * @param plugin\n * @param method\n */\nexport function pluginWarn(pluginName, plugin, method) {\n  if (method) {\n    console.warn('Native: tried calling ' + pluginName + '.' + method + ', but the ' + pluginName + ' plugin is not installed.');\n  } else {\n    console.warn(\"Native: tried accessing the \" + pluginName + \" plugin but it's not installed.\");\n  }\n  if (plugin) {\n    console.warn(\"Install the \" + pluginName + \" plugin: 'ionic cordova plugin add \" + plugin + \"'\");\n  }\n}\n/**\n * @private\n * @param pluginName\n * @param method\n */\nexport function cordovaWarn(pluginName, method) {\n  if (typeof process === 'undefined') {\n    if (method) {\n      console.warn('Native: tried calling ' + pluginName + '.' + method + ', but Cordova is not available. Make sure to include cordova.js or run in a device/simulator');\n    } else {\n      console.warn('Native: tried accessing the ' + pluginName + ' plugin but Cordova is not available. Make sure to include cordova.js or run in a device/simulator');\n    }\n  }\n}\n/**\n * @param pluginObj\n * @param methodName\n * @param opts\n * @private\n */\nexport var wrap = function (pluginObj, methodName, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (opts.sync) {\n      // Sync doesn't wrap the plugin with a promise or observable, it returns the result as-is\n      return callCordovaPlugin(pluginObj, methodName, args, opts);\n    } else if (opts.observable) {\n      return wrapObservable(pluginObj, methodName, args, opts);\n    } else if (opts.eventObservable && opts.event) {\n      return wrapEventObservable(opts.event, opts.element);\n    } else if (opts.otherPromise) {\n      return wrapOtherPromise(pluginObj, methodName, args, opts);\n    } else {\n      return wrapPromise(pluginObj, methodName, args, opts);\n    }\n  };\n};\n/**\n * @param pluginObj\n * @param methodName\n * @param opts\n * @private\n */\nexport function wrapInstance(pluginObj, methodName, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (opts.sync) {\n      return callInstance(pluginObj, methodName, args, opts);\n    } else if (opts.observable) {\n      return new Observable(function (observer) {\n        var pluginResult;\n        if (opts.destruct) {\n          pluginResult = callInstance(pluginObj, methodName, args, opts, function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            return observer.next(args);\n          }, function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            return observer.error(args);\n          });\n        } else {\n          pluginResult = callInstance(pluginObj, methodName, args, opts, observer.next.bind(observer), observer.error.bind(observer));\n        }\n        if (pluginResult && pluginResult.error) {\n          observer.error(pluginResult.error);\n        }\n        return function () {\n          try {\n            if (opts.clearWithArgs) {\n              return callInstance(pluginObj, opts.clearFunction, args, opts, observer.next.bind(observer), observer.error.bind(observer));\n            }\n            return callInstance(pluginObj, opts.clearFunction, []);\n          } catch (e) {\n            console.warn('Unable to clear the previous observable watch for', pluginObj.constructor.getPluginName(), methodName);\n            console.warn(e);\n          }\n        };\n      });\n    } else if (opts.otherPromise) {\n      return getPromise(function (resolve, reject) {\n        var result;\n        if (opts.destruct) {\n          result = callInstance(pluginObj, methodName, args, opts, function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            return resolve(args);\n          }, function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            return reject(args);\n          });\n        } else {\n          result = callInstance(pluginObj, methodName, args, opts, resolve, reject);\n        }\n        if (result && result.then) {\n          result.then(resolve, reject);\n        } else {\n          reject();\n        }\n      });\n    } else {\n      var pluginResult_1, rej_1;\n      var p = getPromise(function (resolve, reject) {\n        if (opts.destruct) {\n          pluginResult_1 = callInstance(pluginObj, methodName, args, opts, function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            return resolve(args);\n          }, function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n              args[_i] = arguments[_i];\n            }\n            return reject(args);\n          });\n        } else {\n          pluginResult_1 = callInstance(pluginObj, methodName, args, opts, resolve, reject);\n        }\n        rej_1 = reject;\n      });\n      // Angular throws an error on unhandled rejection, but in this case we have already printed\n      // a warning that Cordova is undefined or the plugin is uninstalled, so there is no reason\n      // to error\n      if (pluginResult_1 && pluginResult_1.error) {\n        p.catch(function () {});\n        typeof rej_1 === 'function' && rej_1(pluginResult_1.error);\n      }\n      return p;\n    }\n  };\n}\n", "/**\n * @param element\n * @param path\n * @private\n */\nexport function get(element, path) {\n  var paths = path.split('.');\n  var obj = element;\n  for (var i = 0; i < paths.length; i++) {\n    if (!obj) {\n      return null;\n    }\n    obj = obj[paths[i]];\n  }\n  return obj;\n}\n/**\n * @param callback\n * @private\n */\nexport function getPromise(callback) {\n  if (callback === void 0) {\n    callback = function () {};\n  }\n  var tryNativePromise = function () {\n    if (typeof Promise === 'function' || typeof window !== 'undefined' && window.Promise) {\n      return new Promise(function (resolve, reject) {\n        callback(resolve, reject);\n      });\n    } else {\n      console.error('No Promise support or polyfill found. To enable Ionic Native support, please add the es6-promise polyfill before this script, or run with a library like Angular or on a recent browser.');\n    }\n  };\n  return tryNativePromise();\n}\n", "import { checkAvailability } from './decorators/common';\nimport { get } from './util';\nvar AwesomeCordovaNativePlugin = /** @class */function () {\n  function AwesomeCordovaNativePlugin() {}\n  /**\n   * Returns a boolean that indicates whether the plugin is installed\n   *\n   * @returns {boolean}\n   */\n  AwesomeCordovaNativePlugin.installed = function () {\n    var isAvailable = checkAvailability(this.pluginRef) === true;\n    return isAvailable;\n  };\n  /**\n   * Returns the original plugin object\n   */\n  AwesomeCordovaNativePlugin.getPlugin = function () {\n    if (typeof window !== 'undefined') {\n      return get(window, this.pluginRef);\n    }\n    return null;\n  };\n  /**\n   * Returns the plugin's name\n   */\n  AwesomeCordovaNativePlugin.getPluginName = function () {\n    var pluginName = this.pluginName;\n    return pluginName;\n  };\n  /**\n   * Returns the plugin's reference\n   */\n  AwesomeCordovaNativePlugin.getPluginRef = function () {\n    var pluginRef = this.pluginRef;\n    return pluginRef;\n  };\n  /**\n   * Returns the plugin's install name\n   */\n  AwesomeCordovaNativePlugin.getPluginInstallName = function () {\n    var plugin = this.plugin;\n    return plugin;\n  };\n  /**\n   * Returns the plugin's supported platforms\n   */\n  AwesomeCordovaNativePlugin.getSupportedPlatforms = function () {\n    var platform = this.platforms;\n    return platform;\n  };\n  AwesomeCordovaNativePlugin.pluginName = '';\n  AwesomeCordovaNativePlugin.pluginRef = '';\n  AwesomeCordovaNativePlugin.plugin = '';\n  AwesomeCordovaNativePlugin.repo = '';\n  AwesomeCordovaNativePlugin.platforms = [];\n  AwesomeCordovaNativePlugin.install = '';\n  return AwesomeCordovaNativePlugin;\n}();\nexport { AwesomeCordovaNativePlugin };\n", "import { wrap } from './common';\n/**\n * @param pluginObj\n * @param methodName\n * @param config\n * @param args\n */\nexport function cordova(pluginObj, methodName, config, args) {\n  return wrap(pluginObj, methodName, config).apply(this, args);\n}\n", "import { checkReady } from './bootstrap';\nexport { AwesomeCordovaNativePlugin } from './awesome-cordova-plugin';\n// Decorators\nexport { checkAvailability, instanceAvailability, wrap, getPromise } from './decorators/common';\nexport * from './decorators/cordova';\nexport * from './decorators/cordova-function-override';\nexport * from './decorators/cordova-instance';\nexport * from './decorators/cordova-property';\nexport * from './decorators/instance-property';\nexport * from './decorators/interfaces';\ncheckReady();\n", "import { __decorate, __extends } from \"tslib\";\nimport { AwesomeCordovaNativePlugin, cordova } from '@awesome-cordova-plugins/core';\nimport { Injectable } from '@angular/core';\nimport { Observable } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nvar FCM = /** @class */function (_super) {\n  __extends(FCM, _super);\n  function FCM() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  FCM.prototype.getAPNSToken = function () {\n    return cordova(this, \"getAPNSToken\", {}, arguments);\n  };\n  FCM.prototype.getToken = function () {\n    return cordova(this, \"getToken\", {}, arguments);\n  };\n  FCM.prototype.onTokenRefresh = function () {\n    return cordova(this, \"onTokenRefresh\", {\n      \"observable\": true\n    }, arguments);\n  };\n  FCM.prototype.subscribeToTopic = function (topic) {\n    return cordova(this, \"subscribeToTopic\", {}, arguments);\n  };\n  FCM.prototype.unsubscribeFromTopic = function (topic) {\n    return cordova(this, \"unsubscribeFromTopic\", {}, arguments);\n  };\n  FCM.prototype.hasPermission = function () {\n    return cordova(this, \"hasPermission\", {}, arguments);\n  };\n  FCM.prototype.onNotification = function () {\n    return cordova(this, \"onNotification\", {\n      \"observable\": true,\n      \"successIndex\": 0,\n      \"errorIndex\": 2\n    }, arguments);\n  };\n  FCM.prototype.clearAllNotifications = function () {\n    return cordova(this, \"clearAllNotifications\", {}, arguments);\n  };\n  FCM.prototype.requestPushPermissionIOS = function (options) {\n    return cordova(this, \"requestPushPermissionIOS\", {}, arguments);\n  };\n  FCM.prototype.createNotificationChannelAndroid = function (channelConfig) {\n    return cordova(this, \"createNotificationChannelAndroid\", {}, arguments);\n  };\n  FCM.ɵfac = /* @__PURE__ */(() => {\n    let ɵFCM_BaseFactory;\n    return function FCM_Factory(__ngFactoryType__) {\n      return (ɵFCM_BaseFactory || (ɵFCM_BaseFactory = i0.ɵɵgetInheritedFactory(FCM)))(__ngFactoryType__ || FCM);\n    };\n  })();\n  FCM.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FCM,\n    factory: FCM.ɵfac\n  });\n  FCM.pluginName = \"FCM\";\n  FCM.plugin = \"cordova-plugin-fcm-with-dependecy-updated\";\n  FCM.pluginRef = \"FCMPlugin\";\n  FCM.repo = \"https://github.com/andrehtissot/cordova-plugin-fcm-with-dependecy-updated\";\n  FCM.platforms = [\"Android\", \"iOS\"];\n  FCM = __decorate([], FCM);\n  return FCM;\n}(AwesomeCordovaNativePlugin);\nexport { FCM };\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FCM, [{\n    type: Injectable\n  }], null, {\n    getAPNSToken: [],\n    getToken: [],\n    onTokenRefresh: [],\n    subscribeToTopic: [],\n    unsubscribeFromTopic: [],\n    hasPermission: [],\n    onNotification: [],\n    clearAllNotifications: [],\n    requestPushPermissionIOS: [],\n    createNotificationChannelAndroid: []\n  });\n})();\n", "/// <reference types=\"@capacitor/cli\" />\n/**\n * The importance level.\n *\n * For more details, see the [Android Developer Docs](https://developer.android.com/reference/android/app/NotificationManager#IMPORTANCE_DEFAULT)\n *\n * @since 1.4.0\n */\nexport var Importance;\n(function (Importance) {\n  /**\n   * @since 1.4.0\n   */\n  Importance[Importance[\"Min\"] = 1] = \"Min\";\n  /**\n   * @since 1.4.0\n   */\n  Importance[Importance[\"Low\"] = 2] = \"Low\";\n  /**\n   * @since 1.4.0\n   */\n  Importance[Importance[\"Default\"] = 3] = \"Default\";\n  /**\n   * @since 1.4.0\n   */\n  Importance[Importance[\"High\"] = 4] = \"High\";\n  /**\n   * @since 1.4.0\n   */\n  Importance[Importance[\"Max\"] = 5] = \"Max\";\n})(Importance || (Importance = {}));\n/**\n * The notification visibility.\n *\n * For more details, see the [Android Developer Docs](https://developer.android.com/reference/androidx/core/app/NotificationCompat#VISIBILITY_PRIVATE())\n *\n * @since 1.4.0\n */\nexport var Visibility;\n(function (Visibility) {\n  /**\n   * @since 1.4.0\n   */\n  Visibility[Visibility[\"Secret\"] = -1] = \"Secret\";\n  /**\n   * @since 1.4.0\n   */\n  Visibility[Visibility[\"Private\"] = 0] = \"Private\";\n  /**\n   * @since 1.4.0\n   */\n  Visibility[Visibility[\"Public\"] = 1] = \"Public\";\n})(Visibility || (Visibility = {}));\n", "import { registerPlugin } from '@capacitor/core';\nconst FirebaseMessaging = registerPlugin('FirebaseMessaging', {\n  web: () => import('./web').then(m => new m.FirebaseMessagingWeb())\n});\nexport * from './definitions';\nexport { FirebaseMessaging };\n", "import { Injectable } from '@angular/core';\r\nimport { FCM } from '@awesome-cordova-plugins/fcm/ngx';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { environment } from '../../environments/environment';\r\nimport { Platform, ToastController, AlertController } from '@ionic/angular';\r\nimport { Router } from '@angular/router';\r\nimport { Subject } from 'rxjs';\r\nimport { FirebaseMessaging } from '@capacitor-firebase/messaging';\r\nimport { Capacitor } from '@capacitor/core';\r\n\r\nexport interface FCMNotification {\r\n  title: string;\r\n  body: string;\r\n  category?: string;\r\n  severity?: string;\r\n  wasTapped?: boolean;\r\n  time?: string | number | Date;\r\n  notification_id?: string | number | null;\r\n  project_id?: string;\r\n  // Allow any other properties\r\n  [key: string]: any;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class FcmService {\r\n  private notificationSubject = new Subject<FCMNotification>();\r\n  public notifications$ = this.notificationSubject.asObservable();\r\n\r\n  constructor(\r\n    private fcm: FCM,\r\n    private http: HttpClient,\r\n    private platform: Platform,\r\n    private toastCtrl: ToastController,\r\n    private alertCtrl: AlertController,\r\n    private router: Router\r\n  ) {}\r\n\r\n  async initPush() {\r\n    try {\r\n      // Only initialize FCM on actual devices\r\n      if (this.platform.is('cordova') || this.platform.is('capacitor')) {\r\n        console.log('Initializing FCM...');\r\n\r\n        // Create Android notification channels first (if on Android)\r\n        if (this.platform.is('android')) {\r\n          await this.createAndroidNotificationChannels();\r\n        }\r\n\r\n        // Check if Google Play Services is available\r\n        const isGooglePlayAvailable = await this.checkGooglePlayServices();\r\n        if (!isGooglePlayAvailable) {\r\n          console.warn('Google Play Services not available. FCM may not work properly.');\r\n          // Show alert to user\r\n          this.alertCtrl.create({\r\n            header: 'Google Play Services Required',\r\n            message: 'This app requires Google Play Services for push notifications. Please install or update Google Play Services and restart the app.',\r\n            buttons: ['OK']\r\n          }).then(alert => alert.present());\r\n\r\n          // Store a flag in localStorage\r\n          localStorage.setItem('google_play_services_missing', 'true');\r\n\r\n          // Still continue with the rest of the initialization\r\n          // as some devices might still work without Google Play Services\r\n        } else {\r\n          localStorage.removeItem('google_play_services_missing');\r\n        }\r\n\r\n        if (this.platform.is('capacitor')) {\r\n          try {\r\n            // Request permission for notifications (required for iOS and newer Android)\r\n            const permissionResult = await FirebaseMessaging.requestPermissions();\r\n            console.log('FCM permission result:', permissionResult);\r\n\r\n            if (permissionResult.receive === 'granted') {\r\n              console.log('FCM permission granted');\r\n\r\n              // Register with FCM - no need to call registerDevice explicitly\r\n              // It's automatically registered when permission is granted\r\n              console.log('Device registered with FCM');\r\n            } else {\r\n              console.warn('FCM permission not granted:', permissionResult.receive);\r\n\r\n              // Show alert to user about notification permissions\r\n              this.alertCtrl.create({\r\n                header: 'Notification Permission Required',\r\n                message: 'This app requires notification permissions to alert you about emergencies. Please enable notifications for this app in your device settings.',\r\n                buttons: ['OK']\r\n              }).then(alert => alert.present());\r\n            }\r\n          } catch (error) {\r\n            console.error('Error initializing Capacitor Firebase Messaging:', error);\r\n          }\r\n        }\r\n\r\n        // Get the FCM token\r\n        try {\r\n          const token = await this.getToken();\r\n\r\n          // Log the token for debugging (console only)\r\n          console.log('FCM Token registered:', token.substring(0, 20) + '...');\r\n\r\n          this.registerTokenWithBackend(token);\r\n        } catch (error) {\r\n          console.error('Error getting FCM token:', error);\r\n          // Continue app initialization even if FCM fails\r\n        }\r\n\r\n        try {\r\n          // Handle token refresh for Cordova FCM\r\n          if (this.platform.is('cordova')) {\r\n            this.fcm.onTokenRefresh().subscribe({\r\n              next: token => {\r\n                console.log('FCM Token refreshed (Cordova):', token);\r\n                this.registerTokenWithBackend(token);\r\n              },\r\n              error: error => {\r\n                console.error('Error in token refresh (Cordova):', error);\r\n              }\r\n            });\r\n          }\r\n\r\n          // Handle token refresh for Capacitor Firebase Messaging\r\n          if (this.platform.is('capacitor')) {\r\n            FirebaseMessaging.addListener('tokenReceived', (event: { token: string }) => {\r\n              console.log('FCM Token refreshed (Capacitor):', event.token);\r\n              this.registerTokenWithBackend(event.token);\r\n            });\r\n          }\r\n        } catch (error) {\r\n          console.error('Failed to set up token refresh:', error);\r\n        }\r\n\r\n        // Set up notification handlers\r\n        this.setupNotificationListeners();\r\n      } else {\r\n        console.log('FCM not initialized: not running on a device');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error in initPush:', error);\r\n      // Allow the app to continue even if FCM initialization fails\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get the FCM token for the device\r\n   * @returns Promise with the FCM token\r\n   */\r\n  async getToken(): Promise<string> {\r\n    if (this.platform.is('capacitor')) {\r\n      try {\r\n        // Try Capacitor Firebase Messaging first\r\n        const result = await FirebaseMessaging.getToken();\r\n        console.log('Got FCM token from Capacitor Firebase Messaging:', result.token);\r\n        return result.token;\r\n      } catch (capacitorError) {\r\n        console.error('Error getting token from Capacitor Firebase Messaging:', capacitorError);\r\n\r\n        // Fall back to Cordova FCM plugin\r\n        try {\r\n          const token = await this.fcm.getToken();\r\n          console.log('Got FCM token from Cordova FCM plugin:', token);\r\n          return token;\r\n        } catch (cordovaError) {\r\n          console.error('Error getting token from Cordova FCM plugin:', cordovaError);\r\n          throw cordovaError;\r\n        }\r\n      }\r\n    } else if (this.platform.is('cordova')) {\r\n      // Use Cordova FCM plugin directly\r\n      return this.fcm.getToken();\r\n    } else {\r\n      // For browser testing, return a mock token\r\n      const mockToken = 'browser-mock-token-' + Math.random().toString(36).substring(2, 15);\r\n      console.log('Using mock FCM token for browser:', mockToken);\r\n      return Promise.resolve(mockToken);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Register FCM token with the backend\r\n   * @param token The FCM token to register\r\n   * @param userId Optional user ID to associate with the token\r\n   */\r\n  public registerTokenWithBackend(token: string, userId?: number) {\r\n    // Check if this token is already registered\r\n    const storedToken = localStorage.getItem('fcm_token');\r\n    if (storedToken === token) {\r\n      console.log('Token already registered, skipping registration');\r\n      return;\r\n    }\r\n\r\n    // Determine platform\r\n    let deviceType = 'web';\r\n    if (this.platform.is('ios')) {\r\n      deviceType = 'ios';\r\n    } else if (this.platform.is('android')) {\r\n      deviceType = 'android';\r\n    }\r\n\r\n    console.log(`Registering ${deviceType} token with backend...`);\r\n\r\n    // Include Firebase project ID in the request\r\n    const payload: any = {\r\n      token: token,\r\n      device_type: deviceType,\r\n      project_id: environment.firebase.projectId || 'last-5acaf' // Use correct project ID\r\n    };\r\n\r\n    // If we have a user ID, include it in the payload\r\n    if (userId) {\r\n      payload.user_id = userId;\r\n      console.log(`Associating token with user ID: ${userId}`);\r\n    } else {\r\n      // Try to get user ID from localStorage if available\r\n      const authToken = localStorage.getItem('token');\r\n      if (authToken) {\r\n        try {\r\n          // Try to decode the JWT token to get the user ID\r\n          const tokenData = this.parseJwt(authToken);\r\n          if (tokenData && tokenData.sub) {\r\n            payload.user_id = tokenData.sub;\r\n            console.log(`Associating token with user ID from JWT: ${tokenData.sub}`);\r\n          }\r\n        } catch (error) {\r\n          console.error('Error parsing JWT token:', error);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Only send to backend if we have a valid token and project ID is configured\r\n    if (token && (environment.firebase.projectId || payload.project_id)) {\r\n      // Store the token in localStorage immediately to prevent duplicate registrations\r\n      localStorage.setItem('fcm_token', token);\r\n\r\n      // Set a flag to indicate we're currently registering this token\r\n      localStorage.setItem('fcm_token_registering', 'true');\r\n\r\n      // Send token to your Laravel backend\r\n      this.http.post(`${environment.apiUrl}/device-token`, payload)\r\n        .subscribe({\r\n          next: (res) => {\r\n            console.log('Token registered with backend:', res);\r\n            // Clear the registering flag\r\n            localStorage.removeItem('fcm_token_registering');\r\n          },\r\n          error: (err) => {\r\n            console.error('Error registering token:', err);\r\n\r\n            // Clear the registering flag\r\n            localStorage.removeItem('fcm_token_registering');\r\n\r\n            // Don't retry automatically - this causes too many requests\r\n          }\r\n        });\r\n    } else {\r\n      console.log('Skipping token registration: Missing project ID or token');\r\n      // Still store the token for later use\r\n      if (token) {\r\n        localStorage.setItem('fcm_token', token);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parse a JWT token to get the payload\r\n   * @param token The JWT token to parse\r\n   * @returns The decoded token payload\r\n   */\r\n  private parseJwt(token: string): any {\r\n    try {\r\n      const base64Url = token.split('.')[1];\r\n      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\r\n      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {\r\n        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\r\n      }).join(''));\r\n      return JSON.parse(jsonPayload);\r\n    } catch (error) {\r\n      console.error('Error parsing JWT token:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  private setupNotificationListeners() {\r\n    // Try to set up both Capacitor and Cordova notification listeners for maximum compatibility\r\n    this.setupCapacitorNotificationListeners();\r\n    this.setupCordovaNotificationListeners();\r\n  }\r\n\r\n  private setupCapacitorNotificationListeners() {\r\n    try {\r\n      if (this.platform.is('capacitor')) {\r\n        console.log('Setting up Capacitor Firebase Messaging notification listeners');\r\n\r\n        // Listen for messages received in the foreground\r\n        FirebaseMessaging.addListener('notificationReceived', (event: {\r\n          notification: {\r\n            title?: string;\r\n            body?: string;\r\n            data?: any\r\n          }\r\n        }) => {\r\n          console.log('Capacitor: Notification received in foreground:', event);\r\n          // Extract data first\r\n          const notificationData = event.notification.data || {};\r\n\r\n          // Create notification object with proper properties\r\n          this.processNotification({\r\n            title: event.notification.title || '',\r\n            body: event.notification.body || '',\r\n            category: notificationData.category || '',\r\n            severity: notificationData.severity || 'low',\r\n            wasTapped: false, // Foreground notification\r\n            notification_id: notificationData.notification_id || null,\r\n            time: new Date().toISOString(),\r\n            // Add any other properties that weren't explicitly set above\r\n            ...Object.keys(notificationData)\r\n              .filter(key => !['category', 'severity', 'notification_id'].includes(key))\r\n              .reduce((obj, key) => {\r\n                obj[key] = notificationData[key];\r\n                return obj;\r\n              }, {} as Record<string, any>)\r\n          });\r\n        });\r\n\r\n        // Listen for messages received in the background and tapped\r\n        FirebaseMessaging.addListener('notificationActionPerformed', (event: {\r\n          notification: {\r\n            title?: string;\r\n            body?: string;\r\n            data?: any\r\n          }\r\n        }) => {\r\n          console.log('Capacitor: Notification tapped:', event);\r\n          // Extract data first\r\n          const notificationData = event.notification.data || {};\r\n\r\n          // Create notification object with proper properties\r\n          this.processNotification({\r\n            title: event.notification.title || '',\r\n            body: event.notification.body || '',\r\n            category: notificationData.category || '',\r\n            severity: notificationData.severity || 'low',\r\n            wasTapped: true, // Background notification that was tapped\r\n            notification_id: notificationData.notification_id || null,\r\n            time: new Date().toISOString(),\r\n            // Add any other properties that weren't explicitly set above\r\n            ...Object.keys(notificationData)\r\n              .filter(key => !['category', 'severity', 'notification_id'].includes(key))\r\n              .reduce((obj, key) => {\r\n                obj[key] = notificationData[key];\r\n                return obj;\r\n              }, {} as Record<string, any>)\r\n          });\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to set up Capacitor notification listeners:', error);\r\n    }\r\n  }\r\n\r\n  private setupCordovaNotificationListeners() {\r\n    try {\r\n      if (this.platform.is('cordova')) {\r\n        console.log('Setting up Cordova FCM notification listeners');\r\n\r\n        this.fcm.onNotification().subscribe({\r\n          next: (data) => {\r\n            console.log('Cordova FCM notification received:', data);\r\n            // Extract data first\r\n            const notificationData = { ...data };\r\n\r\n            // Create notification object with proper properties\r\n            this.processNotification({\r\n              title: data['title'] || (data['aps'] && data['aps']['alert'] && data['aps']['alert']['title']) || '',\r\n              body: data['body'] || (data['aps'] && data['aps']['alert'] && data['aps']['alert']['body']) || data['message'] || '',\r\n              category: data['category'] || '',\r\n              severity: data['severity'] || 'low',\r\n              wasTapped: data['wasTapped'] || false,\r\n              notification_id: data['notification_id'] || null,\r\n              time: data['time'] || new Date().toISOString(),\r\n              // Add any other properties that weren't explicitly set above\r\n              ...Object.keys(notificationData)\r\n                .filter(key => !['title', 'body', 'category', 'severity', 'wasTapped', 'notification_id', 'time'].includes(key))\r\n                .reduce((obj, key) => {\r\n                  obj[key] = notificationData[key];\r\n                  return obj;\r\n                }, {} as Record<string, any>)\r\n            });\r\n          },\r\n          error: (error) => {\r\n            console.error('Error in Cordova FCM notification subscription:', error);\r\n          }\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to set up Cordova FCM notification listeners:', error);\r\n    }\r\n  }\r\n\r\n  private processNotification(notification: FCMNotification) {\r\n    try {\r\n      // Log the notification details for debugging\r\n      console.log('Processed notification:', {\r\n        title: notification.title,\r\n        body: notification.body,\r\n        category: notification.category,\r\n        severity: notification.severity,\r\n        wasTapped: notification.wasTapped,\r\n        notification_id: notification.notification_id,\r\n        project_id: environment.firebase.projectId || 'new-firebase-project'\r\n      });\r\n\r\n      // Broadcast the notification to any components that are listening\r\n      this.notificationSubject.next(notification);\r\n\r\n      if (notification.wasTapped) {\r\n        // Notification was received in background and tapped by the user\r\n        console.log('Notification tapped in background');\r\n        this.handleBackgroundNotification(notification);\r\n      } else {\r\n        // Notification was received in foreground\r\n        console.log('Notification received in foreground');\r\n        this.handleForegroundNotification(notification);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error processing notification:', error, notification);\r\n    }\r\n  }\r\n\r\n  private async handleForegroundNotification(notification: FCMNotification) {\r\n    try {\r\n      console.log('🔔 Handling foreground notification:', notification);\r\n\r\n      // Try to vibrate the device (works on most mobile browsers and devices)\r\n      this.vibrateDevice();\r\n\r\n      // Play notification sound\r\n      this.playNotificationSound();\r\n\r\n      // Add a small delay to ensure app is ready for modal\r\n      await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n      // Show custom emergency notification modal for all notifications\r\n      await this.showEmergencyNotificationModal(notification);\r\n\r\n    } catch (error) {\r\n      console.error('❌ Error handling foreground notification:', error);\r\n      // Fallback: show a toast notification instead\r\n      await this.showFallbackToast(notification);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Show custom emergency notification modal with disaster-specific colors\r\n   */\r\n  private async showEmergencyNotificationModal(notification: FCMNotification) {\r\n    try {\r\n      console.log('📱 Creating emergency notification modal...', notification);\r\n\r\n      // Ensure we're in the right zone for Angular\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      // Check if there's already an alert present\r\n      const existingAlert = await this.alertCtrl.getTop();\r\n      if (existingAlert) {\r\n        console.log('⚠️ Alert already present, dismissing first...');\r\n        await existingAlert.dismiss();\r\n        // Wait a bit before showing new alert\r\n        await new Promise(resolve => setTimeout(resolve, 500));\r\n      }\r\n\r\n      // Force show fallback toast first to ensure user sees something\r\n      console.log('🍞 Showing immediate toast notification as backup...');\r\n      await this.showFallbackToast(notification);\r\n\r\n      // Then try to show the modal\r\n      const alert = await this.alertCtrl.create({\r\n        header: notification.title || 'EMERGENCY ALERT',\r\n        subHeader: notification.category ? `${notification.category.toUpperCase()} ALERT` : '',\r\n        message: notification.body || '',\r\n        buttons: [\r\n          {\r\n            text: 'Go to Safe Area',\r\n            cssClass: 'alert-button-primary',\r\n            handler: () => {\r\n              console.log('🗺️ User tapped Go to Safe Area from modal');\r\n              this.navigateBasedOnNotification(notification);\r\n              return true;\r\n            }\r\n          },\r\n          {\r\n            text: 'Dismiss',\r\n            role: 'cancel',\r\n            cssClass: 'alert-button-secondary',\r\n            handler: () => {\r\n              console.log('❌ User dismissed notification modal');\r\n              return true;\r\n            }\r\n          }\r\n        ],\r\n        cssClass: `emergency-notification ${notification.category?.toLowerCase() || 'general'}-alert`,\r\n        backdropDismiss: false, // Prevent accidental dismissal\r\n        keyboardClose: false\r\n      });\r\n\r\n      console.log('✅ Emergency modal created, presenting...');\r\n      await alert.present();\r\n      console.log('✅ Emergency modal presented successfully');\r\n\r\n    } catch (error: any) {\r\n      console.error('❌ Error creating emergency modal:', error);\r\n      console.error('❌ Modal error details:', error?.message, error?.stack);\r\n      // Fallback to toast (already shown above, but ensure it's visible)\r\n      await this.showFallbackToast(notification);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fallback toast notification when modal fails\r\n   */\r\n  private async showFallbackToast(notification: FCMNotification) {\r\n    try {\r\n      console.log('🍞 Showing emergency toast notification');\r\n\r\n      const toast = await this.toastCtrl.create({\r\n        header: `🚨 ${notification.title || 'EMERGENCY ALERT'}`,\r\n        message: notification.body || 'Emergency notification received',\r\n        duration: 8000, // Longer duration for emergency\r\n        position: 'top',\r\n        color: this.getToastColor(notification.category),\r\n        cssClass: 'emergency-toast',\r\n        buttons: [\r\n          {\r\n            text: '🗺️ Go to Safe Area',\r\n            handler: () => {\r\n              console.log('🗺️ User tapped Go to Safe Area from toast');\r\n              this.navigateBasedOnNotification(notification);\r\n              return true;\r\n            }\r\n          },\r\n          {\r\n            text: 'Dismiss',\r\n            role: 'cancel',\r\n            handler: () => {\r\n              console.log('❌ User dismissed toast notification');\r\n              return true;\r\n            }\r\n          }\r\n        ]\r\n      });\r\n\r\n      await toast.present();\r\n      console.log('✅ Emergency toast shown successfully');\r\n\r\n      // Also vibrate for the toast\r\n      this.vibrateDevice();\r\n\r\n    } catch (error: any) {\r\n      console.error('❌ Even fallback toast failed:', error);\r\n      // Last resort: show a simple alert\r\n      try {\r\n        const simpleAlert = await this.alertCtrl.create({\r\n          header: '🚨 EMERGENCY ALERT',\r\n          message: `${notification.title}\\n\\n${notification.body}`,\r\n          buttons: [\r\n            {\r\n              text: 'Go to Safe Area',\r\n              handler: () => this.navigateBasedOnNotification(notification)\r\n            },\r\n            'Dismiss'\r\n          ]\r\n        });\r\n        await simpleAlert.present();\r\n        console.log('✅ Simple alert shown as last resort');\r\n      } catch (finalError) {\r\n        console.error('❌ All notification methods failed:', finalError);\r\n        console.log('📢 EMERGENCY NOTIFICATION (all display methods failed):', notification);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get toast color based on disaster category\r\n   */\r\n  private getToastColor(category?: string): string {\r\n    if (!category) return 'warning';\r\n\r\n    switch (category.toLowerCase()) {\r\n      case 'earthquake': return 'warning';\r\n      case 'flood': return 'primary';\r\n      case 'typhoon': return 'success';\r\n      case 'fire': return 'danger';\r\n      default: return 'warning';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Test method to simulate foreground notification\r\n   */\r\n  async simulateForegroundNotification(notification: FCMNotification): Promise<void> {\r\n    console.log('🧪 Simulating foreground notification:', notification);\r\n    await this.processNotification(notification);\r\n  }\r\n\r\n  /**\r\n   * Test method to simulate background notification\r\n   */\r\n  async simulateBackgroundNotification(notification: FCMNotification): Promise<void> {\r\n    console.log('🧪 Simulating background notification:', notification);\r\n    notification.wasTapped = true; // Mark as tapped\r\n    await this.processNotification(notification);\r\n  }\r\n\r\n  /**\r\n   * Get disaster-specific styling\r\n   */\r\n  private getDisasterStyle(category?: string): { color: string, icon: string } {\r\n    if (!category) {\r\n      return { color: '#666666', icon: 'notifications-outline' };\r\n    }\r\n\r\n    const type = category.toLowerCase();\r\n\r\n    if (type.includes('earthquake') || type.includes('quake')) {\r\n      return { color: '#ffa500', icon: 'earth-outline' }; // Orange for earthquake\r\n    } else if (type.includes('flood') || type.includes('flash')) {\r\n      return { color: '#0000ff', icon: 'water-outline' }; // Blue for flood\r\n    } else if (type.includes('typhoon') || type.includes('storm') || type.includes('hurricane')) {\r\n      return { color: '#008000', icon: 'thunderstorm-outline' }; // Green for typhoon\r\n    } else if (type.includes('fire')) {\r\n      return { color: '#ff0000', icon: 'flame-outline' }; // Red for fire\r\n    }\r\n\r\n    return { color: '#666666', icon: 'alert-circle-outline' }; // Default gray\r\n  }\r\n\r\n  /**\r\n   * Vibrate the device if the API is available\r\n   */\r\n  private vibrateDevice() {\r\n    // Check if the vibration API is available\r\n    if ('vibrate' in navigator) {\r\n      // Vibrate for 1000ms, pause for 200ms, then vibrate for 1000ms (stronger pattern)\r\n      navigator.vibrate([1000, 200, 1000, 200, 1000]);\r\n      console.log('Device vibration triggered with strong pattern');\r\n    } else {\r\n      console.log('Vibration API not supported on this device');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Play a notification sound\r\n   */\r\n  private playNotificationSound() {\r\n    try {\r\n      // Create an audio element\r\n      const audio = new Audio();\r\n\r\n      // Set the source to a notification sound (use a default system sound)\r\n      audio.src = 'data:audio/wav;base64,UklGRl9vT19XQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YU...';\r\n\r\n      // Set volume to maximum\r\n      audio.volume = 1.0;\r\n\r\n      // Play the sound\r\n      audio.play().catch(error => {\r\n        console.error('Error playing notification sound:', error);\r\n      });\r\n    } catch (error) {\r\n      console.error('Error creating audio element:', error);\r\n    }\r\n  }\r\n\r\n  // Removed duplicate function\r\n\r\n  private async handleBackgroundNotification(notification: FCMNotification) {\r\n    try {\r\n      console.log('🔔 Handling background notification tap:', notification);\r\n\r\n      // When a background notification is tapped, we should vibrate to provide feedback\r\n      this.vibrateDevice();\r\n\r\n      // Add delay to ensure app is fully loaded\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n      // Show the emergency modal even for background notifications when app opens\r\n      await this.showEmergencyNotificationModal(notification);\r\n\r\n      // Handle navigation based on the notification (after modal is dismissed)\r\n      // Note: Navigation will be handled by the modal buttons\r\n\r\n    } catch (error) {\r\n      console.error('❌ Error handling background notification:', error);\r\n      // Fallback: just navigate directly\r\n      this.navigateBasedOnNotification(notification);\r\n    }\r\n  }\r\n\r\n  private navigateBasedOnNotification(notification: FCMNotification) {\r\n    console.log('🗺️ Navigating based on notification:', notification);\r\n\r\n    // Check if this is a new evacuation center notification\r\n    if (notification.category === 'evacuation_center' ||\r\n        (notification['data'] && notification['data']['type'] === 'evacuation_center_added')) {\r\n      console.log('🏢 New evacuation center notification detected');\r\n      this.handleEvacuationCenterNotification(notification);\r\n      return;\r\n    }\r\n\r\n    // Navigate based on notification category or type\r\n    if (notification.category) {\r\n      const category = notification.category.toLowerCase();\r\n      console.log('📍 Notification category:', category);\r\n\r\n      // Map to backend enum values that the map page expects\r\n      let mappedDisasterType = '';\r\n\r\n      switch(category) {\r\n        case 'flood':\r\n        case 'flashflood':\r\n          mappedDisasterType = 'Flood';\r\n          break;\r\n        case 'earthquake':\r\n        case 'quake':\r\n          mappedDisasterType = 'Earthquake';\r\n          break;\r\n        case 'typhoon':\r\n        case 'storm':\r\n        case 'hurricane':\r\n          mappedDisasterType = 'Typhoon';\r\n          break;\r\n        case 'fire':\r\n          mappedDisasterType = 'Fire';\r\n          break;\r\n        default:\r\n          console.warn('Unknown disaster category:', category);\r\n          mappedDisasterType = 'all';\r\n          break;\r\n      }\r\n\r\n      console.log(`🗺️ Mapped disaster type: ${category} -> ${mappedDisasterType}`);\r\n\r\n      if (mappedDisasterType && mappedDisasterType !== 'all') {\r\n        // Navigate directly to disaster-specific map pages\r\n        console.log('🗺️ Navigating to disaster-specific map:', mappedDisasterType);\r\n\r\n        let route = '';\r\n        switch (mappedDisasterType.toLowerCase()) {\r\n          case 'earthquake':\r\n            route = '/earthquake-map';\r\n            break;\r\n          case 'typhoon':\r\n            route = '/typhoon-map';\r\n            break;\r\n          case 'flood':\r\n            route = '/flood-map';\r\n            break;\r\n          default:\r\n            route = '/tabs/map';\r\n            break;\r\n        }\r\n\r\n        console.log(`🗺️ Navigating to route: ${route}`);\r\n        this.router.navigate([route]);\r\n      } else {\r\n        // Default navigation to home\r\n        console.log('🏠 Navigating to home (unknown disaster type)');\r\n        this.router.navigate(['/tabs/home']);\r\n      }\r\n    } else {\r\n      // Default navigation if no category\r\n      console.log('🏠 Navigating to home (no category)');\r\n      this.router.navigate(['/tabs/home']);\r\n    }\r\n  }\r\n\r\n  private handleEvacuationCenterNotification(notification: FCMNotification) {\r\n    console.log('🏢 Handling evacuation center notification:', notification);\r\n\r\n    try {\r\n      // Extract evacuation center data from notification\r\n      let evacuationData = null;\r\n\r\n      if (notification['data']) {\r\n        evacuationData = notification['data'];\r\n      }\r\n\r\n      if (evacuationData && evacuationData.evacuation_center_id) {\r\n        const centerId = evacuationData.evacuation_center_id;\r\n        const disasterType = evacuationData.disaster_type;\r\n        const latitude = evacuationData.location?.latitude;\r\n        const longitude = evacuationData.location?.longitude;\r\n\r\n        console.log(`🏢 Evacuation center details:`, {\r\n          id: centerId,\r\n          disasterType: disasterType,\r\n          lat: latitude,\r\n          lng: longitude\r\n        });\r\n\r\n        // Navigate to the appropriate disaster-specific map based on disaster type\r\n        let route = '/all-maps'; // Default to all maps\r\n\r\n        if (disasterType) {\r\n          switch (disasterType.toLowerCase()) {\r\n            case 'earthquake':\r\n              route = '/earthquake-map';\r\n              break;\r\n            case 'typhoon':\r\n              route = '/typhoon-map';\r\n              break;\r\n            case 'flood':\r\n            case 'flash flood':\r\n              route = '/flood-map';\r\n              break;\r\n            default:\r\n              route = '/all-maps';\r\n              break;\r\n          }\r\n        }\r\n\r\n        console.log(`🗺️ Navigating to ${route} for new evacuation center`);\r\n\r\n        // Navigate with query parameters to highlight the new center\r\n        this.router.navigate([route], {\r\n          queryParams: {\r\n            newCenterId: centerId,\r\n            highlightCenter: 'true',\r\n            centerLat: latitude,\r\n            centerLng: longitude\r\n          }\r\n        });\r\n\r\n      } else {\r\n        console.log('🏢 No evacuation center data found, navigating to all maps');\r\n        this.router.navigate(['/all-maps']);\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('❌ Error handling evacuation center notification:', error);\r\n      // Fallback to all maps\r\n      this.router.navigate(['/all-maps']);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if Google Play Services is available on the device\r\n   * @returns Promise<boolean> True if Google Play Services is available\r\n   */\r\n  private async checkGooglePlayServices(): Promise<boolean> {\r\n    try {\r\n      // For Capacitor, we can try to initialize Firebase Messaging\r\n      // If it fails with a specific error, it might be due to missing Google Play Services\r\n      if (this.platform.is('capacitor') && this.platform.is('android')) {\r\n        try {\r\n          // Try to get the token - this will fail if Google Play Services is not available\r\n          await FirebaseMessaging.getToken();\r\n          return true;\r\n        } catch (error: any) {\r\n          console.error('Error checking Google Play Services:', error);\r\n\r\n          // Check for specific error messages that indicate Google Play Services issues\r\n          const errorMessage = error.message || '';\r\n          if (\r\n            errorMessage.includes('Google Play Services') ||\r\n            errorMessage.includes('GoogleApiAvailability') ||\r\n            errorMessage.includes('API unavailable')\r\n          ) {\r\n            return false;\r\n          }\r\n\r\n          // If it's some other error, we assume Google Play Services is available\r\n          // but there's another issue\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // For non-Android platforms or non-Capacitor, assume Google Play Services is available\r\n      // as it's not required on iOS or in web browsers\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error in checkGooglePlayServices:', error);\r\n      // Default to true to avoid blocking the app functionality\r\n      return true;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create notification channels for Android\r\n   * This is required for notifications to display properly on Android 8.0+\r\n   */\r\n  private async createAndroidNotificationChannels() {\r\n    try {\r\n      if (this.platform.is('android')) {\r\n        console.log('Creating Android notification channels');\r\n\r\n        // For Android, we need to create notification channels\r\n        // This is done through the native layer\r\n\r\n        // We'll use a workaround since we don't have direct access to the LocalNotifications plugin\r\n        // Send a test notification to create the channel\r\n\r\n        // High priority channel for emergency alerts\r\n        await this.sendTestChannelNotification(\r\n          'emergency-alerts',\r\n          'Emergency Alerts',\r\n          'High priority notifications for emergencies',\r\n          'high'\r\n        );\r\n\r\n        // Standard channel for general notifications\r\n        await this.sendTestChannelNotification(\r\n          'general-notifications',\r\n          'General Notifications',\r\n          'Standard notifications',\r\n          'default'\r\n        );\r\n\r\n        console.log('Android notification channels created successfully');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error creating Android notification channels:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Send a test notification to create a channel\r\n   * This is a workaround to create notification channels on Android\r\n   */\r\n  private async sendTestChannelNotification(\r\n    channelId: string,\r\n    channelName: string,\r\n    channelDescription: string,\r\n    importance: 'high' | 'default' | 'low'\r\n  ) {\r\n    try {\r\n      // Use Firebase Messaging to create a channel\r\n      // This is done by sending a notification with the channel ID\r\n\r\n      // Create a notification payload with the channel ID\r\n      const payload = {\r\n        notification: {\r\n          title: 'Channel Setup',\r\n          body: 'Setting up notification channels',\r\n          android: {\r\n            channelId: channelId,\r\n            priority: importance === 'high' ? 'high' : (importance === 'default' ? 'default' : 'low'),\r\n            sound: importance !== 'low',\r\n            vibrate: importance !== 'low',\r\n            visibility: 'public'\r\n          }\r\n        }\r\n      };\r\n\r\n      // Log the channel creation\r\n      console.log(`Created notification channel: ${channelId} (${channelName})`);\r\n\r\n    } catch (error) {\r\n      console.error(`Error creating notification channel ${channelId}:`, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Refresh FCM token and re-register with backend\r\n   * This can be called when the user is having trouble receiving notifications\r\n   * @param userId Optional user ID to associate with the token\r\n   * @returns Promise<boolean> True if refresh was successful\r\n   */\r\n  public async refreshFCMToken(userId?: number): Promise<boolean> {\r\n    try {\r\n      console.log('Refreshing FCM token...');\r\n\r\n      // For Capacitor\r\n      if (this.platform.is('capacitor')) {\r\n        try {\r\n          // Delete the existing token\r\n          await FirebaseMessaging.deleteToken();\r\n          console.log('Existing FCM token deleted');\r\n\r\n          // Get a new token\r\n          const result = await FirebaseMessaging.getToken();\r\n          console.log('New FCM token obtained:', result.token);\r\n\r\n          // Register the new token with the backend\r\n          this.registerTokenWithBackend(result.token, userId);\r\n\r\n          return true;\r\n        } catch (error) {\r\n          console.error('Error refreshing Capacitor FCM token:', error);\r\n          return false;\r\n        }\r\n      }\r\n      // For Cordova\r\n      else if (this.platform.is('cordova')) {\r\n        try {\r\n          // For Cordova, we can't delete the token, but we can get a new one\r\n          const token = await this.fcm.getToken();\r\n          console.log('New FCM token obtained from Cordova:', token);\r\n\r\n          // Register the new token with the backend\r\n          this.registerTokenWithBackend(token, userId);\r\n\r\n          return true;\r\n        } catch (error) {\r\n          console.error('Error refreshing Cordova FCM token:', error);\r\n          return false;\r\n        }\r\n      }\r\n      // For browser testing\r\n      else {\r\n        // Generate a new mock token\r\n        const mockToken = 'browser-mock-token-' + Math.random().toString(36).substring(2, 15);\r\n        console.log('New mock FCM token generated:', mockToken);\r\n\r\n        // Register the mock token with the backend\r\n        this.registerTokenWithBackend(mockToken, userId);\r\n\r\n        return true;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error in refreshFCMToken:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,SAAS,aAAa;AAC3B,MAAI,OAAO,YAAY,aAAa;AAClC,QAAI,QAAQ,OAAO,WAAW,cAAc,SAAS,CAAC;AACtD,QAAI,yBAAyB;AAK7B,QAAI,WAAW,KAAK,IAAI;AACxB,QAAI,iBAAiB;AACrB,UAAM,SAAS,iBAAiB,eAAe,WAAY;AACzD,cAAQ,IAAI,kDAAkD,KAAK,IAAI,IAAI,YAAY,KAAK;AAC5F,uBAAiB;AAAA,IACnB,CAAC;AACD,eAAW,WAAY;AACrB,UAAI,CAAC,kBAAkB,MAAM,SAAS;AACpC,gBAAQ,KAAK,mDAAmD,yBAAyB,0HAA0H;AAAA,MACrN;AAAA,IACF,GAAG,sBAAsB;AAAA,EAC3B;AACF;;;ACtBO,IAAI,4BAA4B;AAAA,EACrC,OAAO;AACT;AACO,IAAI,2BAA2B;AAAA,EACpC,OAAO;AACT;AAIO,SAAS,WAAW,UAAU;AACnC,MAAI,mBAAmB,WAAY;AACjC,QAAI,SAAS;AACX,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,iBAAS,SAAS,MAAM;AAAA,MAC1B,CAAC;AAAA,IACH,OAAO;AACL,cAAQ,MAAM,0LAA0L;AAAA,IAC1M;AAAA,EACF;AACA,MAAI,OAAO,WAAW,eAAe,OAAO,SAAS;AACnD,QAAI,MAAM,OAAO;AACjB,QAAI,WAAW,OAAO,QAAQ,QAAQ,IAAI,cAAc,UAAU,KAAK,IAAI,IAAI,EAAE,SAAS;AAC1F,QAAI,UAAU;AACZ,UAAI,KAAK,SAAS,IAAI,IAAI;AAC1B,aAAO,GAAG,SAAU,SAAS,QAAQ;AACnC,iBAAS,SAAS,MAAM;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,YAAQ,KAAK,+NAA+N;AAAA,EAC9O;AACA,SAAO,iBAAiB;AAC1B;AAOO,SAAS,YAAY,WAAW,YAAY,MAAM,MAAM;AAC7D,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,cAAc;AAClB,MAAI,IAAI,WAAW,SAAU,SAAS,QAAQ;AAC5C,QAAI,KAAK,UAAU;AACjB,qBAAe,kBAAkB,WAAW,YAAY,MAAM,MAAM,WAAY;AAC9E,YAAIA,QAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,UAAAA,MAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QACzB;AACA,eAAO,QAAQA,KAAI;AAAA,MACrB,GAAG,WAAY;AACb,YAAIA,QAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,UAAAA,MAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QACzB;AACA,eAAO,OAAOA,KAAI;AAAA,MACpB,CAAC;AAAA,IACH,OAAO;AACL,qBAAe,kBAAkB,WAAW,YAAY,MAAM,MAAM,SAAS,MAAM;AAAA,IACrF;AACA,UAAM;AAAA,EACR,CAAC;AAID,MAAI,gBAAgB,aAAa,OAAO;AACtC,MAAE,MAAM,WAAY;AAAA,IAAC,CAAC;AACtB,WAAO,QAAQ,cAAc,IAAI,aAAa,KAAK;AAAA,EACrD;AACA,SAAO;AACT;AAOA,SAAS,iBAAiB,WAAW,YAAY,MAAM,MAAM;AAC3D,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,SAAO,WAAW,SAAU,SAAS,QAAQ;AAC3C,QAAI,eAAe,kBAAkB,WAAW,YAAY,MAAM,IAAI;AACtE,QAAI,cAAc;AAChB,UAAI,aAAa,OAAO;AACtB,eAAO,aAAa,KAAK;AAAA,MAC3B,WAAW,aAAa,MAAM;AAC5B,qBAAa,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,MACzC;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AAOA,SAAS,eAAe,WAAW,YAAY,MAAM,MAAM;AACzD,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,SAAO,IAAI,WAAW,SAAU,UAAU;AACxC,QAAI;AACJ,QAAI,KAAK,UAAU;AACjB,qBAAe,kBAAkB,WAAW,YAAY,MAAM,MAAM,WAAY;AAC9E,YAAIA,QAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,UAAAA,MAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QACzB;AACA,eAAO,SAAS,KAAKA,KAAI;AAAA,MAC3B,GAAG,WAAY;AACb,YAAIA,QAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,UAAAA,MAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QACzB;AACA,eAAO,SAAS,MAAMA,KAAI;AAAA,MAC5B,CAAC;AAAA,IACH,OAAO;AACL,qBAAe,kBAAkB,WAAW,YAAY,MAAM,MAAM,SAAS,KAAK,KAAK,QAAQ,GAAG,SAAS,MAAM,KAAK,QAAQ,CAAC;AAAA,IACjI;AACA,QAAI,gBAAgB,aAAa,OAAO;AACtC,eAAS,MAAM,aAAa,KAAK;AACjC,eAAS,SAAS;AAAA,IACpB;AACA,WAAO,WAAY;AACjB,UAAI;AACF,YAAI,KAAK,eAAe;AACtB,cAAI,KAAK,eAAe;AACtB,mBAAO,kBAAkB,WAAW,KAAK,eAAe,MAAM,MAAM,SAAS,KAAK,KAAK,QAAQ,GAAG,SAAS,MAAM,KAAK,QAAQ,CAAC;AAAA,UACjI;AACA,iBAAO,kBAAkB,WAAW,KAAK,eAAe,CAAC,CAAC;AAAA,QAC5D;AAAA,MACF,SAAS,GAAG;AACV,gBAAQ,KAAK,qDAAqD,UAAU,YAAY,cAAc,GAAG,UAAU;AACnH,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAAA,IACF;AAAA,EACF,CAAC;AACH;AASA,SAAS,oBAAoB,OAAO,SAAS;AAC3C,YAAU,OAAO,WAAW,eAAe,UAAU,IAAI,QAAQ,OAAO,IAAI,YAAY,OAAO,WAAW,cAAc,SAAS,CAAC;AAClI,SAAO,UAAU,SAAS,KAAK;AACjC;AAMO,SAAS,kBAAkB,QAAQ,YAAY,YAAY;AAChE,MAAI,WAAW;AACf,MAAI,OAAO,WAAW,UAAU;AAC9B,gBAAY;AAAA,EACd,OAAO;AACL,gBAAY,OAAO,YAAY,aAAa;AAC5C,iBAAa,OAAO,YAAY,cAAc;AAC9C,oBAAgB,OAAO,YAAY,qBAAqB;AAAA,EAC1D;AACA,MAAI,iBAAiB,UAAU,SAAS;AACxC,MAAI,CAAC,kBAAkB,CAAC,CAAC,cAAc,OAAO,eAAe,UAAU,MAAM,aAAa;AACxF,QAAI,OAAO,WAAW,eAAe,CAAC,OAAO,SAAS;AACpD,kBAAY,YAAY,UAAU;AAClC,aAAO;AAAA,IACT;AACA,eAAW,YAAY,eAAe,UAAU;AAChD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAiBO,SAAS,SAAS,MAAM,MAAM,SAAS,QAAQ;AACpD,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,KAAK,MAAM;AACb,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,kBAAkB,WAAW;AAEpC,SAAK,QAAQ,MAAM;AACnB,SAAK,QAAQ,OAAO;AAAA,EACtB,WAAW,KAAK,kBAAkB,QAAQ;AACxC,SAAK,KAAK,SAAU,KAAK,QAAQ;AAC/B,UAAI,KAAK;AACP,eAAO,GAAG;AAAA,MACZ,OAAO;AACL,gBAAQ,MAAM;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH,WAAW,KAAK,kBAAkB,YAAY,KAAK,eAAe,KAAK,WAAW;AAChF,QAAI,MAAM,CAAC;AACX,QAAI,KAAK,WAAW,IAAI;AACxB,QAAI,KAAK,SAAS,IAAI;AACtB,SAAK,KAAK,GAAG;AAAA,EACf,WAAW,OAAO,KAAK,iBAAiB,eAAe,OAAO,KAAK,eAAe,aAAa;AAC7F,QAAI,kBAAkB,WAAY;AAEhC,UAAI,KAAK,eAAe,KAAK,QAAQ;AACnC,aAAK,KAAK,YAAY,IAAI;AAAA,MAC5B,OAAO;AACL,aAAK,OAAO,KAAK,cAAc,GAAG,OAAO;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,gBAAgB,WAAY;AAG9B,UAAI,KAAK,aAAa,KAAK,QAAQ;AACjC,aAAK,KAAK,UAAU,IAAI;AAAA,MAC1B,OAAO;AACL,aAAK,OAAO,KAAK,YAAY,GAAG,MAAM;AAAA,MACxC;AAAA,IACF;AACA,QAAI,KAAK,eAAe,KAAK,YAAY;AACvC,oBAAc;AACd,sBAAgB;AAAA,IAClB,OAAO;AACL,sBAAgB;AAChB,oBAAc;AAAA,IAChB;AAAA,EACF,OAAO;AAGL,SAAK,KAAK,OAAO;AACjB,SAAK,KAAK,MAAM;AAAA,EAClB;AACA,SAAO;AACT;AASO,SAAS,kBAAkB,WAAW,YAAY,MAAM,MAAM,SAAS,QAAQ;AACpF,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAGA,SAAO,SAAS,MAAM,MAAM,SAAS,MAAM;AAC3C,MAAI,oBAAoB,kBAAkB,WAAW,UAAU;AAC/D,MAAI,sBAAsB,MAAM;AAC9B,QAAI,iBAAiB,UAAU,UAAU,YAAY,aAAa,CAAC;AAEnE,WAAO,eAAe,UAAU,EAAE,MAAM,gBAAgB,IAAI;AAAA,EAC9D,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAsBO,SAAS,UAAU,WAAW;AACnC,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO,IAAI,QAAQ,SAAS;AAAA,EAC9B;AACA,SAAO;AACT;AAKO,SAAS,IAAI,SAAS,MAAM;AACjC,MAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,UAAM,IAAI,MAAM,CAAC,CAAC;AAAA,EACpB;AACA,SAAO;AACT;AAMO,SAAS,WAAW,YAAY,QAAQ,QAAQ;AACrD,MAAI,QAAQ;AACV,YAAQ,KAAK,2BAA2B,aAAa,MAAM,SAAS,eAAe,aAAa,2BAA2B;AAAA,EAC7H,OAAO;AACL,YAAQ,KAAK,iCAAiC,aAAa,iCAAiC;AAAA,EAC9F;AACA,MAAI,QAAQ;AACV,YAAQ,KAAK,iBAAiB,aAAa,wCAAwC,SAAS,GAAG;AAAA,EACjG;AACF;AAMO,SAAS,YAAY,YAAY,QAAQ;AAC9C,MAAI,OAAO,YAAY,aAAa;AAClC,QAAI,QAAQ;AACV,cAAQ,KAAK,2BAA2B,aAAa,MAAM,SAAS,8FAA8F;AAAA,IACpK,OAAO;AACL,cAAQ,KAAK,iCAAiC,aAAa,oGAAoG;AAAA,IACjK;AAAA,EACF;AACF;AAOO,IAAI,OAAO,SAAU,WAAW,YAAY,MAAM;AACvD,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,SAAO,WAAY;AACjB,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IACzB;AACA,QAAI,KAAK,MAAM;AAEb,aAAO,kBAAkB,WAAW,YAAY,MAAM,IAAI;AAAA,IAC5D,WAAW,KAAK,YAAY;AAC1B,aAAO,eAAe,WAAW,YAAY,MAAM,IAAI;AAAA,IACzD,WAAW,KAAK,mBAAmB,KAAK,OAAO;AAC7C,aAAO,oBAAoB,KAAK,OAAO,KAAK,OAAO;AAAA,IACrD,WAAW,KAAK,cAAc;AAC5B,aAAO,iBAAiB,WAAW,YAAY,MAAM,IAAI;AAAA,IAC3D,OAAO;AACL,aAAO,YAAY,WAAW,YAAY,MAAM,IAAI;AAAA,IACtD;AAAA,EACF;AACF;;;ACxXO,SAASC,KAAI,SAAS,MAAM;AACjC,MAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,UAAM,IAAI,MAAM,CAAC,CAAC;AAAA,EACpB;AACA,SAAO;AACT;;;ACbA,IAAI;AAAA;AAAA,EAA0C,WAAY;AACxD,aAASC,8BAA6B;AAAA,IAAC;AAMvC,IAAAA,4BAA2B,YAAY,WAAY;AACjD,UAAI,cAAc,kBAAkB,KAAK,SAAS,MAAM;AACxD,aAAO;AAAA,IACT;AAIA,IAAAA,4BAA2B,YAAY,WAAY;AACjD,UAAI,OAAO,WAAW,aAAa;AACjC,eAAOC,KAAI,QAAQ,KAAK,SAAS;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AAIA,IAAAD,4BAA2B,gBAAgB,WAAY;AACrD,UAAI,aAAa,KAAK;AACtB,aAAO;AAAA,IACT;AAIA,IAAAA,4BAA2B,eAAe,WAAY;AACpD,UAAI,YAAY,KAAK;AACrB,aAAO;AAAA,IACT;AAIA,IAAAA,4BAA2B,uBAAuB,WAAY;AAC5D,UAAI,SAAS,KAAK;AAClB,aAAO;AAAA,IACT;AAIA,IAAAA,4BAA2B,wBAAwB,WAAY;AAC7D,UAAI,WAAW,KAAK;AACpB,aAAO;AAAA,IACT;AACA,IAAAA,4BAA2B,aAAa;AACxC,IAAAA,4BAA2B,YAAY;AACvC,IAAAA,4BAA2B,SAAS;AACpC,IAAAA,4BAA2B,OAAO;AAClC,IAAAA,4BAA2B,YAAY,CAAC;AACxC,IAAAA,4BAA2B,UAAU;AACrC,WAAOA;AAAA,EACT,EAAE;AAAA;;;AClDK,SAAS,QAAQ,WAAW,YAAY,QAAQ,MAAM;AAC3D,SAAO,KAAK,WAAW,YAAY,MAAM,EAAE,MAAM,MAAM,IAAI;AAC7D;;;ACCA,WAAW;;;ACLX,IAAI;AAAA;AAAA,EAAmB,SAAU,QAAQ;AACvC,cAAUE,MAAK,MAAM;AACrB,aAASA,OAAM;AACb,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,IAAAA,KAAI,UAAU,eAAe,WAAY;AACvC,aAAO,QAAQ,MAAM,gBAAgB,CAAC,GAAG,SAAS;AAAA,IACpD;AACA,IAAAA,KAAI,UAAU,WAAW,WAAY;AACnC,aAAO,QAAQ,MAAM,YAAY,CAAC,GAAG,SAAS;AAAA,IAChD;AACA,IAAAA,KAAI,UAAU,iBAAiB,WAAY;AACzC,aAAO,QAAQ,MAAM,kBAAkB;AAAA,QACrC,cAAc;AAAA,MAChB,GAAG,SAAS;AAAA,IACd;AACA,IAAAA,KAAI,UAAU,mBAAmB,SAAU,OAAO;AAChD,aAAO,QAAQ,MAAM,oBAAoB,CAAC,GAAG,SAAS;AAAA,IACxD;AACA,IAAAA,KAAI,UAAU,uBAAuB,SAAU,OAAO;AACpD,aAAO,QAAQ,MAAM,wBAAwB,CAAC,GAAG,SAAS;AAAA,IAC5D;AACA,IAAAA,KAAI,UAAU,gBAAgB,WAAY;AACxC,aAAO,QAAQ,MAAM,iBAAiB,CAAC,GAAG,SAAS;AAAA,IACrD;AACA,IAAAA,KAAI,UAAU,iBAAiB,WAAY;AACzC,aAAO,QAAQ,MAAM,kBAAkB;AAAA,QACrC,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB,GAAG,SAAS;AAAA,IACd;AACA,IAAAA,KAAI,UAAU,wBAAwB,WAAY;AAChD,aAAO,QAAQ,MAAM,yBAAyB,CAAC,GAAG,SAAS;AAAA,IAC7D;AACA,IAAAA,KAAI,UAAU,2BAA2B,SAAU,SAAS;AAC1D,aAAO,QAAQ,MAAM,4BAA4B,CAAC,GAAG,SAAS;AAAA,IAChE;AACA,IAAAA,KAAI,UAAU,mCAAmC,SAAU,eAAe;AACxE,aAAO,QAAQ,MAAM,oCAAoC,CAAC,GAAG,SAAS;AAAA,IACxE;AACA,IAAAA,KAAI,YAAuB,uBAAM;AAC/B,UAAI;AACJ,aAAO,SAAS,YAAY,mBAAmB;AAC7C,gBAAQ,0BAAqB,wBAAsB,gCAAsBA,IAAG,IAAI,qBAAqBA,IAAG;AAAA,MAC1G;AAAA,IACF,GAAG;AACH,IAAAA,KAAI,aAAuB,gBAAG,6BAAmB;AAAA,MAC/C,OAAOA;AAAA,MACP,SAASA,KAAI;AAAA,IACf,CAAC;AACD,IAAAA,KAAI,aAAa;AACjB,IAAAA,KAAI,SAAS;AACb,IAAAA,KAAI,YAAY;AAChB,IAAAA,KAAI,OAAO;AACX,IAAAA,KAAI,YAAY,CAAC,WAAW,KAAK;AACjC,IAAAA,OAAM,WAAW,CAAC,GAAGA,IAAG;AACxB,WAAOA;AAAA,EACT,EAAE,0BAA0B;AAAA;CAE3B,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,KAAK,CAAC;AAAA,IAC5E,MAAM;AAAA,EACR,CAAC,GAAG,MAAM;AAAA,IACR,cAAc,CAAC;AAAA,IACf,UAAU,CAAC;AAAA,IACX,gBAAgB,CAAC;AAAA,IACjB,kBAAkB,CAAC;AAAA,IACnB,sBAAsB,CAAC;AAAA,IACvB,eAAe,CAAC;AAAA,IAChB,gBAAgB,CAAC;AAAA,IACjB,uBAAuB,CAAC;AAAA,IACxB,0BAA0B,CAAC;AAAA,IAC3B,kCAAkC,CAAC;AAAA,EACrC,CAAC;AACH,GAAG;;;ACxEI,IAAI;AAAA,CACV,SAAUC,aAAY;AAIrB,EAAAA,YAAWA,YAAW,KAAK,IAAI,CAAC,IAAI;AAIpC,EAAAA,YAAWA,YAAW,KAAK,IAAI,CAAC,IAAI;AAIpC,EAAAA,YAAWA,YAAW,SAAS,IAAI,CAAC,IAAI;AAIxC,EAAAA,YAAWA,YAAW,MAAM,IAAI,CAAC,IAAI;AAIrC,EAAAA,YAAWA,YAAW,KAAK,IAAI,CAAC,IAAI;AACtC,GAAG,eAAe,aAAa,CAAC,EAAE;AAQ3B,IAAI;AAAA,CACV,SAAUC,aAAY;AAIrB,EAAAA,YAAWA,YAAW,QAAQ,IAAI,EAAE,IAAI;AAIxC,EAAAA,YAAWA,YAAW,SAAS,IAAI,CAAC,IAAI;AAIxC,EAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACzC,GAAG,eAAe,aAAa,CAAC,EAAE;;;ACnDlC,IAAM,oBAAoB,eAAe,qBAAqB;AAAA,EAC5D,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,qBAAqB,CAAC;AACnE,CAAC;;;ACuBK,IAAO,aAAP,MAAO,YAAU;EAIrB,YACU,KACA,MACA,UACA,WACA,WACA,QAAc;AALd,SAAA,MAAA;AACA,SAAA,OAAA;AACA,SAAA,WAAA;AACA,SAAA,YAAA;AACA,SAAA,YAAA;AACA,SAAA,SAAA;AATF,SAAA,sBAAsB,IAAI,QAAO;AAClC,SAAA,iBAAiB,KAAK,oBAAoB,aAAY;EAS1D;EAEG,WAAQ;;AACZ,UAAI;AAEF,YAAI,KAAK,SAAS,GAAG,SAAS,KAAK,KAAK,SAAS,GAAG,WAAW,GAAG;AAChE,kBAAQ,IAAI,qBAAqB;AAGjC,cAAI,KAAK,SAAS,GAAG,SAAS,GAAG;AAC/B,kBAAM,KAAK,kCAAiC;UAC9C;AAGA,gBAAM,wBAAwB,MAAM,KAAK,wBAAuB;AAChE,cAAI,CAAC,uBAAuB;AAC1B,oBAAQ,KAAK,gEAAgE;AAE7E,iBAAK,UAAU,OAAO;cACpB,QAAQ;cACR,SAAS;cACT,SAAS,CAAC,IAAI;aACf,EAAE,KAAK,WAAS,MAAM,QAAO,CAAE;AAGhC,yBAAa,QAAQ,gCAAgC,MAAM;UAI7D,OAAO;AACL,yBAAa,WAAW,8BAA8B;UACxD;AAEA,cAAI,KAAK,SAAS,GAAG,WAAW,GAAG;AACjC,gBAAI;AAEF,oBAAM,mBAAmB,MAAM,kBAAkB,mBAAkB;AACnE,sBAAQ,IAAI,0BAA0B,gBAAgB;AAEtD,kBAAI,iBAAiB,YAAY,WAAW;AAC1C,wBAAQ,IAAI,wBAAwB;AAIpC,wBAAQ,IAAI,4BAA4B;cAC1C,OAAO;AACL,wBAAQ,KAAK,+BAA+B,iBAAiB,OAAO;AAGpE,qBAAK,UAAU,OAAO;kBACpB,QAAQ;kBACR,SAAS;kBACT,SAAS,CAAC,IAAI;iBACf,EAAE,KAAK,WAAS,MAAM,QAAO,CAAE;cAClC;YACF,SAAS,OAAO;AACd,sBAAQ,MAAM,oDAAoD,KAAK;YACzE;UACF;AAGA,cAAI;AACF,kBAAM,QAAQ,MAAM,KAAK,SAAQ;AAGjC,oBAAQ,IAAI,yBAAyB,MAAM,UAAU,GAAG,EAAE,IAAI,KAAK;AAEnE,iBAAK,yBAAyB,KAAK;UACrC,SAAS,OAAO;AACd,oBAAQ,MAAM,4BAA4B,KAAK;UAEjD;AAEA,cAAI;AAEF,gBAAI,KAAK,SAAS,GAAG,SAAS,GAAG;AAC/B,mBAAK,IAAI,eAAc,EAAG,UAAU;gBAClC,MAAM,WAAQ;AACZ,0BAAQ,IAAI,kCAAkC,KAAK;AACnD,uBAAK,yBAAyB,KAAK;gBACrC;gBACA,OAAO,WAAQ;AACb,0BAAQ,MAAM,qCAAqC,KAAK;gBAC1D;eACD;YACH;AAGA,gBAAI,KAAK,SAAS,GAAG,WAAW,GAAG;AACjC,gCAAkB,YAAY,iBAAiB,CAAC,UAA4B;AAC1E,wBAAQ,IAAI,oCAAoC,MAAM,KAAK;AAC3D,qBAAK,yBAAyB,MAAM,KAAK;cAC3C,CAAC;YACH;UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,mCAAmC,KAAK;UACxD;AAGA,eAAK,2BAA0B;QACjC,OAAO;AACL,kBAAQ,IAAI,8CAA8C;QAC5D;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,sBAAsB,KAAK;MAE3C;IACF;;;;;;EAMM,WAAQ;;AACZ,UAAI,KAAK,SAAS,GAAG,WAAW,GAAG;AACjC,YAAI;AAEF,gBAAM,SAAS,MAAM,kBAAkB,SAAQ;AAC/C,kBAAQ,IAAI,oDAAoD,OAAO,KAAK;AAC5E,iBAAO,OAAO;QAChB,SAAS,gBAAgB;AACvB,kBAAQ,MAAM,0DAA0D,cAAc;AAGtF,cAAI;AACF,kBAAM,QAAQ,MAAM,KAAK,IAAI,SAAQ;AACrC,oBAAQ,IAAI,0CAA0C,KAAK;AAC3D,mBAAO;UACT,SAAS,cAAc;AACrB,oBAAQ,MAAM,gDAAgD,YAAY;AAC1E,kBAAM;UACR;QACF;MACF,WAAW,KAAK,SAAS,GAAG,SAAS,GAAG;AAEtC,eAAO,KAAK,IAAI,SAAQ;MAC1B,OAAO;AAEL,cAAM,YAAY,wBAAwB,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AACpF,gBAAQ,IAAI,qCAAqC,SAAS;AAC1D,eAAO,QAAQ,QAAQ,SAAS;MAClC;IACF;;;;;;;EAOO,yBAAyB,OAAe,QAAe;AAE5D,UAAM,cAAc,aAAa,QAAQ,WAAW;AACpD,QAAI,gBAAgB,OAAO;AACzB,cAAQ,IAAI,iDAAiD;AAC7D;IACF;AAGA,QAAI,aAAa;AACjB,QAAI,KAAK,SAAS,GAAG,KAAK,GAAG;AAC3B,mBAAa;IACf,WAAW,KAAK,SAAS,GAAG,SAAS,GAAG;AACtC,mBAAa;IACf;AAEA,YAAQ,IAAI,eAAe,UAAU,wBAAwB;AAG7D,UAAM,UAAe;MACnB;MACA,aAAa;MACb,YAAY,YAAY,SAAS,aAAa;;;AAIhD,QAAI,QAAQ;AACV,cAAQ,UAAU;AAClB,cAAQ,IAAI,mCAAmC,MAAM,EAAE;IACzD,OAAO;AAEL,YAAM,YAAY,aAAa,QAAQ,OAAO;AAC9C,UAAI,WAAW;AACb,YAAI;AAEF,gBAAM,YAAY,KAAK,SAAS,SAAS;AACzC,cAAI,aAAa,UAAU,KAAK;AAC9B,oBAAQ,UAAU,UAAU;AAC5B,oBAAQ,IAAI,4CAA4C,UAAU,GAAG,EAAE;UACzE;QACF,SAAS,OAAO;AACd,kBAAQ,MAAM,4BAA4B,KAAK;QACjD;MACF;IACF;AAGA,QAAI,UAAU,YAAY,SAAS,aAAa,QAAQ,aAAa;AAEnE,mBAAa,QAAQ,aAAa,KAAK;AAGvC,mBAAa,QAAQ,yBAAyB,MAAM;AAGpD,WAAK,KAAK,KAAK,GAAG,YAAY,MAAM,iBAAiB,OAAO,EACzD,UAAU;QACT,MAAM,CAAC,QAAO;AACZ,kBAAQ,IAAI,kCAAkC,GAAG;AAEjD,uBAAa,WAAW,uBAAuB;QACjD;QACA,OAAO,CAAC,QAAO;AACb,kBAAQ,MAAM,4BAA4B,GAAG;AAG7C,uBAAa,WAAW,uBAAuB;QAGjD;OACD;IACL,OAAO;AACL,cAAQ,IAAI,0DAA0D;AAEtE,UAAI,OAAO;AACT,qBAAa,QAAQ,aAAa,KAAK;MACzC;IACF;EACF;;;;;;EAOQ,SAAS,OAAa;AAC5B,QAAI;AACF,YAAM,YAAY,MAAM,MAAM,GAAG,EAAE,CAAC;AACpC,YAAM,SAAS,UAAU,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AAC7D,YAAM,cAAc,mBAAmB,KAAK,MAAM,EAAE,MAAM,EAAE,EAAE,IAAI,SAAS,GAAC;AAC1E,eAAO,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE;MAC7D,CAAC,EAAE,KAAK,EAAE,CAAC;AACX,aAAO,KAAK,MAAM,WAAW;IAC/B,SAAS,OAAO;AACd,cAAQ,MAAM,4BAA4B,KAAK;AAC/C,aAAO;IACT;EACF;EAEQ,6BAA0B;AAEhC,SAAK,oCAAmC;AACxC,SAAK,kCAAiC;EACxC;EAEQ,sCAAmC;AACzC,QAAI;AACF,UAAI,KAAK,SAAS,GAAG,WAAW,GAAG;AACjC,gBAAQ,IAAI,gEAAgE;AAG5E,0BAAkB,YAAY,wBAAwB,CAAC,UAMlD;AACH,kBAAQ,IAAI,mDAAmD,KAAK;AAEpE,gBAAM,mBAAmB,MAAM,aAAa,QAAQ,CAAA;AAGpD,eAAK,oBAAoB;YACvB,OAAO,MAAM,aAAa,SAAS;YACnC,MAAM,MAAM,aAAa,QAAQ;YACjC,UAAU,iBAAiB,YAAY;YACvC,UAAU,iBAAiB,YAAY;YACvC,WAAW;;YACX,iBAAiB,iBAAiB,mBAAmB;YACrD,OAAM,oBAAI,KAAI,GAAG,YAAW;aAEzB,OAAO,KAAK,gBAAgB,EAC5B,OAAO,SAAO,CAAC,CAAC,YAAY,YAAY,iBAAiB,EAAE,SAAS,GAAG,CAAC,EACxE,OAAO,CAAC,KAAK,QAAO;AACnB,gBAAI,GAAG,IAAI,iBAAiB,GAAG;AAC/B,mBAAO;UACT,GAAG,CAAA,CAAyB,EAC/B;QACH,CAAC;AAGD,0BAAkB,YAAY,+BAA+B,CAAC,UAMzD;AACH,kBAAQ,IAAI,mCAAmC,KAAK;AAEpD,gBAAM,mBAAmB,MAAM,aAAa,QAAQ,CAAA;AAGpD,eAAK,oBAAoB;YACvB,OAAO,MAAM,aAAa,SAAS;YACnC,MAAM,MAAM,aAAa,QAAQ;YACjC,UAAU,iBAAiB,YAAY;YACvC,UAAU,iBAAiB,YAAY;YACvC,WAAW;;YACX,iBAAiB,iBAAiB,mBAAmB;YACrD,OAAM,oBAAI,KAAI,GAAG,YAAW;aAEzB,OAAO,KAAK,gBAAgB,EAC5B,OAAO,SAAO,CAAC,CAAC,YAAY,YAAY,iBAAiB,EAAE,SAAS,GAAG,CAAC,EACxE,OAAO,CAAC,KAAK,QAAO;AACnB,gBAAI,GAAG,IAAI,iBAAiB,GAAG;AAC/B,mBAAO;UACT,GAAG,CAAA,CAAyB,EAC/B;QACH,CAAC;MACH;IACF,SAAS,OAAO;AACd,cAAQ,MAAM,sDAAsD,KAAK;IAC3E;EACF;EAEQ,oCAAiC;AACvC,QAAI;AACF,UAAI,KAAK,SAAS,GAAG,SAAS,GAAG;AAC/B,gBAAQ,IAAI,+CAA+C;AAE3D,aAAK,IAAI,eAAc,EAAG,UAAU;UAClC,MAAM,CAAC,SAAQ;AACb,oBAAQ,IAAI,sCAAsC,IAAI;AAEtD,kBAAM,mBAAmB,mBAAK;AAG9B,iBAAK,oBAAoB;cACvB,OAAO,KAAK,OAAO,KAAM,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,OAAO,KAAK,KAAK,KAAK,EAAE,OAAO,EAAE,OAAO,KAAM;cAClG,MAAM,KAAK,MAAM,KAAM,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,OAAO,KAAK,KAAK,KAAK,EAAE,OAAO,EAAE,MAAM,KAAM,KAAK,SAAS,KAAK;cAClH,UAAU,KAAK,UAAU,KAAK;cAC9B,UAAU,KAAK,UAAU,KAAK;cAC9B,WAAW,KAAK,WAAW,KAAK;cAChC,iBAAiB,KAAK,iBAAiB,KAAK;cAC5C,MAAM,KAAK,MAAM,MAAK,oBAAI,KAAI,GAAG,YAAW;eAEzC,OAAO,KAAK,gBAAgB,EAC5B,OAAO,SAAO,CAAC,CAAC,SAAS,QAAQ,YAAY,YAAY,aAAa,mBAAmB,MAAM,EAAE,SAAS,GAAG,CAAC,EAC9G,OAAO,CAAC,KAAK,QAAO;AACnB,kBAAI,GAAG,IAAI,iBAAiB,GAAG;AAC/B,qBAAO;YACT,GAAG,CAAA,CAAyB,EAC/B;UACH;UACA,OAAO,CAAC,UAAS;AACf,oBAAQ,MAAM,mDAAmD,KAAK;UACxE;SACD;MACH;IACF,SAAS,OAAO;AACd,cAAQ,MAAM,wDAAwD,KAAK;IAC7E;EACF;EAEQ,oBAAoB,cAA6B;AACvD,QAAI;AAEF,cAAQ,IAAI,2BAA2B;QACrC,OAAO,aAAa;QACpB,MAAM,aAAa;QACnB,UAAU,aAAa;QACvB,UAAU,aAAa;QACvB,WAAW,aAAa;QACxB,iBAAiB,aAAa;QAC9B,YAAY,YAAY,SAAS,aAAa;OAC/C;AAGD,WAAK,oBAAoB,KAAK,YAAY;AAE1C,UAAI,aAAa,WAAW;AAE1B,gBAAQ,IAAI,mCAAmC;AAC/C,aAAK,6BAA6B,YAAY;MAChD,OAAO;AAEL,gBAAQ,IAAI,qCAAqC;AACjD,aAAK,6BAA6B,YAAY;MAChD;IACF,SAAS,OAAO;AACd,cAAQ,MAAM,kCAAkC,OAAO,YAAY;IACrE;EACF;EAEc,6BAA6B,cAA6B;;AACtE,UAAI;AACF,gBAAQ,IAAI,+CAAwC,YAAY;AAGhE,aAAK,cAAa;AAGlB,aAAK,sBAAqB;AAG1B,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAGrD,cAAM,KAAK,+BAA+B,YAAY;MAExD,SAAS,OAAO;AACd,gBAAQ,MAAM,kDAA6C,KAAK;AAEhE,cAAM,KAAK,kBAAkB,YAAY;MAC3C;IACF;;;;;EAKc,+BAA+B,cAA6B;;AACxE,UAAI;AACF,gBAAQ,IAAI,sDAA+C,YAAY;AAGvE,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAGrD,cAAM,gBAAgB,MAAM,KAAK,UAAU,OAAM;AACjD,YAAI,eAAe;AACjB,kBAAQ,IAAI,yDAA+C;AAC3D,gBAAM,cAAc,QAAO;AAE3B,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;QACvD;AAGA,gBAAQ,IAAI,6DAAsD;AAClE,cAAM,KAAK,kBAAkB,YAAY;AAGzC,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,QAAQ,aAAa,SAAS;UAC9B,WAAW,aAAa,WAAW,GAAG,aAAa,SAAS,YAAW,CAAE,WAAW;UACpF,SAAS,aAAa,QAAQ;UAC9B,SAAS;YACP;cACE,MAAM;cACN,UAAU;cACV,SAAS,MAAK;AACZ,wBAAQ,IAAI,wDAA4C;AACxD,qBAAK,4BAA4B,YAAY;AAC7C,uBAAO;cACT;;YAEF;cACE,MAAM;cACN,MAAM;cACN,UAAU;cACV,SAAS,MAAK;AACZ,wBAAQ,IAAI,0CAAqC;AACjD,uBAAO;cACT;;;UAGJ,UAAU,0BAA0B,aAAa,UAAU,YAAW,KAAM,SAAS;UACrF,iBAAiB;;UACjB,eAAe;SAChB;AAED,gBAAQ,IAAI,+CAA0C;AACtD,cAAM,MAAM,QAAO;AACnB,gBAAQ,IAAI,+CAA0C;MAExD,SAAS,OAAY;AACnB,gBAAQ,MAAM,0CAAqC,KAAK;AACxD,gBAAQ,MAAM,+BAA0B,OAAO,SAAS,OAAO,KAAK;AAEpE,cAAM,KAAK,kBAAkB,YAAY;MAC3C;IACF;;;;;EAKc,kBAAkB,cAA6B;;AAC3D,UAAI;AACF,gBAAQ,IAAI,gDAAyC;AAErD,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;UACxC,QAAQ,aAAM,aAAa,SAAS,iBAAiB;UACrD,SAAS,aAAa,QAAQ;UAC9B,UAAU;;UACV,UAAU;UACV,OAAO,KAAK,cAAc,aAAa,QAAQ;UAC/C,UAAU;UACV,SAAS;YACP;cACE,MAAM;cACN,SAAS,MAAK;AACZ,wBAAQ,IAAI,wDAA4C;AACxD,qBAAK,4BAA4B,YAAY;AAC7C,uBAAO;cACT;;YAEF;cACE,MAAM;cACN,MAAM;cACN,SAAS,MAAK;AACZ,wBAAQ,IAAI,0CAAqC;AACjD,uBAAO;cACT;;;SAGL;AAED,cAAM,MAAM,QAAO;AACnB,gBAAQ,IAAI,2CAAsC;AAGlD,aAAK,cAAa;MAEpB,SAAS,OAAY;AACnB,gBAAQ,MAAM,sCAAiC,KAAK;AAEpD,YAAI;AACF,gBAAM,cAAc,MAAM,KAAK,UAAU,OAAO;YAC9C,QAAQ;YACR,SAAS,GAAG,aAAa,KAAK;;EAAO,aAAa,IAAI;YACtD,SAAS;cACP;gBACE,MAAM;gBACN,SAAS,MAAM,KAAK,4BAA4B,YAAY;;cAE9D;;WAEH;AACD,gBAAM,YAAY,QAAO;AACzB,kBAAQ,IAAI,0CAAqC;QACnD,SAAS,YAAY;AACnB,kBAAQ,MAAM,2CAAsC,UAAU;AAC9D,kBAAQ,IAAI,kEAA2D,YAAY;QACrF;MACF;IACF;;;;;EAKQ,cAAc,UAAiB;AACrC,QAAI,CAAC;AAAU,aAAO;AAEtB,YAAQ,SAAS,YAAW,GAAI;MAC9B,KAAK;AAAc,eAAO;MAC1B,KAAK;AAAS,eAAO;MACrB,KAAK;AAAW,eAAO;MACvB,KAAK;AAAQ,eAAO;MACpB;AAAS,eAAO;IAClB;EACF;;;;EAKM,+BAA+B,cAA6B;;AAChE,cAAQ,IAAI,iDAA0C,YAAY;AAClE,YAAM,KAAK,oBAAoB,YAAY;IAC7C;;;;;EAKM,+BAA+B,cAA6B;;AAChE,cAAQ,IAAI,iDAA0C,YAAY;AAClE,mBAAa,YAAY;AACzB,YAAM,KAAK,oBAAoB,YAAY;IAC7C;;;;;EAKQ,iBAAiB,UAAiB;AACxC,QAAI,CAAC,UAAU;AACb,aAAO,EAAE,OAAO,WAAW,MAAM,wBAAuB;IAC1D;AAEA,UAAM,OAAO,SAAS,YAAW;AAEjC,QAAI,KAAK,SAAS,YAAY,KAAK,KAAK,SAAS,OAAO,GAAG;AACzD,aAAO,EAAE,OAAO,WAAW,MAAM,gBAAe;IAClD,WAAW,KAAK,SAAS,OAAO,KAAK,KAAK,SAAS,OAAO,GAAG;AAC3D,aAAO,EAAE,OAAO,WAAW,MAAM,gBAAe;IAClD,WAAW,KAAK,SAAS,SAAS,KAAK,KAAK,SAAS,OAAO,KAAK,KAAK,SAAS,WAAW,GAAG;AAC3F,aAAO,EAAE,OAAO,WAAW,MAAM,uBAAsB;IACzD,WAAW,KAAK,SAAS,MAAM,GAAG;AAChC,aAAO,EAAE,OAAO,WAAW,MAAM,gBAAe;IAClD;AAEA,WAAO,EAAE,OAAO,WAAW,MAAM,uBAAsB;EACzD;;;;EAKQ,gBAAa;AAEnB,QAAI,aAAa,WAAW;AAE1B,gBAAU,QAAQ,CAAC,KAAM,KAAK,KAAM,KAAK,GAAI,CAAC;AAC9C,cAAQ,IAAI,gDAAgD;IAC9D,OAAO;AACL,cAAQ,IAAI,4CAA4C;IAC1D;EACF;;;;EAKQ,wBAAqB;AAC3B,QAAI;AAEF,YAAM,QAAQ,IAAI,MAAK;AAGvB,YAAM,MAAM;AAGZ,YAAM,SAAS;AAGf,YAAM,KAAI,EAAG,MAAM,WAAQ;AACzB,gBAAQ,MAAM,qCAAqC,KAAK;MAC1D,CAAC;IACH,SAAS,OAAO;AACd,cAAQ,MAAM,iCAAiC,KAAK;IACtD;EACF;;EAIc,6BAA6B,cAA6B;;AACtE,UAAI;AACF,gBAAQ,IAAI,mDAA4C,YAAY;AAGpE,aAAK,cAAa;AAGlB,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAGtD,cAAM,KAAK,+BAA+B,YAAY;MAKxD,SAAS,OAAO;AACd,gBAAQ,MAAM,kDAA6C,KAAK;AAEhE,aAAK,4BAA4B,YAAY;MAC/C;IACF;;EAEQ,4BAA4B,cAA6B;AAC/D,YAAQ,IAAI,qDAAyC,YAAY;AAGjE,QAAI,aAAa,aAAa,uBACzB,aAAa,MAAM,KAAK,aAAa,MAAM,EAAE,MAAM,MAAM,2BAA4B;AACxF,cAAQ,IAAI,uDAAgD;AAC5D,WAAK,mCAAmC,YAAY;AACpD;IACF;AAGA,QAAI,aAAa,UAAU;AACzB,YAAM,WAAW,aAAa,SAAS,YAAW;AAClD,cAAQ,IAAI,oCAA6B,QAAQ;AAGjD,UAAI,qBAAqB;AAEzB,cAAO,UAAU;QACf,KAAK;QACL,KAAK;AACH,+BAAqB;AACrB;QACF,KAAK;QACL,KAAK;AACH,+BAAqB;AACrB;QACF,KAAK;QACL,KAAK;QACL,KAAK;AACH,+BAAqB;AACrB;QACF,KAAK;AACH,+BAAqB;AACrB;QACF;AACE,kBAAQ,KAAK,8BAA8B,QAAQ;AACnD,+BAAqB;AACrB;MACJ;AAEA,cAAQ,IAAI,yCAA6B,QAAQ,OAAO,kBAAkB,EAAE;AAE5E,UAAI,sBAAsB,uBAAuB,OAAO;AAEtD,gBAAQ,IAAI,wDAA4C,kBAAkB;AAE1E,YAAI,QAAQ;AACZ,gBAAQ,mBAAmB,YAAW,GAAI;UACxC,KAAK;AACH,oBAAQ;AACR;UACF,KAAK;AACH,oBAAQ;AACR;UACF,KAAK;AACH,oBAAQ;AACR;UACF;AACE,oBAAQ;AACR;QACJ;AAEA,gBAAQ,IAAI,wCAA4B,KAAK,EAAE;AAC/C,aAAK,OAAO,SAAS,CAAC,KAAK,CAAC;MAC9B,OAAO;AAEL,gBAAQ,IAAI,sDAA+C;AAC3D,aAAK,OAAO,SAAS,CAAC,YAAY,CAAC;MACrC;IACF,OAAO;AAEL,cAAQ,IAAI,4CAAqC;AACjD,WAAK,OAAO,SAAS,CAAC,YAAY,CAAC;IACrC;EACF;EAEQ,mCAAmC,cAA6B;AACtE,YAAQ,IAAI,sDAA+C,YAAY;AAEvE,QAAI;AAEF,UAAI,iBAAiB;AAErB,UAAI,aAAa,MAAM,GAAG;AACxB,yBAAiB,aAAa,MAAM;MACtC;AAEA,UAAI,kBAAkB,eAAe,sBAAsB;AACzD,cAAM,WAAW,eAAe;AAChC,cAAM,eAAe,eAAe;AACpC,cAAM,WAAW,eAAe,UAAU;AAC1C,cAAM,YAAY,eAAe,UAAU;AAE3C,gBAAQ,IAAI,wCAAiC;UAC3C,IAAI;UACJ;UACA,KAAK;UACL,KAAK;SACN;AAGD,YAAI,QAAQ;AAEZ,YAAI,cAAc;AAChB,kBAAQ,aAAa,YAAW,GAAI;YAClC,KAAK;AACH,sBAAQ;AACR;YACF,KAAK;AACH,sBAAQ;AACR;YACF,KAAK;YACL,KAAK;AACH,sBAAQ;AACR;YACF;AACE,sBAAQ;AACR;UACJ;QACF;AAEA,gBAAQ,IAAI,iCAAqB,KAAK,4BAA4B;AAGlE,aAAK,OAAO,SAAS,CAAC,KAAK,GAAG;UAC5B,aAAa;YACX,aAAa;YACb,iBAAiB;YACjB,WAAW;YACX,WAAW;;SAEd;MAEH,OAAO;AACL,gBAAQ,IAAI,mEAA4D;AACxE,aAAK,OAAO,SAAS,CAAC,WAAW,CAAC;MACpC;IAEF,SAAS,OAAO;AACd,cAAQ,MAAM,yDAAoD,KAAK;AAEvE,WAAK,OAAO,SAAS,CAAC,WAAW,CAAC;IACpC;EACF;;;;;EAMc,0BAAuB;;AACnC,UAAI;AAGF,YAAI,KAAK,SAAS,GAAG,WAAW,KAAK,KAAK,SAAS,GAAG,SAAS,GAAG;AAChE,cAAI;AAEF,kBAAM,kBAAkB,SAAQ;AAChC,mBAAO;UACT,SAAS,OAAY;AACnB,oBAAQ,MAAM,wCAAwC,KAAK;AAG3D,kBAAM,eAAe,MAAM,WAAW;AACtC,gBACE,aAAa,SAAS,sBAAsB,KAC5C,aAAa,SAAS,uBAAuB,KAC7C,aAAa,SAAS,iBAAiB,GACvC;AACA,qBAAO;YACT;AAIA,mBAAO;UACT;QACF;AAIA,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,qCAAqC,KAAK;AAExD,eAAO;MACT;IACF;;;;;;EAMc,oCAAiC;;AAC7C,UAAI;AACF,YAAI,KAAK,SAAS,GAAG,SAAS,GAAG;AAC/B,kBAAQ,IAAI,wCAAwC;AASpD,gBAAM,KAAK,4BACT,oBACA,oBACA,+CACA,MAAM;AAIR,gBAAM,KAAK,4BACT,yBACA,yBACA,0BACA,SAAS;AAGX,kBAAQ,IAAI,oDAAoD;QAClE;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,iDAAiD,KAAK;MACtE;IACF;;;;;;EAMc,4BACZ,WACA,aACA,oBACA,YAAsC;;AAEtC,UAAI;AAKF,cAAM,UAAU;UACd,cAAc;YACZ,OAAO;YACP,MAAM;YACN,SAAS;cACP;cACA,UAAU,eAAe,SAAS,SAAU,eAAe,YAAY,YAAY;cACnF,OAAO,eAAe;cACtB,SAAS,eAAe;cACxB,YAAY;;;;AAMlB,gBAAQ,IAAI,iCAAiC,SAAS,KAAK,WAAW,GAAG;MAE3E,SAAS,OAAO;AACd,gBAAQ,MAAM,uCAAuC,SAAS,KAAK,KAAK;MAC1E;IACF;;;;;;;;EAQa,gBAAgB,QAAe;;AAC1C,UAAI;AACF,gBAAQ,IAAI,yBAAyB;AAGrC,YAAI,KAAK,SAAS,GAAG,WAAW,GAAG;AACjC,cAAI;AAEF,kBAAM,kBAAkB,YAAW;AACnC,oBAAQ,IAAI,4BAA4B;AAGxC,kBAAM,SAAS,MAAM,kBAAkB,SAAQ;AAC/C,oBAAQ,IAAI,2BAA2B,OAAO,KAAK;AAGnD,iBAAK,yBAAyB,OAAO,OAAO,MAAM;AAElD,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,yCAAyC,KAAK;AAC5D,mBAAO;UACT;QACF,WAES,KAAK,SAAS,GAAG,SAAS,GAAG;AACpC,cAAI;AAEF,kBAAM,QAAQ,MAAM,KAAK,IAAI,SAAQ;AACrC,oBAAQ,IAAI,wCAAwC,KAAK;AAGzD,iBAAK,yBAAyB,OAAO,MAAM;AAE3C,mBAAO;UACT,SAAS,OAAO;AACd,oBAAQ,MAAM,uCAAuC,KAAK;AAC1D,mBAAO;UACT;QACF,OAEK;AAEH,gBAAM,YAAY,wBAAwB,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AACpF,kBAAQ,IAAI,iCAAiC,SAAS;AAGtD,eAAK,yBAAyB,WAAW,MAAM;AAE/C,iBAAO;QACT;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,6BAA6B,KAAK;AAChD,eAAO;MACT;IACF;;;;uCAx+BW,aAAU,mBAAA,GAAA,GAAA,mBAAA,UAAA,GAAA,mBAAA,QAAA,GAAA,mBAAA,eAAA,GAAA,mBAAA,eAAA,GAAA,mBAAA,MAAA,CAAA;IAAA;EAAA;;4EAAV,aAAU,SAAV,YAAU,WAAA,YAFT,OAAM,CAAA;EAAA;;;sEAEP,YAAU,CAAA;UAHtB;WAAW;MACV,YAAY;KACb;;;", "names": ["args", "get", "AwesomeCordovaNativePlugin", "get", "FCM", "Importance", "Visibility"], "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}
{"version": 3, "sources": ["src/app/services/offline-routing.service.ts", "src/app/components/offline-banner.component.ts", "src/app/pages/home/<USER>", "src/app/pages/home/<USER>"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { OfflineStorageService, OfflineRoute } from './offline-storage.service';\r\nimport { MapboxRoutingService } from './mapbox-routing.service';\r\nimport * as L from 'leaflet';\r\n\r\nexport interface RouteResult {\r\n  coordinates: [number, number][];\r\n  distance: number; // in meters\r\n  duration: number; // in seconds\r\n  isOffline: boolean;\r\n  travelMode: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class OfflineRoutingService {\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private offlineStorage: OfflineStorageService,\r\n    private mapboxRouting: MapboxRoutingService\r\n  ) {}\r\n\r\n  /**\r\n   * Get route with offline fallback\r\n   */\r\n  async getRoute(\r\n    startLat: number,\r\n    startLng: number,\r\n    endLat: number,\r\n    endLng: number,\r\n    travelMode: 'walking' | 'cycling' | 'driving' = 'walking'\r\n  ): Promise<RouteResult | null> {\r\n\r\n    // First try to get cached route\r\n    const cachedRoute = await this.offlineStorage.getRoute(\r\n      startLat, startLng, endLat, endLng, travelMode\r\n    );\r\n\r\n    if (cachedRoute) {\r\n      console.log('📍 Using cached route');\r\n      return {\r\n        coordinates: JSON.parse(cachedRoute.route_data),\r\n        distance: cachedRoute.distance,\r\n        duration: cachedRoute.duration,\r\n        isOffline: true,\r\n        travelMode: cachedRoute.travel_mode\r\n      };\r\n    }\r\n\r\n    // If online, try to get fresh route\r\n    if (this.offlineStorage.isOnline() && !this.offlineStorage.isOfflineMode()) {\r\n      try {\r\n        const onlineRoute = await this.getOnlineRoute(\r\n          startLat, startLng, endLat, endLng, travelMode\r\n        );\r\n\r\n        if (onlineRoute) {\r\n          // Cache the route for offline use\r\n          await this.cacheRoute(\r\n            startLat, startLng, endLat, endLng,\r\n            onlineRoute, travelMode\r\n          );\r\n\r\n          return {\r\n            ...onlineRoute,\r\n            isOffline: false\r\n          };\r\n        }\r\n      } catch (error) {\r\n        console.warn('⚠️ Online routing failed, falling back to offline:', error);\r\n      }\r\n    }\r\n\r\n    // In offline mode, we don't provide routing - just show evacuation centers\r\n    console.log('⚠️ Offline mode: No routing available. Show evacuation centers only.');\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Get route from online service (Mapbox)\r\n   */\r\n  private async getOnlineRoute(\r\n    startLat: number,\r\n    startLng: number,\r\n    endLat: number,\r\n    endLng: number,\r\n    travelMode: string\r\n  ): Promise<RouteResult | null> {\r\n    try {\r\n      const response = await this.mapboxRouting.getDirections(\r\n        startLng, startLat, endLng, endLat,\r\n        travelMode as any\r\n      );\r\n\r\n      if (response.routes && response.routes.length > 0) {\r\n        const route = response.routes[0];\r\n        const coordinates = route.geometry.coordinates.map(coord => [coord[1], coord[0]] as [number, number]);\r\n\r\n        return {\r\n          coordinates,\r\n          distance: route.distance,\r\n          duration: route.duration,\r\n          isOffline: false,\r\n          travelMode\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Mapbox routing error:', error);\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Cache route for offline use\r\n   */\r\n  private async cacheRoute(\r\n    startLat: number,\r\n    startLng: number,\r\n    endLat: number,\r\n    endLng: number,\r\n    route: RouteResult,\r\n    travelMode: string\r\n  ): Promise<void> {\r\n    const offlineRoute: OfflineRoute = {\r\n      start_lat: startLat,\r\n      start_lng: startLng,\r\n      end_lat: endLat,\r\n      end_lng: endLng,\r\n      disaster_type: 'general', // Can be updated based on context\r\n      route_data: JSON.stringify(route.coordinates),\r\n      distance: route.distance,\r\n      duration: route.duration,\r\n      travel_mode: travelMode\r\n    };\r\n\r\n    await this.offlineStorage.saveRoute(offlineRoute);\r\n  }\r\n\r\n  /**\r\n   * Generate offline route - NO ROUTING, just show distance and direction\r\n   */\r\n  private generateOfflineRoute(\r\n    startLat: number,\r\n    startLng: number,\r\n    endLat: number,\r\n    endLng: number,\r\n    travelMode: string\r\n  ): RouteResult | null {\r\n    // In offline mode, we DON'T provide routing\r\n    // Instead, we just calculate distance and let users navigate manually\r\n    console.log('⚠️ No routing available in offline mode - showing distance only');\r\n\r\n    return null; // No route provided - users must navigate manually\r\n  }\r\n\r\n  /**\r\n   * Calculate distance between two points using Haversine formula\r\n   */\r\n  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {\r\n    const R = 6371; // Earth's radius in kilometers\r\n    const dLat = this.toRadians(lat2 - lat1);\r\n    const dLng = this.toRadians(lng2 - lng1);\r\n\r\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +\r\n              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *\r\n              Math.sin(dLng / 2) * Math.sin(dLng / 2);\r\n\r\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\r\n    return R * c;\r\n  }\r\n\r\n  private toRadians(degrees: number): number {\r\n    return degrees * (Math.PI / 180);\r\n  }\r\n\r\n  /**\r\n   * Estimate travel duration based on distance and mode\r\n   */\r\n  private estimateDuration(distanceMeters: number, travelMode: string): number {\r\n    const distanceKm = distanceMeters / 1000;\r\n\r\n    // Average speeds (km/h)\r\n    const speeds = {\r\n      walking: 5,\r\n      cycling: 15,\r\n      driving: 40\r\n    };\r\n\r\n    const speed = speeds[travelMode as keyof typeof speeds] || speeds.walking;\r\n    return (distanceKm / speed) * 3600; // Convert hours to seconds\r\n  }\r\n\r\n  /**\r\n   * Add route to Leaflet map\r\n   */\r\n  addRouteToMap(\r\n    map: L.Map,\r\n    route: RouteResult,\r\n    color: string = '#007bff',\r\n    weight: number = 4\r\n  ): L.Polyline {\r\n    const polyline = L.polyline(route.coordinates, {\r\n      color: color,\r\n      weight: weight,\r\n      opacity: route.isOffline ? 0.7 : 1.0,\r\n      dashArray: route.isOffline ? '10, 5' : undefined // Dashed line for offline routes\r\n    }).addTo(map);\r\n\r\n    // Add popup with route info\r\n    const distanceKm = (route.distance / 1000).toFixed(1);\r\n    const durationMin = Math.round(route.duration / 60);\r\n    const routeType = route.isOffline ? 'Offline Route' : 'Online Route';\r\n\r\n    polyline.bindPopup(`\r\n      <div class=\"route-popup\">\r\n        <strong>${routeType}</strong><br>\r\n        Distance: ${distanceKm} km<br>\r\n        Duration: ${durationMin} min<br>\r\n        Mode: ${route.travelMode}\r\n      </div>\r\n    `);\r\n\r\n    return polyline;\r\n  }\r\n\r\n  /**\r\n   * Pre-cache routes for common evacuation centers\r\n   */\r\n  async preCacheRoutes(\r\n    userLat: number,\r\n    userLng: number,\r\n    evacuationCenters: any[],\r\n    travelModes: string[] = ['walking', 'cycling']\r\n  ): Promise<void> {\r\n    if (!this.offlineStorage.isOnline()) {\r\n      console.log('⚠️ Cannot pre-cache routes while offline');\r\n      return;\r\n    }\r\n\r\n    console.log('🔄 Pre-caching routes for evacuation centers...');\r\n    let cachedCount = 0;\r\n\r\n    for (const center of evacuationCenters) {\r\n      for (const mode of travelModes) {\r\n        try {\r\n          await this.getRoute(\r\n            userLat, userLng,\r\n            center.latitude, center.longitude,\r\n            mode as any\r\n          );\r\n          cachedCount++;\r\n\r\n          // Add delay to avoid overwhelming the API\r\n          await new Promise(resolve => setTimeout(resolve, 500));\r\n        } catch (error) {\r\n          console.warn(`Failed to cache route to ${center.name}:`, error);\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log(`✅ Pre-cached ${cachedCount} routes`);\r\n  }\r\n}\r\n", "import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IonicModule, AlertController, LoadingController, ToastController } from '@ionic/angular';\r\nimport { OfflineStorageService } from '../services/offline-storage.service';\r\nimport { OfflineMapService } from '../services/offline-map.service';\r\nimport { OfflineRoutingService } from '../services/offline-routing.service';\r\nimport { Geolocation } from '@capacitor/geolocation';\r\n\r\n@Component({\r\n  selector: 'app-offline-banner',\r\n  template: `\r\n    <div class=\"offline-banner\" [ngClass]=\"getBannerClass()\">\r\n      <div class=\"banner-content\">\r\n        <ion-icon [name]=\"getBannerIcon()\" class=\"banner-icon\"></ion-icon>\r\n        <div class=\"banner-text\">\r\n          <div class=\"banner-title\">{{ getBannerTitle() }}</div>\r\n          <div class=\"banner-subtitle\">{{ getBannerSubtitle() }}</div>\r\n        </div>\r\n        <div class=\"banner-actions\">\r\n          <ion-button \r\n            *ngIf=\"showOfflineButton()\" \r\n            fill=\"clear\" \r\n            size=\"small\" \r\n            color=\"light\"\r\n            (click)=\"enableOfflineMode()\">\r\n            Continue Offline\r\n          </ion-button>\r\n          <ion-button \r\n            *ngIf=\"showSyncButton()\" \r\n            fill=\"clear\" \r\n            size=\"small\" \r\n            color=\"light\"\r\n            (click)=\"syncData()\">\r\n            <ion-icon name=\"sync-outline\"></ion-icon>\r\n            Sync\r\n          </ion-button>\r\n          <ion-button \r\n            *ngIf=\"showPrepareButton()\" \r\n            fill=\"clear\" \r\n            size=\"small\" \r\n            color=\"light\"\r\n            (click)=\"prepareOfflineData()\">\r\n            <ion-icon name=\"download-outline\"></ion-icon>\r\n            Prepare\r\n          </ion-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- Progress bar for data preparation -->\r\n      <div *ngIf=\"isPreparingData\" class=\"preparation-progress\">\r\n        <ion-progress-bar [value]=\"preparationProgress\"></ion-progress-bar>\r\n        <div class=\"progress-text\">{{ preparationStatus }}</div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .offline-banner {\r\n      padding: 12px 16px;\r\n      margin: 8px 16px;\r\n      border-radius: 8px;\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    .offline-banner.online {\r\n      background: linear-gradient(135deg, #28a745, #20c997);\r\n      color: white;\r\n    }\r\n\r\n    .offline-banner.offline {\r\n      background: linear-gradient(135deg, #dc3545, #fd7e14);\r\n      color: white;\r\n    }\r\n\r\n    .offline-banner.preparing {\r\n      background: linear-gradient(135deg, #007bff, #6610f2);\r\n      color: white;\r\n    }\r\n\r\n    .offline-banner.warning {\r\n      background: linear-gradient(135deg, #ffc107, #fd7e14);\r\n      color: #212529;\r\n    }\r\n\r\n    .banner-content {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 12px;\r\n    }\r\n\r\n    .banner-icon {\r\n      font-size: 24px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .banner-text {\r\n      flex: 1;\r\n    }\r\n\r\n    .banner-title {\r\n      font-weight: 600;\r\n      font-size: 14px;\r\n      margin-bottom: 2px;\r\n    }\r\n\r\n    .banner-subtitle {\r\n      font-size: 12px;\r\n      opacity: 0.9;\r\n    }\r\n\r\n    .banner-actions {\r\n      display: flex;\r\n      gap: 8px;\r\n    }\r\n\r\n    .preparation-progress {\r\n      margin-top: 12px;\r\n    }\r\n\r\n    .progress-text {\r\n      font-size: 12px;\r\n      text-align: center;\r\n      margin-top: 4px;\r\n      opacity: 0.9;\r\n    }\r\n\r\n    ion-progress-bar {\r\n      height: 4px;\r\n      border-radius: 2px;\r\n    }\r\n  `],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule]\r\n})\r\nexport class OfflineBannerComponent implements OnInit, OnDestroy {\r\n  @Output() offlineModeEnabled = new EventEmitter<void>();\r\n  @Output() dataSynced = new EventEmitter<void>();\r\n\r\n  isOnline = navigator.onLine;\r\n  isOfflineMode = false;\r\n  hasOfflineData = false;\r\n  isPreparingData = false;\r\n  preparationProgress = 0;\r\n  preparationStatus = '';\r\n  lastSyncTime: string | null = null;\r\n\r\n  private onlineListener?: () => void;\r\n  private offlineListener?: () => void;\r\n\r\n  constructor(\r\n    private offlineStorage: OfflineStorageService,\r\n    private offlineMap: OfflineMapService,\r\n    private offlineRouting: OfflineRoutingService,\r\n    private alertCtrl: AlertController,\r\n    private loadingCtrl: LoadingController,\r\n    private toastCtrl: ToastController\r\n  ) {}\r\n\r\n  async ngOnInit() {\r\n    // Set up network listeners\r\n    this.onlineListener = () => {\r\n      this.isOnline = true;\r\n      this.checkDataStatus();\r\n    };\r\n    \r\n    this.offlineListener = () => {\r\n      this.isOnline = false;\r\n      this.checkDataStatus();\r\n    };\r\n\r\n    window.addEventListener('online', this.onlineListener);\r\n    window.addEventListener('offline', this.offlineListener);\r\n\r\n    // Initial status check\r\n    await this.checkDataStatus();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.onlineListener) {\r\n      window.removeEventListener('online', this.onlineListener);\r\n    }\r\n    if (this.offlineListener) {\r\n      window.removeEventListener('offline', this.offlineListener);\r\n    }\r\n  }\r\n\r\n  private async checkDataStatus() {\r\n    this.isOfflineMode = this.offlineStorage.isOfflineMode();\r\n    this.hasOfflineData = await this.offlineStorage.isDataAvailable();\r\n    this.lastSyncTime = this.offlineStorage.getLastSyncTime();\r\n  }\r\n\r\n  getBannerClass(): string {\r\n    if (this.isPreparingData) return 'preparing';\r\n    if (!this.isOnline) return 'offline';\r\n    if (this.isOnline && !this.hasOfflineData) return 'warning';\r\n    return 'online';\r\n  }\r\n\r\n  getBannerIcon(): string {\r\n    if (this.isPreparingData) return 'download-outline';\r\n    if (!this.isOnline) return 'wifi-outline';\r\n    if (this.isOnline && !this.hasOfflineData) return 'warning-outline';\r\n    return 'checkmark-circle-outline';\r\n  }\r\n\r\n  getBannerTitle(): string {\r\n    if (this.isPreparingData) return 'Preparing Offline Data';\r\n    if (!this.isOnline && this.hasOfflineData) return 'Offline Mode Available';\r\n    if (!this.isOnline && !this.hasOfflineData) return 'No Internet Connection';\r\n    if (this.isOnline && !this.hasOfflineData) return 'Offline Data Not Ready';\r\n    return 'Connected & Ready';\r\n  }\r\n\r\n  getBannerSubtitle(): string {\r\n    if (this.isPreparingData) return this.preparationStatus;\r\n    if (!this.isOnline && this.hasOfflineData) return 'Emergency data is available offline';\r\n    if (!this.isOnline && !this.hasOfflineData) return 'Limited functionality available';\r\n    if (this.isOnline && !this.hasOfflineData) return 'Prepare offline data for emergencies';\r\n    \r\n    if (this.lastSyncTime) {\r\n      const syncDate = new Date(this.lastSyncTime);\r\n      return `Last synced: ${syncDate.toLocaleDateString()}`;\r\n    }\r\n    return 'All systems operational';\r\n  }\r\n\r\n  showOfflineButton(): boolean {\r\n    return !this.isOnline && !this.isOfflineMode && this.hasOfflineData;\r\n  }\r\n\r\n  showSyncButton(): boolean {\r\n    return this.isOnline && this.hasOfflineData && !this.isPreparingData;\r\n  }\r\n\r\n  showPrepareButton(): boolean {\r\n    return this.isOnline && !this.hasOfflineData && !this.isPreparingData;\r\n  }\r\n\r\n  async enableOfflineMode() {\r\n    const alert = await this.alertCtrl.create({\r\n      header: 'Enable Offline Mode',\r\n      message: 'Switch to offline mode to access cached evacuation data and maps?',\r\n      buttons: [\r\n        {\r\n          text: 'Cancel',\r\n          role: 'cancel'\r\n        },\r\n        {\r\n          text: 'Continue Offline',\r\n          handler: () => {\r\n            this.offlineStorage.setOfflineMode(true);\r\n            this.isOfflineMode = true;\r\n            this.offlineModeEnabled.emit();\r\n            this.showToast('Offline mode enabled. Using cached data.', 'success');\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async syncData() {\r\n    const loading = await this.loadingCtrl.create({\r\n      message: 'Syncing evacuation data...'\r\n    });\r\n    await loading.present();\r\n\r\n    try {\r\n      const success = await this.offlineStorage.syncEvacuationCenters();\r\n      await loading.dismiss();\r\n\r\n      if (success) {\r\n        this.dataSynced.emit();\r\n        this.checkDataStatus();\r\n        this.showToast('Data synced successfully', 'success');\r\n      } else {\r\n        this.showToast('Sync failed. Please try again.', 'danger');\r\n      }\r\n    } catch (error) {\r\n      await loading.dismiss();\r\n      this.showToast('Sync error. Check your connection.', 'danger');\r\n    }\r\n  }\r\n\r\n  async prepareOfflineData() {\r\n    const alert = await this.alertCtrl.create({\r\n      header: 'Prepare Offline Data',\r\n      message: 'Download evacuation centers and map data for offline use? This may take a few minutes and use mobile data.',\r\n      buttons: [\r\n        {\r\n          text: 'Cancel',\r\n          role: 'cancel'\r\n        },\r\n        {\r\n          text: 'Download',\r\n          handler: () => this.startDataPreparation()\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  private async startDataPreparation() {\r\n    this.isPreparingData = true;\r\n    this.preparationProgress = 0;\r\n\r\n    try {\r\n      // Step 1: Sync evacuation centers\r\n      this.preparationStatus = 'Downloading evacuation centers...';\r\n      const syncSuccess = await this.offlineStorage.syncEvacuationCenters();\r\n      this.preparationProgress = 0.3;\r\n\r\n      if (!syncSuccess) {\r\n        throw new Error('Failed to sync evacuation centers');\r\n      }\r\n\r\n      // Step 2: Get user location for map caching\r\n      this.preparationStatus = 'Getting your location...';\r\n      const position = await Geolocation.getCurrentPosition({\r\n        enableHighAccuracy: true,\r\n        timeout: 10000\r\n      });\r\n      this.preparationProgress = 0.4;\r\n\r\n      const userLat = position.coords.latitude;\r\n      const userLng = position.coords.longitude;\r\n\r\n      // Step 3: Cache map tiles\r\n      this.preparationStatus = 'Downloading map tiles...';\r\n      await this.offlineMap.preloadMapTiles(\r\n        userLat, userLng, 25, // 25km radius\r\n        (current, total) => {\r\n          const mapProgress = 0.4 + (current / total) * 0.4; // 40% of total progress\r\n          this.preparationProgress = mapProgress;\r\n          this.preparationStatus = `Downloading map tiles... ${current}/${total}`;\r\n        }\r\n      );\r\n\r\n      // Step 4: Pre-cache routes\r\n      this.preparationStatus = 'Pre-caching routes...';\r\n      const centers = await this.offlineStorage.getEvacuationCenters();\r\n      await this.offlineRouting.preCacheRoutes(userLat, userLng, centers.slice(0, 10)); // Cache routes to nearest 10 centers\r\n      this.preparationProgress = 1.0;\r\n\r\n      this.preparationStatus = 'Preparation complete!';\r\n      await this.checkDataStatus();\r\n      \r\n      setTimeout(() => {\r\n        this.isPreparingData = false;\r\n        this.showToast('Offline data prepared successfully!', 'success');\r\n      }, 1000);\r\n\r\n    } catch (error) {\r\n      console.error('Data preparation failed:', error);\r\n      this.isPreparingData = false;\r\n      this.showToast('Failed to prepare offline data. Please try again.', 'danger');\r\n    }\r\n  }\r\n\r\n  private async showToast(message: string, color: string) {\r\n    const toast = await this.toastCtrl.create({\r\n      message,\r\n      duration: 3000,\r\n      color,\r\n      position: 'bottom'\r\n    });\r\n    await toast.present();\r\n  }\r\n}\r\n", "import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IonicModule, ToastController } from '@ionic/angular';\r\nimport { Router } from '@angular/router';\r\nimport { FcmService } from '../../services/fcm.service';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { environment } from '../../../environments/environment';\r\nimport { Subscription, interval } from 'rxjs';\r\nimport { OfflineBannerComponent } from '../../components/offline-banner.component';\r\nimport { OfflineStorageService } from '../../services/offline-storage.service';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.page.html',\r\n  styleUrls: ['./home.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, OfflineBannerComponent]\r\n})\r\nexport class HomePage implements OnInit, OnDestroy {\r\n  isOffline = false;\r\n  unreadNotificationCount = 0;\r\n  private notificationSubscription: Subscription | null = null;\r\n  private pollSubscription: Subscription | null = null;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private toastCtrl: ToastController,\r\n    private fcmService: FcmService,\r\n    private http: HttpClient,\r\n    private offlineStorage: OfflineStorageService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    const savedOfflineStatus = localStorage.getItem('isOffline');\r\n    this.isOffline = savedOfflineStatus === 'true';\r\n\r\n    // Load initial unread count\r\n    this.loadUnreadCount();\r\n\r\n    // Poll for unread count every 30 seconds\r\n    this.pollSubscription = interval(30000).subscribe(() => {\r\n      this.loadUnreadCount();\r\n    });\r\n\r\n    // Subscribe to new notifications\r\n    this.notificationSubscription = this.fcmService.notifications$.subscribe(() => {\r\n      this.loadUnreadCount();\r\n    });\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.notificationSubscription) {\r\n      this.notificationSubscription.unsubscribe();\r\n    }\r\n    if (this.pollSubscription) {\r\n      this.pollSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  toggleStatus() {\r\n    this.isOffline = !this.isOffline;\r\n    localStorage.setItem('isOffline', String(this.isOffline));\r\n  }\r\n\r\n  openDisasterMap(disasterType: string) {\r\n    console.log(`🏠 HOME: Opening disaster-specific map for: ${disasterType}`);\r\n\r\n    // Map the disaster type to display names and routes\r\n    let displayName = disasterType;\r\n    let route = '';\r\n\r\n    if (disasterType === 'earthquake') {\r\n      displayName = 'Earthquake';\r\n      route = '/earthquake-map';\r\n    } else if (disasterType === 'typhoon') {\r\n      displayName = 'Typhoon';\r\n      route = '/typhoon-map';\r\n    } else if (disasterType === 'flashflood') {\r\n      displayName = 'Flash Flood';\r\n      route = '/flood-map';\r\n    }\r\n\r\n    console.log(`🏠 HOME: Navigating to ${route} for ${displayName}`);\r\n\r\n    // Show loading toast\r\n    this.toastCtrl.create({\r\n      message: `🗺️ Opening ${displayName} evacuation centers...`,\r\n      duration: 2000,\r\n      color: 'primary'\r\n    }).then(toast => toast.present());\r\n\r\n    // Navigate to the disaster-specific map\r\n    this.router.navigate([route]);\r\n  }\r\n\r\n  viewMap() {\r\n    console.log(`🏠 HOME: Opening complete evacuation centers map`);\r\n\r\n    // Show loading toast\r\n    this.toastCtrl.create({\r\n      message: '🗺️ Opening complete evacuation centers map...',\r\n      duration: 2000,\r\n      color: 'secondary'\r\n    }).then(toast => toast.present());\r\n\r\n    // Navigate to the all-maps page\r\n    this.router.navigate(['/all-maps']);\r\n  }\r\n\r\n  async loadUnreadCount() {\r\n    try {\r\n      const response = await this.http.get<{ unread_count: number }>(`${environment.apiUrl}/notifications/unread-count`).toPromise();\r\n      if (response) {\r\n        this.unreadNotificationCount = response.unread_count;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading unread notification count:', error);\r\n    }\r\n  }\r\n\r\n  openNotifications() {\r\n    this.router.navigate(['/notifications']);\r\n  }\r\n\r\n  openDataDebug() {\r\n    console.log('🐛 Opening data debug page');\r\n    this.router.navigate(['/data-debug']);\r\n  }\r\n\r\n  // Offline banner event handlers\r\n  onOfflineModeEnabled() {\r\n    console.log('🔄 Offline mode enabled from banner');\r\n    this.isOffline = true;\r\n    this.showToast('Offline mode enabled. Using cached data.', 'success');\r\n  }\r\n\r\n  onDataSynced() {\r\n    console.log('🔄 Data synced from banner');\r\n    this.showToast('Evacuation data updated successfully', 'success');\r\n  }\r\n\r\n  private async showToast(message: string, color: string) {\r\n    const toast = await this.toastCtrl.create({\r\n      message,\r\n      duration: 3000,\r\n      color,\r\n      position: 'bottom'\r\n    });\r\n    await toast.present();\r\n  }\r\n}", "\r\n<ion-header [translucent]=\"true\">\r\n  <ion-toolbar>\r\n    <ion-title>\r\n      Alerto\r\n    </ion-title>\r\n    <ion-buttons slot=\"end\">\r\n      <ion-button (click)=\"openNotifications()\" class=\"notification-button\">\r\n        <ion-icon name=\"notifications-outline\"></ion-icon>\r\n        <ion-badge *ngIf=\"unreadNotificationCount > 0\" class=\"notification-badge\">\r\n          {{ unreadNotificationCount > 99 ? '99+' : unreadNotificationCount }}\r\n        </ion-badge>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content>\r\n  <!-- Offline Banner -->\r\n  <app-offline-banner\r\n    (offlineModeEnabled)=\"onOfflineModeEnabled()\"\r\n    (dataSynced)=\"onDataSynced()\">\r\n  </app-offline-banner>\r\n\r\n  <div class=\"ion-padding\">\r\n    <div class=\"disaster-container\">\r\n      <div class=\"home-title\"><img src=\"assets/ALERTO.png\" alt=\"App Logo\" class=\"home-logo\"/> <div style=\"font-size: 22px;\">Hi, Welcome to <p style=\"font-size: 35px; color: #1565c0; margin-top: 0px;\">Safe Area!</p></div></div>\r\n      <div class=\"top-disaster\">\r\n\r\n      <ion-card class=\"disaster earthquake\" (click)=\"openDisasterMap('earthquake')\">\r\n        <ion-card-content>\r\n          <img src=\"assets/earthquake.png\" alt=\"Earthquake\">\r\n          <ion-text><u>Earthquake</u></ion-text>\r\n        </ion-card-content>\r\n      </ion-card>\r\n\r\n      <ion-card class=\"disaster typhoon\" (click)=\"openDisasterMap('typhoon')\">\r\n        <ion-card-content>\r\n          <img src=\"assets/typhoon.png\" alt=\"Typhoon\">\r\n          <ion-text><u>Typhoon</u></ion-text>\r\n        </ion-card-content>\r\n      </ion-card>\r\n\r\n      <ion-card class=\"disaster flood\" (click)=\"openDisasterMap('flashflood')\">\r\n        <ion-card-content>\r\n          <img src=\"assets/flood.png\" alt=\"Flood\">\r\n          <ion-text><u>Flash Flood</u></ion-text>\r\n        </ion-card-content>\r\n      </ion-card>\r\n    </div>\r\n\r\n    <ion-button expand=\"block\" class=\"view-map\" (click)=\"viewMap()\" [disabled]=\"isOffline\" style=\"margin-top: 24px; width: 80%; height: 45px; --border-radius: 25px;\">\r\n      <ion-icon name=\"map\" slot=\"start\"></ion-icon>\r\n      See the Whole Map\r\n    </ion-button>\r\n\r\n    <!-- Debug Button (temporary) - HIDDEN -->\r\n    <!--\r\n    <ion-button expand=\"block\" fill=\"outline\" color=\"warning\" (click)=\"openDataDebug()\" style=\"margin-top: 12px; width: 80%; height: 40px; --border-radius: 20px;\">\r\n      <ion-icon name=\"bug\" slot=\"start\"></ion-icon>\r\n      Debug Data\r\n    </ion-button>\r\n    -->\r\n\r\n  </div>\r\n</div>\r\n\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,QAAmB;AAab,IAAO,wBAAP,MAAO,uBAAqB;EAEhC,YACU,MACA,gBACA,eAAmC;AAFnC,SAAA,OAAA;AACA,SAAA,iBAAA;AACA,SAAA,gBAAA;EACP;;;;EAKG,SACJ,UACA,UACA,QACA,QACA,aAAgD,WAAS;;AAIzD,YAAM,cAAc,MAAM,KAAK,eAAe,SAC5C,UAAU,UAAU,QAAQ,QAAQ,UAAU;AAGhD,UAAI,aAAa;AACf,gBAAQ,IAAI,8BAAuB;AACnC,eAAO;UACL,aAAa,KAAK,MAAM,YAAY,UAAU;UAC9C,UAAU,YAAY;UACtB,UAAU,YAAY;UACtB,WAAW;UACX,YAAY,YAAY;;MAE5B;AAGA,UAAI,KAAK,eAAe,SAAQ,KAAM,CAAC,KAAK,eAAe,cAAa,GAAI;AAC1E,YAAI;AACF,gBAAM,cAAc,MAAM,KAAK,eAC7B,UAAU,UAAU,QAAQ,QAAQ,UAAU;AAGhD,cAAI,aAAa;AAEf,kBAAM,KAAK,WACT,UAAU,UAAU,QAAQ,QAC5B,aAAa,UAAU;AAGzB,mBAAO,iCACF,cADE;cAEL,WAAW;;UAEf;QACF,SAAS,OAAO;AACd,kBAAQ,KAAK,gEAAsD,KAAK;QAC1E;MACF;AAGA,cAAQ,IAAI,gFAAsE;AAClF,aAAO;IACT;;;;;EAKc,eACZ,UACA,UACA,QACA,QACA,YAAkB;;AAElB,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,cAAc,cACxC,UAAU,UAAU,QAAQ,QAC5B,UAAiB;AAGnB,YAAI,SAAS,UAAU,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,QAAQ,SAAS,OAAO,CAAC;AAC/B,gBAAM,cAAc,MAAM,SAAS,YAAY,IAAI,WAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAqB;AAEpG,iBAAO;YACL;YACA,UAAU,MAAM;YAChB,UAAU,MAAM;YAChB,WAAW;YACX;;QAEJ;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,gCAA2B,KAAK;MAChD;AAEA,aAAO;IACT;;;;;EAKc,WACZ,UACA,UACA,QACA,QACA,OACA,YAAkB;;AAElB,YAAM,eAA6B;QACjC,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,eAAe;;QACf,YAAY,KAAK,UAAU,MAAM,WAAW;QAC5C,UAAU,MAAM;QAChB,UAAU,MAAM;QAChB,aAAa;;AAGf,YAAM,KAAK,eAAe,UAAU,YAAY;IAClD;;;;;EAKQ,qBACN,UACA,UACA,QACA,QACA,YAAkB;AAIlB,YAAQ,IAAI,2EAAiE;AAE7E,WAAO;EACT;;;;EAKQ,kBAAkB,MAAc,MAAc,MAAc,MAAY;AAC9E,UAAM,IAAI;AACV,UAAM,OAAO,KAAK,UAAU,OAAO,IAAI;AACvC,UAAM,OAAO,KAAK,UAAU,OAAO,IAAI;AAEvC,UAAM,IAAI,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,IACtC,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,IAC9D,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC;AAEhD,UAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AACvD,WAAO,IAAI;EACb;EAEQ,UAAU,SAAe;AAC/B,WAAO,WAAW,KAAK,KAAK;EAC9B;;;;EAKQ,iBAAiB,gBAAwB,YAAkB;AACjE,UAAM,aAAa,iBAAiB;AAGpC,UAAM,SAAS;MACb,SAAS;MACT,SAAS;MACT,SAAS;;AAGX,UAAM,QAAQ,OAAO,UAAiC,KAAK,OAAO;AAClE,WAAQ,aAAa,QAAS;EAChC;;;;EAKA,cACE,KACA,OACA,QAAgB,WAChB,SAAiB,GAAC;AAElB,UAAMA,YAAa,WAAS,MAAM,aAAa;MAC7C;MACA;MACA,SAAS,MAAM,YAAY,MAAM;MACjC,WAAW,MAAM,YAAY,UAAU;;KACxC,EAAE,MAAM,GAAG;AAGZ,UAAM,cAAc,MAAM,WAAW,KAAM,QAAQ,CAAC;AACpD,UAAM,cAAc,KAAK,MAAM,MAAM,WAAW,EAAE;AAClD,UAAM,YAAY,MAAM,YAAY,kBAAkB;AAEtD,IAAAA,UAAS,UAAU;;kBAEL,SAAS;oBACP,UAAU;oBACV,WAAW;gBACf,MAAM,UAAU;;KAE3B;AAED,WAAOA;EACT;;;;EAKM,eACJ,IACA,IACA,IAC8C;+CAH9C,SACA,SACA,mBACA,cAAwB,CAAC,WAAW,SAAS,GAAC;AAE9C,UAAI,CAAC,KAAK,eAAe,SAAQ,GAAI;AACnC,gBAAQ,IAAI,oDAA0C;AACtD;MACF;AAEA,cAAQ,IAAI,wDAAiD;AAC7D,UAAI,cAAc;AAElB,iBAAW,UAAU,mBAAmB;AACtC,mBAAW,QAAQ,aAAa;AAC9B,cAAI;AACF,kBAAM,KAAK,SACT,SAAS,SACT,OAAO,UAAU,OAAO,WACxB,IAAW;AAEb;AAGA,kBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;UACvD,SAAS,OAAO;AACd,oBAAQ,KAAK,4BAA4B,OAAO,IAAI,KAAK,KAAK;UAChE;QACF;MACF;AAEA,cAAQ,IAAI,qBAAgB,WAAW,SAAS;IAClD;;;;uCAxPW,wBAAqB,mBAAA,UAAA,GAAA,mBAAA,qBAAA,GAAA,mBAAA,oBAAA,CAAA;IAAA;EAAA;;4EAArB,wBAAqB,SAArB,uBAAqB,WAAA,YAFpB,OAAM,CAAA;EAAA;;;sEAEP,uBAAqB,CAAA;UAHjC;WAAW;MACV,YAAY;KACb;;;;;;;;ACES,IAAA,yBAAA,GAAA,cAAA,CAAA;AAKE,IAAA,qBAAA,SAAA,SAAA,2EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,CAAmB;IAAA,CAAA;AAC5B,IAAA,iBAAA,GAAA,oBAAA;AACF,IAAA,uBAAA;;;;;;AACA,IAAA,yBAAA,GAAA,cAAA,CAAA;AAKE,IAAA,qBAAA,SAAA,SAAA,4EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,SAAA,CAAU;IAAA,CAAA;AACnB,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,GAAA,QAAA;AACF,IAAA,uBAAA;;;;;;AACA,IAAA,yBAAA,GAAA,cAAA,CAAA;AAKE,IAAA,qBAAA,SAAA,SAAA,4EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,mBAAA,CAAoB;IAAA,CAAA;AAC7B,IAAA,oBAAA,GAAA,YAAA,EAAA;AACA,IAAA,iBAAA,GAAA,WAAA;AACF,IAAA,uBAAA;;;;;AAKJ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,oBAAA,EAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA2B,IAAA,iBAAA,CAAA;AAAuB,IAAA,uBAAA,EAAM;;;;AADtC,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,mBAAA;AACS,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,iBAAA;;;AAkF7B,IAAO,yBAAP,MAAO,wBAAsB;EAejC,YACU,gBACA,YACA,gBACA,WACA,aACA,WAA0B;AAL1B,SAAA,iBAAA;AACA,SAAA,aAAA;AACA,SAAA,iBAAA;AACA,SAAA,YAAA;AACA,SAAA,cAAA;AACA,SAAA,YAAA;AApBA,SAAA,qBAAqB,IAAI,aAAY;AACrC,SAAA,aAAa,IAAI,aAAY;AAEvC,SAAA,WAAW,UAAU;AACrB,SAAA,gBAAgB;AAChB,SAAA,iBAAiB;AACjB,SAAA,kBAAkB;AAClB,SAAA,sBAAsB;AACtB,SAAA,oBAAoB;AACpB,SAAA,eAA8B;EAY3B;EAEG,WAAQ;;AAEZ,WAAK,iBAAiB,MAAK;AACzB,aAAK,WAAW;AAChB,aAAK,gBAAe;MACtB;AAEA,WAAK,kBAAkB,MAAK;AAC1B,aAAK,WAAW;AAChB,aAAK,gBAAe;MACtB;AAEA,aAAO,iBAAiB,UAAU,KAAK,cAAc;AACrD,aAAO,iBAAiB,WAAW,KAAK,eAAe;AAGvD,YAAM,KAAK,gBAAe;IAC5B;;EAEA,cAAW;AACT,QAAI,KAAK,gBAAgB;AACvB,aAAO,oBAAoB,UAAU,KAAK,cAAc;IAC1D;AACA,QAAI,KAAK,iBAAiB;AACxB,aAAO,oBAAoB,WAAW,KAAK,eAAe;IAC5D;EACF;EAEc,kBAAe;;AAC3B,WAAK,gBAAgB,KAAK,eAAe,cAAa;AACtD,WAAK,iBAAiB,MAAM,KAAK,eAAe,gBAAe;AAC/D,WAAK,eAAe,KAAK,eAAe,gBAAe;IACzD;;EAEA,iBAAc;AACZ,QAAI,KAAK;AAAiB,aAAO;AACjC,QAAI,CAAC,KAAK;AAAU,aAAO;AAC3B,QAAI,KAAK,YAAY,CAAC,KAAK;AAAgB,aAAO;AAClD,WAAO;EACT;EAEA,gBAAa;AACX,QAAI,KAAK;AAAiB,aAAO;AACjC,QAAI,CAAC,KAAK;AAAU,aAAO;AAC3B,QAAI,KAAK,YAAY,CAAC,KAAK;AAAgB,aAAO;AAClD,WAAO;EACT;EAEA,iBAAc;AACZ,QAAI,KAAK;AAAiB,aAAO;AACjC,QAAI,CAAC,KAAK,YAAY,KAAK;AAAgB,aAAO;AAClD,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK;AAAgB,aAAO;AACnD,QAAI,KAAK,YAAY,CAAC,KAAK;AAAgB,aAAO;AAClD,WAAO;EACT;EAEA,oBAAiB;AACf,QAAI,KAAK;AAAiB,aAAO,KAAK;AACtC,QAAI,CAAC,KAAK,YAAY,KAAK;AAAgB,aAAO;AAClD,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK;AAAgB,aAAO;AACnD,QAAI,KAAK,YAAY,CAAC,KAAK;AAAgB,aAAO;AAElD,QAAI,KAAK,cAAc;AACrB,YAAM,WAAW,IAAI,KAAK,KAAK,YAAY;AAC3C,aAAO,gBAAgB,SAAS,mBAAkB,CAAE;IACtD;AACA,WAAO;EACT;EAEA,oBAAiB;AACf,WAAO,CAAC,KAAK,YAAY,CAAC,KAAK,iBAAiB,KAAK;EACvD;EAEA,iBAAc;AACZ,WAAO,KAAK,YAAY,KAAK,kBAAkB,CAAC,KAAK;EACvD;EAEA,oBAAiB;AACf,WAAO,KAAK,YAAY,CAAC,KAAK,kBAAkB,CAAC,KAAK;EACxD;EAEM,oBAAiB;;AACrB,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC,QAAQ;QACR,SAAS;QACT,SAAS;UACP;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,SAAS,MAAK;AACZ,mBAAK,eAAe,eAAe,IAAI;AACvC,mBAAK,gBAAgB;AACrB,mBAAK,mBAAmB,KAAI;AAC5B,mBAAK,UAAU,4CAA4C,SAAS;YACtE;;;OAGL;AAED,YAAM,MAAM,QAAO;IACrB;;EAEM,WAAQ;;AACZ,YAAM,UAAU,MAAM,KAAK,YAAY,OAAO;QAC5C,SAAS;OACV;AACD,YAAM,QAAQ,QAAO;AAErB,UAAI;AACF,cAAM,UAAU,MAAM,KAAK,eAAe,sBAAqB;AAC/D,cAAM,QAAQ,QAAO;AAErB,YAAI,SAAS;AACX,eAAK,WAAW,KAAI;AACpB,eAAK,gBAAe;AACpB,eAAK,UAAU,4BAA4B,SAAS;QACtD,OAAO;AACL,eAAK,UAAU,kCAAkC,QAAQ;QAC3D;MACF,SAAS,OAAO;AACd,cAAM,QAAQ,QAAO;AACrB,aAAK,UAAU,sCAAsC,QAAQ;MAC/D;IACF;;EAEM,qBAAkB;;AACtB,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC,QAAQ;QACR,SAAS;QACT,SAAS;UACP;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,SAAS,MAAM,KAAK,qBAAoB;;;OAG7C;AAED,YAAM,MAAM,QAAO;IACrB;;EAEc,uBAAoB;;AAChC,WAAK,kBAAkB;AACvB,WAAK,sBAAsB;AAE3B,UAAI;AAEF,aAAK,oBAAoB;AACzB,cAAM,cAAc,MAAM,KAAK,eAAe,sBAAqB;AACnE,aAAK,sBAAsB;AAE3B,YAAI,CAAC,aAAa;AAChB,gBAAM,IAAI,MAAM,mCAAmC;QACrD;AAGA,aAAK,oBAAoB;AACzB,cAAM,WAAW,MAAM,YAAY,mBAAmB;UACpD,oBAAoB;UACpB,SAAS;SACV;AACD,aAAK,sBAAsB;AAE3B,cAAM,UAAU,SAAS,OAAO;AAChC,cAAM,UAAU,SAAS,OAAO;AAGhC,aAAK,oBAAoB;AACzB,cAAM,KAAK,WAAW;UACpB;UAAS;UAAS;;UAClB,CAAC,SAAS,UAAS;AACjB,kBAAM,cAAc,MAAO,UAAU,QAAS;AAC9C,iBAAK,sBAAsB;AAC3B,iBAAK,oBAAoB,4BAA4B,OAAO,IAAI,KAAK;UACvE;QAAC;AAIH,aAAK,oBAAoB;AACzB,cAAM,UAAU,MAAM,KAAK,eAAe,qBAAoB;AAC9D,cAAM,KAAK,eAAe,eAAe,SAAS,SAAS,QAAQ,MAAM,GAAG,EAAE,CAAC;AAC/E,aAAK,sBAAsB;AAE3B,aAAK,oBAAoB;AACzB,cAAM,KAAK,gBAAe;AAE1B,mBAAW,MAAK;AACd,eAAK,kBAAkB;AACvB,eAAK,UAAU,uCAAuC,SAAS;QACjE,GAAG,GAAI;MAET,SAAS,OAAO;AACd,gBAAQ,MAAM,4BAA4B,KAAK;AAC/C,aAAK,kBAAkB;AACvB,aAAK,UAAU,qDAAqD,QAAQ;MAC9E;IACF;;EAEc,UAAU,SAAiB,OAAa;;AACpD,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC;QACA,UAAU;QACV;QACA,UAAU;OACX;AACD,YAAM,MAAM,QAAO;IACrB;;;;uCA5OW,yBAAsB,4BAAA,qBAAA,GAAA,4BAAA,iBAAA,GAAA,4BAAA,qBAAA,GAAA,4BAAA,eAAA,GAAA,4BAAA,iBAAA,GAAA,4BAAA,eAAA,CAAA;IAAA;EAAA;;yEAAtB,yBAAsB,WAAA,CAAA,CAAA,oBAAA,CAAA,GAAA,SAAA,EAAA,oBAAA,sBAAA,YAAA,aAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,kBAAA,GAAA,SAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,SAAA,QAAA,SAAA,SAAA,SAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,wBAAA,GAAA,MAAA,GAAA,CAAA,QAAA,SAAA,QAAA,SAAA,SAAA,SAAA,GAAA,OAAA,GAAA,CAAA,QAAA,cAAA,GAAA,CAAA,QAAA,kBAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,eAAA,CAAA,GAAA,UAAA,SAAA,gCAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;AA1H/B,QAAA,yBAAA,GAAA,OAAA,CAAA,EAAyD,GAAA,OAAA,CAAA;AAErD,QAAA,oBAAA,GAAA,YAAA,CAAA;AACA,QAAA,yBAAA,GAAA,OAAA,CAAA,EAAyB,GAAA,OAAA,CAAA;AACG,QAAA,iBAAA,CAAA;AAAsB,QAAA,uBAAA;AAChD,QAAA,yBAAA,GAAA,OAAA,CAAA;AAA6B,QAAA,iBAAA,CAAA;AAAyB,QAAA,uBAAA,EAAM;AAE9D,QAAA,yBAAA,GAAA,OAAA,CAAA;AACE,QAAA,qBAAA,GAAA,8CAAA,GAAA,GAAA,cAAA,CAAA,EAKgC,IAAA,+CAAA,GAAA,GAAA,cAAA,CAAA,EAQT,IAAA,+CAAA,GAAA,GAAA,cAAA,CAAA;AAazB,QAAA,uBAAA,EAAM;AAIR,QAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,QAAA,uBAAA;;;AA1C4B,QAAA,qBAAA,WAAA,IAAA,eAAA,CAAA;AAEd,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,cAAA,CAAA;AAEkB,QAAA,oBAAA,CAAA;AAAA,QAAA,4BAAA,IAAA,eAAA,CAAA;AACG,QAAA,oBAAA,CAAA;AAAA,QAAA,4BAAA,IAAA,kBAAA,CAAA;AAI1B,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,kBAAA,CAAA;AAQA,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,eAAA,CAAA;AASA,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,kBAAA,CAAA;AAYD,QAAA,oBAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,eAAA;;sBAkFA,aAAW,WAAA,SAAA,gBAAE,cAAY,SAAA,IAAA,GAAA,QAAA,CAAA,khDAAA,EAAA,CAAA;EAAA;;;sEAExB,wBAAsB,CAAA;UA7HlC;uBACW,sBAAoB,UACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4CT,YA4EW,MAAI,SACP,CAAC,aAAa,YAAY,GAAC,QAAA,CAAA,y8CAAA,EAAA,CAAA;kMAG1B,oBAAkB,CAAA;UAA3B;MACS,YAAU,CAAA;UAAnB;;;;6EAFU,wBAAsB,EAAA,WAAA,0BAAA,UAAA,kDAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;;;AE5H3B,IAAA,yBAAA,GAAA,aAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,0BAAA,KAAA,QAAA,OAAA,yBAAA,GAAA;;;ADQJ,IAAO,WAAP,MAAO,UAAQ;EAMnB,YACU,QACA,WACA,YACA,MACA,gBAAqC;AAJrC,SAAA,SAAA;AACA,SAAA,YAAA;AACA,SAAA,aAAA;AACA,SAAA,OAAA;AACA,SAAA,iBAAA;AAVV,SAAA,YAAY;AACZ,SAAA,0BAA0B;AAClB,SAAA,2BAAgD;AAChD,SAAA,mBAAwC;EAQ7C;EAEH,WAAQ;AACN,UAAM,qBAAqB,aAAa,QAAQ,WAAW;AAC3D,SAAK,YAAY,uBAAuB;AAGxC,SAAK,gBAAe;AAGpB,SAAK,mBAAmB,SAAS,GAAK,EAAE,UAAU,MAAK;AACrD,WAAK,gBAAe;IACtB,CAAC;AAGD,SAAK,2BAA2B,KAAK,WAAW,eAAe,UAAU,MAAK;AAC5E,WAAK,gBAAe;IACtB,CAAC;EACH;EAEA,cAAW;AACT,QAAI,KAAK,0BAA0B;AACjC,WAAK,yBAAyB,YAAW;IAC3C;AACA,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,YAAW;IACnC;EACF;EAEA,eAAY;AACV,SAAK,YAAY,CAAC,KAAK;AACvB,iBAAa,QAAQ,aAAa,OAAO,KAAK,SAAS,CAAC;EAC1D;EAEA,gBAAgB,cAAoB;AAClC,YAAQ,IAAI,sDAA+C,YAAY,EAAE;AAGzE,QAAI,cAAc;AAClB,QAAI,QAAQ;AAEZ,QAAI,iBAAiB,cAAc;AACjC,oBAAc;AACd,cAAQ;IACV,WAAW,iBAAiB,WAAW;AACrC,oBAAc;AACd,cAAQ;IACV,WAAW,iBAAiB,cAAc;AACxC,oBAAc;AACd,cAAQ;IACV;AAEA,YAAQ,IAAI,iCAA0B,KAAK,QAAQ,WAAW,EAAE;AAGhE,SAAK,UAAU,OAAO;MACpB,SAAS,2BAAe,WAAW;MACnC,UAAU;MACV,OAAO;KACR,EAAE,KAAK,WAAS,MAAM,QAAO,CAAE;AAGhC,SAAK,OAAO,SAAS,CAAC,KAAK,CAAC;EAC9B;EAEA,UAAO;AACL,YAAQ,IAAI,yDAAkD;AAG9D,SAAK,UAAU,OAAO;MACpB,SAAS;MACT,UAAU;MACV,OAAO;KACR,EAAE,KAAK,WAAS,MAAM,QAAO,CAAE;AAGhC,SAAK,OAAO,SAAS,CAAC,WAAW,CAAC;EACpC;EAEM,kBAAe;;AACnB,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,KAAK,IAA8B,GAAG,YAAY,MAAM,6BAA6B,EAAE,UAAS;AAC5H,YAAI,UAAU;AACZ,eAAK,0BAA0B,SAAS;QAC1C;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,4CAA4C,KAAK;MACjE;IACF;;EAEA,oBAAiB;AACf,SAAK,OAAO,SAAS,CAAC,gBAAgB,CAAC;EACzC;EAEA,gBAAa;AACX,YAAQ,IAAI,mCAA4B;AACxC,SAAK,OAAO,SAAS,CAAC,aAAa,CAAC;EACtC;;EAGA,uBAAoB;AAClB,YAAQ,IAAI,4CAAqC;AACjD,SAAK,YAAY;AACjB,SAAK,UAAU,4CAA4C,SAAS;EACtE;EAEA,eAAY;AACV,YAAQ,IAAI,mCAA4B;AACxC,SAAK,UAAU,wCAAwC,SAAS;EAClE;EAEc,UAAU,SAAiB,OAAa;;AACpD,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO;QACxC;QACA,UAAU;QACV;QACA,UAAU;OACX;AACD,YAAM,MAAM,QAAO;IACrB;;;;uCAnIW,WAAQ,4BAAA,MAAA,GAAA,4BAAA,eAAA,GAAA,4BAAA,UAAA,GAAA,4BAAA,UAAA,GAAA,4BAAA,qBAAA,CAAA;IAAA;EAAA;;yEAAR,WAAQ,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,KAAA,GAAA,CAAA,GAAA,uBAAA,GAAA,OAAA,GAAA,CAAA,QAAA,uBAAA,GAAA,CAAA,SAAA,sBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,sBAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,OAAA,qBAAA,OAAA,YAAA,GAAA,WAAA,GAAA,CAAA,GAAA,aAAA,MAAA,GAAA,CAAA,GAAA,aAAA,QAAA,SAAA,WAAA,cAAA,KAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,YAAA,cAAA,GAAA,OAAA,GAAA,CAAA,OAAA,yBAAA,OAAA,YAAA,GAAA,CAAA,GAAA,YAAA,WAAA,GAAA,OAAA,GAAA,CAAA,OAAA,sBAAA,OAAA,SAAA,GAAA,CAAA,GAAA,YAAA,SAAA,GAAA,OAAA,GAAA,CAAA,OAAA,oBAAA,OAAA,OAAA,GAAA,CAAA,UAAA,SAAA,GAAA,YAAA,GAAA,cAAA,QAAA,SAAA,OAAA,UAAA,QAAA,mBAAA,QAAA,GAAA,SAAA,UAAA,GAAA,CAAA,QAAA,OAAA,QAAA,OAAA,GAAA,CAAA,GAAA,oBAAA,CAAA,GAAA,UAAA,SAAA,kBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACjBrB,QAAA,yBAAA,GAAA,cAAA,CAAA,EAAiC,GAAA,aAAA,EAClB,GAAA,WAAA;AAET,QAAA,iBAAA,GAAA,UAAA;AACF,QAAA,uBAAA;AACA,QAAA,yBAAA,GAAA,eAAA,CAAA,EAAwB,GAAA,cAAA,CAAA;AACV,QAAA,qBAAA,SAAA,SAAA,gDAAA;AAAA,iBAAS,IAAA,kBAAA;QAAmB,CAAA;AACtC,QAAA,oBAAA,GAAA,YAAA,CAAA;AACA,QAAA,qBAAA,GAAA,+BAAA,GAAA,GAAA,aAAA,CAAA;AAGF,QAAA,uBAAA,EAAa,EACD,EACF;AAGhB,QAAA,yBAAA,GAAA,aAAA,EAAa,GAAA,sBAAA,CAAA;AAGT,QAAA,qBAAA,sBAAA,SAAA,qEAAA;AAAA,iBAAsB,IAAA,qBAAA;QAAsB,CAAA,EAAC,cAAA,SAAA,6DAAA;AAAA,iBAC/B,IAAA,aAAA;QAAc,CAAA;AAC9B,QAAA,uBAAA;AAEA,QAAA,yBAAA,IAAA,OAAA,CAAA,EAAyB,IAAA,OAAA,CAAA,EACS,IAAA,OAAA,CAAA;AACN,QAAA,oBAAA,IAAA,OAAA,CAAA;AAAgE,QAAA,yBAAA,IAAA,OAAA,EAAA;AAA8B,QAAA,iBAAA,IAAA,iBAAA;AAAe,QAAA,yBAAA,IAAA,KAAA,EAAA;AAA6D,QAAA,iBAAA,IAAA,YAAA;AAAU,QAAA,uBAAA,EAAI,EAAM;AACtN,QAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,YAAA,EAAA;AAEY,QAAA,qBAAA,SAAA,SAAA,+CAAA;AAAA,iBAAS,IAAA,gBAAgB,YAAY;QAAC,CAAA;AAC1E,QAAA,yBAAA,IAAA,kBAAA;AACE,QAAA,oBAAA,IAAA,OAAA,EAAA;AACA,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,YAAA;AAAU,QAAA,uBAAA,EAAI,EAAW,EACrB;AAGrB,QAAA,yBAAA,IAAA,YAAA,EAAA;AAAmC,QAAA,qBAAA,SAAA,SAAA,+CAAA;AAAA,iBAAS,IAAA,gBAAgB,SAAS;QAAC,CAAA;AACpE,QAAA,yBAAA,IAAA,kBAAA;AACE,QAAA,oBAAA,IAAA,OAAA,EAAA;AACA,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,SAAA;AAAO,QAAA,uBAAA,EAAI,EAAW,EAClB;AAGrB,QAAA,yBAAA,IAAA,YAAA,EAAA;AAAiC,QAAA,qBAAA,SAAA,SAAA,+CAAA;AAAA,iBAAS,IAAA,gBAAgB,YAAY;QAAC,CAAA;AACrE,QAAA,yBAAA,IAAA,kBAAA;AACE,QAAA,oBAAA,IAAA,OAAA,EAAA;AACA,QAAA,yBAAA,IAAA,UAAA,EAAU,IAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,aAAA;AAAW,QAAA,uBAAA,EAAI,EAAW,EACtB,EACV;AAGb,QAAA,yBAAA,IAAA,cAAA,EAAA;AAA4C,QAAA,qBAAA,SAAA,SAAA,iDAAA;AAAA,iBAAS,IAAA,QAAA;QAAS,CAAA;AAC5D,QAAA,oBAAA,IAAA,YAAA,EAAA;AACA,QAAA,iBAAA,IAAA,qBAAA;AACF,QAAA,uBAAA,EAAa,EAUT,EACF;;;AAhEM,QAAA,qBAAA,eAAA,IAAA;AAQQ,QAAA,oBAAA,CAAA;AAAA,QAAA,qBAAA,QAAA,IAAA,0BAAA,CAAA;AA0CgD,QAAA,oBAAA,EAAA;AAAA,QAAA,qBAAA,YAAA,IAAA,SAAA;;sBDnCxD,aAAW,UAAA,WAAA,YAAA,SAAA,gBAAA,YAAA,WAAA,SAAA,SAAA,UAAA,YAAE,cAAY,MAAE,sBAAsB,GAAA,QAAA,CAAA,8hFAAA,EAAA,CAAA;EAAA;;;sEAEhD,UAAQ,CAAA;UAPpB;uBACW,YAAU,YAGR,MAAI,SACP,CAAC,aAAa,cAAc,sBAAsB,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,2oEAAA,EAAA,CAAA;;;;6EAEjD,UAAQ,EAAA,WAAA,YAAA,UAAA,mCAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": ["polyline"]}
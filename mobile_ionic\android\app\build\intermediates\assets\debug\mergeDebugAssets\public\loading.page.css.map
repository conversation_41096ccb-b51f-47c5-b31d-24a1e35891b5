{"version": 3, "sources": ["src/app/pages/loading/loading.page.scss"], "sourcesContent": [".loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  text-align: center;\r\n}\r\n\r\n.loader {\r\n  width: 48px;\r\n  height: 48px;\r\n  border: 5px solid #FFF;\r\n  border-bottom-color: #3880ff;\r\n  border-radius: 50%;\r\n  display: inline-block;\r\n  box-sizing: border-box;\r\n  animation: rotation 1s linear infinite;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n@keyframes rotation {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\nh2 {\r\n  color: #3880ff;\r\n  font-size: 24px;\r\n  margin: 0;\r\n} "], "mappings": ";AAAA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,UAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,UAAA,IAAA,MAAA;AACA,uBAAA;AACA,iBAAA;AACA,WAAA;AACA,cAAA;AACA,aAAA,SAAA,GAAA,OAAA;AACA,iBAAA;;AAGF,WAJE;AAKA;AACE,eAAA,OAAA;;AAEF;AACE,eAAA,OAAA;;;AAIJ;AACE,SAAA;AACA,aAAA;AACA,UAAA;;", "names": []}
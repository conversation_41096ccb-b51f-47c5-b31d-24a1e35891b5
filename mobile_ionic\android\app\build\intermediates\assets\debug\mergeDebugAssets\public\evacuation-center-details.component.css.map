{"version": 3, "sources": ["src/app/pages/map/evacuation-center-details.component.scss"], "sourcesContent": ["ion-header {\r\n  ion-toolbar {\r\n    --background: transparent;\r\n    --border-color: transparent;\r\n  }\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 200px;\r\n\r\n  ion-spinner {\r\n    width: 48px;\r\n    height: 48px;\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  p {\r\n    color: var(--ion-color-medium);\r\n  }\r\n}\r\n\r\n.details-container {\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.center-image {\r\n  width: 100%;\r\n  height: 180px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  margin-bottom: 16px;\r\n  background-color: #f5f5f5;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n\r\n  img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n    object-fit: contain;\r\n    padding: 16px;\r\n  }\r\n}\r\n\r\n.center-name {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  margin: 0 0 8px 0;\r\n  color: var(--ion-color-dark);\r\n}\r\n\r\n.center-type {\r\n  display: flex;\r\n  gap: 8px;\r\n  margin-bottom: 16px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.info-section {\r\n  margin-bottom: 16px;\r\n\r\n  ion-item {\r\n    --padding-start: 0;\r\n    --inner-padding-end: 0;\r\n    --background: transparent;\r\n\r\n    ion-icon {\r\n      font-size: 24px;\r\n    }\r\n\r\n    h2 {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    p {\r\n      font-size: 14px;\r\n      color: var(--ion-color-medium);\r\n    }\r\n  }\r\n}\r\n\r\n.travel-section {\r\n  margin-top: 24px;\r\n\r\n  h2 {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    margin-bottom: 16px;\r\n    color: var(--ion-color-dark);\r\n  }\r\n\r\n  .travel-cards {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 12px;\r\n\r\n    .travel-card {\r\n      margin: 0;\r\n      border-radius: 12px;\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\r\n      transition: transform 0.2s ease, box-shadow 0.2s ease;\r\n\r\n      &:active {\r\n        transform: scale(0.98);\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n      }\r\n\r\n      ion-card-header {\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 12px 16px 0;\r\n\r\n        .travel-icon {\r\n          font-size: 28px;\r\n          margin-right: 12px;\r\n        }\r\n\r\n        ion-card-title {\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n\r\n      ion-card-content {\r\n        padding: 12px 16px 16px;\r\n\r\n        .travel-info {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          margin-bottom: 12px;\r\n\r\n          .travel-time, .travel-distance {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            ion-icon {\r\n              font-size: 18px;\r\n              margin-right: 6px;\r\n              color: var(--ion-color-medium);\r\n            }\r\n\r\n            span {\r\n              font-size: 15px;\r\n              font-weight: 500;\r\n              color: var(--ion-color-dark);\r\n            }\r\n          }\r\n        }\r\n\r\n        ion-button {\r\n          margin-top: 8px;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nion-footer {\r\n  ion-toolbar {\r\n    --background: transparent;\r\n    --border-color: transparent;\r\n    padding: 0 16px 16px;\r\n  }\r\n}\r\n"], "mappings": ";AACE,WAAA;AACE,gBAAA;AACA,kBAAA;;AAIJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA;;AAEA,CAPF,kBAOE;AACE,SAAA;AACA,UAAA;AACA,iBAAA;;AAGF,CAbF,kBAaE;AACE,SAAA,IAAA;;AAIJ,CAAA;AACE,kBAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA;AACA,YAAA;AACA,iBAAA;AACA,oBAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;;AAEA,CAXF,aAWE;AACE,aAAA;AACA,cAAA;AACA,cAAA;AACA,WAAA;;AAIJ,CAAA;AACE,aAAA;AACA,eAAA;AACA,UAAA,EAAA,EAAA,IAAA;AACA,SAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAAA;AACE,iBAAA;;AAEA,CAHF,aAGE;AACE,mBAAA;AACA,uBAAA;AACA,gBAAA;;AAEA,CARJ,aAQI,SAAA;AACE,aAAA;;AAGF,CAZJ,aAYI,SAAA;AACE,aAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CAlBJ,aAkBI,SAAA;AACE,aAAA;AACA,SAAA,IAAA;;AAKN,CAAA;AACE,cAAA;;AAEA,CAHF,eAGE;AACE,aAAA;AACA,eAAA;AACA,iBAAA;AACA,SAAA,IAAA;;AAGF,CAVF,eAUE,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAEA,CAfJ,eAeI,CALF,aAKE,CAAA;AACE,UAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA,UAAA,KAAA,IAAA,EAAA,WAAA,KAAA;;AAEA,CArBN,eAqBM,CAXJ,aAWI,CANF,WAME;AACE,aAAA,MAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CA1BN,eA0BM,CAhBJ,aAgBI,CAXF,YAWE;AACE,WAAA;AACA,eAAA;AACA,WAAA,KAAA,KAAA;;AAEA,CA/BR,eA+BQ,CArBN,aAqBM,CAhBJ,YAgBI,gBAAA,CAAA;AACE,aAAA;AACA,gBAAA;;AAGF,CApCR,eAoCQ,CA1BN,aA0BM,CArBJ,YAqBI,gBAAA;AACE,aAAA;AACA,eAAA;;AAIJ,CA1CN,eA0CM,CAhCJ,aAgCI,CA3BF,YA2BE;AACE,WAAA,KAAA,KAAA;;AAEA,CA7CR,eA6CQ,CAnCN,aAmCM,CA9BJ,YA8BI,iBAAA,CAAA;AACE,WAAA;AACA,mBAAA;AACA,iBAAA;;AAEA,CAlDV,eAkDU,CAxCR,aAwCQ,CAnCN,YAmCM,iBAAA,CALF,YAKE,CAAA;AAAA,CAlDV,eAkDU,CAxCR,aAwCQ,CAnCN,YAmCM,iBAAA,CALF,YAKE,CAAA;AACE,WAAA;AACA,eAAA;;AAEA,CAtDZ,eAsDY,CA5CV,aA4CU,CAvCR,YAuCQ,iBAAA,CATJ,YASI,CAJF,YAIE;AAAA,CAtDZ,eAsDY,CA5CV,aA4CU,CAvCR,YAuCQ,iBAAA,CATJ,YASI,CAJF,gBAIE;AACE,aAAA;AACA,gBAAA;AACA,SAAA,IAAA;;AAGF,CA5DZ,eA4DY,CAlDV,aAkDU,CA7CR,YA6CQ,iBAAA,CAfJ,YAeI,CAVF,YAUE;AAAA,CA5DZ,eA4DY,CAlDV,aAkDU,CA7CR,YA6CQ,iBAAA,CAfJ,YAeI,CAVF,gBAUE;AACE,aAAA;AACA,eAAA;AACA,SAAA,IAAA;;AAKN,CApER,eAoEQ,CA1DN,aA0DM,CArDJ,YAqDI,iBAAA;AACE,cAAA;AACA,eAAA;;AAQR,WAAA;AACE,gBAAA;AACA,kBAAA;AACA,WAAA,EAAA,KAAA;;", "names": []}
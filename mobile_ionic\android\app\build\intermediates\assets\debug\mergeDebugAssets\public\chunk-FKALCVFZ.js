import {
  BehaviorSubject,
  HttpClient,
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-NS3G4TP7.js";
import {
  __async,
  __spreadProps,
  __spreadValues
} from "./chunk-UL2P3LPA.js";

// src/app/services/environment-switcher.service.ts
var EnvironmentSwitcherService = class _EnvironmentSwitcherService {
  constructor(http) {
    this.http = http;
    this.currentApiUrlSubject = new BehaviorSubject("");
    this.currentApiUrl$ = this.currentApiUrlSubject.asObservable();
    this.apiEndpoints = [
      {
        name: "ngrok (Recommended)",
        url: "https://*************************************************.ngrok-free.app/api",
        type: "ngrok",
        description: "Secure tunnel, works from anywhere"
      },
      {
        name: "Home IP (Current)",
        url: "http://***************:8000/api",
        type: "local-ip",
        description: "\u{1F3E0} Home network IP - current location"
      },
      {
        name: "School IP",
        url: "http://*************:8000/api",
        type: "local-ip",
        description: "\u{1F3EB} School network IP - when at school"
      },
      {
        name: "Localhost (Web Only)",
        url: "http://localhost:8000/api",
        type: "localhost",
        description: "Only works in web browser"
      }
    ];
    this.setApiUrl(this.apiEndpoints[1].url);
  }
  /**
   * Get all available API endpoints
   */
  getApiEndpoints() {
    return this.apiEndpoints.map((endpoint) => __spreadProps(__spreadValues({}, endpoint), {
      isActive: endpoint.url === this.getCurrentApiUrl()
    }));
  }
  /**
   * Set the current API URL
   */
  setApiUrl(url) {
    this.currentApiUrlSubject.next(url);
    localStorage.setItem("selectedApiUrl", url);
    console.log("\u{1F504} API URL switched to:", url);
  }
  /**
   * Get the current API URL
   */
  getCurrentApiUrl() {
    const stored = localStorage.getItem("selectedApiUrl");
    if (stored) {
      this.currentApiUrlSubject.next(stored);
      return stored;
    }
    return this.currentApiUrlSubject.value || this.apiEndpoints[1].url;
  }
  /**
   * Test connectivity to an API endpoint
   */
  testEndpoint(url) {
    return __async(this, null, function* () {
      const startTime = Date.now();
      try {
        const healthUrl = url.replace("/api", "/up");
        yield this.http.get(healthUrl, {
          responseType: "text"
        }).toPromise();
        const responseTime = Date.now() - startTime;
        return {
          success: true,
          message: `\u2705 Connected successfully (${responseTime}ms)`,
          responseTime
        };
      } catch (error) {
        const responseTime = Date.now() - startTime;
        let message = "\u274C Connection failed";
        if (error.status === 0) {
          message = "\u274C Network error - Cannot reach server";
        } else if (error.status === 404) {
          message = "\u274C Server found but endpoint not available";
        } else if (error.status >= 500) {
          message = "\u274C Server error";
        } else {
          message = `\u274C Error ${error.status}: ${error.statusText}`;
        }
        return {
          success: false,
          message: `${message} (${responseTime}ms)`,
          responseTime
        };
      }
    });
  }
  /**
   * Test all endpoints and return results
   */
  testAllEndpoints() {
    return __async(this, null, function* () {
      const results = [];
      for (const endpoint of this.apiEndpoints) {
        const testResult = yield this.testEndpoint(endpoint.url);
        results.push(__spreadProps(__spreadValues({}, endpoint), {
          testResult
        }));
      }
      return results;
    });
  }
  /**
   * Auto-detect the best working endpoint
   */
  autoDetectBestEndpoint() {
    return __async(this, null, function* () {
      console.log("\u{1F50D} Auto-detecting best API endpoint...");
      const preferredOrder = ["ngrok", "local-ip", "localhost"];
      for (const type of preferredOrder) {
        const endpoints = this.apiEndpoints.filter((e) => e.type === type);
        for (const endpoint of endpoints) {
          const result = yield this.testEndpoint(endpoint.url);
          if (result.success) {
            console.log("\u2705 Found working endpoint:", endpoint.name);
            this.setApiUrl(endpoint.url);
            return endpoint;
          }
        }
      }
      console.log("\u274C No working endpoints found");
      return null;
    });
  }
  /**
   * Get connection diagnostics
   */
  getConnectionDiagnostics() {
    const diagnostics = [];
    const currentUrl = this.getCurrentApiUrl();
    const currentEndpoint = this.apiEndpoints.find((e) => e.url === currentUrl);
    diagnostics.push("\u{1F517} CONNECTION DIAGNOSTICS");
    diagnostics.push("========================");
    diagnostics.push("");
    diagnostics.push(`\u{1F4E1} Current API: ${currentUrl}`);
    diagnostics.push(`\u{1F3F7}\uFE0F Type: ${currentEndpoint?.type || "unknown"}`);
    diagnostics.push("");
    if (currentEndpoint?.type === "ngrok") {
      diagnostics.push("\u{1F527} NGROK TROUBLESHOOTING:");
      diagnostics.push("1. Check if ngrok is running: http://127.0.0.1:4040");
      diagnostics.push("2. Verify Laravel backend: php artisan serve --host=0.0.0.0 --port=8000");
      diagnostics.push("3. Update ngrok URL if expired");
    } else if (currentEndpoint?.type === "local-ip") {
      diagnostics.push("\u{1F527} LOCAL IP TROUBLESHOOTING:");
      diagnostics.push("1. Both devices must be on same WiFi");
      diagnostics.push("2. Check Windows Firewall settings");
      diagnostics.push("3. Verify backend server is running");
      diagnostics.push("4. Try switching to ngrok for easier setup");
    }
    diagnostics.push("");
    diagnostics.push("\u{1F4A1} QUICK ACTIONS:");
    diagnostics.push("- Use Environment Switcher to test different URLs");
    diagnostics.push("- Run auto-detection to find working endpoint");
    diagnostics.push("- Check network diagnostics page");
    return diagnostics;
  }
  static {
    this.\u0275fac = function EnvironmentSwitcherService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _EnvironmentSwitcherService)(\u0275\u0275inject(HttpClient));
    };
  }
  static {
    this.\u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _EnvironmentSwitcherService, factory: _EnvironmentSwitcherService.\u0275fac, providedIn: "root" });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(EnvironmentSwitcherService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  EnvironmentSwitcherService
};
//# sourceMappingURL=chunk-FKALCVFZ.js.map

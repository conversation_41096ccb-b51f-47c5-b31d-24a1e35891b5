{"version": 3, "sources": ["src/app/pages/register/register.page.scss"], "sourcesContent": [".register-container {\r\n  height: 80vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.register-wrapper {\r\n  width: 100%;\r\n  max-width: 420px;\r\n  padding: 32px 28px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n}\r\n\r\n.register-logo {\r\n  width: 280px;\r\n  height: 280px;\r\n}\r\n\r\n.register-title {\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n \r\n}\r\n\r\n.register-desc {\r\n  color: #888;\r\n  font-size: 1.1rem;\r\n}\r\n.register-form ion-label {\r\n  font-size: 18px;\r\n  color: #888;\r\n}\r\n.register-form ion-item {\r\n  width: 100%;          \r\n  --highlight-color-focused: xz#000000; \r\n  --min-height: 44px;   \r\n  --padding-start: 0;   \r\n  --padding-end: 0;     \r\n  --inner-padding-end: 0;\r\n  --inner-padding-start: 0;\r\n}\r\n\r\n.forgot-link {\r\n  text-align: right;\r\n \r\n  font-size: 0.95rem;\r\n}\r\n.forgot-link a {\r\n  color: #1565c0;\r\n  text-decoration: none;\r\n}\r\n\r\n.register-link a {\r\n  color: #1565c0;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n}\r\n\r\n\r\n\r\n.register-link {\r\n \r\n  font-size: 1rem;\r\n  color: #444;\r\n}\r\n.register-link a {\r\n  color: #1565c0;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n}\r\n\r\n.register-btn {\r\n  --border-radius: 25px;\r\n  font-size: 1.1rem;\r\n  height: 48px;\r\n  width: 100%;\r\n}"], "mappings": ";AAAA,CAAA;AACE,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,WAAA,KAAA;AACA,UAAA,EAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;;AAIF,CAAA;AACE,SAAA;AACA,aAAA;;AAEF,CAAA,cAAA;AACE,aAAA;AACA,SAAA;;AAEF,CAJA,cAIA;AACE,SAAA;AACA,6BAAA,EAAA;AACA,gBAAA;AACA,mBAAA;AACA,iBAAA;AACA,uBAAA;AACA,yBAAA;;AAGF,CAAA;AACE,cAAA;AAEA,aAAA;;AAEF,CALA,YAKA;AACE,SAAA;AACA,mBAAA;;AAGF,CAAA,cAAA;AACE,SAAA;AACA,mBAAA;AACA,eAAA;;AAKF,CARA;AAUE,aAAA;AACA,SAAA;;AAEF,CAbA,cAaA;AACE,SAAA;AACA,mBAAA;AACA,eAAA;;AAGF,CAAA;AACE,mBAAA;AACA,aAAA;AACA,UAAA;AACA,SAAA;;", "names": []}
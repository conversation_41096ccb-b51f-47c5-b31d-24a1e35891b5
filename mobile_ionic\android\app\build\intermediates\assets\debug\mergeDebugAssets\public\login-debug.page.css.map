{"version": 3, "sources": ["src/app/pages/login-debug/login-debug.page.scss"], "sourcesContent": [".debug-container {\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  padding: 16px;\r\n}\r\n\r\n.diagnostic-item {\r\n  margin-bottom: 16px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.diagnostic-header {\r\n  padding: 12px 16px;\r\n  font-weight: 600;\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.diagnostic-header.pending {\r\n  background-color: var(--ion-color-warning);\r\n}\r\n\r\n.diagnostic-header.success {\r\n  background-color: var(--ion-color-success);\r\n}\r\n\r\n.diagnostic-header.error {\r\n  background-color: var(--ion-color-danger);\r\n}\r\n\r\n.diagnostic-header.warning {\r\n  background-color: var(--ion-color-warning);\r\n}\r\n\r\n.diagnostic-content {\r\n  padding: 16px;\r\n  background-color: var(--ion-color-light);\r\n  border-left: 4px solid var(--ion-color-medium);\r\n}\r\n\r\n.diagnostic-content.pending {\r\n  border-left-color: var(--ion-color-warning);\r\n}\r\n\r\n.diagnostic-content.success {\r\n  border-left-color: var(--ion-color-success);\r\n}\r\n\r\n.diagnostic-content.error {\r\n  border-left-color: var(--ion-color-danger);\r\n}\r\n\r\n.diagnostic-content.warning {\r\n  border-left-color: var(--ion-color-warning);\r\n}\r\n\r\n.diagnostic-message {\r\n  font-size: 0.9rem;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.diagnostic-details {\r\n  font-size: 0.8rem;\r\n  color: var(--ion-color-medium);\r\n  font-family: monospace;\r\n  background-color: var(--ion-color-step-50);\r\n  padding: 8px;\r\n  border-radius: 4px;\r\n  white-space: pre-wrap;\r\n  word-break: break-all;\r\n}\r\n\r\n.test-credentials {\r\n  background-color: var(--ion-color-step-100);\r\n  padding: 12px;\r\n  border-radius: 8px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.test-credentials h3 {\r\n  margin: 0 0 8px 0;\r\n  color: var(--ion-color-primary);\r\n}\r\n\r\n.test-credentials p {\r\n  margin: 4px 0;\r\n  font-size: 0.9rem;\r\n  color: var(--ion-color-medium);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.action-buttons ion-button {\r\n  flex: 1;\r\n}\r\n\r\nion-card {\r\n  margin-bottom: 16px;\r\n}\r\n\r\nion-card-title {\r\n  color: var(--ion-color-primary);\r\n}\r\n\r\n.status-icon {\r\n  font-size: 1.2rem;\r\n}\r\n\r\n.loading-spinner {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n\r\n.error-text {\r\n  color: var(--ion-color-danger);\r\n}\r\n\r\n.success-text {\r\n  color: var(--ion-color-success);\r\n}\r\n\r\n.warning-text {\r\n  color: var(--ion-color-warning);\r\n}\r\n"], "mappings": ";AAAA,CAAA;AACE,aAAA;AACA,UAAA,EAAA;AACA,WAAA;;AAGF,CAAA;AACE,iBAAA;AACA,iBAAA;AACA,YAAA;;AAGF,CAAA;AACE,WAAA,KAAA;AACA,eAAA;AACA,SAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CATA,iBASA,CAAA;AACE,oBAAA,IAAA;;AAGF,CAbA,iBAaA,CAAA;AACE,oBAAA,IAAA;;AAGF,CAjBA,iBAiBA,CAAA;AACE,oBAAA,IAAA;;AAGF,CArBA,iBAqBA,CAAA;AACE,oBAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,oBAAA,IAAA;AACA,eAAA,IAAA,MAAA,IAAA;;AAGF,CANA,kBAMA,CAtBA;AAuBE,qBAAA,IAAA;;AAGF,CAVA,kBAUA,CAtBA;AAuBE,qBAAA,IAAA;;AAGF,CAdA,kBAcA,CAtBA;AAuBE,qBAAA,IAAA;;AAGF,CAlBA,kBAkBA,CAtBA;AAuBE,qBAAA,IAAA;;AAGF,CAAA;AACE,aAAA;AACA,iBAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA,IAAA;AACA,eAAA;AACA,oBAAA,IAAA;AACA,WAAA;AACA,iBAAA;AACA,eAAA;AACA,cAAA;;AAGF,CAAA;AACE,oBAAA,IAAA;AACA,WAAA;AACA,iBAAA;AACA,iBAAA;;AAGF,CAPA,iBAOA;AACE,UAAA,EAAA,EAAA,IAAA;AACA,SAAA,IAAA;;AAGF,CAZA,iBAYA;AACE,UAAA,IAAA;AACA,aAAA;AACA,SAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,cAAA;;AAGF,CANA,eAMA;AACE,QAAA;;AAGF;AACE,iBAAA;;AAGF;AACE,SAAA,IAAA;;AAGF,CAAA;AACE,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;;AAGF,CAAA;AACE,SAAA,IAAA;;AAGF,CAAA;AACE,SAAA,IAAA;;AAGF,CAAA;AACE,SAAA,IAAA;;", "names": []}
{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/watch-options-c2911ace.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst watchForOptions = (containerEl, tagName, onChange) => {\n  if (typeof MutationObserver === 'undefined') {\n    return;\n  }\n  const mutation = new MutationObserver(mutationList => {\n    onChange(getSelectedOption(mutationList, tagName));\n  });\n  mutation.observe(containerEl, {\n    childList: true,\n    subtree: true\n  });\n  return mutation;\n};\nconst getSelectedOption = (mutationList, tagName) => {\n  let newOption;\n  mutationList.forEach(mut => {\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let i = 0; i < mut.addedNodes.length; i++) {\n      newOption = findCheckedOption(mut.addedNodes[i], tagName) || newOption;\n    }\n  });\n  return newOption;\n};\n/**\n * The \"value\" key is only set on some components such as ion-select-option.\n * As a result, we create a default union type of HTMLElement and the \"value\" key.\n * However, implementers are required to provide the appropriate component type\n * such as HTMLIonSelectOptionElement.\n */\nconst findCheckedOption = (node, tagName) => {\n  /**\n   * https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\n   * The above check ensures \"node\" is an Element (nodeType 1).\n   */\n  if (node.nodeType !== 1) {\n    return undefined;\n  }\n  // HTMLElement inherits from Element, so we cast \"el\" as T.\n  const el = node;\n  const options = el.tagName === tagName.toUpperCase() ? [el] : Array.from(el.querySelectorAll(tagName));\n  return options.find(o => o.value === el.value);\n};\nexport { watchForOptions as w };"], "mappings": ";AAGA,IAAM,kBAAkB,CAAC,aAAa,SAAS,aAAa;AAC1D,MAAI,OAAO,qBAAqB,aAAa;AAC3C;AAAA,EACF;AACA,QAAM,WAAW,IAAI,iBAAiB,kBAAgB;AACpD,aAAS,kBAAkB,cAAc,OAAO,CAAC;AAAA,EACnD,CAAC;AACD,WAAS,QAAQ,aAAa;AAAA,IAC5B,WAAW;AAAA,IACX,SAAS;AAAA,EACX,CAAC;AACD,SAAO;AACT;AACA,IAAM,oBAAoB,CAAC,cAAc,YAAY;AACnD,MAAI;AACJ,eAAa,QAAQ,SAAO;AAE1B,aAAS,IAAI,GAAG,IAAI,IAAI,WAAW,QAAQ,KAAK;AAC9C,kBAAY,kBAAkB,IAAI,WAAW,CAAC,GAAG,OAAO,KAAK;AAAA,IAC/D;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAOA,IAAM,oBAAoB,CAAC,MAAM,YAAY;AAK3C,MAAI,KAAK,aAAa,GAAG;AACvB,WAAO;AAAA,EACT;AAEA,QAAM,KAAK;AACX,QAAM,UAAU,GAAG,YAAY,QAAQ,YAAY,IAAI,CAAC,EAAE,IAAI,MAAM,KAAK,GAAG,iBAAiB,OAAO,CAAC;AACrG,SAAO,QAAQ,KAAK,OAAK,EAAE,UAAU,GAAG,KAAK;AAC/C;", "names": [], "x_google_ignoreList": [0]}
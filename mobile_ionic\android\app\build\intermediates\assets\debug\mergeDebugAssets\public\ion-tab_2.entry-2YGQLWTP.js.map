{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-tab_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement, c as createEvent } from './index-527b9e34.js';\nimport { a as attachComponent } from './framework-delegate-56b467ad.js';\nimport { d as printIonError } from './index-cfd9c1f2.js';\nimport './helpers-d94bc8ad.js';\nconst tabCss = \":host(.tab-hidden){display:none !important}\";\nconst IonTabStyle0 = tabCss;\nconst Tab = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.loaded = false;\n    this.active = false;\n    this.delegate = undefined;\n    this.tab = undefined;\n    this.component = undefined;\n  }\n  async componentWillLoad() {\n    if (this.active) {\n      await this.setActive();\n    }\n  }\n  /** Set the active component for the tab */\n  async setActive() {\n    await this.prepareLazyLoaded();\n    this.active = true;\n  }\n  changeActive(isActive) {\n    if (isActive) {\n      this.prepareLazyLoaded();\n    }\n  }\n  prepareLazyLoaded() {\n    if (!this.loaded && this.component != null) {\n      this.loaded = true;\n      try {\n        return attachComponent(this.delegate, this.el, this.component, ['ion-page']);\n      } catch (e) {\n        printIonError('[ion-tab] - Exception in prepareLazyLoaded:', e);\n      }\n    }\n    return Promise.resolve(undefined);\n  }\n  render() {\n    const {\n      tab,\n      active,\n      component\n    } = this;\n    return h(Host, {\n      key: 'c36c113e74e12b58459df9e3b546ad4856187e90',\n      role: \"tabpanel\",\n      \"aria-hidden\": !active ? 'true' : null,\n      \"aria-labelledby\": `tab-button-${tab}`,\n      class: {\n        'ion-page': component === undefined,\n        'tab-hidden': !active\n      }\n    }, h(\"slot\", {\n      key: '0d7821dac70ba7a12edfb3331988f3df1566cc1a'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"active\": [\"changeActive\"]\n    };\n  }\n};\nTab.style = IonTabStyle0;\nconst tabsCss = \":host{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:100%;height:100%;contain:layout size style;z-index:0}.tabs-inner{position:relative;-ms-flex:1;flex:1;contain:layout size style}\";\nconst IonTabsStyle0 = tabsCss;\nconst Tabs = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n    this.ionTabsWillChange = createEvent(this, \"ionTabsWillChange\", 3);\n    this.ionTabsDidChange = createEvent(this, \"ionTabsDidChange\", 3);\n    this.transitioning = false;\n    this.onTabClicked = ev => {\n      const {\n        href,\n        tab\n      } = ev.detail;\n      if (this.useRouter && href !== undefined) {\n        const router = document.querySelector('ion-router');\n        if (router) {\n          router.push(href);\n        }\n      } else {\n        this.select(tab);\n      }\n    };\n    this.selectedTab = undefined;\n    this.useRouter = false;\n  }\n  async componentWillLoad() {\n    if (!this.useRouter) {\n      /**\n       * JavaScript and StencilJS use `ion-router`, while\n       * the other frameworks use `ion-router-outlet`.\n       *\n       * If either component is present then tabs will not use\n       * a basic tab-based navigation. It will use the history\n       * stack or URL updates associated with the router.\n       */\n      this.useRouter = (!!this.el.querySelector('ion-router-outlet') || !!document.querySelector('ion-router')) && !this.el.closest('[no-router]');\n    }\n    if (!this.useRouter) {\n      const tabs = this.tabs;\n      if (tabs.length > 0) {\n        await this.select(tabs[0]);\n      }\n    }\n    this.ionNavWillLoad.emit();\n  }\n  componentWillRender() {\n    const tabBar = this.el.querySelector('ion-tab-bar');\n    if (tabBar) {\n      const tab = this.selectedTab ? this.selectedTab.tab : undefined;\n      tabBar.selectedTab = tab;\n    }\n  }\n  /**\n   * Select a tab by the value of its `tab` property or an element reference. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n   *\n   * @param tab The tab instance to select. If passed a string, it should be the value of the tab's `tab` property.\n   */\n  async select(tab) {\n    const selectedTab = getTab(this.tabs, tab);\n    if (!this.shouldSwitch(selectedTab)) {\n      return false;\n    }\n    await this.setActive(selectedTab);\n    await this.notifyRouter();\n    this.tabSwitch();\n    return true;\n  }\n  /**\n   * Get a specific tab by the value of its `tab` property or an element reference. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n   *\n   * @param tab The tab instance to select. If passed a string, it should be the value of the tab's `tab` property.\n   */\n  async getTab(tab) {\n    return getTab(this.tabs, tab);\n  }\n  /**\n   * Get the currently selected tab. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n   */\n  getSelected() {\n    return Promise.resolve(this.selectedTab ? this.selectedTab.tab : undefined);\n  }\n  /** @internal */\n  async setRouteId(id) {\n    const selectedTab = getTab(this.tabs, id);\n    if (!this.shouldSwitch(selectedTab)) {\n      return {\n        changed: false,\n        element: this.selectedTab\n      };\n    }\n    await this.setActive(selectedTab);\n    return {\n      changed: true,\n      element: this.selectedTab,\n      markVisible: () => this.tabSwitch()\n    };\n  }\n  /** @internal */\n  async getRouteId() {\n    var _a;\n    const tabId = (_a = this.selectedTab) === null || _a === void 0 ? void 0 : _a.tab;\n    return tabId !== undefined ? {\n      id: tabId,\n      element: this.selectedTab\n    } : undefined;\n  }\n  setActive(selectedTab) {\n    if (this.transitioning) {\n      return Promise.reject('transitioning already happening');\n    }\n    this.transitioning = true;\n    this.leavingTab = this.selectedTab;\n    this.selectedTab = selectedTab;\n    this.ionTabsWillChange.emit({\n      tab: selectedTab.tab\n    });\n    selectedTab.active = true;\n    return Promise.resolve();\n  }\n  tabSwitch() {\n    const selectedTab = this.selectedTab;\n    const leavingTab = this.leavingTab;\n    this.leavingTab = undefined;\n    this.transitioning = false;\n    if (!selectedTab) {\n      return;\n    }\n    if (leavingTab !== selectedTab) {\n      if (leavingTab) {\n        leavingTab.active = false;\n      }\n      this.ionTabsDidChange.emit({\n        tab: selectedTab.tab\n      });\n    }\n  }\n  notifyRouter() {\n    if (this.useRouter) {\n      const router = document.querySelector('ion-router');\n      if (router) {\n        return router.navChanged('forward');\n      }\n    }\n    return Promise.resolve(false);\n  }\n  shouldSwitch(selectedTab) {\n    const leavingTab = this.selectedTab;\n    return selectedTab !== undefined && selectedTab !== leavingTab && !this.transitioning;\n  }\n  get tabs() {\n    return Array.from(this.el.querySelectorAll('ion-tab'));\n  }\n  render() {\n    return h(Host, {\n      key: '20b97196d78c1b3f3faf31618a8a2347e087f06b',\n      onIonTabButtonClick: this.onTabClicked\n    }, h(\"slot\", {\n      key: 'b0823fbae6e47743cfd12c376b365ad7e32cec7c',\n      name: \"top\"\n    }), h(\"div\", {\n      key: 'eaffd7e4d69ab9489a387e3bbb36e3bab72203a0',\n      class: \"tabs-inner\"\n    }, h(\"slot\", {\n      key: '20bb66a2937e3ec473aa59c4075ce581b5411677'\n    })), h(\"slot\", {\n      key: '1529dd361f050f52074f51c73b3982ba827dc3a5',\n      name: \"bottom\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst getTab = (tabs, tab) => {\n  const tabEl = typeof tab === 'string' ? tabs.find(t => t.tab === tab) : tab;\n  if (!tabEl) {\n    printIonError(`[ion-tabs] - Tab with id: \"${tabEl}\" does not exist`);\n  }\n  return tabEl;\n};\nTabs.style = IonTabsStyle0;\nexport { Tab as ion_tab, Tabs as ion_tabs };"], "mappings": ";;;;;;;;;;;;;;;;;;;AAOA,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,MAAM,MAAM;AAAA,EAChB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,YAAY;AAAA,EACnB;AAAA,EACM,oBAAoB;AAAA;AACxB,UAAI,KAAK,QAAQ;AACf,cAAM,KAAK,UAAU;AAAA,MACvB;AAAA,IACF;AAAA;AAAA;AAAA,EAEM,YAAY;AAAA;AAChB,YAAM,KAAK,kBAAkB;AAC7B,WAAK,SAAS;AAAA,IAChB;AAAA;AAAA,EACA,aAAa,UAAU;AACrB,QAAI,UAAU;AACZ,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,QAAI,CAAC,KAAK,UAAU,KAAK,aAAa,MAAM;AAC1C,WAAK,SAAS;AACd,UAAI;AACF,eAAO,gBAAgB,KAAK,UAAU,KAAK,IAAI,KAAK,WAAW,CAAC,UAAU,CAAC;AAAA,MAC7E,SAAS,GAAG;AACV,sBAAc,+CAA+C,CAAC;AAAA,MAChE;AAAA,IACF;AACA,WAAO,QAAQ,QAAQ,MAAS;AAAA,EAClC;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,MAAM;AAAA,MACN,eAAe,CAAC,SAAS,SAAS;AAAA,MAClC,mBAAmB,cAAc,GAAG;AAAA,MACpC,OAAO;AAAA,QACL,YAAY,cAAc;AAAA,QAC1B,cAAc,CAAC;AAAA,MACjB;AAAA,IACF,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,UAAU,CAAC,cAAc;AAAA,IAC3B;AAAA,EACF;AACF;AACA,IAAI,QAAQ;AACZ,IAAM,UAAU;AAChB,IAAM,gBAAgB;AACtB,IAAM,OAAO,MAAM;AAAA,EACjB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,oBAAoB,YAAY,MAAM,qBAAqB,CAAC;AACjE,SAAK,mBAAmB,YAAY,MAAM,oBAAoB,CAAC;AAC/D,SAAK,gBAAgB;AACrB,SAAK,eAAe,QAAM;AACxB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,GAAG;AACP,UAAI,KAAK,aAAa,SAAS,QAAW;AACxC,cAAM,SAAS,SAAS,cAAc,YAAY;AAClD,YAAI,QAAQ;AACV,iBAAO,KAAK,IAAI;AAAA,QAClB;AAAA,MACF,OAAO;AACL,aAAK,OAAO,GAAG;AAAA,MACjB;AAAA,IACF;AACA,SAAK,cAAc;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA,EACM,oBAAoB;AAAA;AACxB,UAAI,CAAC,KAAK,WAAW;AASnB,aAAK,aAAa,CAAC,CAAC,KAAK,GAAG,cAAc,mBAAmB,KAAK,CAAC,CAAC,SAAS,cAAc,YAAY,MAAM,CAAC,KAAK,GAAG,QAAQ,aAAa;AAAA,MAC7I;AACA,UAAI,CAAC,KAAK,WAAW;AACnB,cAAM,OAAO,KAAK;AAClB,YAAI,KAAK,SAAS,GAAG;AACnB,gBAAM,KAAK,OAAO,KAAK,CAAC,CAAC;AAAA,QAC3B;AAAA,MACF;AACA,WAAK,eAAe,KAAK;AAAA,IAC3B;AAAA;AAAA,EACA,sBAAsB;AACpB,UAAM,SAAS,KAAK,GAAG,cAAc,aAAa;AAClD,QAAI,QAAQ;AACV,YAAM,MAAM,KAAK,cAAc,KAAK,YAAY,MAAM;AACtD,aAAO,cAAc;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,OAAO,KAAK;AAAA;AAChB,YAAM,cAAc,OAAO,KAAK,MAAM,GAAG;AACzC,UAAI,CAAC,KAAK,aAAa,WAAW,GAAG;AACnC,eAAO;AAAA,MACT;AACA,YAAM,KAAK,UAAU,WAAW;AAChC,YAAM,KAAK,aAAa;AACxB,WAAK,UAAU;AACf,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,OAAO,KAAK;AAAA;AAChB,aAAO,OAAO,KAAK,MAAM,GAAG;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,QAAQ,QAAQ,KAAK,cAAc,KAAK,YAAY,MAAM,MAAS;AAAA,EAC5E;AAAA;AAAA,EAEM,WAAW,IAAI;AAAA;AACnB,YAAM,cAAc,OAAO,KAAK,MAAM,EAAE;AACxC,UAAI,CAAC,KAAK,aAAa,WAAW,GAAG;AACnC,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS,KAAK;AAAA,QAChB;AAAA,MACF;AACA,YAAM,KAAK,UAAU,WAAW;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,KAAK;AAAA,QACd,aAAa,MAAM,KAAK,UAAU;AAAA,MACpC;AAAA,IACF;AAAA;AAAA;AAAA,EAEM,aAAa;AAAA;AACjB,UAAI;AACJ,YAAM,SAAS,KAAK,KAAK,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC9E,aAAO,UAAU,SAAY;AAAA,QAC3B,IAAI;AAAA,QACJ,SAAS,KAAK;AAAA,MAChB,IAAI;AAAA,IACN;AAAA;AAAA,EACA,UAAU,aAAa;AACrB,QAAI,KAAK,eAAe;AACtB,aAAO,QAAQ,OAAO,iCAAiC;AAAA,IACzD;AACA,SAAK,gBAAgB;AACrB,SAAK,aAAa,KAAK;AACvB,SAAK,cAAc;AACnB,SAAK,kBAAkB,KAAK;AAAA,MAC1B,KAAK,YAAY;AAAA,IACnB,CAAC;AACD,gBAAY,SAAS;AACrB,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA,EACA,YAAY;AACV,UAAM,cAAc,KAAK;AACzB,UAAM,aAAa,KAAK;AACxB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,QAAI,eAAe,aAAa;AAC9B,UAAI,YAAY;AACd,mBAAW,SAAS;AAAA,MACtB;AACA,WAAK,iBAAiB,KAAK;AAAA,QACzB,KAAK,YAAY;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe;AACb,QAAI,KAAK,WAAW;AAClB,YAAM,SAAS,SAAS,cAAc,YAAY;AAClD,UAAI,QAAQ;AACV,eAAO,OAAO,WAAW,SAAS;AAAA,MACpC;AAAA,IACF;AACA,WAAO,QAAQ,QAAQ,KAAK;AAAA,EAC9B;AAAA,EACA,aAAa,aAAa;AACxB,UAAM,aAAa,KAAK;AACxB,WAAO,gBAAgB,UAAa,gBAAgB,cAAc,CAAC,KAAK;AAAA,EAC1E;AAAA,EACA,IAAI,OAAO;AACT,WAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,SAAS,CAAC;AAAA,EACvD;AAAA,EACA,SAAS;AACP,WAAO,EAAE,MAAM;AAAA,MACb,KAAK;AAAA,MACL,qBAAqB,KAAK;AAAA,IAC5B,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,KAAK;AAAA,MACL,OAAO;AAAA,IACT,GAAG,EAAE,QAAQ;AAAA,MACX,KAAK;AAAA,IACP,CAAC,CAAC,GAAG,EAAE,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,MAAM;AAAA,IACR,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,IAAM,SAAS,CAAC,MAAM,QAAQ;AAC5B,QAAM,QAAQ,OAAO,QAAQ,WAAW,KAAK,KAAK,OAAK,EAAE,QAAQ,GAAG,IAAI;AACxE,MAAI,CAAC,OAAO;AACV,kBAAc,8BAA8B,KAAK,kBAAkB;AAAA,EACrE;AACA,SAAO;AACT;AACA,KAAK,QAAQ;", "names": [], "x_google_ignoreList": [0]}
import {
  CommonModule,
  Component,
  FormsModule,
  IonAlert,
  IonContent,
  IonicModule,
  Router,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵtext
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import "./chunk-UL2P3LPA.js";

// src/app/pages/loading/loading.page.ts
var _c0 = () => ["OK"];
var LoadingPage = class _LoadingPage {
  constructor(router) {
    this.router = router;
    this.isOnline = false;
  }
  ngOnInit() {
    console.log("LoadingPage ngOnInit");
    const win = window;
    if (win.appDebug) {
      win.appDebug("LoadingPage ngOnInit");
    }
    this.checkInternetConnection();
  }
  checkInternetConnection() {
    this.isOnline = navigator.onLine;
    console.log("LoadingPage checkInternetConnection, isOnline:", this.isOnline);
    const win = window;
    if (win.appDebug) {
      win.appDebug("LoadingPage checkInternetConnection, isOnline: " + this.isOnline);
    }
    const token = localStorage.getItem("token");
    const onboardingComplete = localStorage.getItem("onboardingComplete");
    console.log("Auth status - Token:", !!token, "Onboarding complete:", onboardingComplete === "true", "Online:", this.isOnline);
    setTimeout(() => {
      if (token && onboardingComplete === "true") {
        console.log("User is authenticated and onboarding complete - navigating to tabs/home");
        if (win.appDebug) {
          win.appDebug("LoadingPage navigating to tabs/home (authenticated & onboarded)");
        }
        this.router.navigate(["/tabs/home"]);
      } else if (token) {
        console.log("User is authenticated but onboarding incomplete - navigating to welcome");
        if (win.appDebug) {
          win.appDebug("LoadingPage navigating to welcome (authenticated but not onboarded)");
        }
        this.router.navigate(["/welcome"]);
      } else {
        console.log("User is not authenticated - navigating to login");
        if (win.appDebug) {
          win.appDebug("LoadingPage navigating to login (not authenticated)");
        }
        this.router.navigate(["/login"]);
      }
    }, 1e3);
  }
  ionViewWillEnter() {
    window.addEventListener("online", this.updateOnlineStatus.bind(this));
    window.addEventListener("offline", this.updateOnlineStatus.bind(this));
  }
  ionViewWillLeave() {
    window.removeEventListener("online", this.updateOnlineStatus.bind(this));
    window.removeEventListener("offline", this.updateOnlineStatus.bind(this));
  }
  updateOnlineStatus() {
    this.isOnline = navigator.onLine;
    const win = window;
    if (win.appDebug) {
      win.appDebug("LoadingPage updateOnlineStatus, isOnline: " + this.isOnline);
    }
    this.checkInternetConnection();
  }
  static {
    this.\u0275fac = function LoadingPage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _LoadingPage)(\u0275\u0275directiveInject(Router));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _LoadingPage, selectors: [["app-loading"]], decls: 6, vars: 3, consts: [[1, "ion-padding"], [1, "loading-container"], [1, "loader"], ["header", "No Internet Connection", "message", "Please check your internet connection and try again.", 3, "isOpen", "buttons"]], template: function LoadingPage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-content", 0)(1, "div", 1);
        \u0275\u0275element(2, "div", 2);
        \u0275\u0275elementStart(3, "h2");
        \u0275\u0275text(4, "Loading...");
        \u0275\u0275elementEnd();
        \u0275\u0275element(5, "ion-alert", 3);
        \u0275\u0275elementEnd()();
      }
      if (rf & 2) {
        \u0275\u0275advance(5);
        \u0275\u0275property("isOpen", !ctx.isOnline)("buttons", \u0275\u0275pureFunction0(2, _c0));
      }
    }, dependencies: [IonicModule, IonAlert, IonContent, CommonModule, FormsModule], styles: ["\n\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  text-align: center;\n}\n.loader[_ngcontent-%COMP%] {\n  width: 48px;\n  height: 48px;\n  border: 5px solid #FFF;\n  border-bottom-color: #3880ff;\n  border-radius: 50%;\n  display: inline-block;\n  box-sizing: border-box;\n  animation: _ngcontent-%COMP%_rotation 1s linear infinite;\n  margin-bottom: 20px;\n}\n@keyframes _ngcontent-%COMP%_rotation {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\nh2[_ngcontent-%COMP%] {\n  color: #3880ff;\n  font-size: 24px;\n  margin: 0;\n}\n/*# sourceMappingURL=loading.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoadingPage, [{
    type: Component,
    args: [{ selector: "app-loading", standalone: true, imports: [IonicModule, CommonModule, FormsModule], template: `<ion-content class="ion-padding">\r
  <div class="loading-container">\r
    <div class="loader"></div>\r
    <h2>Loading...</h2>\r
    \r
    <ion-alert\r
      [isOpen]="!isOnline"\r
      header="No Internet Connection"\r
      message="Please check your internet connection and try again."\r
      [buttons]="['OK']"\r
    ></ion-alert>\r
  </div>\r
</ion-content> `, styles: ["/* src/app/pages/loading/loading.page.scss */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  text-align: center;\n}\n.loader {\n  width: 48px;\n  height: 48px;\n  border: 5px solid #FFF;\n  border-bottom-color: #3880ff;\n  border-radius: 50%;\n  display: inline-block;\n  box-sizing: border-box;\n  animation: rotation 1s linear infinite;\n  margin-bottom: 20px;\n}\n@keyframes rotation {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\nh2 {\n  color: #3880ff;\n  font-size: 24px;\n  margin: 0;\n}\n/*# sourceMappingURL=loading.page.css.map */\n"] }]
  }], () => [{ type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(LoadingPage, { className: "LoadingPage", filePath: "src/app/pages/loading/loading.page.ts", lineNumber: 14 });
})();
export {
  LoadingPage
};
//# sourceMappingURL=loading.page-IBDZ6QFI.js.map

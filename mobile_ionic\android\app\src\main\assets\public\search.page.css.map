{"version": 3, "sources": ["src/app/pages/search/search.page.scss"], "sourcesContent": ["ion-content {\r\n  --background: #f8f9fa;\r\n}\r\n\r\n.search-container {\r\n  padding: 10px 16px 0;\r\n\r\n  ion-searchbar {\r\n    --border-radius: 10px;\r\n    --box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\r\n    --placeholder-color: var(--ion-color-medium);\r\n    --icon-color: var(--ion-color-primary);\r\n  }\r\n\r\n  .search-hint {\r\n    font-size: 12px;\r\n    margin: 0 0 10px 16px;\r\n    display: block;\r\n  }\r\n}\r\n\r\n.search-results {\r\n  padding: 0 16px 16px;\r\n}\r\n\r\n.loading-container,\r\n.error-container,\r\n.no-results,\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n  padding: 32px 16px;\r\n  min-height: 200px;\r\n\r\n  ion-icon {\r\n    font-size: 48px;\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  p {\r\n    margin: 8px 0;\r\n    font-size: 16px;\r\n  }\r\n\r\n  ion-button {\r\n    margin-top: 16px;\r\n  }\r\n}\r\n\r\nion-list {\r\n  background: transparent;\r\n  padding: 0;\r\n}\r\n\r\nion-item {\r\n  --padding-start: 16px;\r\n  --inner-padding-end: 16px;\r\n  --background: white;\r\n  margin-bottom: 10px;\r\n  border-radius: 10px;\r\n  --border-radius: 10px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n\r\n  h2 {\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    margin-bottom: 4px;\r\n    color: var(--ion-color-dark);\r\n  }\r\n\r\n  p {\r\n    font-size: 14px;\r\n    color: var(--ion-color-medium);\r\n    margin: 2px 0;\r\n  }\r\n\r\n  ion-badge {\r\n    margin-right: 6px;\r\n    padding: 4px 8px;\r\n    border-radius: 4px;\r\n  }\r\n}\r\n\r\n.empty-state {\r\n  h3 {\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    margin: 8px 0;\r\n    color: var(--ion-color-dark);\r\n  }\r\n\r\n  p {\r\n    margin-bottom: 20px;\r\n  }\r\n}"], "mappings": ";AAAA;AACE,gBAAA;;AAGF,CAAA;AACE,WAAA,KAAA,KAAA;;AAEA,CAHF,iBAGE;AACE,mBAAA;AACA,gBAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,uBAAA,IAAA;AACA,gBAAA,IAAA;;AAGF,CAVF,iBAUE,CAAA;AACE,aAAA;AACA,UAAA,EAAA,EAAA,KAAA;AACA,WAAA;;AAIJ,CAAA;AACE,WAAA,EAAA,KAAA;;AAGF,CAAA;AAAA,CAAA;AAAA,CAAA;AAAA,CAAA;AAIE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,cAAA;AACA,WAAA,KAAA;AACA,cAAA;;AAEA,CAZF,kBAYE;AAAA,CAZF,gBAYE;AAAA,CAZF,WAYE;AAAA,CAZF,YAYE;AACE,aAAA;AACA,iBAAA;;AAGF,CAjBF,kBAiBE;AAAA,CAjBF,gBAiBE;AAAA,CAjBF,WAiBE;AAAA,CAjBF,YAiBE;AACE,UAAA,IAAA;AACA,aAAA;;AAGF,CAtBF,kBAsBE;AAAA,CAtBF,gBAsBE;AAAA,CAtBF,WAsBE;AAAA,CAtBF,YAsBE;AACE,cAAA;;AAIJ;AACE,cAAA;AACA,WAAA;;AAGF;AACE,mBAAA;AACA,uBAAA;AACA,gBAAA;AACA,iBAAA;AACA,iBAAA;AACA,mBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,SAAA;AACE,aAAA;AACA,eAAA;AACA,iBAAA;AACA,SAAA,IAAA;;AAGF,SAAA;AACE,aAAA;AACA,SAAA,IAAA;AACA,UAAA,IAAA;;AAGF,SAAA;AACE,gBAAA;AACA,WAAA,IAAA;AACA,iBAAA;;AAKF,CA9DF,YA8DE;AACE,aAAA;AACA,eAAA;AACA,UAAA,IAAA;AACA,SAAA,IAAA;;AAGF,CArEF,YAqEE;AACE,iBAAA;;", "names": []}
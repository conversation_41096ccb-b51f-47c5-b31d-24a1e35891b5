{"version": 3, "sources": ["src/app/components/fcm-refresh/fcm-refresh.component.ts", "src/app/components/fcm-refresh/fcm-refresh.component.html", "src/app/pages/settings/settings.page.ts", "src/app/pages/settings/settings.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { IonicModule, AlertController, LoadingController } from '@ionic/angular';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FcmService } from '../../services/fcm.service';\r\nimport { AuthService } from '../../services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-fcm-refresh',\r\n  templateUrl: './fcm-refresh.component.html',\r\n  styleUrls: ['./fcm-refresh.component.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule]\r\n})\r\nexport class FcmRefreshComponent implements OnInit {\r\n  userId: number | null = null;\r\n\r\n  constructor(\r\n    private fcmService: FcmService,\r\n    private authService: AuthService,\r\n    private alertController: AlertController,\r\n    private loadingController: LoadingController\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    // Try to get user ID from local storage\r\n    const token = localStorage.getItem('token');\r\n    if (token) {\r\n      try {\r\n        const tokenData = this.parseJwt(token);\r\n        if (tokenData && tokenData.sub) {\r\n          this.userId = tokenData.sub;\r\n        }\r\n      } catch (error) {\r\n        console.error('Error parsing JWT token:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parse a JWT token to get the payload\r\n   * @param token The JWT token to parse\r\n   * @returns The decoded token payload\r\n   */\r\n  private parseJwt(token: string): any {\r\n    try {\r\n      const base64Url = token.split('.')[1];\r\n      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\r\n      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {\r\n        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\r\n      }).join(''));\r\n      return JSON.parse(jsonPayload);\r\n    } catch (error) {\r\n      console.error('Error parsing JWT token:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Refresh the FCM token and re-register with the backend\r\n   */\r\n  async refreshFCMToken() {\r\n    const loading = await this.loadingController.create({\r\n      message: 'Refreshing notification settings...',\r\n      spinner: 'circles'\r\n    });\r\n    \r\n    await loading.present();\r\n    \r\n    try {\r\n      const success = await this.fcmService.refreshFCMToken(this.userId || undefined);\r\n      \r\n      await loading.dismiss();\r\n      \r\n      if (success) {\r\n        const alert = await this.alertController.create({\r\n          header: 'Success',\r\n          message: 'Notification settings refreshed successfully. You should now be able to receive notifications.',\r\n          buttons: ['OK']\r\n        });\r\n        \r\n        await alert.present();\r\n      } else {\r\n        const alert = await this.alertController.create({\r\n          header: 'Error',\r\n          message: 'Failed to refresh notification settings. Please try again later.',\r\n          buttons: ['OK']\r\n        });\r\n        \r\n        await alert.present();\r\n      }\r\n    } catch (error) {\r\n      await loading.dismiss();\r\n      \r\n      const alert = await this.alertController.create({\r\n        header: 'Error',\r\n        message: 'An error occurred while refreshing notification settings. Please try again later.',\r\n        buttons: ['OK']\r\n      });\r\n      \r\n      await alert.present();\r\n    }\r\n  }\r\n}\r\n", "<ion-card>\r\n  <ion-card-header>\r\n    <ion-card-title>Notification Settings</ion-card-title>\r\n    <ion-card-subtitle>Not receiving notifications?</ion-card-subtitle>\r\n  </ion-card-header>\r\n  \r\n  <ion-card-content>\r\n    <p>If you're not receiving notifications, you can try refreshing your notification settings.</p>\r\n    <p>This will generate a new notification token and register it with our servers.</p>\r\n    \r\n    <ion-button expand=\"block\" (click)=\"refreshFCMToken()\">\r\n      <ion-icon name=\"refresh-outline\" slot=\"start\"></ion-icon>\r\n      Refresh Notification Settings\r\n    </ion-button>\r\n  </ion-card-content>\r\n</ion-card>\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FcmRefreshComponent } from '../../components/fcm-refresh/fcm-refresh.component';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-settings',\r\n  templateUrl: './settings.page.html',\r\n  styleUrls: ['./settings.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FcmRefreshComponent]\r\n})\r\nexport class SettingsPage implements OnInit {\r\n\r\n  constructor(private router: Router) { }\r\n\r\n  ngOnInit() {\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigate(['/tabs/home']);\r\n  }\r\n\r\n  logout() {\r\n    // Clear authentication token\r\n    localStorage.removeItem('token');\r\n    \r\n    // Navigate to login page\r\n    this.router.navigate(['/login']);\r\n  }\r\n}\r\n", "<ion-header>\r\n  <ion-toolbar>\r\n    <ion-buttons slot=\"start\">\r\n      <ion-button (click)=\"goBack()\">\r\n        <ion-icon slot=\"icon-only\" name=\"arrow-back\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n    <ion-title>Settings</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content>\r\n  <ion-list>\r\n    <ion-item-group>\r\n      <ion-item-divider>\r\n        <ion-label>Notifications</ion-label>\r\n      </ion-item-divider>\r\n      \r\n      <!-- FCM Refresh Component -->\r\n      <app-fcm-refresh></app-fcm-refresh>\r\n    </ion-item-group>\r\n    \r\n    <ion-item-group>\r\n      <ion-item-divider>\r\n        <ion-label>Account</ion-label>\r\n      </ion-item-divider>\r\n      \r\n      <ion-item button (click)=\"logout()\">\r\n        <ion-icon name=\"log-out-outline\" slot=\"start\" color=\"danger\"></ion-icon>\r\n        <ion-label>Logout</ion-label>\r\n      </ion-item>\r\n    </ion-item-group>\r\n  </ion-list>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaM,IAAO,sBAAP,MAAO,qBAAmB;EAG9B,YACU,YACA,aACA,iBACA,mBAAoC;AAHpC,SAAA,aAAA;AACA,SAAA,cAAA;AACA,SAAA,kBAAA;AACA,SAAA,oBAAA;AANV,SAAA,SAAwB;EAOpB;EAEJ,WAAQ;AAEN,UAAM,QAAQ,aAAa,QAAQ,OAAO;AAC1C,QAAI,OAAO;AACT,UAAI;AACF,cAAM,YAAY,KAAK,SAAS,KAAK;AACrC,YAAI,aAAa,UAAU,KAAK;AAC9B,eAAK,SAAS,UAAU;QAC1B;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,4BAA4B,KAAK;MACjD;IACF;EACF;;;;;;EAOQ,SAAS,OAAa;AAC5B,QAAI;AACF,YAAM,YAAY,MAAM,MAAM,GAAG,EAAE,CAAC;AACpC,YAAM,SAAS,UAAU,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AAC7D,YAAM,cAAc,mBAAmB,KAAK,MAAM,EAAE,MAAM,EAAE,EAAE,IAAI,SAAS,GAAC;AAC1E,eAAO,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE;MAC7D,CAAC,EAAE,KAAK,EAAE,CAAC;AACX,aAAO,KAAK,MAAM,WAAW;IAC/B,SAAS,OAAO;AACd,cAAQ,MAAM,4BAA4B,KAAK;AAC/C,aAAO;IACT;EACF;;;;EAKM,kBAAe;;AACnB,YAAM,UAAU,MAAM,KAAK,kBAAkB,OAAO;QAClD,SAAS;QACT,SAAS;OACV;AAED,YAAM,QAAQ,QAAO;AAErB,UAAI;AACF,cAAM,UAAU,MAAM,KAAK,WAAW,gBAAgB,KAAK,UAAU,MAAS;AAE9E,cAAM,QAAQ,QAAO;AAErB,YAAI,SAAS;AACX,gBAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;YAC9C,QAAQ;YACR,SAAS;YACT,SAAS,CAAC,IAAI;WACf;AAED,gBAAM,MAAM,QAAO;QACrB,OAAO;AACL,gBAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;YAC9C,QAAQ;YACR,SAAS;YACT,SAAS,CAAC,IAAI;WACf;AAED,gBAAM,MAAM,QAAO;QACrB;MACF,SAAS,OAAO;AACd,cAAM,QAAQ,QAAO;AAErB,cAAM,QAAQ,MAAM,KAAK,gBAAgB,OAAO;UAC9C,QAAQ;UACR,SAAS;UACT,SAAS,CAAC,IAAI;SACf;AAED,cAAM,MAAM,QAAO;MACrB;IACF;;;;uCAxFW,sBAAmB,4BAAA,UAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,eAAA,GAAA,4BAAA,iBAAA,CAAA;IAAA;EAAA;;yEAAnB,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,UAAA,SAAA,GAAA,OAAA,GAAA,CAAA,QAAA,mBAAA,QAAA,OAAA,CAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACbhC,QAAA,yBAAA,GAAA,UAAA,EAAU,GAAA,iBAAA,EACS,GAAA,gBAAA;AACC,QAAA,iBAAA,GAAA,uBAAA;AAAqB,QAAA,uBAAA;AACrC,QAAA,yBAAA,GAAA,mBAAA;AAAmB,QAAA,iBAAA,GAAA,8BAAA;AAA4B,QAAA,uBAAA,EAAoB;AAGrE,QAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,GAAA;AACb,QAAA,iBAAA,GAAA,2FAAA;AAAyF,QAAA,uBAAA;AAC5F,QAAA,yBAAA,GAAA,GAAA;AAAG,QAAA,iBAAA,IAAA,+EAAA;AAA6E,QAAA,uBAAA;AAEhF,QAAA,yBAAA,IAAA,cAAA,CAAA;AAA2B,QAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,iBAAS,IAAA,gBAAA;QAAiB,CAAA;AACnD,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,iBAAA,IAAA,iCAAA;AACF,QAAA,uBAAA,EAAa,EACI;;sBDHT,aAAW,WAAA,SAAA,gBAAA,eAAA,iBAAA,cAAA,SAAE,YAAY,GAAA,QAAA,CAAA,+lBAAA,EAAA,CAAA;EAAA;;;sEAExB,qBAAmB,CAAA;UAP/B;uBACW,mBAAiB,YAGf,MAAI,SACP,CAAC,aAAa,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,4iBAAA,EAAA,CAAA;;;;6EAEzB,qBAAmB,EAAA,WAAA,uBAAA,UAAA,2DAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;AEA1B,IAAO,eAAP,MAAO,cAAY;EAEvB,YAAoB,QAAc;AAAd,SAAA,SAAA;EAAkB;EAEtC,WAAQ;EACR;EAEA,SAAM;AACJ,SAAK,OAAO,SAAS,CAAC,YAAY,CAAC;EACrC;EAEA,SAAM;AAEJ,iBAAa,WAAW,OAAO;AAG/B,SAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;EACjC;;;uCAjBW,eAAY,4BAAA,MAAA,CAAA;IAAA;EAAA;;yEAAZ,eAAY,WAAA,CAAA,CAAA,cAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,QAAA,OAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,aAAA,QAAA,YAAA,GAAA,CAAA,UAAA,IAAA,GAAA,OAAA,GAAA,CAAA,QAAA,mBAAA,QAAA,SAAA,SAAA,QAAA,CAAA,GAAA,UAAA,SAAA,sBAAA,IAAA,KAAA;AAAA,UAAA,KAAA,GAAA;ACbzB,QAAA,yBAAA,GAAA,YAAA,EAAY,GAAA,aAAA,EACG,GAAA,eAAA,CAAA,EACe,GAAA,cAAA,CAAA;AACZ,QAAA,qBAAA,SAAA,SAAA,oDAAA;AAAA,iBAAS,IAAA,OAAA;QAAQ,CAAA;AAC3B,QAAA,oBAAA,GAAA,YAAA,CAAA;AACF,QAAA,uBAAA,EAAa;AAEf,QAAA,yBAAA,GAAA,WAAA;AAAW,QAAA,iBAAA,GAAA,UAAA;AAAQ,QAAA,uBAAA,EAAY,EACnB;AAGhB,QAAA,yBAAA,GAAA,aAAA,EAAa,GAAA,UAAA,EACD,GAAA,gBAAA,EACQ,IAAA,kBAAA,EACI,IAAA,WAAA;AACL,QAAA,iBAAA,IAAA,eAAA;AAAa,QAAA,uBAAA,EAAY;AAItC,QAAA,oBAAA,IAAA,iBAAA;AACF,QAAA,uBAAA;AAEA,QAAA,yBAAA,IAAA,gBAAA,EAAgB,IAAA,kBAAA,EACI,IAAA,WAAA;AACL,QAAA,iBAAA,IAAA,SAAA;AAAO,QAAA,uBAAA,EAAY;AAGhC,QAAA,yBAAA,IAAA,YAAA,CAAA;AAAiB,QAAA,qBAAA,SAAA,SAAA,mDAAA;AAAA,iBAAS,IAAA,OAAA;QAAQ,CAAA;AAChC,QAAA,oBAAA,IAAA,YAAA,CAAA;AACA,QAAA,yBAAA,IAAA,WAAA;AAAW,QAAA,iBAAA,IAAA,QAAA;AAAM,QAAA,uBAAA,EAAY,EACpB,EACI,EACR;;sBDrBD,aAAW,WAAA,YAAA,YAAA,WAAA,SAAA,SAAA,gBAAA,cAAA,UAAA,SAAA,UAAA,YAAE,cAAc,mBAAmB,GAAA,QAAA,CAAA,8XAAA,EAAA,CAAA;EAAA;;;sEAE7C,cAAY,CAAA;UAPxB;uBACW,gBAAc,YAGZ,MAAI,SACP,CAAC,aAAa,cAAc,mBAAmB,GAAC,UAAA,s9BAAA,QAAA,CAAA,uYAAA,EAAA,CAAA;;;;6EAE9C,cAAY,EAAA,WAAA,gBAAA,UAAA,2CAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
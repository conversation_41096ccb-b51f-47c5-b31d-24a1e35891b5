{"version": 3, "sources": ["node_modules/@ionic/core/dist/esm/ion-modal.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, w as writeTask, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { f as findClosestIonContent, i as isIonContent, d as disableContentScrollY, r as resetContentScrollY, a as findIonContent, p as printIonContentErrorMsg } from './index-9a17db3d.js';\nimport { C as CoreDelegate, a as attachComponent, d as detachComponent } from './framework-delegate-56b467ad.js';\nimport { g as getElementRoot, j as clamp, r as raf, h as inheritAttributes, k as hasLazyBuild } from './helpers-d94bc8ad.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { p as printIonWarning, c as config } from './index-cfd9c1f2.js';\nimport { g as getCapacitor } from './capacitor-59395cbd.js';\nimport { G as GESTURE, O as OVERLAY_GESTURE_PRIORITY, F as FOCUS_TRAP_DISABLE_CLASS, e as createTriggerController, B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod } from './overlays-d99dcb0a.js';\nimport { g as getClassMap } from './theme-01f3f29c.js';\nimport { e as deepReady, w as waitForMount } from './index-68c0d151.js';\nimport { b as getIonMode } from './ionic-global-b26f573e.js';\nimport { KEYBOARD_DID_OPEN } from './keyboard-52278bd7.js';\nimport { c as createAnimation } from './animation-8b25e105.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { createGesture } from './index-39782642.js';\nimport { w as win } from './index-a5d50daf.js';\nimport './hardware-back-button-a7eb8233.js';\nimport './gesture-controller-314a54f6.js';\nimport './keyboard-73175e24.js';\nvar Style;\n(function (Style) {\n  Style[\"Dark\"] = \"DARK\";\n  Style[\"Light\"] = \"LIGHT\";\n  Style[\"Default\"] = \"DEFAULT\";\n})(Style || (Style = {}));\nconst StatusBar = {\n  getEngine() {\n    const capacitor = getCapacitor();\n    if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('StatusBar')) {\n      return capacitor.Plugins.StatusBar;\n    }\n    return undefined;\n  },\n  setStyle(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.setStyle(options);\n  },\n  getStyle: async function () {\n    const engine = this.getEngine();\n    if (!engine) {\n      return Style.Default;\n    }\n    const {\n      style\n    } = await engine.getInfo();\n    return style;\n  }\n};\n\n/**\n * Use y = mx + b to\n * figure out the backdrop value\n * at a particular x coordinate. This\n * is useful when the backdrop does\n * not begin to fade in until after\n * the 0 breakpoint.\n */\nconst getBackdropValueForSheet = (x, backdropBreakpoint) => {\n  /**\n   * We will use these points:\n   * (backdropBreakpoint, 0)\n   * (maxBreakpoint, 1)\n   * We know that at the beginning breakpoint,\n   * the backdrop will be hidden. We also\n   * know that at the maxBreakpoint, the backdrop\n   * must be fully visible. maxBreakpoint should\n   * always be 1 even if the maximum value\n   * of the breakpoints array is not 1 since\n   * the animation runs from a progress of 0\n   * to a progress of 1.\n   * m = (y2 - y1) / (x2 - x1)\n   *\n   * This is simplified from:\n   * m = (1 - 0) / (maxBreakpoint - backdropBreakpoint)\n   *\n   * If the backdropBreakpoint is 1, we return 0 as the\n   * backdrop is completely hidden.\n   *\n   */\n  if (backdropBreakpoint === 1) {\n    return 0;\n  }\n  const slope = 1 / (1 - backdropBreakpoint);\n  /**\n   * From here, compute b which is\n   * the backdrop opacity if the offset\n   * is 0. If the backdrop does not\n   * begin to fade in until after the\n   * 0 breakpoint, this b value will be\n   * negative. This is fine as we never pass\n   * b directly into the animation keyframes.\n   * b = y - mx\n   * Use a known point: (backdropBreakpoint, 0)\n   * This is simplified from:\n   * b = 0 - (backdropBreakpoint * slope)\n   */\n  const b = -(backdropBreakpoint * slope);\n  /**\n   * Finally, we can now determine the\n   * backdrop offset given an arbitrary\n   * gesture offset.\n   */\n  return x * slope + b;\n};\n/**\n * The tablet/desktop card modal activates\n * when the window width is >= 768.\n * At that point, the presenting element\n * is not transformed, so we do not need to\n * adjust the status bar color.\n *\n */\nconst setCardStatusBarDark = () => {\n  if (!win || win.innerWidth >= 768) {\n    return;\n  }\n  StatusBar.setStyle({\n    style: Style.Dark\n  });\n};\nconst setCardStatusBarDefault = (defaultStyle = Style.Default) => {\n  if (!win || win.innerWidth >= 768) {\n    return;\n  }\n  StatusBar.setStyle({\n    style: defaultStyle\n  });\n};\nconst handleCanDismiss = async (el, animation) => {\n  /**\n   * If canDismiss is not a function\n   * then we can return early. If canDismiss is `true`,\n   * then canDismissBlocksGesture is `false` as canDismiss\n   * will never interrupt the gesture. As a result,\n   * this code block is never reached. If canDismiss is `false`,\n   * then we never dismiss.\n   */\n  if (typeof el.canDismiss !== 'function') {\n    return;\n  }\n  /**\n   * Run the canDismiss callback.\n   * If the function returns `true`,\n   * then we can proceed with dismiss.\n   */\n  const shouldDismiss = await el.canDismiss(undefined, GESTURE);\n  if (!shouldDismiss) {\n    return;\n  }\n  /**\n   * If canDismiss resolved after the snap\n   * back animation finished, we can\n   * dismiss immediately.\n   *\n   * If canDismiss resolved before the snap\n   * back animation finished, we need to\n   * wait until the snap back animation is\n   * done before dismissing.\n   */\n  if (animation.isRunning()) {\n    animation.onFinish(() => {\n      el.dismiss(undefined, 'handler');\n    }, {\n      oneTimeCallback: true\n    });\n  } else {\n    el.dismiss(undefined, 'handler');\n  }\n};\n/**\n * This function lets us simulate a realistic spring-like animation\n * when swiping down on the modal.\n * There are two forces that we need to use to compute the spring physics:\n *\n * 1. Stiffness, k: This is a measure of resistance applied a spring.\n * 2. Dampening, c: This value has the effect of reducing or preventing oscillation.\n *\n * Using these two values, we can calculate the Spring Force and the Dampening Force\n * to compute the total force applied to a spring.\n *\n * Spring Force: This force pulls a spring back into its equilibrium position.\n * Hooke's Law tells us that that spring force (FS) = kX.\n * k is the stiffness of a spring, and X is the displacement of the spring from its\n * equilibrium position. In this case, it is the amount by which the free end\n * of a spring was displaced (stretched/pushed) from its \"relaxed\" position.\n *\n * Dampening Force: This force slows down motion. Without it, a spring would oscillate forever.\n * The dampening force, FD, can be found via this formula: FD = -cv\n * where c the dampening value and v is velocity.\n *\n * Therefore, the resulting force that is exerted on the block is:\n * F = FS + FD = -kX - cv\n *\n * Newton's 2nd Law tells us that F = ma:\n * ma = -kX - cv.\n *\n * For Ionic's purposes, we can assume that m = 1:\n * a = -kX - cv\n *\n * Imagine a block attached to the end of a spring. At equilibrium\n * the block is at position x = 1.\n * Pressing on the block moves it to position x = 0;\n * So, to calculate the displacement, we need to take the\n * current position and subtract the previous position from it.\n * X = x - x0 = 0 - 1 = -1.\n *\n * For Ionic's purposes, we are only pushing on the spring modal\n * so we have a max position of 1.\n * As a result, we can expand displacement to this formula:\n * X = x - 1\n *\n * a = -k(x - 1) - cv\n *\n * We can represent the motion of something as a function of time: f(t) = x.\n * The derivative of position gives us the velocity: f'(t)\n * The derivative of the velocity gives us the acceleration: f''(t)\n *\n * We can substitute the formula above with these values:\n *\n * f\"(t) = -k * (f(t) - 1) - c * f'(t)\n *\n * This is called a differential equation.\n *\n * We know that at t = 0, we are at x = 0 because the modal does not move: f(0) = 0\n * This means our velocity is also zero: f'(0) = 0.\n *\n * We can cheat a bit and plug the formula into Wolfram Alpha.\n * However, we need to pick stiffness and dampening values:\n * k = 0.57\n * c = 15\n *\n * I picked these as they are fairly close to native iOS's spring effect\n * with the modal.\n *\n * What we plug in is this: f(0) = 0; f'(0) = 0; f''(t) = -0.57(f(t) - 1) - 15f'(t)\n *\n * The result is a formula that lets us calculate the acceleration\n * for a given time t.\n * Note: This is the approximate form of the solution. Wolfram Alpha will\n * give you a complex differential equation too.\n */\nconst calculateSpringStep = t => {\n  return 0.00255275 * 2.71828 ** (-14.9619 * t) - 1.00255 * 2.71828 ** (-0.0380968 * t) + 1;\n};\n\n// Defaults for the card swipe animation\nconst SwipeToCloseDefaults = {\n  MIN_PRESENTING_SCALE: 0.915\n};\nconst createSwipeToCloseGesture = (el, animation, statusBarStyle, onDismiss) => {\n  /**\n   * The step value at which a card modal\n   * is eligible for dismissing via gesture.\n   */\n  const DISMISS_THRESHOLD = 0.5;\n  const height = el.offsetHeight;\n  let isOpen = false;\n  let canDismissBlocksGesture = false;\n  let contentEl = null;\n  let scrollEl = null;\n  const canDismissMaxStep = 0.2;\n  let initialScrollY = true;\n  let lastStep = 0;\n  const getScrollY = () => {\n    if (contentEl && isIonContent(contentEl)) {\n      return contentEl.scrollY;\n      /**\n       * Custom scroll containers are intended to be\n       * used with virtual scrolling, so we assume\n       * there is scrolling in this case.\n       */\n    } else {\n      return true;\n    }\n  };\n  const canStart = detail => {\n    const target = detail.event.target;\n    if (target === null || !target.closest) {\n      return true;\n    }\n    /**\n     * If we are swiping on the content,\n     * swiping should only be possible if\n     * the content is scrolled all the way\n     * to the top so that we do not interfere\n     * with scrolling.\n     *\n     * We cannot assume that the `ion-content`\n     * target will remain consistent between\n     * swipes. For example, when using\n     * ion-nav within a card modal it is\n     * possible to swipe, push a view, and then\n     * swipe again. The target content will not\n     * be the same between swipes.\n     */\n    contentEl = findClosestIonContent(target);\n    if (contentEl) {\n      /**\n       * The card should never swipe to close\n       * on the content with a refresher.\n       * Note: We cannot solve this by making the\n       * swipeToClose gesture have a higher priority\n       * than the refresher gesture as the iOS native\n       * refresh gesture uses a scroll listener in\n       * addition to a gesture.\n       *\n       * Note: Do not use getScrollElement here\n       * because we need this to be a synchronous\n       * operation, and getScrollElement is\n       * asynchronous.\n       */\n      if (isIonContent(contentEl)) {\n        const root = getElementRoot(contentEl);\n        scrollEl = root.querySelector('.inner-scroll');\n      } else {\n        scrollEl = contentEl;\n      }\n      const hasRefresherInContent = !!contentEl.querySelector('ion-refresher');\n      return !hasRefresherInContent && scrollEl.scrollTop === 0;\n    }\n    /**\n     * Card should be swipeable on all\n     * parts of the modal except for the footer.\n     */\n    const footer = target.closest('ion-footer');\n    if (footer === null) {\n      return true;\n    }\n    return false;\n  };\n  const onStart = detail => {\n    const {\n      deltaY\n    } = detail;\n    /**\n     * Get the initial scrollY value so\n     * that we can correctly reset the scrollY\n     * prop when the gesture ends.\n     */\n    initialScrollY = getScrollY();\n    /**\n     * If canDismiss is anything other than `true`\n     * then users should be able to swipe down\n     * until a threshold is hit. At that point,\n     * the card modal should not proceed any further.\n     * TODO (FW-937)\n     * Remove undefined check\n     */\n    canDismissBlocksGesture = el.canDismiss !== undefined && el.canDismiss !== true;\n    /**\n     * If we are pulling down, then\n     * it is possible we are pulling on the\n     * content. We do not want scrolling to\n     * happen at the same time as the gesture.\n     */\n    if (deltaY > 0 && contentEl) {\n      disableContentScrollY(contentEl);\n    }\n    animation.progressStart(true, isOpen ? 1 : 0);\n  };\n  const onMove = detail => {\n    const {\n      deltaY\n    } = detail;\n    /**\n     * If we are pulling down, then\n     * it is possible we are pulling on the\n     * content. We do not want scrolling to\n     * happen at the same time as the gesture.\n     */\n    if (deltaY > 0 && contentEl) {\n      disableContentScrollY(contentEl);\n    }\n    /**\n     * If we are swiping on the content\n     * then the swipe gesture should only\n     * happen if we are pulling down.\n     *\n     * However, if we pull up and\n     * then down such that the scroll position\n     * returns to 0, we should be able to swipe\n     * the card.\n     */\n    const step = detail.deltaY / height;\n    /**\n     * Check if user is swiping down and\n     * if we have a canDismiss value that\n     * should block the gesture from\n     * proceeding,\n     */\n    const isAttemptingDismissWithCanDismiss = step >= 0 && canDismissBlocksGesture;\n    /**\n     * If we are blocking the gesture from dismissing,\n     * set the max step value so that the sheet cannot be\n     * completely hidden.\n     */\n    const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n    /**\n     * If we are blocking the gesture from\n     * dismissing, calculate the spring modifier value\n     * this will be added to the starting breakpoint\n     * value to give the gesture a spring-like feeling.\n     * Note that the starting breakpoint is always 0,\n     * so we omit adding 0 to the result.\n     */\n    const processedStep = isAttemptingDismissWithCanDismiss ? calculateSpringStep(step / maxStep) : step;\n    const clampedStep = clamp(0.0001, processedStep, maxStep);\n    animation.progressStep(clampedStep);\n    /**\n     * When swiping down half way, the status bar style\n     * should be reset to its default value.\n     *\n     * We track lastStep so that we do not fire these\n     * functions on every onMove, only when the user has\n     * crossed a certain threshold.\n     */\n    if (clampedStep >= DISMISS_THRESHOLD && lastStep < DISMISS_THRESHOLD) {\n      setCardStatusBarDefault(statusBarStyle);\n      /**\n       * However, if we swipe back up, then the\n       * status bar style should be set to have light\n       * text on a dark background.\n       */\n    } else if (clampedStep < DISMISS_THRESHOLD && lastStep >= DISMISS_THRESHOLD) {\n      setCardStatusBarDark();\n    }\n    lastStep = clampedStep;\n  };\n  const onEnd = detail => {\n    const velocity = detail.velocityY;\n    const step = detail.deltaY / height;\n    const isAttemptingDismissWithCanDismiss = step >= 0 && canDismissBlocksGesture;\n    const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n    const processedStep = isAttemptingDismissWithCanDismiss ? calculateSpringStep(step / maxStep) : step;\n    const clampedStep = clamp(0.0001, processedStep, maxStep);\n    const threshold = (detail.deltaY + velocity * 1000) / height;\n    /**\n     * If canDismiss blocks\n     * the swipe gesture, then the\n     * animation can never complete until\n     * canDismiss is checked.\n     */\n    const shouldComplete = !isAttemptingDismissWithCanDismiss && threshold >= DISMISS_THRESHOLD;\n    let newStepValue = shouldComplete ? -0.001 : 0.001;\n    if (!shouldComplete) {\n      animation.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n      newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], clampedStep)[0];\n    } else {\n      animation.easing('cubic-bezier(0.32, 0.72, 0, 1)');\n      newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], clampedStep)[0];\n    }\n    const duration = shouldComplete ? computeDuration(step * height, velocity) : computeDuration((1 - clampedStep) * height, velocity);\n    isOpen = shouldComplete;\n    gesture.enable(false);\n    if (contentEl) {\n      resetContentScrollY(contentEl, initialScrollY);\n    }\n    animation.onFinish(() => {\n      if (!shouldComplete) {\n        gesture.enable(true);\n      }\n    }).progressEnd(shouldComplete ? 1 : 0, newStepValue, duration);\n    /**\n     * If the canDismiss value blocked the gesture\n     * from proceeding, then we should ignore whatever\n     * shouldComplete is. Whether or not the modal\n     * animation should complete is now determined by\n     * canDismiss.\n     *\n     * If the user swiped >25% of the way\n     * to the max step, then we should\n     * check canDismiss. 25% was chosen\n     * to avoid accidental swipes.\n     */\n    if (isAttemptingDismissWithCanDismiss && clampedStep > maxStep / 4) {\n      handleCanDismiss(el, animation);\n    } else if (shouldComplete) {\n      onDismiss();\n    }\n  };\n  const gesture = createGesture({\n    el,\n    gestureName: 'modalSwipeToClose',\n    gesturePriority: OVERLAY_GESTURE_PRIORITY,\n    direction: 'y',\n    threshold: 10,\n    canStart,\n    onStart,\n    onMove,\n    onEnd\n  });\n  return gesture;\n};\nconst computeDuration = (remaining, velocity) => {\n  return clamp(400, remaining / Math.abs(velocity * 1.1), 500);\n};\nconst createSheetEnterAnimation = opts => {\n  const {\n    currentBreakpoint,\n    backdropBreakpoint,\n    expandToScroll\n  } = opts;\n  /**\n   * If the backdropBreakpoint is undefined, then the backdrop\n   * should always fade in. If the backdropBreakpoint came before the\n   * current breakpoint, then the backdrop should be fading in.\n   */\n  const shouldShowBackdrop = backdropBreakpoint === undefined || backdropBreakpoint < currentBreakpoint;\n  const initialBackdrop = shouldShowBackdrop ? `calc(var(--backdrop-opacity) * ${currentBreakpoint})` : '0';\n  const backdropAnimation = createAnimation('backdropAnimation').fromTo('opacity', 0, initialBackdrop);\n  if (shouldShowBackdrop) {\n    backdropAnimation.beforeStyles({\n      'pointer-events': 'none'\n    }).afterClearStyles(['pointer-events']);\n  }\n  const wrapperAnimation = createAnimation('wrapperAnimation').keyframes([{\n    offset: 0,\n    opacity: 1,\n    transform: 'translateY(100%)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: `translateY(${100 - currentBreakpoint * 100}%)`\n  }]);\n  /**\n   * This allows the content to be scrollable at any breakpoint.\n   */\n  const contentAnimation = !expandToScroll ? createAnimation('contentAnimation').keyframes([{\n    offset: 0,\n    opacity: 1,\n    maxHeight: `${(1 - currentBreakpoint) * 100}%`\n  }, {\n    offset: 1,\n    opacity: 1,\n    maxHeight: `${currentBreakpoint * 100}%`\n  }]) : undefined;\n  return {\n    wrapperAnimation,\n    backdropAnimation,\n    contentAnimation\n  };\n};\nconst createSheetLeaveAnimation = opts => {\n  const {\n    currentBreakpoint,\n    backdropBreakpoint\n  } = opts;\n  /**\n   * Backdrop does not always fade in from 0 to 1 if backdropBreakpoint\n   * is defined, so we need to account for that offset by figuring out\n   * what the current backdrop value should be.\n   */\n  const backdropValue = `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(currentBreakpoint, backdropBreakpoint)})`;\n  const defaultBackdrop = [{\n    offset: 0,\n    opacity: backdropValue\n  }, {\n    offset: 1,\n    opacity: 0\n  }];\n  const customBackdrop = [{\n    offset: 0,\n    opacity: backdropValue\n  }, {\n    offset: backdropBreakpoint,\n    opacity: 0\n  }, {\n    offset: 1,\n    opacity: 0\n  }];\n  const backdropAnimation = createAnimation('backdropAnimation').keyframes(backdropBreakpoint !== 0 ? customBackdrop : defaultBackdrop);\n  const wrapperAnimation = createAnimation('wrapperAnimation').keyframes([{\n    offset: 0,\n    opacity: 1,\n    transform: `translateY(${100 - currentBreakpoint * 100}%)`\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: `translateY(100%)`\n  }]);\n  return {\n    wrapperAnimation,\n    backdropAnimation\n  };\n};\nconst createEnterAnimation$1 = () => {\n  const backdropAnimation = createAnimation().fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  const wrapperAnimation = createAnimation().fromTo('transform', 'translateY(100vh)', 'translateY(0vh)');\n  return {\n    backdropAnimation,\n    wrapperAnimation,\n    contentAnimation: undefined\n  };\n};\n/**\n * iOS Modal Enter Animation for the Card presentation style\n */\nconst iosEnterAnimation = (baseEl, opts) => {\n  const {\n    presentingEl,\n    currentBreakpoint,\n    expandToScroll\n  } = opts;\n  const root = getElementRoot(baseEl);\n  const {\n    wrapperAnimation,\n    backdropAnimation,\n    contentAnimation\n  } = currentBreakpoint !== undefined ? createSheetEnterAnimation(opts) : createEnterAnimation$1();\n  backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n  wrapperAnimation.addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow')).beforeStyles({\n    opacity: 1\n  });\n  // The content animation is only added if scrolling is enabled for\n  // all the breakpoints.\n  !expandToScroll && (contentAnimation === null || contentAnimation === void 0 ? void 0 : contentAnimation.addElement(baseEl.querySelector('.ion-page')));\n  const baseAnimation = createAnimation('entering-base').addElement(baseEl).easing('cubic-bezier(0.32,0.72,0,1)').duration(500).addAnimation([wrapperAnimation]).beforeAddWrite(() => {\n    if (expandToScroll) {\n      // Scroll can only be done when the modal is fully expanded.\n      return;\n    }\n    /**\n     * There are some browsers that causes flickering when\n     * dragging the content when scroll is enabled at every\n     * breakpoint. This is due to the wrapper element being\n     * transformed off the screen and having a snap animation.\n     *\n     * A workaround is to clone the footer element and append\n     * it outside of the wrapper element. This way, the footer\n     * is still visible and the drag can be done without\n     * flickering. The original footer is hidden until the modal\n     * is dismissed. This maintains the animation of the footer\n     * when the modal is dismissed.\n     *\n     * The workaround needs to be done before the animation starts\n     * so there are no flickering issues.\n     */\n    const ionFooter = baseEl.querySelector('ion-footer');\n    /**\n     * This check is needed to prevent more than one footer\n     * from being appended to the shadow root.\n     * Otherwise, iOS and MD enter animations would append\n     * the footer twice.\n     */\n    const ionFooterAlreadyAppended = baseEl.shadowRoot.querySelector('ion-footer');\n    if (ionFooter && !ionFooterAlreadyAppended) {\n      const footerHeight = ionFooter.clientHeight;\n      const clonedFooter = ionFooter.cloneNode(true);\n      baseEl.shadowRoot.appendChild(clonedFooter);\n      ionFooter.style.setProperty('display', 'none');\n      ionFooter.setAttribute('aria-hidden', 'true');\n      // Padding is added to prevent some content from being hidden.\n      const page = baseEl.querySelector('.ion-page');\n      page.style.setProperty('padding-bottom', `${footerHeight}px`);\n    }\n  });\n  if (contentAnimation) {\n    baseAnimation.addAnimation(contentAnimation);\n  }\n  if (presentingEl) {\n    const isMobile = window.innerWidth < 768;\n    const hasCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n    const presentingElRoot = getElementRoot(presentingEl);\n    const presentingAnimation = createAnimation().beforeStyles({\n      transform: 'translateY(0)',\n      'transform-origin': 'top center',\n      overflow: 'hidden'\n    });\n    const bodyEl = document.body;\n    if (isMobile) {\n      /**\n       * Fallback for browsers that does not support `max()` (ex: Firefox)\n       * No need to worry about statusbar padding since engines like Gecko\n       * are not used as the engine for standalone Cordova/Capacitor apps\n       */\n      const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n      const modalTransform = hasCardModal ? '-10px' : transformOffset;\n      const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n      const finalTransform = `translateY(${modalTransform}) scale(${toPresentingScale})`;\n      presentingAnimation.afterStyles({\n        transform: finalTransform\n      }).beforeAddWrite(() => bodyEl.style.setProperty('background-color', 'black')).addElement(presentingEl).keyframes([{\n        offset: 0,\n        filter: 'contrast(1)',\n        transform: 'translateY(0px) scale(1)',\n        borderRadius: '0px'\n      }, {\n        offset: 1,\n        filter: 'contrast(0.85)',\n        transform: finalTransform,\n        borderRadius: '10px 10px 0 0'\n      }]);\n      baseAnimation.addAnimation(presentingAnimation);\n    } else {\n      baseAnimation.addAnimation(backdropAnimation);\n      if (!hasCardModal) {\n        wrapperAnimation.fromTo('opacity', '0', '1');\n      } else {\n        const toPresentingScale = hasCardModal ? SwipeToCloseDefaults.MIN_PRESENTING_SCALE : 1;\n        const finalTransform = `translateY(-10px) scale(${toPresentingScale})`;\n        presentingAnimation.afterStyles({\n          transform: finalTransform\n        }).addElement(presentingElRoot.querySelector('.modal-wrapper')).keyframes([{\n          offset: 0,\n          filter: 'contrast(1)',\n          transform: 'translateY(0) scale(1)'\n        }, {\n          offset: 1,\n          filter: 'contrast(0.85)',\n          transform: finalTransform\n        }]);\n        const shadowAnimation = createAnimation().afterStyles({\n          transform: finalTransform\n        }).addElement(presentingElRoot.querySelector('.modal-shadow')).keyframes([{\n          offset: 0,\n          opacity: '1',\n          transform: 'translateY(0) scale(1)'\n        }, {\n          offset: 1,\n          opacity: '0',\n          transform: finalTransform\n        }]);\n        baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n      }\n    }\n  } else {\n    baseAnimation.addAnimation(backdropAnimation);\n  }\n  return baseAnimation;\n};\nconst createLeaveAnimation$1 = () => {\n  const backdropAnimation = createAnimation().fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  const wrapperAnimation = createAnimation().fromTo('transform', 'translateY(0vh)', 'translateY(100vh)');\n  return {\n    backdropAnimation,\n    wrapperAnimation\n  };\n};\n/**\n * iOS Modal Leave Animation\n */\nconst iosLeaveAnimation = (baseEl, opts, duration = 500) => {\n  const {\n    presentingEl,\n    currentBreakpoint,\n    expandToScroll\n  } = opts;\n  const root = getElementRoot(baseEl);\n  const {\n    wrapperAnimation,\n    backdropAnimation\n  } = currentBreakpoint !== undefined ? createSheetLeaveAnimation(opts) : createLeaveAnimation$1();\n  backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n  wrapperAnimation.addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow')).beforeStyles({\n    opacity: 1\n  });\n  const baseAnimation = createAnimation('leaving-base').addElement(baseEl).easing('cubic-bezier(0.32,0.72,0,1)').duration(duration).addAnimation(wrapperAnimation).beforeAddWrite(() => {\n    if (expandToScroll) {\n      // Scroll can only be done when the modal is fully expanded.\n      return;\n    }\n    /**\n     * If expandToScroll is disabled, we need to swap\n     * the visibility to the original, so the footer\n     * dismisses with the modal and doesn't stay\n     * until the modal is removed from the DOM.\n     */\n    const ionFooter = baseEl.querySelector('ion-footer');\n    if (ionFooter) {\n      const clonedFooter = baseEl.shadowRoot.querySelector('ion-footer');\n      ionFooter.style.removeProperty('display');\n      ionFooter.removeAttribute('aria-hidden');\n      clonedFooter.style.setProperty('display', 'none');\n      clonedFooter.setAttribute('aria-hidden', 'true');\n      const page = baseEl.querySelector('.ion-page');\n      page.style.removeProperty('padding-bottom');\n    }\n  });\n  if (presentingEl) {\n    const isMobile = window.innerWidth < 768;\n    const hasCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n    const presentingElRoot = getElementRoot(presentingEl);\n    const presentingAnimation = createAnimation().beforeClearStyles(['transform']).afterClearStyles(['transform']).onFinish(currentStep => {\n      // only reset background color if this is the last card-style modal\n      if (currentStep !== 1) {\n        return;\n      }\n      presentingEl.style.setProperty('overflow', '');\n      const numModals = Array.from(bodyEl.querySelectorAll('ion-modal:not(.overlay-hidden)')).filter(m => m.presentingElement !== undefined).length;\n      if (numModals <= 1) {\n        bodyEl.style.setProperty('background-color', '');\n      }\n    });\n    const bodyEl = document.body;\n    if (isMobile) {\n      const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n      const modalTransform = hasCardModal ? '-10px' : transformOffset;\n      const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n      const finalTransform = `translateY(${modalTransform}) scale(${toPresentingScale})`;\n      presentingAnimation.addElement(presentingEl).keyframes([{\n        offset: 0,\n        filter: 'contrast(0.85)',\n        transform: finalTransform,\n        borderRadius: '10px 10px 0 0'\n      }, {\n        offset: 1,\n        filter: 'contrast(1)',\n        transform: 'translateY(0px) scale(1)',\n        borderRadius: '0px'\n      }]);\n      baseAnimation.addAnimation(presentingAnimation);\n    } else {\n      baseAnimation.addAnimation(backdropAnimation);\n      if (!hasCardModal) {\n        wrapperAnimation.fromTo('opacity', '1', '0');\n      } else {\n        const toPresentingScale = hasCardModal ? SwipeToCloseDefaults.MIN_PRESENTING_SCALE : 1;\n        const finalTransform = `translateY(-10px) scale(${toPresentingScale})`;\n        presentingAnimation.addElement(presentingElRoot.querySelector('.modal-wrapper')).afterStyles({\n          transform: 'translate3d(0, 0, 0)'\n        }).keyframes([{\n          offset: 0,\n          filter: 'contrast(0.85)',\n          transform: finalTransform\n        }, {\n          offset: 1,\n          filter: 'contrast(1)',\n          transform: 'translateY(0) scale(1)'\n        }]);\n        const shadowAnimation = createAnimation().addElement(presentingElRoot.querySelector('.modal-shadow')).afterStyles({\n          transform: 'translateY(0) scale(1)'\n        }).keyframes([{\n          offset: 0,\n          opacity: '0',\n          transform: finalTransform\n        }, {\n          offset: 1,\n          opacity: '1',\n          transform: 'translateY(0) scale(1)'\n        }]);\n        baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n      }\n    }\n  } else {\n    baseAnimation.addAnimation(backdropAnimation);\n  }\n  return baseAnimation;\n};\nconst createEnterAnimation = () => {\n  const backdropAnimation = createAnimation().fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  const wrapperAnimation = createAnimation().keyframes([{\n    offset: 0,\n    opacity: 0.01,\n    transform: 'translateY(40px)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: `translateY(0px)`\n  }]);\n  return {\n    backdropAnimation,\n    wrapperAnimation,\n    contentAnimation: undefined\n  };\n};\n/**\n * Md Modal Enter Animation\n */\nconst mdEnterAnimation = (baseEl, opts) => {\n  const {\n    currentBreakpoint,\n    expandToScroll\n  } = opts;\n  const root = getElementRoot(baseEl);\n  const {\n    wrapperAnimation,\n    backdropAnimation,\n    contentAnimation\n  } = currentBreakpoint !== undefined ? createSheetEnterAnimation(opts) : createEnterAnimation();\n  backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n  wrapperAnimation.addElement(root.querySelector('.modal-wrapper'));\n  // The content animation is only added if scrolling is enabled for\n  // all the breakpoints.\n  expandToScroll && (contentAnimation === null || contentAnimation === void 0 ? void 0 : contentAnimation.addElement(baseEl.querySelector('.ion-page')));\n  const baseAnimation = createAnimation().addElement(baseEl).easing('cubic-bezier(0.36,0.66,0.04,1)').duration(280).addAnimation([backdropAnimation, wrapperAnimation]).beforeAddWrite(() => {\n    if (expandToScroll) {\n      // Scroll can only be done when the modal is fully expanded.\n      return;\n    }\n    /**\n     * There are some browsers that causes flickering when\n     * dragging the content when scroll is enabled at every\n     * breakpoint. This is due to the wrapper element being\n     * transformed off the screen and having a snap animation.\n     *\n     * A workaround is to clone the footer element and append\n     * it outside of the wrapper element. This way, the footer\n     * is still visible and the drag can be done without\n     * flickering. The original footer is hidden until the modal\n     * is dismissed. This maintains the animation of the footer\n     * when the modal is dismissed.\n     *\n     * The workaround needs to be done before the animation starts\n     * so there are no flickering issues.\n     */\n    const ionFooter = baseEl.querySelector('ion-footer');\n    /**\n     * This check is needed to prevent more than one footer\n     * from being appended to the shadow root.\n     * Otherwise, iOS and MD enter animations would append\n     * the footer twice.\n     */\n    const ionFooterAlreadyAppended = baseEl.shadowRoot.querySelector('ion-footer');\n    if (ionFooter && !ionFooterAlreadyAppended) {\n      const footerHeight = ionFooter.clientHeight;\n      const clonedFooter = ionFooter.cloneNode(true);\n      baseEl.shadowRoot.appendChild(clonedFooter);\n      ionFooter.style.setProperty('display', 'none');\n      ionFooter.setAttribute('aria-hidden', 'true');\n      // Padding is added to prevent some content from being hidden.\n      const page = baseEl.querySelector('.ion-page');\n      page.style.setProperty('padding-bottom', `${footerHeight}px`);\n    }\n  });\n  if (contentAnimation) {\n    baseAnimation.addAnimation(contentAnimation);\n  }\n  return baseAnimation;\n};\nconst createLeaveAnimation = () => {\n  const backdropAnimation = createAnimation().fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  const wrapperAnimation = createAnimation().keyframes([{\n    offset: 0,\n    opacity: 0.99,\n    transform: `translateY(0px)`\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: 'translateY(40px)'\n  }]);\n  return {\n    backdropAnimation,\n    wrapperAnimation\n  };\n};\n/**\n * Md Modal Leave Animation\n */\nconst mdLeaveAnimation = (baseEl, opts) => {\n  const {\n    currentBreakpoint,\n    expandToScroll\n  } = opts;\n  const root = getElementRoot(baseEl);\n  const {\n    wrapperAnimation,\n    backdropAnimation\n  } = currentBreakpoint !== undefined ? createSheetLeaveAnimation(opts) : createLeaveAnimation();\n  backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n  wrapperAnimation.addElement(root.querySelector('.modal-wrapper'));\n  const baseAnimation = createAnimation().easing('cubic-bezier(0.47,0,0.745,0.715)').duration(200).addAnimation([backdropAnimation, wrapperAnimation]).beforeAddWrite(() => {\n    if (expandToScroll) {\n      // Scroll can only be done when the modal is fully expanded.\n      return;\n    }\n    /**\n     * If expandToScroll is disabled, we need to swap\n     * the visibility to the original, so the footer\n     * dismisses with the modal and doesn't stay\n     * until the modal is removed from the DOM.\n     */\n    const ionFooter = baseEl.querySelector('ion-footer');\n    if (ionFooter) {\n      const clonedFooter = baseEl.shadowRoot.querySelector('ion-footer');\n      ionFooter.style.removeProperty('display');\n      ionFooter.removeAttribute('aria-hidden');\n      clonedFooter.style.setProperty('display', 'none');\n      clonedFooter.setAttribute('aria-hidden', 'true');\n      const page = baseEl.querySelector('.ion-page');\n      page.style.removeProperty('padding-bottom');\n    }\n  });\n  return baseAnimation;\n};\nconst createSheetGesture = (baseEl, backdropEl, wrapperEl, initialBreakpoint, backdropBreakpoint, animation, breakpoints = [], expandToScroll, getCurrentBreakpoint, onDismiss, onBreakpointChange) => {\n  // Defaults for the sheet swipe animation\n  const defaultBackdrop = [{\n    offset: 0,\n    opacity: 'var(--backdrop-opacity)'\n  }, {\n    offset: 1,\n    opacity: 0.01\n  }];\n  const customBackdrop = [{\n    offset: 0,\n    opacity: 'var(--backdrop-opacity)'\n  }, {\n    offset: 1 - backdropBreakpoint,\n    opacity: 0\n  }, {\n    offset: 1,\n    opacity: 0\n  }];\n  const SheetDefaults = {\n    WRAPPER_KEYFRAMES: [{\n      offset: 0,\n      transform: 'translateY(0%)'\n    }, {\n      offset: 1,\n      transform: 'translateY(100%)'\n    }],\n    BACKDROP_KEYFRAMES: backdropBreakpoint !== 0 ? customBackdrop : defaultBackdrop,\n    CONTENT_KEYFRAMES: [{\n      offset: 0,\n      maxHeight: '100%'\n    }, {\n      offset: 1,\n      maxHeight: '0%'\n    }]\n  };\n  const contentEl = baseEl.querySelector('ion-content');\n  const height = wrapperEl.clientHeight;\n  let currentBreakpoint = initialBreakpoint;\n  let offset = 0;\n  let canDismissBlocksGesture = false;\n  let cachedScrollEl = null;\n  const canDismissMaxStep = 0.95;\n  const maxBreakpoint = breakpoints[breakpoints.length - 1];\n  const minBreakpoint = breakpoints[0];\n  const wrapperAnimation = animation.childAnimations.find(ani => ani.id === 'wrapperAnimation');\n  const backdropAnimation = animation.childAnimations.find(ani => ani.id === 'backdropAnimation');\n  const contentAnimation = animation.childAnimations.find(ani => ani.id === 'contentAnimation');\n  const enableBackdrop = () => {\n    baseEl.style.setProperty('pointer-events', 'auto');\n    backdropEl.style.setProperty('pointer-events', 'auto');\n    /**\n     * When the backdrop is enabled, elements such\n     * as inputs should not be focusable outside\n     * the sheet.\n     */\n    baseEl.classList.remove(FOCUS_TRAP_DISABLE_CLASS);\n  };\n  const disableBackdrop = () => {\n    baseEl.style.setProperty('pointer-events', 'none');\n    backdropEl.style.setProperty('pointer-events', 'none');\n    /**\n     * When the backdrop is enabled, elements such\n     * as inputs should not be focusable outside\n     * the sheet.\n     * Adding this class disables focus trapping\n     * for the sheet temporarily.\n     */\n    baseEl.classList.add(FOCUS_TRAP_DISABLE_CLASS);\n  };\n  /**\n   * Toggles the visible modal footer when `expandToScroll` is disabled.\n   * @param footer The footer to show.\n   */\n  const swapFooterVisibility = footer => {\n    const originalFooter = baseEl.querySelector('ion-footer');\n    if (!originalFooter) {\n      return;\n    }\n    const clonedFooter = wrapperEl.nextElementSibling;\n    const footerToHide = footer === 'original' ? clonedFooter : originalFooter;\n    const footerToShow = footer === 'original' ? originalFooter : clonedFooter;\n    footerToShow.style.removeProperty('display');\n    footerToShow.removeAttribute('aria-hidden');\n    const page = baseEl.querySelector('.ion-page');\n    if (footer === 'original') {\n      page.style.removeProperty('padding-bottom');\n    } else {\n      const pagePadding = footerToShow.clientHeight;\n      page.style.setProperty('padding-bottom', `${pagePadding}px`);\n    }\n    footerToHide.style.setProperty('display', 'none');\n    footerToHide.setAttribute('aria-hidden', 'true');\n  };\n  /**\n   * After the entering animation completes,\n   * we need to set the animation to go from\n   * offset 0 to offset 1 so that users can\n   * swipe in any direction. We then set the\n   * animation offset to the current breakpoint\n   * so there is no flickering.\n   */\n  if (wrapperAnimation && backdropAnimation) {\n    wrapperAnimation.keyframes([...SheetDefaults.WRAPPER_KEYFRAMES]);\n    backdropAnimation.keyframes([...SheetDefaults.BACKDROP_KEYFRAMES]);\n    contentAnimation === null || contentAnimation === void 0 ? void 0 : contentAnimation.keyframes([...SheetDefaults.CONTENT_KEYFRAMES]);\n    animation.progressStart(true, 1 - currentBreakpoint);\n    /**\n     * If backdrop is not enabled, then content\n     * behind modal should be clickable. To do this, we need\n     * to remove pointer-events from ion-modal as a whole.\n     * ion-backdrop and .modal-wrapper always have pointer-events: auto\n     * applied, so the modal content can still be interacted with.\n     */\n    const shouldEnableBackdrop = currentBreakpoint > backdropBreakpoint;\n    if (shouldEnableBackdrop) {\n      enableBackdrop();\n    } else {\n      disableBackdrop();\n    }\n  }\n  if (contentEl && currentBreakpoint !== maxBreakpoint && expandToScroll) {\n    contentEl.scrollY = false;\n  }\n  const canStart = detail => {\n    /**\n     * If we are swiping on the content, swiping should only be possible if the content\n     * is scrolled all the way to the top so that we do not interfere with scrolling.\n     *\n     * We cannot assume that the `ion-content` target will remain consistent between swipes.\n     * For example, when using ion-nav within a modal it is possible to swipe, push a view,\n     * and then swipe again. The target content will not be the same between swipes.\n     */\n    const contentEl = findClosestIonContent(detail.event.target);\n    currentBreakpoint = getCurrentBreakpoint();\n    /**\n     * If `expandToScroll` is disabled, we should not allow the swipe gesture\n     * to start if the content is not scrolled to the top.\n     */\n    if (!expandToScroll && contentEl) {\n      const scrollEl = isIonContent(contentEl) ? getElementRoot(contentEl).querySelector('.inner-scroll') : contentEl;\n      return scrollEl.scrollTop === 0;\n    }\n    if (currentBreakpoint === 1 && contentEl) {\n      /**\n       * The modal should never swipe to close on the content with a refresher.\n       * Note 1: We cannot solve this by making this gesture have a higher priority than\n       * the refresher gesture as the iOS native refresh gesture uses a scroll listener in\n       * addition to a gesture.\n       *\n       * Note 2: Do not use getScrollElement here because we need this to be a synchronous\n       * operation, and getScrollElement is asynchronous.\n       */\n      const scrollEl = isIonContent(contentEl) ? getElementRoot(contentEl).querySelector('.inner-scroll') : contentEl;\n      const hasRefresherInContent = !!contentEl.querySelector('ion-refresher');\n      return !hasRefresherInContent && scrollEl.scrollTop === 0;\n    }\n    return true;\n  };\n  const onStart = detail => {\n    /**\n     * If canDismiss is anything other than `true`\n     * then users should be able to swipe down\n     * until a threshold is hit. At that point,\n     * the card modal should not proceed any further.\n     *\n     * canDismiss is never fired via gesture if there is\n     * no 0 breakpoint. However, it can be fired if the user\n     * presses Esc or the hardware back button.\n     * TODO (FW-937)\n     * Remove undefined check\n     */\n    canDismissBlocksGesture = baseEl.canDismiss !== undefined && baseEl.canDismiss !== true && minBreakpoint === 0;\n    /**\n     * Cache the scroll element reference when the gesture starts,\n     * this allows us to avoid querying the DOM for the target in onMove,\n     * which would impact performance significantly.\n     */\n    if (!expandToScroll) {\n      const targetEl = findClosestIonContent(detail.event.target);\n      cachedScrollEl = targetEl && isIonContent(targetEl) ? getElementRoot(targetEl).querySelector('.inner-scroll') : targetEl;\n    }\n    /**\n     * If expandToScroll is disabled, we need to swap\n     * the footer visibility to the original, so if the modal\n     * is dismissed, the footer dismisses with the modal\n     * and doesn't stay on the screen after the modal is gone.\n     */\n    if (!expandToScroll) {\n      swapFooterVisibility('original');\n    }\n    /**\n     * If we are pulling down, then it is possible we are pulling on the content.\n     * We do not want scrolling to happen at the same time as the gesture.\n     */\n    if (detail.deltaY > 0 && contentEl) {\n      contentEl.scrollY = false;\n    }\n    raf(() => {\n      /**\n       * Dismisses the open keyboard when the sheet drag gesture is started.\n       * Sets the focus onto the modal element.\n       */\n      baseEl.focus();\n    });\n    animation.progressStart(true, 1 - currentBreakpoint);\n  };\n  const onMove = detail => {\n    /**\n     * If `expandToScroll` is disabled, and an upwards swipe gesture is done within\n     * the scrollable content, we should not allow the swipe gesture to continue.\n     */\n    if (!expandToScroll && detail.deltaY <= 0 && cachedScrollEl) {\n      return;\n    }\n    /**\n     * If we are pulling down, then it is possible we are pulling on the content.\n     * We do not want scrolling to happen at the same time as the gesture.\n     * This accounts for when the user scrolls down, scrolls all the way up, and then\n     * pulls down again such that the modal should start to move.\n     */\n    if (detail.deltaY > 0 && contentEl) {\n      contentEl.scrollY = false;\n    }\n    /**\n     * Given the change in gesture position on the Y axis,\n     * compute where the offset of the animation should be\n     * relative to where the user dragged.\n     */\n    const initialStep = 1 - currentBreakpoint;\n    const secondToLastBreakpoint = breakpoints.length > 1 ? 1 - breakpoints[1] : undefined;\n    const step = initialStep + detail.deltaY / height;\n    const isAttemptingDismissWithCanDismiss = secondToLastBreakpoint !== undefined && step >= secondToLastBreakpoint && canDismissBlocksGesture;\n    /**\n     * If we are blocking the gesture from dismissing,\n     * set the max step value so that the sheet cannot be\n     * completely hidden.\n     */\n    const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n    /**\n     * If we are blocking the gesture from\n     * dismissing, calculate the spring modifier value\n     * this will be added to the starting breakpoint\n     * value to give the gesture a spring-like feeling.\n     * Note that when isAttemptingDismissWithCanDismiss is true,\n     * the modifier is always added to the breakpoint that\n     * appears right after the 0 breakpoint.\n     *\n     * Note that this modifier is essentially the progression\n     * between secondToLastBreakpoint and maxStep which is\n     * why we subtract secondToLastBreakpoint. This lets us get\n     * the result as a value from 0 to 1.\n     */\n    const processedStep = isAttemptingDismissWithCanDismiss && secondToLastBreakpoint !== undefined ? secondToLastBreakpoint + calculateSpringStep((step - secondToLastBreakpoint) / (maxStep - secondToLastBreakpoint)) : step;\n    offset = clamp(0.0001, processedStep, maxStep);\n    animation.progressStep(offset);\n  };\n  const onEnd = detail => {\n    /**\n     * If expandToScroll is disabled, we should not allow the moveSheetToBreakpoint\n     * function to be called if the user is trying to swipe content upwards and the content\n     * is not scrolled to the top.\n     */\n    if (!expandToScroll && detail.deltaY <= 0 && cachedScrollEl && cachedScrollEl.scrollTop > 0) {\n      return;\n    }\n    /**\n     * When the gesture releases, we need to determine\n     * the closest breakpoint to snap to.\n     */\n    const velocity = detail.velocityY;\n    const threshold = (detail.deltaY + velocity * 350) / height;\n    const diff = currentBreakpoint - threshold;\n    const closest = breakpoints.reduce((a, b) => {\n      return Math.abs(b - diff) < Math.abs(a - diff) ? b : a;\n    });\n    moveSheetToBreakpoint({\n      breakpoint: closest,\n      breakpointOffset: offset,\n      canDismiss: canDismissBlocksGesture,\n      /**\n       * The swipe is user-driven, so we should\n       * always animate when the gesture ends.\n       */\n      animated: true\n    });\n  };\n  const moveSheetToBreakpoint = options => {\n    const {\n      breakpoint,\n      canDismiss,\n      breakpointOffset,\n      animated\n    } = options;\n    /**\n     * canDismiss should only prevent snapping\n     * when users are trying to dismiss. If canDismiss\n     * is present but the user is trying to swipe upwards,\n     * we should allow that to happen,\n     */\n    const shouldPreventDismiss = canDismiss && breakpoint === 0;\n    const snapToBreakpoint = shouldPreventDismiss ? currentBreakpoint : breakpoint;\n    const shouldRemainOpen = snapToBreakpoint !== 0;\n    currentBreakpoint = 0;\n    /**\n     * Update the animation so that it plays from\n     * the last offset to the closest snap point.\n     */\n    if (wrapperAnimation && backdropAnimation) {\n      wrapperAnimation.keyframes([{\n        offset: 0,\n        transform: `translateY(${breakpointOffset * 100}%)`\n      }, {\n        offset: 1,\n        transform: `translateY(${(1 - snapToBreakpoint) * 100}%)`\n      }]);\n      backdropAnimation.keyframes([{\n        offset: 0,\n        opacity: `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(1 - breakpointOffset, backdropBreakpoint)})`\n      }, {\n        offset: 1,\n        opacity: `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(snapToBreakpoint, backdropBreakpoint)})`\n      }]);\n      if (contentAnimation) {\n        /**\n         * The modal content should scroll at any breakpoint when expandToScroll\n         * is disabled. In order to do this, the content needs to be completely\n         * viewable so scrolling can access everything. Otherwise, the default\n         * behavior would show the content off the screen and only allow\n         * scrolling when the sheet is fully expanded.\n         */\n        contentAnimation.keyframes([{\n          offset: 0,\n          maxHeight: `${(1 - breakpointOffset) * 100}%`\n        }, {\n          offset: 1,\n          maxHeight: `${snapToBreakpoint * 100}%`\n        }]);\n      }\n      animation.progressStep(0);\n    }\n    /**\n     * Gesture should remain disabled until the\n     * snapping animation completes.\n     */\n    gesture.enable(false);\n    /**\n     * If expandToScroll is disabled, we need to swap\n     * the footer visibility to the cloned one so the footer\n     * doesn't flicker when the sheet's height is animated.\n     */\n    if (!expandToScroll && shouldRemainOpen) {\n      swapFooterVisibility('cloned');\n    }\n    if (shouldPreventDismiss) {\n      handleCanDismiss(baseEl, animation);\n    } else if (!shouldRemainOpen) {\n      onDismiss();\n    }\n    /**\n     * Enables scrolling immediately if the sheet is about to fully expand\n     * or if it allows scrolling at any breakpoint. Without this, there would\n     * be a ~500ms delay while the modal animation completes, causing a\n     * noticeable lag. Native iOS allows scrolling as soon as the gesture is\n     * released, so we align with that behavior.\n     */\n    if (contentEl && (snapToBreakpoint === breakpoints[breakpoints.length - 1] || !expandToScroll)) {\n      contentEl.scrollY = true;\n    }\n    return new Promise(resolve => {\n      animation.onFinish(() => {\n        if (shouldRemainOpen) {\n          /**\n           * Once the snapping animation completes,\n           * we need to reset the animation to go\n           * from 0 to 1 so users can swipe in any direction.\n           * We then set the animation offset to the current\n           * breakpoint so that it starts at the snapped position.\n           */\n          if (wrapperAnimation && backdropAnimation) {\n            raf(() => {\n              wrapperAnimation.keyframes([...SheetDefaults.WRAPPER_KEYFRAMES]);\n              backdropAnimation.keyframes([...SheetDefaults.BACKDROP_KEYFRAMES]);\n              contentAnimation === null || contentAnimation === void 0 ? void 0 : contentAnimation.keyframes([...SheetDefaults.CONTENT_KEYFRAMES]);\n              animation.progressStart(true, 1 - snapToBreakpoint);\n              currentBreakpoint = snapToBreakpoint;\n              onBreakpointChange(currentBreakpoint);\n              /**\n               * Backdrop should become enabled\n               * after the backdropBreakpoint value\n               */\n              const shouldEnableBackdrop = currentBreakpoint > backdropBreakpoint;\n              if (shouldEnableBackdrop) {\n                enableBackdrop();\n              } else {\n                disableBackdrop();\n              }\n              gesture.enable(true);\n              resolve();\n            });\n          } else {\n            gesture.enable(true);\n            resolve();\n          }\n        } else {\n          resolve();\n        }\n        /**\n         * This must be a one time callback\n         * otherwise a new callback will\n         * be added every time onEnd runs.\n         */\n      }, {\n        oneTimeCallback: true\n      }).progressEnd(1, 0, animated ? 500 : 0);\n    });\n  };\n  const gesture = createGesture({\n    el: wrapperEl,\n    gestureName: 'modalSheet',\n    gesturePriority: 40,\n    direction: 'y',\n    threshold: 10,\n    canStart,\n    onStart,\n    onMove,\n    onEnd\n  });\n  return {\n    gesture,\n    moveSheetToBreakpoint\n  };\n};\nconst modalIosCss = \":host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:\\\"\\\"}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer ion-toolbar:first-of-type{padding-top:6px}\";\nconst IonModalIosStyle0 = modalIosCss;\nconst modalMdCss = \":host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:\\\"\\\"}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}\";\nconst IonModalMdStyle0 = modalMdCss;\nconst Modal = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.didPresent = createEvent(this, \"ionModalDidPresent\", 7);\n    this.willPresent = createEvent(this, \"ionModalWillPresent\", 7);\n    this.willDismiss = createEvent(this, \"ionModalWillDismiss\", 7);\n    this.didDismiss = createEvent(this, \"ionModalDidDismiss\", 7);\n    this.ionBreakpointDidChange = createEvent(this, \"ionBreakpointDidChange\", 7);\n    this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n    this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n    this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n    this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n    this.ionMount = createEvent(this, \"ionMount\", 7);\n    this.lockController = createLockController();\n    this.triggerController = createTriggerController();\n    this.coreDelegate = CoreDelegate();\n    this.isSheetModal = false;\n    this.inheritedAttributes = {};\n    this.inline = false;\n    // Whether or not modal is being dismissed via gesture\n    this.gestureAnimationDismissing = false;\n    this.onHandleClick = () => {\n      const {\n        sheetTransition,\n        handleBehavior\n      } = this;\n      if (handleBehavior !== 'cycle' || sheetTransition !== undefined) {\n        /**\n         * The sheet modal should not advance to the next breakpoint\n         * if the handle behavior is not `cycle` or if the handle\n         * is clicked while the sheet is moving to a breakpoint.\n         */\n        return;\n      }\n      this.moveToNextBreakpoint();\n    };\n    this.onBackdropTap = () => {\n      const {\n        sheetTransition\n      } = this;\n      if (sheetTransition !== undefined) {\n        /**\n         * When the handle is double clicked at the largest breakpoint,\n         * it will start to move to the first breakpoint. While transitioning,\n         * the backdrop will often receive the second click. We prevent the\n         * backdrop from dismissing the modal while moving between breakpoints.\n         */\n        return;\n      }\n      this.dismiss(undefined, BACKDROP);\n    };\n    this.onLifecycle = modalEvent => {\n      const el = this.usersElement;\n      const name = LIFECYCLE_MAP[modalEvent.type];\n      if (el && name) {\n        const ev = new CustomEvent(name, {\n          bubbles: false,\n          cancelable: false,\n          detail: modalEvent.detail\n        });\n        el.dispatchEvent(ev);\n      }\n    };\n    this.presented = false;\n    this.hasController = false;\n    this.overlayIndex = undefined;\n    this.delegate = undefined;\n    this.keyboardClose = true;\n    this.enterAnimation = undefined;\n    this.leaveAnimation = undefined;\n    this.breakpoints = undefined;\n    this.expandToScroll = true;\n    this.initialBreakpoint = undefined;\n    this.backdropBreakpoint = 0;\n    this.handle = undefined;\n    this.handleBehavior = 'none';\n    this.component = undefined;\n    this.componentProps = undefined;\n    this.cssClass = undefined;\n    this.backdropDismiss = true;\n    this.showBackdrop = true;\n    this.animated = true;\n    this.presentingElement = undefined;\n    this.htmlAttributes = undefined;\n    this.isOpen = false;\n    this.trigger = undefined;\n    this.keepContentsMounted = false;\n    this.focusTrap = true;\n    this.canDismiss = true;\n  }\n  onIsOpenChange(newValue, oldValue) {\n    if (newValue === true && oldValue === false) {\n      this.present();\n    } else if (newValue === false && oldValue === true) {\n      this.dismiss();\n    }\n  }\n  triggerChanged() {\n    const {\n      trigger,\n      el,\n      triggerController\n    } = this;\n    if (trigger) {\n      triggerController.addClickListener(el, trigger);\n    }\n  }\n  breakpointsChanged(breakpoints) {\n    if (breakpoints !== undefined) {\n      this.sortedBreakpoints = breakpoints.sort((a, b) => a - b);\n    }\n  }\n  connectedCallback() {\n    const {\n      el\n    } = this;\n    prepareOverlay(el);\n    this.triggerChanged();\n  }\n  disconnectedCallback() {\n    this.triggerController.removeClickListener();\n  }\n  componentWillLoad() {\n    var _a;\n    const {\n      breakpoints,\n      initialBreakpoint,\n      el,\n      htmlAttributes\n    } = this;\n    const isSheetModal = this.isSheetModal = breakpoints !== undefined && initialBreakpoint !== undefined;\n    const attributesToInherit = ['aria-label', 'role'];\n    this.inheritedAttributes = inheritAttributes(el, attributesToInherit);\n    /**\n     * When using a controller modal you can set attributes\n     * using the htmlAttributes property. Since the above attributes\n     * need to be inherited inside of the modal, we need to look\n     * and see if these attributes are being set via htmlAttributes.\n     *\n     * We could alternatively move this to componentDidLoad to simplify the work\n     * here, but we'd then need to make inheritedAttributes a State variable,\n     * thus causing another render to always happen after the first render.\n     */\n    if (htmlAttributes !== undefined) {\n      attributesToInherit.forEach(attribute => {\n        const attributeValue = htmlAttributes[attribute];\n        if (attributeValue) {\n          /**\n           * If an attribute we need to inherit was\n           * set using htmlAttributes then add it to\n           * inheritedAttributes and remove it from htmlAttributes.\n           * This ensures the attribute is inherited and not\n           * set on the host.\n           *\n           * In this case, if an inherited attribute is set\n           * on the host element and using htmlAttributes then\n           * htmlAttributes wins, but that's not a pattern that we recommend.\n           * The only time you'd need htmlAttributes is when using modalController.\n           */\n          this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), {\n            [attribute]: htmlAttributes[attribute]\n          });\n          delete htmlAttributes[attribute];\n        }\n      });\n    }\n    if (isSheetModal) {\n      this.currentBreakpoint = this.initialBreakpoint;\n    }\n    if (breakpoints !== undefined && initialBreakpoint !== undefined && !breakpoints.includes(initialBreakpoint)) {\n      printIonWarning('[ion-modal] - Your breakpoints array must include the initialBreakpoint value.');\n    }\n    if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n      setOverlayId(this.el);\n    }\n  }\n  componentDidLoad() {\n    /**\n     * If modal was rendered with isOpen=\"true\"\n     * then we should open modal immediately.\n     */\n    if (this.isOpen === true) {\n      raf(() => this.present());\n    }\n    this.breakpointsChanged(this.breakpoints);\n    /**\n     * When binding values in frameworks such as Angular\n     * it is possible for the value to be set after the Web Component\n     * initializes but before the value watcher is set up in Stencil.\n     * As a result, the watcher callback may not be fired.\n     * We work around this by manually calling the watcher\n     * callback when the component has loaded and the watcher\n     * is configured.\n     */\n    this.triggerChanged();\n  }\n  /**\n   * Determines whether or not an overlay\n   * is being used inline or via a controller/JS\n   * and returns the correct delegate.\n   * By default, subsequent calls to getDelegate\n   * will use a cached version of the delegate.\n   * This is useful for calling dismiss after\n   * present so that the correct delegate is given.\n   */\n  getDelegate(force = false) {\n    if (this.workingDelegate && !force) {\n      return {\n        delegate: this.workingDelegate,\n        inline: this.inline\n      };\n    }\n    /**\n     * If using overlay inline\n     * we potentially need to use the coreDelegate\n     * so that this works in vanilla JS apps.\n     * If a developer has presented this component\n     * via a controller, then we can assume\n     * the component is already in the\n     * correct place.\n     */\n    const parentEl = this.el.parentNode;\n    const inline = this.inline = parentEl !== null && !this.hasController;\n    const delegate = this.workingDelegate = inline ? this.delegate || this.coreDelegate : this.delegate;\n    return {\n      inline,\n      delegate\n    };\n  }\n  /**\n   * Determines whether or not the\n   * modal is allowed to dismiss based\n   * on the state of the canDismiss prop.\n   */\n  async checkCanDismiss(data, role) {\n    const {\n      canDismiss\n    } = this;\n    if (typeof canDismiss === 'function') {\n      return canDismiss(data, role);\n    }\n    return canDismiss;\n  }\n  /**\n   * Present the modal overlay after it has been created.\n   */\n  async present() {\n    const unlock = await this.lockController.lock();\n    if (this.presented) {\n      unlock();\n      return;\n    }\n    const {\n      presentingElement,\n      el\n    } = this;\n    /**\n     * If the modal is presented multiple times (inline modals), we\n     * need to reset the current breakpoint to the initial breakpoint.\n     */\n    this.currentBreakpoint = this.initialBreakpoint;\n    const {\n      inline,\n      delegate\n    } = this.getDelegate(true);\n    /**\n     * Emit ionMount so JS Frameworks have an opportunity\n     * to add the child component to the DOM. The child\n     * component will be assigned to this.usersElement below.\n     */\n    this.ionMount.emit();\n    this.usersElement = await attachComponent(delegate, el, this.component, ['ion-page'], this.componentProps, inline);\n    /**\n     * When using the lazy loaded build of Stencil, we need to wait\n     * for every Stencil component instance to be ready before presenting\n     * otherwise there can be a flash of unstyled content. With the\n     * custom elements bundle we need to wait for the JS framework\n     * mount the inner contents of the overlay otherwise WebKit may\n     * get the transition incorrect.\n     */\n    if (hasLazyBuild(el)) {\n      await deepReady(this.usersElement);\n      /**\n       * If keepContentsMounted=\"true\" then the\n       * JS Framework has already mounted the inner\n       * contents so there is no need to wait.\n       * Otherwise, we need to wait for the JS\n       * Framework to mount the inner contents\n       * of this component.\n       */\n    } else if (!this.keepContentsMounted) {\n      await waitForMount();\n    }\n    writeTask(() => this.el.classList.add('show-modal'));\n    const hasCardModal = presentingElement !== undefined;\n    /**\n     * We need to change the status bar at the\n     * start of the animation so that it completes\n     * by the time the card animation is done.\n     */\n    if (hasCardModal && getIonMode(this) === 'ios') {\n      // Cache the original status bar color before the modal is presented\n      this.statusBarStyle = await StatusBar.getStyle();\n      setCardStatusBarDark();\n    }\n    await present(this, 'modalEnter', iosEnterAnimation, mdEnterAnimation, {\n      presentingEl: presentingElement,\n      currentBreakpoint: this.initialBreakpoint,\n      backdropBreakpoint: this.backdropBreakpoint,\n      expandToScroll: this.expandToScroll\n    });\n    /* tslint:disable-next-line */\n    if (typeof window !== 'undefined') {\n      /**\n       * This needs to be setup before any\n       * non-transition async work so it can be dereferenced\n       * in the dismiss method. The dismiss method\n       * only waits for the entering transition\n       * to finish. It does not wait for all of the `present`\n       * method to resolve.\n       */\n      this.keyboardOpenCallback = () => {\n        if (this.gesture) {\n          /**\n           * When the native keyboard is opened and the webview\n           * is resized, the gesture implementation will become unresponsive\n           * and enter a free-scroll mode.\n           *\n           * When the keyboard is opened, we disable the gesture for\n           * a single frame and re-enable once the contents have repositioned\n           * from the keyboard placement.\n           */\n          this.gesture.enable(false);\n          raf(() => {\n            if (this.gesture) {\n              this.gesture.enable(true);\n            }\n          });\n        }\n      };\n      window.addEventListener(KEYBOARD_DID_OPEN, this.keyboardOpenCallback);\n    }\n    if (this.isSheetModal) {\n      this.initSheetGesture();\n    } else if (hasCardModal) {\n      this.initSwipeToClose();\n    }\n    unlock();\n  }\n  initSwipeToClose() {\n    var _a;\n    if (getIonMode(this) !== 'ios') {\n      return;\n    }\n    const {\n      el\n    } = this;\n    // All of the elements needed for the swipe gesture\n    // should be in the DOM and referenced by now, except\n    // for the presenting el\n    const animationBuilder = this.leaveAnimation || config.get('modalLeave', iosLeaveAnimation);\n    const ani = this.animation = animationBuilder(el, {\n      presentingEl: this.presentingElement,\n      expandToScroll: this.expandToScroll\n    });\n    const contentEl = findIonContent(el);\n    if (!contentEl) {\n      printIonContentErrorMsg(el);\n      return;\n    }\n    const statusBarStyle = (_a = this.statusBarStyle) !== null && _a !== void 0 ? _a : Style.Default;\n    this.gesture = createSwipeToCloseGesture(el, ani, statusBarStyle, () => {\n      /**\n       * While the gesture animation is finishing\n       * it is possible for a user to tap the backdrop.\n       * This would result in the dismiss animation\n       * being played again. Typically this is avoided\n       * by setting `presented = false` on the overlay\n       * component; however, we cannot do that here as\n       * that would prevent the element from being\n       * removed from the DOM.\n       */\n      this.gestureAnimationDismissing = true;\n      /**\n       * Reset the status bar style as the dismiss animation\n       * starts otherwise the status bar will be the wrong\n       * color for the duration of the dismiss animation.\n       * The dismiss method does this as well, but\n       * in this case it's only called once the animation\n       * has finished.\n       */\n      setCardStatusBarDefault(this.statusBarStyle);\n      this.animation.onFinish(async () => {\n        await this.dismiss(undefined, GESTURE);\n        this.gestureAnimationDismissing = false;\n      });\n    });\n    this.gesture.enable(true);\n  }\n  initSheetGesture() {\n    const {\n      wrapperEl,\n      initialBreakpoint,\n      backdropBreakpoint\n    } = this;\n    if (!wrapperEl || initialBreakpoint === undefined) {\n      return;\n    }\n    const animationBuilder = this.enterAnimation || config.get('modalEnter', iosEnterAnimation);\n    const ani = this.animation = animationBuilder(this.el, {\n      presentingEl: this.presentingElement,\n      currentBreakpoint: initialBreakpoint,\n      backdropBreakpoint,\n      expandToScroll: this.expandToScroll\n    });\n    ani.progressStart(true, 1);\n    const {\n      gesture,\n      moveSheetToBreakpoint\n    } = createSheetGesture(this.el, this.backdropEl, wrapperEl, initialBreakpoint, backdropBreakpoint, ani, this.sortedBreakpoints, this.expandToScroll, () => {\n      var _a;\n      return (_a = this.currentBreakpoint) !== null && _a !== void 0 ? _a : 0;\n    }, () => this.sheetOnDismiss(), breakpoint => {\n      if (this.currentBreakpoint !== breakpoint) {\n        this.currentBreakpoint = breakpoint;\n        this.ionBreakpointDidChange.emit({\n          breakpoint\n        });\n      }\n    });\n    this.gesture = gesture;\n    this.moveSheetToBreakpoint = moveSheetToBreakpoint;\n    this.gesture.enable(true);\n  }\n  sheetOnDismiss() {\n    /**\n     * While the gesture animation is finishing\n     * it is possible for a user to tap the backdrop.\n     * This would result in the dismiss animation\n     * being played again. Typically this is avoided\n     * by setting `presented = false` on the overlay\n     * component; however, we cannot do that here as\n     * that would prevent the element from being\n     * removed from the DOM.\n     */\n    this.gestureAnimationDismissing = true;\n    this.animation.onFinish(async () => {\n      this.currentBreakpoint = 0;\n      this.ionBreakpointDidChange.emit({\n        breakpoint: this.currentBreakpoint\n      });\n      await this.dismiss(undefined, GESTURE);\n      this.gestureAnimationDismissing = false;\n    });\n  }\n  /**\n   * Dismiss the modal overlay after it has been presented.\n   *\n   * @param data Any data to emit in the dismiss events.\n   * @param role The role of the element that is dismissing the modal. For example, 'cancel' or 'backdrop'.\n   *\n   * This is a no-op if the overlay has not been presented yet. If you want\n   * to remove an overlay from the DOM that was never presented, use the\n   * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n   */\n  async dismiss(data, role) {\n    var _a;\n    if (this.gestureAnimationDismissing && role !== GESTURE) {\n      return false;\n    }\n    /**\n     * Because the canDismiss check below is async,\n     * we need to claim a lock before the check happens,\n     * in case the dismiss transition does run.\n     */\n    const unlock = await this.lockController.lock();\n    /**\n     * If a canDismiss handler is responsible\n     * for calling the dismiss method, we should\n     * not run the canDismiss check again.\n     */\n    if (role !== 'handler' && !(await this.checkCanDismiss(data, role))) {\n      unlock();\n      return false;\n    }\n    const {\n      presentingElement\n    } = this;\n    /**\n     * We need to start the status bar change\n     * before the animation so that the change\n     * finishes when the dismiss animation does.\n     */\n    const hasCardModal = presentingElement !== undefined;\n    if (hasCardModal && getIonMode(this) === 'ios') {\n      setCardStatusBarDefault(this.statusBarStyle);\n    }\n    /* tslint:disable-next-line */\n    if (typeof window !== 'undefined' && this.keyboardOpenCallback) {\n      window.removeEventListener(KEYBOARD_DID_OPEN, this.keyboardOpenCallback);\n      this.keyboardOpenCallback = undefined;\n    }\n    const dismissed = await dismiss(this, data, role, 'modalLeave', iosLeaveAnimation, mdLeaveAnimation, {\n      presentingEl: presentingElement,\n      currentBreakpoint: (_a = this.currentBreakpoint) !== null && _a !== void 0 ? _a : this.initialBreakpoint,\n      backdropBreakpoint: this.backdropBreakpoint,\n      expandToScroll: this.expandToScroll\n    });\n    if (dismissed) {\n      const {\n        delegate\n      } = this.getDelegate();\n      await detachComponent(delegate, this.usersElement);\n      writeTask(() => this.el.classList.remove('show-modal'));\n      if (this.animation) {\n        this.animation.destroy();\n      }\n      if (this.gesture) {\n        this.gesture.destroy();\n      }\n    }\n    this.currentBreakpoint = undefined;\n    this.animation = undefined;\n    unlock();\n    return dismissed;\n  }\n  /**\n   * Returns a promise that resolves when the modal did dismiss.\n   */\n  onDidDismiss() {\n    return eventMethod(this.el, 'ionModalDidDismiss');\n  }\n  /**\n   * Returns a promise that resolves when the modal will dismiss.\n   */\n  onWillDismiss() {\n    return eventMethod(this.el, 'ionModalWillDismiss');\n  }\n  /**\n   * Move a sheet style modal to a specific breakpoint. The breakpoint value must\n   * be a value defined in your `breakpoints` array.\n   */\n  async setCurrentBreakpoint(breakpoint) {\n    if (!this.isSheetModal) {\n      printIonWarning('[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.');\n      return;\n    }\n    if (!this.breakpoints.includes(breakpoint)) {\n      printIonWarning(`[ion-modal] - Attempted to set invalid breakpoint value ${breakpoint}. Please double check that the breakpoint value is part of your defined breakpoints.`);\n      return;\n    }\n    const {\n      currentBreakpoint,\n      moveSheetToBreakpoint,\n      canDismiss,\n      breakpoints,\n      animated\n    } = this;\n    if (currentBreakpoint === breakpoint) {\n      return;\n    }\n    if (moveSheetToBreakpoint) {\n      this.sheetTransition = moveSheetToBreakpoint({\n        breakpoint,\n        breakpointOffset: 1 - currentBreakpoint,\n        canDismiss: canDismiss !== undefined && canDismiss !== true && breakpoints[0] === 0,\n        animated\n      });\n      await this.sheetTransition;\n      this.sheetTransition = undefined;\n    }\n  }\n  /**\n   * Returns the current breakpoint of a sheet style modal\n   */\n  async getCurrentBreakpoint() {\n    return this.currentBreakpoint;\n  }\n  async moveToNextBreakpoint() {\n    const {\n      breakpoints,\n      currentBreakpoint\n    } = this;\n    if (!breakpoints || currentBreakpoint == null) {\n      /**\n       * If the modal does not have breakpoints and/or the current\n       * breakpoint is not set, we can't move to the next breakpoint.\n       */\n      return false;\n    }\n    const allowedBreakpoints = breakpoints.filter(b => b !== 0);\n    const currentBreakpointIndex = allowedBreakpoints.indexOf(currentBreakpoint);\n    const nextBreakpointIndex = (currentBreakpointIndex + 1) % allowedBreakpoints.length;\n    const nextBreakpoint = allowedBreakpoints[nextBreakpointIndex];\n    /**\n     * Sets the current breakpoint to the next available breakpoint.\n     * If the current breakpoint is the last breakpoint, we set the current\n     * breakpoint to the first non-zero breakpoint to avoid dismissing the sheet.\n     */\n    await this.setCurrentBreakpoint(nextBreakpoint);\n    return true;\n  }\n  render() {\n    const {\n      handle,\n      isSheetModal,\n      presentingElement,\n      htmlAttributes,\n      handleBehavior,\n      inheritedAttributes,\n      focusTrap,\n      expandToScroll\n    } = this;\n    const showHandle = handle !== false && isSheetModal;\n    const mode = getIonMode(this);\n    const isCardModal = presentingElement !== undefined && mode === 'ios';\n    const isHandleCycle = handleBehavior === 'cycle';\n    return h(Host, Object.assign({\n      key: '0991b2e4e32da511e59fb1463b47e4ac1b86d1ca',\n      \"no-router\": true,\n      tabindex: \"-1\"\n    }, htmlAttributes, {\n      style: {\n        zIndex: `${20000 + this.overlayIndex}`\n      },\n      class: Object.assign({\n        [mode]: true,\n        ['modal-default']: !isCardModal && !isSheetModal,\n        [`modal-card`]: isCardModal,\n        [`modal-sheet`]: isSheetModal,\n        [`modal-no-expand-scroll`]: isSheetModal && !expandToScroll,\n        'overlay-hidden': true,\n        [FOCUS_TRAP_DISABLE_CLASS]: focusTrap === false\n      }, getClassMap(this.cssClass)),\n      onIonBackdropTap: this.onBackdropTap,\n      onIonModalDidPresent: this.onLifecycle,\n      onIonModalWillPresent: this.onLifecycle,\n      onIonModalWillDismiss: this.onLifecycle,\n      onIonModalDidDismiss: this.onLifecycle\n    }), h(\"ion-backdrop\", {\n      key: 'ca9453ffe1021fb252ad9460676cfabb5633f00f',\n      ref: el => this.backdropEl = el,\n      visible: this.showBackdrop,\n      tappable: this.backdropDismiss,\n      part: \"backdrop\"\n    }), mode === 'ios' && h(\"div\", {\n      key: '9f8da446a7b0f3b26aec856e13f6d6d131a7e37b',\n      class: \"modal-shadow\"\n    }), h(\"div\", Object.assign({\n      key: '9d08bf600571849c97b58f66df40b496a358d1e1',\n      /*\n        role and aria-modal must be used on the\n        same element. They must also be set inside the\n        shadow DOM otherwise ion-button will not be highlighted\n        when using VoiceOver: https://bugs.webkit.org/show_bug.cgi?id=247134\n      */\n      role: \"dialog\"\n    }, inheritedAttributes, {\n      \"aria-modal\": \"true\",\n      class: \"modal-wrapper ion-overlay-wrapper\",\n      part: \"content\",\n      ref: el => this.wrapperEl = el\n    }), showHandle && h(\"button\", {\n      key: 'f8bf0d1126e5376519101225d9965727121ee042',\n      class: \"modal-handle\",\n      // Prevents the handle from receiving keyboard focus when it does not cycle\n      tabIndex: !isHandleCycle ? -1 : 0,\n      \"aria-label\": \"Activate to adjust the size of the dialog overlaying the screen\",\n      onClick: isHandleCycle ? this.onHandleClick : undefined,\n      part: \"handle\"\n    }), h(\"slot\", {\n      key: '6d52849df98f2c6c8fbc03996a931ea6a39a512b'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"isOpen\": [\"onIsOpenChange\"],\n      \"trigger\": [\"triggerChanged\"]\n    };\n  }\n};\nconst LIFECYCLE_MAP = {\n  ionModalDidPresent: 'ionViewDidEnter',\n  ionModalWillPresent: 'ionViewWillEnter',\n  ionModalWillDismiss: 'ionViewWillLeave',\n  ionModalDidDismiss: 'ionViewDidLeave'\n};\nModal.style = {\n  ios: IonModalIosStyle0,\n  md: IonModalMdStyle0\n};\nexport { Modal as ion_modal };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA,IAAI;AAAA,CACH,SAAUA,QAAO;AAChB,EAAAA,OAAM,MAAM,IAAI;AAChB,EAAAA,OAAM,OAAO,IAAI;AACjB,EAAAA,OAAM,SAAS,IAAI;AACrB,GAAG,UAAU,QAAQ,CAAC,EAAE;AACxB,IAAM,YAAY;AAAA,EAChB,YAAY;AACV,UAAM,YAAY,aAAa;AAC/B,QAAI,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,kBAAkB,WAAW,GAAG;AAClG,aAAO,UAAU,QAAQ;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,SAAS;AAChB,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,WAAO,SAAS,OAAO;AAAA,EACzB;AAAA,EACA,UAAU,WAAkB;AAAA;AAC1B,YAAM,SAAS,KAAK,UAAU;AAC9B,UAAI,CAAC,QAAQ;AACX,eAAO,MAAM;AAAA,MACf;AACA,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,MAAM,OAAO,QAAQ;AACzB,aAAO;AAAA,IACT;AAAA;AACF;AAUA,IAAM,2BAA2B,CAAC,GAAG,uBAAuB;AAsB1D,MAAI,uBAAuB,GAAG;AAC5B,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,KAAK,IAAI;AAcvB,QAAM,IAAI,EAAE,qBAAqB;AAMjC,SAAO,IAAI,QAAQ;AACrB;AASA,IAAM,uBAAuB,MAAM;AACjC,MAAI,CAAC,OAAO,IAAI,cAAc,KAAK;AACjC;AAAA,EACF;AACA,YAAU,SAAS;AAAA,IACjB,OAAO,MAAM;AAAA,EACf,CAAC;AACH;AACA,IAAM,0BAA0B,CAAC,eAAe,MAAM,YAAY;AAChE,MAAI,CAAC,OAAO,IAAI,cAAc,KAAK;AACjC;AAAA,EACF;AACA,YAAU,SAAS;AAAA,IACjB,OAAO;AAAA,EACT,CAAC;AACH;AACA,IAAM,mBAAmB,CAAO,IAAI,cAAc;AAShD,MAAI,OAAO,GAAG,eAAe,YAAY;AACvC;AAAA,EACF;AAMA,QAAM,gBAAgB,MAAM,GAAG,WAAW,QAAW,OAAO;AAC5D,MAAI,CAAC,eAAe;AAClB;AAAA,EACF;AAWA,MAAI,UAAU,UAAU,GAAG;AACzB,cAAU,SAAS,MAAM;AACvB,SAAG,QAAQ,QAAW,SAAS;AAAA,IACjC,GAAG;AAAA,MACD,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH,OAAO;AACL,OAAG,QAAQ,QAAW,SAAS;AAAA,EACjC;AACF;AAyEA,IAAM,sBAAsB,OAAK;AAC/B,SAAO,YAAa,YAAY,WAAW,KAAK,UAAU,YAAY,aAAa,KAAK;AAC1F;AAGA,IAAM,uBAAuB;AAAA,EAC3B,sBAAsB;AACxB;AACA,IAAM,4BAA4B,CAAC,IAAI,WAAW,gBAAgB,cAAc;AAK9E,QAAM,oBAAoB;AAC1B,QAAM,SAAS,GAAG;AAClB,MAAI,SAAS;AACb,MAAI,0BAA0B;AAC9B,MAAI,YAAY;AAChB,MAAI,WAAW;AACf,QAAM,oBAAoB;AAC1B,MAAI,iBAAiB;AACrB,MAAI,WAAW;AACf,QAAM,aAAa,MAAM;AACvB,QAAI,aAAa,aAAa,SAAS,GAAG;AACxC,aAAO,UAAU;AAAA,IAMnB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,WAAW,YAAU;AACzB,UAAM,SAAS,OAAO,MAAM;AAC5B,QAAI,WAAW,QAAQ,CAAC,OAAO,SAAS;AACtC,aAAO;AAAA,IACT;AAgBA,gBAAY,sBAAsB,MAAM;AACxC,QAAI,WAAW;AAeb,UAAI,aAAa,SAAS,GAAG;AAC3B,cAAM,OAAO,eAAe,SAAS;AACrC,mBAAW,KAAK,cAAc,eAAe;AAAA,MAC/C,OAAO;AACL,mBAAW;AAAA,MACb;AACA,YAAM,wBAAwB,CAAC,CAAC,UAAU,cAAc,eAAe;AACvE,aAAO,CAAC,yBAAyB,SAAS,cAAc;AAAA,IAC1D;AAKA,UAAM,SAAS,OAAO,QAAQ,YAAY;AAC1C,QAAI,WAAW,MAAM;AACnB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,UAAU,YAAU;AACxB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAMJ,qBAAiB,WAAW;AAS5B,8BAA0B,GAAG,eAAe,UAAa,GAAG,eAAe;AAO3E,QAAI,SAAS,KAAK,WAAW;AAC3B,4BAAsB,SAAS;AAAA,IACjC;AACA,cAAU,cAAc,MAAM,SAAS,IAAI,CAAC;AAAA,EAC9C;AACA,QAAM,SAAS,YAAU;AACvB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAOJ,QAAI,SAAS,KAAK,WAAW;AAC3B,4BAAsB,SAAS;AAAA,IACjC;AAWA,UAAM,OAAO,OAAO,SAAS;AAO7B,UAAM,oCAAoC,QAAQ,KAAK;AAMvD,UAAM,UAAU,oCAAoC,oBAAoB;AASxE,UAAM,gBAAgB,oCAAoC,oBAAoB,OAAO,OAAO,IAAI;AAChG,UAAM,cAAc,MAAM,MAAQ,eAAe,OAAO;AACxD,cAAU,aAAa,WAAW;AASlC,QAAI,eAAe,qBAAqB,WAAW,mBAAmB;AACpE,8BAAwB,cAAc;AAAA,IAMxC,WAAW,cAAc,qBAAqB,YAAY,mBAAmB;AAC3E,2BAAqB;AAAA,IACvB;AACA,eAAW;AAAA,EACb;AACA,QAAM,QAAQ,YAAU;AACtB,UAAM,WAAW,OAAO;AACxB,UAAM,OAAO,OAAO,SAAS;AAC7B,UAAM,oCAAoC,QAAQ,KAAK;AACvD,UAAM,UAAU,oCAAoC,oBAAoB;AACxE,UAAM,gBAAgB,oCAAoC,oBAAoB,OAAO,OAAO,IAAI;AAChG,UAAM,cAAc,MAAM,MAAQ,eAAe,OAAO;AACxD,UAAM,aAAa,OAAO,SAAS,WAAW,OAAQ;AAOtD,UAAM,iBAAiB,CAAC,qCAAqC,aAAa;AAC1E,QAAI,eAAe,iBAAiB,QAAS;AAC7C,QAAI,CAAC,gBAAgB;AACnB,gBAAU,OAAO,gCAAgC;AACjD,sBAAgB,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC;AAAA,IAC9F,OAAO;AACL,gBAAU,OAAO,gCAAgC;AACjD,sBAAgB,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC;AAAA,IAC9F;AACA,UAAM,WAAW,iBAAiB,gBAAgB,OAAO,QAAQ,QAAQ,IAAI,iBAAiB,IAAI,eAAe,QAAQ,QAAQ;AACjI,aAAS;AACT,YAAQ,OAAO,KAAK;AACpB,QAAI,WAAW;AACb,0BAAoB,WAAW,cAAc;AAAA,IAC/C;AACA,cAAU,SAAS,MAAM;AACvB,UAAI,CAAC,gBAAgB;AACnB,gBAAQ,OAAO,IAAI;AAAA,MACrB;AAAA,IACF,CAAC,EAAE,YAAY,iBAAiB,IAAI,GAAG,cAAc,QAAQ;AAa7D,QAAI,qCAAqC,cAAc,UAAU,GAAG;AAClE,uBAAiB,IAAI,SAAS;AAAA,IAChC,WAAW,gBAAgB;AACzB,gBAAU;AAAA,IACZ;AAAA,EACF;AACA,QAAM,UAAU,cAAc;AAAA,IAC5B;AAAA,IACA,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,IAAM,kBAAkB,CAAC,WAAW,aAAa;AAC/C,SAAO,MAAM,KAAK,YAAY,KAAK,IAAI,WAAW,GAAG,GAAG,GAAG;AAC7D;AACA,IAAM,4BAA4B,UAAQ;AACxC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAMJ,QAAM,qBAAqB,uBAAuB,UAAa,qBAAqB;AACpF,QAAM,kBAAkB,qBAAqB,kCAAkC,iBAAiB,MAAM;AACtG,QAAM,oBAAoB,gBAAgB,mBAAmB,EAAE,OAAO,WAAW,GAAG,eAAe;AACnG,MAAI,oBAAoB;AACtB,sBAAkB,aAAa;AAAA,MAC7B,kBAAkB;AAAA,IACpB,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC;AAAA,EACxC;AACA,QAAM,mBAAmB,gBAAgB,kBAAkB,EAAE,UAAU,CAAC;AAAA,IACtE,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW,cAAc,MAAM,oBAAoB,GAAG;AAAA,EACxD,CAAC,CAAC;AAIF,QAAM,mBAAmB,CAAC,iBAAiB,gBAAgB,kBAAkB,EAAE,UAAU,CAAC;AAAA,IACxF,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW,IAAI,IAAI,qBAAqB,GAAG;AAAA,EAC7C,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW,GAAG,oBAAoB,GAAG;AAAA,EACvC,CAAC,CAAC,IAAI;AACN,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,4BAA4B,UAAQ;AACxC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAMJ,QAAM,gBAAgB,kCAAkC,yBAAyB,mBAAmB,kBAAkB,CAAC;AACvH,QAAM,kBAAkB,CAAC;AAAA,IACvB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,CAAC;AACD,QAAM,iBAAiB,CAAC;AAAA,IACtB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,CAAC;AACD,QAAM,oBAAoB,gBAAgB,mBAAmB,EAAE,UAAU,uBAAuB,IAAI,iBAAiB,eAAe;AACpI,QAAM,mBAAmB,gBAAgB,kBAAkB,EAAE,UAAU,CAAC;AAAA,IACtE,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW,cAAc,MAAM,oBAAoB,GAAG;AAAA,EACxD,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,yBAAyB,MAAM;AACnC,QAAM,oBAAoB,gBAAgB,EAAE,OAAO,WAAW,MAAM,yBAAyB,EAAE,aAAa;AAAA,IAC1G,kBAAkB;AAAA,EACpB,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC;AACtC,QAAM,mBAAmB,gBAAgB,EAAE,OAAO,aAAa,qBAAqB,iBAAiB;AACrG,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,EACpB;AACF;AAIA,IAAM,oBAAoB,CAAC,QAAQ,SAAS;AAC1C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,sBAAsB,SAAY,0BAA0B,IAAI,IAAI,uBAAuB;AAC/F,oBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC;AAC/D,mBAAiB,WAAW,KAAK,iBAAiB,+BAA+B,CAAC,EAAE,aAAa;AAAA,IAC/F,SAAS;AAAA,EACX,CAAC;AAGD,GAAC,mBAAmB,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,WAAW,OAAO,cAAc,WAAW,CAAC;AACrJ,QAAM,gBAAgB,gBAAgB,eAAe,EAAE,WAAW,MAAM,EAAE,OAAO,6BAA6B,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,gBAAgB,CAAC,EAAE,eAAe,MAAM;AAClL,QAAI,gBAAgB;AAElB;AAAA,IACF;AAiBA,UAAM,YAAY,OAAO,cAAc,YAAY;AAOnD,UAAM,2BAA2B,OAAO,WAAW,cAAc,YAAY;AAC7E,QAAI,aAAa,CAAC,0BAA0B;AAC1C,YAAM,eAAe,UAAU;AAC/B,YAAM,eAAe,UAAU,UAAU,IAAI;AAC7C,aAAO,WAAW,YAAY,YAAY;AAC1C,gBAAU,MAAM,YAAY,WAAW,MAAM;AAC7C,gBAAU,aAAa,eAAe,MAAM;AAE5C,YAAM,OAAO,OAAO,cAAc,WAAW;AAC7C,WAAK,MAAM,YAAY,kBAAkB,GAAG,YAAY,IAAI;AAAA,IAC9D;AAAA,EACF,CAAC;AACD,MAAI,kBAAkB;AACpB,kBAAc,aAAa,gBAAgB;AAAA,EAC7C;AACA,MAAI,cAAc;AAChB,UAAM,WAAW,OAAO,aAAa;AACrC,UAAM,eAAe,aAAa,YAAY,eAAe,aAAa,sBAAsB;AAChG,UAAM,mBAAmB,eAAe,YAAY;AACpD,UAAM,sBAAsB,gBAAgB,EAAE,aAAa;AAAA,MACzD,WAAW;AAAA,MACX,oBAAoB;AAAA,MACpB,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,SAAS,SAAS;AACxB,QAAI,UAAU;AAMZ,YAAM,kBAAkB,CAAC,IAAI,SAAS,SAAS,eAAe,IAAI,SAAS;AAC3E,YAAM,iBAAiB,eAAe,UAAU;AAChD,YAAM,oBAAoB,qBAAqB;AAC/C,YAAM,iBAAiB,cAAc,cAAc,WAAW,iBAAiB;AAC/E,0BAAoB,YAAY;AAAA,QAC9B,WAAW;AAAA,MACb,CAAC,EAAE,eAAe,MAAM,OAAO,MAAM,YAAY,oBAAoB,OAAO,CAAC,EAAE,WAAW,YAAY,EAAE,UAAU,CAAC;AAAA,QACjH,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,cAAc;AAAA,MAChB,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,cAAc;AAAA,MAChB,CAAC,CAAC;AACF,oBAAc,aAAa,mBAAmB;AAAA,IAChD,OAAO;AACL,oBAAc,aAAa,iBAAiB;AAC5C,UAAI,CAAC,cAAc;AACjB,yBAAiB,OAAO,WAAW,KAAK,GAAG;AAAA,MAC7C,OAAO;AACL,cAAM,oBAAoB,eAAe,qBAAqB,uBAAuB;AACrF,cAAM,iBAAiB,2BAA2B,iBAAiB;AACnE,4BAAoB,YAAY;AAAA,UAC9B,WAAW;AAAA,QACb,CAAC,EAAE,WAAW,iBAAiB,cAAc,gBAAgB,CAAC,EAAE,UAAU,CAAC;AAAA,UACzE,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,WAAW;AAAA,QACb,GAAG;AAAA,UACD,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,WAAW;AAAA,QACb,CAAC,CAAC;AACF,cAAM,kBAAkB,gBAAgB,EAAE,YAAY;AAAA,UACpD,WAAW;AAAA,QACb,CAAC,EAAE,WAAW,iBAAiB,cAAc,eAAe,CAAC,EAAE,UAAU,CAAC;AAAA,UACxE,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,WAAW;AAAA,QACb,GAAG;AAAA,UACD,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,WAAW;AAAA,QACb,CAAC,CAAC;AACF,sBAAc,aAAa,CAAC,qBAAqB,eAAe,CAAC;AAAA,MACnE;AAAA,IACF;AAAA,EACF,OAAO;AACL,kBAAc,aAAa,iBAAiB;AAAA,EAC9C;AACA,SAAO;AACT;AACA,IAAM,yBAAyB,MAAM;AACnC,QAAM,oBAAoB,gBAAgB,EAAE,OAAO,WAAW,2BAA2B,CAAC;AAC1F,QAAM,mBAAmB,gBAAgB,EAAE,OAAO,aAAa,mBAAmB,mBAAmB;AACrG,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAIA,IAAM,oBAAoB,CAAC,QAAQ,MAAM,WAAW,QAAQ;AAC1D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,sBAAsB,SAAY,0BAA0B,IAAI,IAAI,uBAAuB;AAC/F,oBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC;AAC/D,mBAAiB,WAAW,KAAK,iBAAiB,+BAA+B,CAAC,EAAE,aAAa;AAAA,IAC/F,SAAS;AAAA,EACX,CAAC;AACD,QAAM,gBAAgB,gBAAgB,cAAc,EAAE,WAAW,MAAM,EAAE,OAAO,6BAA6B,EAAE,SAAS,QAAQ,EAAE,aAAa,gBAAgB,EAAE,eAAe,MAAM;AACpL,QAAI,gBAAgB;AAElB;AAAA,IACF;AAOA,UAAM,YAAY,OAAO,cAAc,YAAY;AACnD,QAAI,WAAW;AACb,YAAM,eAAe,OAAO,WAAW,cAAc,YAAY;AACjE,gBAAU,MAAM,eAAe,SAAS;AACxC,gBAAU,gBAAgB,aAAa;AACvC,mBAAa,MAAM,YAAY,WAAW,MAAM;AAChD,mBAAa,aAAa,eAAe,MAAM;AAC/C,YAAM,OAAO,OAAO,cAAc,WAAW;AAC7C,WAAK,MAAM,eAAe,gBAAgB;AAAA,IAC5C;AAAA,EACF,CAAC;AACD,MAAI,cAAc;AAChB,UAAM,WAAW,OAAO,aAAa;AACrC,UAAM,eAAe,aAAa,YAAY,eAAe,aAAa,sBAAsB;AAChG,UAAM,mBAAmB,eAAe,YAAY;AACpD,UAAM,sBAAsB,gBAAgB,EAAE,kBAAkB,CAAC,WAAW,CAAC,EAAE,iBAAiB,CAAC,WAAW,CAAC,EAAE,SAAS,iBAAe;AAErI,UAAI,gBAAgB,GAAG;AACrB;AAAA,MACF;AACA,mBAAa,MAAM,YAAY,YAAY,EAAE;AAC7C,YAAM,YAAY,MAAM,KAAK,OAAO,iBAAiB,gCAAgC,CAAC,EAAE,OAAO,OAAK,EAAE,sBAAsB,MAAS,EAAE;AACvI,UAAI,aAAa,GAAG;AAClB,eAAO,MAAM,YAAY,oBAAoB,EAAE;AAAA,MACjD;AAAA,IACF,CAAC;AACD,UAAM,SAAS,SAAS;AACxB,QAAI,UAAU;AACZ,YAAM,kBAAkB,CAAC,IAAI,SAAS,SAAS,eAAe,IAAI,SAAS;AAC3E,YAAM,iBAAiB,eAAe,UAAU;AAChD,YAAM,oBAAoB,qBAAqB;AAC/C,YAAM,iBAAiB,cAAc,cAAc,WAAW,iBAAiB;AAC/E,0BAAoB,WAAW,YAAY,EAAE,UAAU,CAAC;AAAA,QACtD,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,cAAc;AAAA,MAChB,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,cAAc;AAAA,MAChB,CAAC,CAAC;AACF,oBAAc,aAAa,mBAAmB;AAAA,IAChD,OAAO;AACL,oBAAc,aAAa,iBAAiB;AAC5C,UAAI,CAAC,cAAc;AACjB,yBAAiB,OAAO,WAAW,KAAK,GAAG;AAAA,MAC7C,OAAO;AACL,cAAM,oBAAoB,eAAe,qBAAqB,uBAAuB;AACrF,cAAM,iBAAiB,2BAA2B,iBAAiB;AACnE,4BAAoB,WAAW,iBAAiB,cAAc,gBAAgB,CAAC,EAAE,YAAY;AAAA,UAC3F,WAAW;AAAA,QACb,CAAC,EAAE,UAAU,CAAC;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,WAAW;AAAA,QACb,GAAG;AAAA,UACD,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,WAAW;AAAA,QACb,CAAC,CAAC;AACF,cAAM,kBAAkB,gBAAgB,EAAE,WAAW,iBAAiB,cAAc,eAAe,CAAC,EAAE,YAAY;AAAA,UAChH,WAAW;AAAA,QACb,CAAC,EAAE,UAAU,CAAC;AAAA,UACZ,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,WAAW;AAAA,QACb,GAAG;AAAA,UACD,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,WAAW;AAAA,QACb,CAAC,CAAC;AACF,sBAAc,aAAa,CAAC,qBAAqB,eAAe,CAAC;AAAA,MACnE;AAAA,IACF;AAAA,EACF,OAAO;AACL,kBAAc,aAAa,iBAAiB;AAAA,EAC9C;AACA,SAAO;AACT;AACA,IAAM,uBAAuB,MAAM;AACjC,QAAM,oBAAoB,gBAAgB,EAAE,OAAO,WAAW,MAAM,yBAAyB,EAAE,aAAa;AAAA,IAC1G,kBAAkB;AAAA,EACpB,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC;AACtC,QAAM,mBAAmB,gBAAgB,EAAE,UAAU,CAAC;AAAA,IACpD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,EACpB;AACF;AAIA,IAAM,mBAAmB,CAAC,QAAQ,SAAS;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,sBAAsB,SAAY,0BAA0B,IAAI,IAAI,qBAAqB;AAC7F,oBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC;AAC/D,mBAAiB,WAAW,KAAK,cAAc,gBAAgB,CAAC;AAGhE,qBAAmB,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,WAAW,OAAO,cAAc,WAAW,CAAC;AACpJ,QAAM,gBAAgB,gBAAgB,EAAE,WAAW,MAAM,EAAE,OAAO,gCAAgC,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC,EAAE,eAAe,MAAM;AACzL,QAAI,gBAAgB;AAElB;AAAA,IACF;AAiBA,UAAM,YAAY,OAAO,cAAc,YAAY;AAOnD,UAAM,2BAA2B,OAAO,WAAW,cAAc,YAAY;AAC7E,QAAI,aAAa,CAAC,0BAA0B;AAC1C,YAAM,eAAe,UAAU;AAC/B,YAAM,eAAe,UAAU,UAAU,IAAI;AAC7C,aAAO,WAAW,YAAY,YAAY;AAC1C,gBAAU,MAAM,YAAY,WAAW,MAAM;AAC7C,gBAAU,aAAa,eAAe,MAAM;AAE5C,YAAM,OAAO,OAAO,cAAc,WAAW;AAC7C,WAAK,MAAM,YAAY,kBAAkB,GAAG,YAAY,IAAI;AAAA,IAC9D;AAAA,EACF,CAAC;AACD,MAAI,kBAAkB;AACpB,kBAAc,aAAa,gBAAgB;AAAA,EAC7C;AACA,SAAO;AACT;AACA,IAAM,uBAAuB,MAAM;AACjC,QAAM,oBAAoB,gBAAgB,EAAE,OAAO,WAAW,2BAA2B,CAAC;AAC1F,QAAM,mBAAmB,gBAAgB,EAAE,UAAU,CAAC;AAAA,IACpD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACb,CAAC,CAAC;AACF,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAIA,IAAM,mBAAmB,CAAC,QAAQ,SAAS;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,sBAAsB,SAAY,0BAA0B,IAAI,IAAI,qBAAqB;AAC7F,oBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC;AAC/D,mBAAiB,WAAW,KAAK,cAAc,gBAAgB,CAAC;AAChE,QAAM,gBAAgB,gBAAgB,EAAE,OAAO,kCAAkC,EAAE,SAAS,GAAG,EAAE,aAAa,CAAC,mBAAmB,gBAAgB,CAAC,EAAE,eAAe,MAAM;AACxK,QAAI,gBAAgB;AAElB;AAAA,IACF;AAOA,UAAM,YAAY,OAAO,cAAc,YAAY;AACnD,QAAI,WAAW;AACb,YAAM,eAAe,OAAO,WAAW,cAAc,YAAY;AACjE,gBAAU,MAAM,eAAe,SAAS;AACxC,gBAAU,gBAAgB,aAAa;AACvC,mBAAa,MAAM,YAAY,WAAW,MAAM;AAChD,mBAAa,aAAa,eAAe,MAAM;AAC/C,YAAM,OAAO,OAAO,cAAc,WAAW;AAC7C,WAAK,MAAM,eAAe,gBAAgB;AAAA,IAC5C;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,IAAM,qBAAqB,CAAC,QAAQ,YAAY,WAAW,mBAAmB,oBAAoB,WAAW,cAAc,CAAC,GAAG,gBAAgB,sBAAsB,WAAW,uBAAuB;AAErM,QAAM,kBAAkB,CAAC;AAAA,IACvB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,CAAC;AACD,QAAM,iBAAiB,CAAC;AAAA,IACtB,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,GAAG;AAAA,IACD,QAAQ,IAAI;AAAA,IACZ,SAAS;AAAA,EACX,GAAG;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,CAAC;AACD,QAAM,gBAAgB;AAAA,IACpB,mBAAmB,CAAC;AAAA,MAClB,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,CAAC;AAAA,IACD,oBAAoB,uBAAuB,IAAI,iBAAiB;AAAA,IAChE,mBAAmB,CAAC;AAAA,MAClB,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AACA,QAAM,YAAY,OAAO,cAAc,aAAa;AACpD,QAAM,SAAS,UAAU;AACzB,MAAI,oBAAoB;AACxB,MAAI,SAAS;AACb,MAAI,0BAA0B;AAC9B,MAAI,iBAAiB;AACrB,QAAM,oBAAoB;AAC1B,QAAM,gBAAgB,YAAY,YAAY,SAAS,CAAC;AACxD,QAAM,gBAAgB,YAAY,CAAC;AACnC,QAAM,mBAAmB,UAAU,gBAAgB,KAAK,SAAO,IAAI,OAAO,kBAAkB;AAC5F,QAAM,oBAAoB,UAAU,gBAAgB,KAAK,SAAO,IAAI,OAAO,mBAAmB;AAC9F,QAAM,mBAAmB,UAAU,gBAAgB,KAAK,SAAO,IAAI,OAAO,kBAAkB;AAC5F,QAAM,iBAAiB,MAAM;AAC3B,WAAO,MAAM,YAAY,kBAAkB,MAAM;AACjD,eAAW,MAAM,YAAY,kBAAkB,MAAM;AAMrD,WAAO,UAAU,OAAO,wBAAwB;AAAA,EAClD;AACA,QAAM,kBAAkB,MAAM;AAC5B,WAAO,MAAM,YAAY,kBAAkB,MAAM;AACjD,eAAW,MAAM,YAAY,kBAAkB,MAAM;AAQrD,WAAO,UAAU,IAAI,wBAAwB;AAAA,EAC/C;AAKA,QAAM,uBAAuB,YAAU;AACrC,UAAM,iBAAiB,OAAO,cAAc,YAAY;AACxD,QAAI,CAAC,gBAAgB;AACnB;AAAA,IACF;AACA,UAAM,eAAe,UAAU;AAC/B,UAAM,eAAe,WAAW,aAAa,eAAe;AAC5D,UAAM,eAAe,WAAW,aAAa,iBAAiB;AAC9D,iBAAa,MAAM,eAAe,SAAS;AAC3C,iBAAa,gBAAgB,aAAa;AAC1C,UAAM,OAAO,OAAO,cAAc,WAAW;AAC7C,QAAI,WAAW,YAAY;AACzB,WAAK,MAAM,eAAe,gBAAgB;AAAA,IAC5C,OAAO;AACL,YAAM,cAAc,aAAa;AACjC,WAAK,MAAM,YAAY,kBAAkB,GAAG,WAAW,IAAI;AAAA,IAC7D;AACA,iBAAa,MAAM,YAAY,WAAW,MAAM;AAChD,iBAAa,aAAa,eAAe,MAAM;AAAA,EACjD;AASA,MAAI,oBAAoB,mBAAmB;AACzC,qBAAiB,UAAU,CAAC,GAAG,cAAc,iBAAiB,CAAC;AAC/D,sBAAkB,UAAU,CAAC,GAAG,cAAc,kBAAkB,CAAC;AACjE,yBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,UAAU,CAAC,GAAG,cAAc,iBAAiB,CAAC;AACnI,cAAU,cAAc,MAAM,IAAI,iBAAiB;AAQnD,UAAM,uBAAuB,oBAAoB;AACjD,QAAI,sBAAsB;AACxB,qBAAe;AAAA,IACjB,OAAO;AACL,sBAAgB;AAAA,IAClB;AAAA,EACF;AACA,MAAI,aAAa,sBAAsB,iBAAiB,gBAAgB;AACtE,cAAU,UAAU;AAAA,EACtB;AACA,QAAM,WAAW,YAAU;AASzB,UAAMC,aAAY,sBAAsB,OAAO,MAAM,MAAM;AAC3D,wBAAoB,qBAAqB;AAKzC,QAAI,CAAC,kBAAkBA,YAAW;AAChC,YAAM,WAAW,aAAaA,UAAS,IAAI,eAAeA,UAAS,EAAE,cAAc,eAAe,IAAIA;AACtG,aAAO,SAAS,cAAc;AAAA,IAChC;AACA,QAAI,sBAAsB,KAAKA,YAAW;AAUxC,YAAM,WAAW,aAAaA,UAAS,IAAI,eAAeA,UAAS,EAAE,cAAc,eAAe,IAAIA;AACtG,YAAM,wBAAwB,CAAC,CAACA,WAAU,cAAc,eAAe;AACvE,aAAO,CAAC,yBAAyB,SAAS,cAAc;AAAA,IAC1D;AACA,WAAO;AAAA,EACT;AACA,QAAM,UAAU,YAAU;AAaxB,8BAA0B,OAAO,eAAe,UAAa,OAAO,eAAe,QAAQ,kBAAkB;AAM7G,QAAI,CAAC,gBAAgB;AACnB,YAAM,WAAW,sBAAsB,OAAO,MAAM,MAAM;AAC1D,uBAAiB,YAAY,aAAa,QAAQ,IAAI,eAAe,QAAQ,EAAE,cAAc,eAAe,IAAI;AAAA,IAClH;AAOA,QAAI,CAAC,gBAAgB;AACnB,2BAAqB,UAAU;AAAA,IACjC;AAKA,QAAI,OAAO,SAAS,KAAK,WAAW;AAClC,gBAAU,UAAU;AAAA,IACtB;AACA,QAAI,MAAM;AAKR,aAAO,MAAM;AAAA,IACf,CAAC;AACD,cAAU,cAAc,MAAM,IAAI,iBAAiB;AAAA,EACrD;AACA,QAAM,SAAS,YAAU;AAKvB,QAAI,CAAC,kBAAkB,OAAO,UAAU,KAAK,gBAAgB;AAC3D;AAAA,IACF;AAOA,QAAI,OAAO,SAAS,KAAK,WAAW;AAClC,gBAAU,UAAU;AAAA,IACtB;AAMA,UAAM,cAAc,IAAI;AACxB,UAAM,yBAAyB,YAAY,SAAS,IAAI,IAAI,YAAY,CAAC,IAAI;AAC7E,UAAM,OAAO,cAAc,OAAO,SAAS;AAC3C,UAAM,oCAAoC,2BAA2B,UAAa,QAAQ,0BAA0B;AAMpH,UAAM,UAAU,oCAAoC,oBAAoB;AAexE,UAAM,gBAAgB,qCAAqC,2BAA2B,SAAY,yBAAyB,qBAAqB,OAAO,2BAA2B,UAAU,uBAAuB,IAAI;AACvN,aAAS,MAAM,MAAQ,eAAe,OAAO;AAC7C,cAAU,aAAa,MAAM;AAAA,EAC/B;AACA,QAAM,QAAQ,YAAU;AAMtB,QAAI,CAAC,kBAAkB,OAAO,UAAU,KAAK,kBAAkB,eAAe,YAAY,GAAG;AAC3F;AAAA,IACF;AAKA,UAAM,WAAW,OAAO;AACxB,UAAM,aAAa,OAAO,SAAS,WAAW,OAAO;AACrD,UAAM,OAAO,oBAAoB;AACjC,UAAM,UAAU,YAAY,OAAO,CAAC,GAAG,MAAM;AAC3C,aAAO,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,IACvD,CAAC;AACD,0BAAsB;AAAA,MACpB,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,MAKZ,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,QAAM,wBAAwB,aAAW;AACvC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAOJ,UAAM,uBAAuB,cAAc,eAAe;AAC1D,UAAM,mBAAmB,uBAAuB,oBAAoB;AACpE,UAAM,mBAAmB,qBAAqB;AAC9C,wBAAoB;AAKpB,QAAI,oBAAoB,mBAAmB;AACzC,uBAAiB,UAAU,CAAC;AAAA,QAC1B,QAAQ;AAAA,QACR,WAAW,cAAc,mBAAmB,GAAG;AAAA,MACjD,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,WAAW,eAAe,IAAI,oBAAoB,GAAG;AAAA,MACvD,CAAC,CAAC;AACF,wBAAkB,UAAU,CAAC;AAAA,QAC3B,QAAQ;AAAA,QACR,SAAS,kCAAkC,yBAAyB,IAAI,kBAAkB,kBAAkB,CAAC;AAAA,MAC/G,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS,kCAAkC,yBAAyB,kBAAkB,kBAAkB,CAAC;AAAA,MAC3G,CAAC,CAAC;AACF,UAAI,kBAAkB;AAQpB,yBAAiB,UAAU,CAAC;AAAA,UAC1B,QAAQ;AAAA,UACR,WAAW,IAAI,IAAI,oBAAoB,GAAG;AAAA,QAC5C,GAAG;AAAA,UACD,QAAQ;AAAA,UACR,WAAW,GAAG,mBAAmB,GAAG;AAAA,QACtC,CAAC,CAAC;AAAA,MACJ;AACA,gBAAU,aAAa,CAAC;AAAA,IAC1B;AAKA,YAAQ,OAAO,KAAK;AAMpB,QAAI,CAAC,kBAAkB,kBAAkB;AACvC,2BAAqB,QAAQ;AAAA,IAC/B;AACA,QAAI,sBAAsB;AACxB,uBAAiB,QAAQ,SAAS;AAAA,IACpC,WAAW,CAAC,kBAAkB;AAC5B,gBAAU;AAAA,IACZ;AAQA,QAAI,cAAc,qBAAqB,YAAY,YAAY,SAAS,CAAC,KAAK,CAAC,iBAAiB;AAC9F,gBAAU,UAAU;AAAA,IACtB;AACA,WAAO,IAAI,QAAQ,aAAW;AAC5B,gBAAU,SAAS,MAAM;AACvB,YAAI,kBAAkB;AAQpB,cAAI,oBAAoB,mBAAmB;AACzC,gBAAI,MAAM;AACR,+BAAiB,UAAU,CAAC,GAAG,cAAc,iBAAiB,CAAC;AAC/D,gCAAkB,UAAU,CAAC,GAAG,cAAc,kBAAkB,CAAC;AACjE,mCAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,UAAU,CAAC,GAAG,cAAc,iBAAiB,CAAC;AACnI,wBAAU,cAAc,MAAM,IAAI,gBAAgB;AAClD,kCAAoB;AACpB,iCAAmB,iBAAiB;AAKpC,oBAAM,uBAAuB,oBAAoB;AACjD,kBAAI,sBAAsB;AACxB,+BAAe;AAAA,cACjB,OAAO;AACL,gCAAgB;AAAA,cAClB;AACA,sBAAQ,OAAO,IAAI;AACnB,sBAAQ;AAAA,YACV,CAAC;AAAA,UACH,OAAO;AACL,oBAAQ,OAAO,IAAI;AACnB,oBAAQ;AAAA,UACV;AAAA,QACF,OAAO;AACL,kBAAQ;AAAA,QACV;AAAA,MAMF,GAAG;AAAA,QACD,iBAAiB;AAAA,MACnB,CAAC,EAAE,YAAY,GAAG,GAAG,WAAW,MAAM,CAAC;AAAA,IACzC,CAAC;AAAA,EACH;AACA,QAAM,UAAU,cAAc;AAAA,IAC5B,IAAI;AAAA,IACJ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,QAAQ,MAAM;AAAA,EAClB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,aAAa,YAAY,MAAM,sBAAsB,CAAC;AAC3D,SAAK,cAAc,YAAY,MAAM,uBAAuB,CAAC;AAC7D,SAAK,cAAc,YAAY,MAAM,uBAAuB,CAAC;AAC7D,SAAK,aAAa,YAAY,MAAM,sBAAsB,CAAC;AAC3D,SAAK,yBAAyB,YAAY,MAAM,0BAA0B,CAAC;AAC3E,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,iBAAiB,qBAAqB;AAC3C,SAAK,oBAAoB,wBAAwB;AACjD,SAAK,eAAe,aAAa;AACjC,SAAK,eAAe;AACpB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,SAAS;AAEd,SAAK,6BAA6B;AAClC,SAAK,gBAAgB,MAAM;AACzB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,mBAAmB,WAAW,oBAAoB,QAAW;AAM/D;AAAA,MACF;AACA,WAAK,qBAAqB;AAAA,IAC5B;AACA,SAAK,gBAAgB,MAAM;AACzB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,oBAAoB,QAAW;AAOjC;AAAA,MACF;AACA,WAAK,QAAQ,QAAW,QAAQ;AAAA,IAClC;AACA,SAAK,cAAc,gBAAc;AAC/B,YAAM,KAAK,KAAK;AAChB,YAAM,OAAO,cAAc,WAAW,IAAI;AAC1C,UAAI,MAAM,MAAM;AACd,cAAM,KAAK,IAAI,YAAY,MAAM;AAAA,UAC/B,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ,WAAW;AAAA,QACrB,CAAC;AACD,WAAG,cAAc,EAAE;AAAA,MACrB;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AACzB,SAAK,qBAAqB;AAC1B,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,sBAAsB;AAC3B,SAAK,YAAY;AACjB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,eAAe,UAAU,UAAU;AACjC,QAAI,aAAa,QAAQ,aAAa,OAAO;AAC3C,WAAK,QAAQ;AAAA,IACf,WAAW,aAAa,SAAS,aAAa,MAAM;AAClD,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,wBAAkB,iBAAiB,IAAI,OAAO;AAAA,IAChD;AAAA,EACF;AAAA,EACA,mBAAmB,aAAa;AAC9B,QAAI,gBAAgB,QAAW;AAC7B,WAAK,oBAAoB,YAAY,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,mBAAe,EAAE;AACjB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,uBAAuB;AACrB,SAAK,kBAAkB,oBAAoB;AAAA,EAC7C;AAAA,EACA,oBAAoB;AAClB,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,KAAK,eAAe,gBAAgB,UAAa,sBAAsB;AAC5F,UAAM,sBAAsB,CAAC,cAAc,MAAM;AACjD,SAAK,sBAAsB,kBAAkB,IAAI,mBAAmB;AAWpE,QAAI,mBAAmB,QAAW;AAChC,0BAAoB,QAAQ,eAAa;AACvC,cAAM,iBAAiB,eAAe,SAAS;AAC/C,YAAI,gBAAgB;AAalB,eAAK,sBAAsB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,mBAAmB,GAAG;AAAA,YACpF,CAAC,SAAS,GAAG,eAAe,SAAS;AAAA,UACvC,CAAC;AACD,iBAAO,eAAe,SAAS;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,cAAc;AAChB,WAAK,oBAAoB,KAAK;AAAA,IAChC;AACA,QAAI,gBAAgB,UAAa,sBAAsB,UAAa,CAAC,YAAY,SAAS,iBAAiB,GAAG;AAC5G,sBAAgB,gFAAgF;AAAA,IAClG;AACA,QAAI,GAAG,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC5E,mBAAa,KAAK,EAAE;AAAA,IACtB;AAAA,EACF;AAAA,EACA,mBAAmB;AAKjB,QAAI,KAAK,WAAW,MAAM;AACxB,UAAI,MAAM,KAAK,QAAQ,CAAC;AAAA,IAC1B;AACA,SAAK,mBAAmB,KAAK,WAAW;AAUxC,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,QAAQ,OAAO;AACzB,QAAI,KAAK,mBAAmB,CAAC,OAAO;AAClC,aAAO;AAAA,QACL,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK;AAAA,MACf;AAAA,IACF;AAUA,UAAM,WAAW,KAAK,GAAG;AACzB,UAAM,SAAS,KAAK,SAAS,aAAa,QAAQ,CAAC,KAAK;AACxD,UAAM,WAAW,KAAK,kBAAkB,SAAS,KAAK,YAAY,KAAK,eAAe,KAAK;AAC3F,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,gBAAgB,MAAM,MAAM;AAAA;AAChC,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,OAAO,eAAe,YAAY;AACpC,eAAO,WAAW,MAAM,IAAI;AAAA,MAC9B;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,UAAU;AAAA;AACd,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,UAAI,KAAK,WAAW;AAClB,eAAO;AACP;AAAA,MACF;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AAKJ,WAAK,oBAAoB,KAAK;AAC9B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,YAAY,IAAI;AAMzB,WAAK,SAAS,KAAK;AACnB,WAAK,eAAe,MAAM,gBAAgB,UAAU,IAAI,KAAK,WAAW,CAAC,UAAU,GAAG,KAAK,gBAAgB,MAAM;AASjH,UAAI,aAAa,EAAE,GAAG;AACpB,cAAM,UAAU,KAAK,YAAY;AAAA,MASnC,WAAW,CAAC,KAAK,qBAAqB;AACpC,cAAM,aAAa;AAAA,MACrB;AACA,gBAAU,MAAM,KAAK,GAAG,UAAU,IAAI,YAAY,CAAC;AACnD,YAAM,eAAe,sBAAsB;AAM3C,UAAI,gBAAgB,WAAW,IAAI,MAAM,OAAO;AAE9C,aAAK,iBAAiB,MAAM,UAAU,SAAS;AAC/C,6BAAqB;AAAA,MACvB;AACA,YAAM,QAAQ,MAAM,cAAc,mBAAmB,kBAAkB;AAAA,QACrE,cAAc;AAAA,QACd,mBAAmB,KAAK;AAAA,QACxB,oBAAoB,KAAK;AAAA,QACzB,gBAAgB,KAAK;AAAA,MACvB,CAAC;AAED,UAAI,OAAO,WAAW,aAAa;AASjC,aAAK,uBAAuB,MAAM;AAChC,cAAI,KAAK,SAAS;AAUhB,iBAAK,QAAQ,OAAO,KAAK;AACzB,gBAAI,MAAM;AACR,kBAAI,KAAK,SAAS;AAChB,qBAAK,QAAQ,OAAO,IAAI;AAAA,cAC1B;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO,iBAAiB,mBAAmB,KAAK,oBAAoB;AAAA,MACtE;AACA,UAAI,KAAK,cAAc;AACrB,aAAK,iBAAiB;AAAA,MACxB,WAAW,cAAc;AACvB,aAAK,iBAAiB;AAAA,MACxB;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACA,mBAAmB;AACjB,QAAI;AACJ,QAAI,WAAW,IAAI,MAAM,OAAO;AAC9B;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AAIJ,UAAM,mBAAmB,KAAK,kBAAkB,OAAO,IAAI,cAAc,iBAAiB;AAC1F,UAAM,MAAM,KAAK,YAAY,iBAAiB,IAAI;AAAA,MAChD,cAAc,KAAK;AAAA,MACnB,gBAAgB,KAAK;AAAA,IACvB,CAAC;AACD,UAAM,YAAY,eAAe,EAAE;AACnC,QAAI,CAAC,WAAW;AACd,8BAAwB,EAAE;AAC1B;AAAA,IACF;AACA,UAAM,kBAAkB,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,KAAK,MAAM;AACzF,SAAK,UAAU,0BAA0B,IAAI,KAAK,gBAAgB,MAAM;AAWtE,WAAK,6BAA6B;AASlC,8BAAwB,KAAK,cAAc;AAC3C,WAAK,UAAU,SAAS,MAAY;AAClC,cAAM,KAAK,QAAQ,QAAW,OAAO;AACrC,aAAK,6BAA6B;AAAA,MACpC,EAAC;AAAA,IACH,CAAC;AACD,SAAK,QAAQ,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,mBAAmB;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,aAAa,sBAAsB,QAAW;AACjD;AAAA,IACF;AACA,UAAM,mBAAmB,KAAK,kBAAkB,OAAO,IAAI,cAAc,iBAAiB;AAC1F,UAAM,MAAM,KAAK,YAAY,iBAAiB,KAAK,IAAI;AAAA,MACrD,cAAc,KAAK;AAAA,MACnB,mBAAmB;AAAA,MACnB;AAAA,MACA,gBAAgB,KAAK;AAAA,IACvB,CAAC;AACD,QAAI,cAAc,MAAM,CAAC;AACzB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,mBAAmB,KAAK,IAAI,KAAK,YAAY,WAAW,mBAAmB,oBAAoB,KAAK,KAAK,mBAAmB,KAAK,gBAAgB,MAAM;AACzJ,UAAI;AACJ,cAAQ,KAAK,KAAK,uBAAuB,QAAQ,OAAO,SAAS,KAAK;AAAA,IACxE,GAAG,MAAM,KAAK,eAAe,GAAG,gBAAc;AAC5C,UAAI,KAAK,sBAAsB,YAAY;AACzC,aAAK,oBAAoB;AACzB,aAAK,uBAAuB,KAAK;AAAA,UAC/B;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,SAAK,UAAU;AACf,SAAK,wBAAwB;AAC7B,SAAK,QAAQ,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,iBAAiB;AAWf,SAAK,6BAA6B;AAClC,SAAK,UAAU,SAAS,MAAY;AAClC,WAAK,oBAAoB;AACzB,WAAK,uBAAuB,KAAK;AAAA,QAC/B,YAAY,KAAK;AAAA,MACnB,CAAC;AACD,YAAM,KAAK,QAAQ,QAAW,OAAO;AACrC,WAAK,6BAA6B;AAAA,IACpC,EAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWM,QAAQ,MAAM,MAAM;AAAA;AACxB,UAAI;AACJ,UAAI,KAAK,8BAA8B,SAAS,SAAS;AACvD,eAAO;AAAA,MACT;AAMA,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAM9C,UAAI,SAAS,aAAa,EAAE,MAAM,KAAK,gBAAgB,MAAM,IAAI,IAAI;AACnE,eAAO;AACP,eAAO;AAAA,MACT;AACA,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AAMJ,YAAM,eAAe,sBAAsB;AAC3C,UAAI,gBAAgB,WAAW,IAAI,MAAM,OAAO;AAC9C,gCAAwB,KAAK,cAAc;AAAA,MAC7C;AAEA,UAAI,OAAO,WAAW,eAAe,KAAK,sBAAsB;AAC9D,eAAO,oBAAoB,mBAAmB,KAAK,oBAAoB;AACvE,aAAK,uBAAuB;AAAA,MAC9B;AACA,YAAM,YAAY,MAAM,QAAQ,MAAM,MAAM,MAAM,cAAc,mBAAmB,kBAAkB;AAAA,QACnG,cAAc;AAAA,QACd,oBAAoB,KAAK,KAAK,uBAAuB,QAAQ,OAAO,SAAS,KAAK,KAAK;AAAA,QACvF,oBAAoB,KAAK;AAAA,QACzB,gBAAgB,KAAK;AAAA,MACvB,CAAC;AACD,UAAI,WAAW;AACb,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,KAAK,YAAY;AACrB,cAAM,gBAAgB,UAAU,KAAK,YAAY;AACjD,kBAAU,MAAM,KAAK,GAAG,UAAU,OAAO,YAAY,CAAC;AACtD,YAAI,KAAK,WAAW;AAClB,eAAK,UAAU,QAAQ;AAAA,QACzB;AACA,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,QAAQ;AAAA,QACvB;AAAA,MACF;AACA,WAAK,oBAAoB;AACzB,WAAK,YAAY;AACjB,aAAO;AACP,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,WAAO,YAAY,KAAK,IAAI,oBAAoB;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,WAAO,YAAY,KAAK,IAAI,qBAAqB;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,qBAAqB,YAAY;AAAA;AACrC,UAAI,CAAC,KAAK,cAAc;AACtB,wBAAgB,uEAAuE;AACvF;AAAA,MACF;AACA,UAAI,CAAC,KAAK,YAAY,SAAS,UAAU,GAAG;AAC1C,wBAAgB,2DAA2D,UAAU,sFAAsF;AAC3K;AAAA,MACF;AACA,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,sBAAsB,YAAY;AACpC;AAAA,MACF;AACA,UAAI,uBAAuB;AACzB,aAAK,kBAAkB,sBAAsB;AAAA,UAC3C;AAAA,UACA,kBAAkB,IAAI;AAAA,UACtB,YAAY,eAAe,UAAa,eAAe,QAAQ,YAAY,CAAC,MAAM;AAAA,UAClF;AAAA,QACF,CAAC;AACD,cAAM,KAAK;AACX,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,uBAAuB;AAAA;AAC3B,aAAO,KAAK;AAAA,IACd;AAAA;AAAA,EACM,uBAAuB;AAAA;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,CAAC,eAAe,qBAAqB,MAAM;AAK7C,eAAO;AAAA,MACT;AACA,YAAM,qBAAqB,YAAY,OAAO,OAAK,MAAM,CAAC;AAC1D,YAAM,yBAAyB,mBAAmB,QAAQ,iBAAiB;AAC3E,YAAM,uBAAuB,yBAAyB,KAAK,mBAAmB;AAC9E,YAAM,iBAAiB,mBAAmB,mBAAmB;AAM7D,YAAM,KAAK,qBAAqB,cAAc;AAC9C,aAAO;AAAA,IACT;AAAA;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,aAAa,WAAW,SAAS;AACvC,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,cAAc,sBAAsB,UAAa,SAAS;AAChE,UAAM,gBAAgB,mBAAmB;AACzC,WAAO,EAAE,MAAM,OAAO,OAAO;AAAA,MAC3B,KAAK;AAAA,MACL,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,GAAG,gBAAgB;AAAA,MACjB,OAAO;AAAA,QACL,QAAQ,GAAG,MAAQ,KAAK,YAAY;AAAA,MACtC;AAAA,MACA,OAAO,OAAO,OAAO;AAAA,QACnB,CAAC,IAAI,GAAG;AAAA,QACR,CAAC,eAAe,GAAG,CAAC,eAAe,CAAC;AAAA,QACpC,CAAC,YAAY,GAAG;AAAA,QAChB,CAAC,aAAa,GAAG;AAAA,QACjB,CAAC,wBAAwB,GAAG,gBAAgB,CAAC;AAAA,QAC7C,kBAAkB;AAAA,QAClB,CAAC,wBAAwB,GAAG,cAAc;AAAA,MAC5C,GAAG,YAAY,KAAK,QAAQ,CAAC;AAAA,MAC7B,kBAAkB,KAAK;AAAA,MACvB,sBAAsB,KAAK;AAAA,MAC3B,uBAAuB,KAAK;AAAA,MAC5B,uBAAuB,KAAK;AAAA,MAC5B,sBAAsB,KAAK;AAAA,IAC7B,CAAC,GAAG,EAAE,gBAAgB;AAAA,MACpB,KAAK;AAAA,MACL,KAAK,QAAM,KAAK,aAAa;AAAA,MAC7B,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,MACf,MAAM;AAAA,IACR,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO;AAAA,MAC7B,KAAK;AAAA,MACL,OAAO;AAAA,IACT,CAAC,GAAG,EAAE,OAAO,OAAO,OAAO;AAAA,MACzB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOL,MAAM;AAAA,IACR,GAAG,qBAAqB;AAAA,MACtB,cAAc;AAAA,MACd,OAAO;AAAA,MACP,MAAM;AAAA,MACN,KAAK,QAAM,KAAK,YAAY;AAAA,IAC9B,CAAC,GAAG,cAAc,EAAE,UAAU;AAAA,MAC5B,KAAK;AAAA,MACL,OAAO;AAAA;AAAA,MAEP,UAAU,CAAC,gBAAgB,KAAK;AAAA,MAChC,cAAc;AAAA,MACd,SAAS,gBAAgB,KAAK,gBAAgB;AAAA,MAC9C,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,QAAQ;AAAA,MACZ,KAAK;AAAA,IACP,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,UAAU,CAAC,gBAAgB;AAAA,MAC3B,WAAW,CAAC,gBAAgB;AAAA,IAC9B;AAAA,EACF;AACF;AACA,IAAM,gBAAgB;AAAA,EACpB,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,oBAAoB;AACtB;AACA,MAAM,QAAQ;AAAA,EACZ,KAAK;AAAA,EACL,IAAI;AACN;", "names": ["Style", "contentEl"], "x_google_ignoreList": [0]}
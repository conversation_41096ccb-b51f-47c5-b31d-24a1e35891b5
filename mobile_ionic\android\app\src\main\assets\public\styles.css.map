{"version": 3, "sources": ["node_modules/@ionic/angular/src/css/core.scss", "node_modules/@ionic/angular/src/components/modal/modal.vars.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/themes/ionic.globals.scss", "node_modules/@ionic/angular/src/components/menu/menu.ios.vars.scss", "node_modules/@ionic/angular/src/components/menu/menu.md.vars.scss", "node_modules/@ionic/angular/src/css/normalize.scss", "node_modules/@ionic/angular/src/css/structure.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/typography.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/display.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/padding.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/float-elements.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/text-alignment.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/text-transformation.scss", "node_modules/@ionic/angular/src/themes/ionic.mixins.scss", "node_modules/@ionic/angular/src/css/flex-utils.scss", "node_modules/@ionic/angular/src/css/palettes/dark.system.scss", "node_modules/@ionic/angular/src/css/palettes/dark.scss", "src/global.scss", "src/app/styles/emergency-notifications.scss", "node_modules/leaflet/dist/leaflet.css"], "sourcesContent": ["@use \"sass:map\";\n@import \"../themes/ionic.globals\";\n@import \"../components/menu/menu.ios.vars\";\n@import \"../components/menu/menu.md.vars\";\n@import \"../components/modal/modal.vars\";\n\n:root {\n  /**\n   * Loop through each color object from the\n   * `ionic.theme.default.scss` file\n   * and generate CSS Variables for each color.\n   */\n  @each $color-name, $value in $colors {\n    --ion-color-#{$color-name}: #{map.get($value, base)};\n    --ion-color-#{$color-name}-rgb: #{color-to-rgb-list(map.get($value, base))};\n    --ion-color-#{$color-name}-contrast: #{map.get($value, contrast)};\n    --ion-color-#{$color-name}-contrast-rgb: #{color-to-rgb-list(map.get($value, contrast))};\n    --ion-color-#{$color-name}-shade: #{map.get($value, shade)};\n    --ion-color-#{$color-name}-tint: #{map.get($value, tint)};\n  }\n}\n\n// Ionic Font Family\n// --------------------------------------------------\n\nhtml.ios {\n  --ion-default-font: -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", \"Roboto\", sans-serif;\n}\nhtml.md {\n  --ion-default-font: \"Roboto\", \"Helvetica Neue\", sans-serif;\n}\n\nhtml {\n  --ion-dynamic-font: -apple-system-body;\n  --ion-font-family: var(--ion-default-font);\n}\n\nbody {\n  background: var(--ion-background-color);\n  color: var(--ion-text-color);\n}\n\nbody.backdrop-no-scroll {\n  overflow: hidden;\n}\n\n// Modal - Card Style\n// --------------------------------------------------\n/**\n * Card style modal needs additional padding on the\n * top of the header. We accomplish this by targeting\n * the first toolbar in the header.\n * Footer also needs this. We do not adjust the bottom\n * padding though because of the safe area.\n */\nhtml.ios ion-modal.modal-card ion-header ion-toolbar:first-of-type,\nhtml.ios ion-modal.modal-sheet ion-header ion-toolbar:first-of-type,\nhtml.ios ion-modal ion-footer ion-toolbar:first-of-type {\n  padding-top: $modal-sheet-padding-top;\n}\n\n/**\n* Card style modal needs additional padding on the\n* bottom of the header. We accomplish this by targeting\n* the last toolbar in the header.\n*/\nhtml.ios ion-modal.modal-card ion-header ion-toolbar:last-of-type,\nhtml.ios ion-modal.modal-sheet ion-header ion-toolbar:last-of-type {\n  padding-bottom: $modal-sheet-padding-bottom;\n}\n\n/**\n* Add padding on the left and right\n* of toolbars while accounting for\n* safe area values when in landscape.\n*/\nhtml.ios ion-modal ion-toolbar {\n  padding-right: calc(var(--ion-safe-area-right) + 8px);\n  padding-left: calc(var(--ion-safe-area-left) + 8px);\n}\n\n/**\n * Card style modal on iPadOS\n * should only have backdrop on first instance.\n */\n@media screen and (min-width: 768px) {\n  html.ios ion-modal.modal-card:first-of-type {\n    --backdrop-opacity: 0.18;\n  }\n}\n\n/**\n * Subsequent modals should not have a backdrop/box shadow\n * as it will cause the screen to appear to get progressively\n * darker. With Ionic 6, declarative modals made it\n * possible to have multiple non-presented modals in the DOM,\n * so we could no longer rely on ion-modal:first-of-type.\n * Here we disable the opacity/box-shadow for every modal\n * that comes after the first presented modal.\n *\n * Note: ion-modal:not(.overlay-hidden):first-of-type\n * does not match the first modal to not have\n * the .overlay-hidden class, it will match the\n * first modal in general only if it does not\n * have the .overlay-hidden class.\n * The :nth-child() pseudo-class has support\n * for selectors which would help us here. At the\n * time of writing it does not have great cross browser\n * support.\n *\n * Note 2: This should only apply to non-card and\n * non-sheet modals. Card and sheet modals have their\n * own criteria for displaying backdrops/box shadows.\n *\n * Do not use :not(.overlay-hidden) in place of\n * .show-modal because that triggers a memory\n * leak in Blink: https://bugs.chromium.org/p/chromium/issues/detail?id=1418768\n */\nion-modal.modal-default.show-modal ~ ion-modal.modal-default {\n  --backdrop-opacity: 0;\n  --box-shadow: none;\n}\n\n/**\n * This works around a bug in WebKit where the\n * content will overflow outside of the bottom border\n * radius when re-painting. As long as a single\n * border radius value is set on .ion-page, this\n * issue does not happen. We set the top left radius\n * here because the top left corner will always have a\n * radius no matter the platform.\n * This behavior only applies to card modals.\n */\nhtml.ios ion-modal.modal-card .ion-page {\n  border-top-left-radius: var(--border-radius);\n}\n\n// Ionic Colors\n// --------------------------------------------------\n// Generates the color classes and variables based on the\n// colors map\n\n@mixin generate-color($color-name) {\n  $value: map-get($colors, $color-name);\n\n  $base: map-get($value, base);\n  $contrast: map-get($value, contrast);\n  $shade: map-get($value, shade);\n  $tint: map-get($value, tint);\n\n  --ion-color-base: var(--ion-color-#{$color-name}, #{$base}) !important;\n  --ion-color-base-rgb: var(--ion-color-#{$color-name}-rgb, #{color-to-rgb-list($base)}) !important;\n  --ion-color-contrast: var(--ion-color-#{$color-name}-contrast, #{$contrast}) !important;\n  --ion-color-contrast-rgb: var(--ion-color-#{$color-name}-contrast-rgb, #{color-to-rgb-list($contrast)}) !important;\n  --ion-color-shade: var(--ion-color-#{$color-name}-shade, #{$shade}) !important;\n  --ion-color-tint: var(--ion-color-#{$color-name}-tint, #{$tint}) !important;\n}\n\n@each $color-name, $value in $colors {\n  .ion-color-#{$color-name} {\n    @include generate-color($color-name);\n  }\n}\n\n\n// Page Container Structure\n// --------------------------------------------------\n\n.ion-page {\n  @include position(0, 0, 0, 0);\n\n  display: flex;\n  position: absolute;\n\n  flex-direction: column;\n  justify-content: space-between;\n\n  contain: layout size style;\n  z-index: $z-index-page-container;\n}\n\n/**\n * When making custom dialogs, using\n * ion-content is not required. As a result,\n * some developers may wish to have dialogs\n * that are automatically sized by the browser.\n * These changes allow certain dimension values\n * such as fit-content to work correctly.\n */\nion-modal > .ion-page {\n  position: relative;\n\n  contain: layout style;\n\n  height: 100%;\n}\n\n.split-pane-visible > .ion-page.split-pane-main {\n  position: relative;\n}\n\nion-route,\nion-route-redirect,\nion-router,\nion-select-option,\nion-nav-controller,\nion-menu-controller,\nion-action-sheet-controller,\nion-alert-controller,\nion-loading-controller,\nion-modal-controller,\nion-picker-controller,\nion-popover-controller,\nion-toast-controller,\n.ion-page-hidden {\n  /* stylelint-disable-next-line declaration-no-important */\n  display: none !important;\n}\n\n.ion-page-invisible {\n  opacity: 0;\n}\n\n.can-go-back > ion-header ion-back-button {\n  display: block;\n}\n\n\n// Ionic Safe Margins\n// --------------------------------------------------\n\nhtml.plt-ios.plt-hybrid, html.plt-ios.plt-pwa {\n  --ion-statusbar-padding: 20px;\n}\n\n@supports (padding-top: 20px) {\n  html {\n    --ion-safe-area-top: var(--ion-statusbar-padding);\n  }\n}\n\n@supports (padding-top: env(safe-area-inset-top)) {\n  html {\n    --ion-safe-area-top: env(safe-area-inset-top);\n    --ion-safe-area-bottom: env(safe-area-inset-bottom);\n    --ion-safe-area-left: env(safe-area-inset-left);\n    --ion-safe-area-right: env(safe-area-inset-right);\n  }\n}\n\n\n// Global Card Styles\n// --------------------------------------------------\n\nion-card.ion-color .ion-inherit-color,\nion-card-header.ion-color .ion-inherit-color {\n  color: inherit;\n}\n\n\n// Menu Styles\n// --------------------------------------------------\n\n.menu-content {\n  @include transform(translate3d(0, 0, 0));\n}\n\n.menu-content-open {\n  cursor: pointer;\n  touch-action: manipulation;\n\n  /**\n   * The containing element itself should be clickable but\n   * everything inside of it should not clickable when menu is open\n   *\n   * Setting pointer-events after scrolling has already started\n   * will not cancel scrolling which is why we also set\n   * overflow-y below.\n   */\n  pointer-events: none;\n\n  /**\n   * This accounts for scenarios where the main content itself\n   * is scrollable.\n   */\n  overflow-y: hidden;\n}\n\n/**\n * Setting overflow cancels any in-progress scrolling\n * when the menu opens. This prevents users from accidentally\n * scrolling the main content while also dragging the menu open.\n * The code below accounts for both ion-content and then custom\n * scroll containers within ion-content (such as virtual scroll)\n */\n.menu-content-open ion-content {\n  --overflow: hidden;\n}\n\n.menu-content-open .ion-content-scroll-host {\n  overflow: hidden;\n}\n\n.ios .menu-content-reveal {\n  box-shadow: $menu-ios-box-shadow-reveal;\n}\n\n[dir=rtl].ios .menu-content-reveal {\n  box-shadow: $menu-ios-box-shadow-reveal-rtl;\n}\n\n.ios .menu-content-push {\n  box-shadow: $menu-ios-box-shadow-push;\n}\n\n.md .menu-content-reveal {\n  box-shadow: $menu-md-box-shadow;\n}\n\n.md .menu-content-push {\n  box-shadow: $menu-md-box-shadow;\n}\n\n// Accordion Styles\nion-accordion-group.accordion-group-expand-inset > ion-accordion:first-of-type {\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\nion-accordion-group.accordion-group-expand-inset > ion-accordion:last-of-type {\n  border-bottom-left-radius: 8px;\n  border-bottom-right-radius: 8px;\n}\nion-accordion-group > ion-accordion:last-of-type ion-item[slot=\"header\"] {\n  --border-width: 0px;\n}\n\nion-accordion.accordion-animated > [slot=\"header\"] .ion-accordion-toggle-icon {\n  transition: 300ms transform cubic-bezier(0.25, 0.8, 0.5, 1);\n}\n\n@media (prefers-reduced-motion: reduce) {\n  ion-accordion .ion-accordion-toggle-icon {\n    /* stylelint-disable declaration-no-important */\n    transition: none !important;\n  }\n}\n/**\n * The > [slot=\"header\"] selector ensures that we do\n * not modify toggle icons for any nested accordions. The state\n * of one accordion should not affect any accordions inside\n * of a nested accordion group.\n */\nion-accordion.accordion-expanding > [slot=\"header\"] .ion-accordion-toggle-icon,\nion-accordion.accordion-expanded > [slot=\"header\"] .ion-accordion-toggle-icon {\n  transform: rotate(180deg);\n}\n\nion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-previous ion-item[slot=\"header\"] {\n  --border-width: 0px;\n  --inner-border-width: 0px;\n}\n\nion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-expanding:first-of-type,\nion-accordion-group.accordion-group-expand-inset.md > ion-accordion.accordion-expanded:first-of-type {\n  margin-top: 0;\n}\n\n// Safari/iOS 15 changes the appearance of input[type=\"date\"].\n// For backwards compatibility from Ionic 5/Safari 14 designs,\n// we override the appearance only when using within an ion-input.\nion-input input::-webkit-date-and-time-value {\n  text-align: start;\n}\n\n/**\n * The .ion-datetime-button-overlay class contains\n * styles that allow any modal/popover to be\n * sized according to the dimensions of the datetime\n * when used with ion-datetime-button.\n */\n.ion-datetime-button-overlay {\n  --width: fit-content;\n  --height: fit-content;\n}\n\n/**\n * The grid variant can scale down when inline.\n * When used in a `fit-content` overlay, this causes\n * the overlay to shrink when the month/year picker is open.\n * Explicitly setting the dimensions lets us have a consistently\n * sized grid interface.\n */\n.ion-datetime-button-overlay ion-datetime.datetime-grid {\n  width: 320px;\n  min-height: 320px;\n}\n\n/**\n * When moving focus on page transitions we call .focus() on an element which can\n * add an undesired outline ring. This CSS removes the outline ring.\n * We also remove the outline ring from elements that are actively being focused\n * by the focus manager. We are intentionally selective about which elements this\n * applies to so we do not accidentally override outlines set by the developer.\n */\n[ion-last-focus],\nheader[tabindex=\"-1\"]:focus,\n[role=\"banner\"][tabindex=\"-1\"]:focus,\nmain[tabindex=\"-1\"]:focus,\n[role=\"main\"][tabindex=\"-1\"]:focus,\nh1[tabindex=\"-1\"]:focus,\n[role=\"heading\"][aria-level=\"1\"][tabindex=\"-1\"]:focus {\n  outline: none;\n}\n\n/*\n * If a popover has a child ion-content (or class equivalent) then the .popover-viewport element\n * should not be scrollable to ensure the inner content does scroll. However, if the popover\n * does not have a child ion-content (or class equivalent) then the .popover-viewport element\n * should remain scrollable. This code exists globally because popover targets\n * .popover-viewport using ::slotted which only supports simple selectors.\n *\n * Note that we do not need to account for .ion-content-scroll-host here because that\n * class should always be placed within ion-content even if ion-content is not scrollable.\n */\n.popover-viewport:has(> ion-content) {\n  overflow: hidden;\n}\n\n/**\n * :has has cross-browser support, but it is still relatively new. As a result,\n * we should fallback to the old behavior for environments that do not support :has.\n * Developers can explicitly enable this behavior by setting overflow: visible\n * on .popover-viewport if they know they are not going to use an ion-content.\n * TODO FW-6106 Remove this\n */\n@supports not selector(:has(> ion-content)) {\n  .popover-viewport {\n    overflow: hidden;\n  }\n}\n", "@import \"../../themes/ionic.globals\";\n\n// Modals\n// --------------------------------------------------\n\n/// @prop - Min width of the modal inset\n$modal-inset-min-width:         768px;\n\n/// @prop - Minimum height of the small modal inset\n$modal-inset-min-height-small:  600px;\n\n/// @prop - Minimum height of the large modal inset\n$modal-inset-min-height-large:  768px;\n\n/// @prop - Width of the large modal inset\n$modal-inset-width:             600px;\n\n/// @prop - Height of the small modal inset\n$modal-inset-height-small:      500px;\n\n/// @prop - Height of the large modal inset\n$modal-inset-height-large:      600px;\n\n/// @prop - Text color of the modal content\n$modal-text-color:              $text-color;\n\n/// @prop - Padding top of the sheet modal\n$modal-sheet-padding-top:        6px;\n\n/// @prop - Padding bottom of the sheet modal\n$modal-sheet-padding-bottom:     6px;\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "\n// Global Utility Functions\n@import \"./ionic.functions.string\";\n\n// Global Color Functions\n@import \"./ionic.functions.color\";\n\n// Global Font Functions\n@import \"./ionic.functions.font\";\n\n// Global Mixins\n@import \"./ionic.mixins\";\n\n// Default Theme\n@import \"./ionic.theme.default\";\n\n\n// Default General\n// --------------------------------------------------\n$font-family-base:                  var(--ion-font-family, inherit);\n\n// Hairlines width\n$hairlines-width: .55px;\n\n// The minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries\n$screen-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n);\n\n// Input placeholder opacity\n// Ensures that the placeholder has the\n// correct color contrast against the background.\n$placeholder-opacity: var(--ion-placeholder-opacity, 0.6);\n\n$form-control-label-margin: 16px;\n\n// How much the stacked labels should be scaled by\n/// The value 0.75 is used to match the MD spec.\n/// iOS does not have a floating label design spec, so we standardize on 0.75.\n$form-control-label-stacked-scale: 0.75;\n\n\n// Z-Index\n// --------------------------------------------------\n// Grouped by elements which would be siblings\n\n$z-index-menu-overlay:           1000;\n$z-index-overlay:                1001;\n\n$z-index-fixed-content:          999;\n$z-index-refresher:              -1;\n\n$z-index-page-container:         0;\n$z-index-toolbar:                10;\n$z-index-toolbar-background:     -1;\n$z-index-toolbar-buttons:        99;\n\n$z-index-backdrop:               2;\n$z-index-overlay-wrapper:        10;\n\n$z-index-item-options:           1;\n$z-index-item-input:             2;\n$z-index-item-divider:           100;\n\n$z-index-reorder-selected:       100;\n", "@import \"../../themes/ionic.globals.ios\";\n\n// iOS Menu\n// --------------------------------------------------\n\n/// @prop - Box shadow color of the menu\n$menu-ios-box-shadow-color:      rgba(0, 0, 0, .08);\n\n/// @prop - Box shadow of the menu\n$menu-ios-box-shadow:            -8px 0 42px $menu-ios-box-shadow-color;\n\n/// @prop - Box shadow of the menu in rtl mode\n$menu-ios-box-shadow-rtl:        8px 0 42px $menu-ios-box-shadow-color;\n\n/// @prop - Box shadow of the reveal menu\n$menu-ios-box-shadow-reveal:     $menu-ios-box-shadow;\n\n/// @prop - Box shadow of the reveal menu\n$menu-ios-box-shadow-reveal-rtl: $menu-ios-box-shadow-rtl;\n\n/// @prop - Box shadow of the push menu\n$menu-ios-box-shadow-push:       null;\n\n/// @prop - Box shadow of the overlay menu\n$menu-ios-box-shadow-overlay:    null;\n", "@import \"../../themes/ionic.globals.md\";\n\n// Material Design Menu\n// --------------------------------------------------\n\n/// @prop - Box shadow of the menu\n$menu-md-box-shadow:            4px 0px 16px rgba(0, 0, 0, 0.18);\n", "// ! normalize.css v3.0.2 | MIT License | github.com/necolas/normalize.css\n\n\n// HTML5 display definitions\n// ==========================================================================\n\n// 1. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.\naudio,\ncanvas,\nprogress,\nvideo {\n  vertical-align: baseline; // 1\n}\n\n// Prevent modern browsers from displaying `audio` without controls.\n// Remove excess height in iOS 5 devices.\naudio:not([controls]) {\n  display: none;\n\n  height: 0;\n}\n\n\n// Text-level semantics\n// ==========================================================================\n\n// Address style set to `bolder` in Firefox 4+, Safari, and Chrome.\nb,\nstrong {\n  font-weight: bold;\n}\n\n// Embedded content\n// ==========================================================================\n\n// Makes it so the img does not flow outside container\nimg {\n  max-width: 100%;\n}\n\n// Grouping content\n// ==========================================================================\n\nhr {\n  height: 1px;\n\n  border-width: 0;\n\n  box-sizing: content-box;\n}\n\n// Contain overflow in all browsers.\npre {\n  overflow: auto;\n}\n\n// Address odd `em`-unit font size rendering in all browsers.\ncode,\nkbd,\npre,\nsamp {\n  font-family: monospace, monospace;\n  font-size: 1em;\n}\n\n\n// Forms\n// ==========================================================================\n\n// Known limitation: by default, Chrome and Safari on OS X allow very limited\n// styling of `select`, unless a `border` property is set.\n\n// 1. Correct color not being inherited.\n//    Known issue: affects color of disabled elements.\n// 2. Correct font properties not being inherited.\n// 3. Address margins set differently in Firefox 4+, Safari, and Chrome.\n//\n\nlabel,\ninput,\nselect,\ntextarea {\n  font-family: inherit;\n  line-height: normal;\n}\n\ntextarea {\n  overflow: auto;\n\n  height: auto;\n\n  font: inherit;\n  color: inherit;\n}\n\ntextarea::placeholder {\n  padding-left: 2px;\n}\n\nform,\ninput,\noptgroup,\nselect {\n  margin: 0; // 3\n\n  font: inherit; // 2\n  color: inherit; // 1\n}\n\n// 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`\n//    and `video` controls.\n// 2. Correct inability to style clickable `input` types in iOS.\n// 3. Improve usability and consistency of cursor style between image-type\n//    `input` and others.\nhtml input[type=\"button\"], // 1\ninput[type=\"reset\"],\ninput[type=\"submit\"] {\n  cursor: pointer; // 3\n\n  -webkit-appearance: button; // 2\n}\n\n// remove 300ms delay\na,\na div,\na span,\na ion-icon,\na ion-label,\nbutton,\nbutton div,\nbutton span,\nbutton ion-icon,\nbutton ion-label,\n.ion-tappable,\n[tappable],\n[tappable] div,\n[tappable] span,\n[tappable] ion-icon,\n[tappable] ion-label,\ninput,\ntextarea {\n  touch-action: manipulation;\n}\n\na ion-label,\nbutton ion-label {\n  pointer-events: none;\n}\n\nbutton {\n  padding: 0;\n  border: 0;\n  border-radius: 0;\n  font-family: inherit;\n  font-style: inherit;\n  font-variant: inherit;\n  line-height: 1;\n  text-transform: none;\n  cursor: pointer;\n\n  -webkit-appearance: button;\n}\n\n[tappable] {\n  cursor: pointer;\n}\n\n// Re-set default cursor for disabled elements.\na[disabled],\nbutton[disabled],\nhtml input[disabled] {\n  cursor: default;\n}\n\n// Remove inner padding and border in Firefox 4+.\nbutton::-moz-focus-inner,\ninput::-moz-focus-inner {\n  padding: 0;\n\n  border: 0;\n}\n\n// Fix the cursor style for Chrome's increment/decrement buttons. For certain\n// `font-size` values of the `input`, it causes the cursor style of the\n// decrement button to change from `default` to `text`.\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n// Remove inner padding and search cancel button in Safari and Chrome on OS X.\n// Safari (but not Chrome) clips the cancel button when the search input has\n// padding (and `textfield` appearance).\ninput[type=\"search\"]::-webkit-search-cancel-button,\ninput[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n\n// Tables\n// ==========================================================================//\n\n// Remove most spacing between table cells.\ntable {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n\ntd,\nth {\n  padding: 0;\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n\n// Structure\n// --------------------------------------------------\n// Adds structural css to the native html elements\n\n* {\n  box-sizing: border-box;\n\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  -webkit-tap-highlight-color: transparent;\n  -webkit-touch-callout: none;\n}\n\nhtml {\n  width: 100%;\n  height: 100%;\n  -webkit-text-size-adjust: 100%;\n\n  text-size-adjust: 100%;\n}\n\nhtml:not(.hydrated) body {\n  display: none;\n}\n\nhtml.ion-ce body {\n  display: block;\n}\n\nhtml.plt-pwa {\n  height: 100vh;\n}\n\nbody {\n  @include font-smoothing();\n  @include margin(0);\n  @include padding(0);\n\n  position: fixed;\n\n  width: 100%;\n  max-width: 100%;\n  height: 100%;\n  max-height: 100%;\n\n  /**\n   * Because body has position: fixed,\n   * it should be promoted to its own\n   * layer.\n   *\n   * WebKit does not always promote\n   * the body to its own layer on page\n   * load in Ionic apps. Once scrolling on\n   * ion-content starts, WebKit will promote\n   * body. Unfortunately, this causes a re-paint\n   * which results in scrolling being halted\n   * until the next user gesture.\n   *\n   * This impacts the Custom Elements build.\n   * The lazy loaded build causes the browser to\n   * re-paint during hydration which causes WebKit\n   * to promote body to its own layer.\n   * In the CE Build, this hydration does not\n   * happen, so the additional re-paint does not occur.\n   */\n  transform: translateZ(0);\n\n  text-rendering: optimizeLegibility;\n\n  overflow: hidden;\n\n  touch-action: manipulation;\n\n  -webkit-user-drag: none;\n\n  -ms-content-zooming: none;\n\n  word-wrap: break-word;\n\n  overscroll-behavior-y: none;\n  -webkit-text-size-adjust: none;\n\n  text-size-adjust: none;\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Typography\n// --------------------------------------------------\n\n/// @prop - Font weight of all headings\n$headings-font-weight:         500;\n\n/// @prop - Line height of all headings\n$headings-line-height:         1.2;\n\n/// @prop - Font size of heading level 1\n$h1-font-size:                 dynamic-font(26px);\n\n/// @prop - Font size of heading level 2\n$h2-font-size:                 dynamic-font(24px);\n\n/// @prop - Font size of heading level 3\n$h3-font-size:                 dynamic-font(22px);\n\n/// @prop - Font size of heading level 4\n$h4-font-size:                 dynamic-font(20px);\n\n/// @prop - Font size of heading level 5\n$h5-font-size:                 dynamic-font(18px);\n\n/// @prop - Font size of heading level 6\n$h6-font-size:                 dynamic-font(16px);\n\nhtml {\n  font-family: var(--ion-font-family);\n}\n\n/**\n * Dynamic Type is an iOS-only feature, so\n * this should only be enabled on iOS devices.\n */\n@supports (-webkit-touch-callout: none) {\n  html {\n    /**\n     * Includes fallback if Dynamic Type is not enabled.\n     */\n    font: var(--ion-dynamic-font, 16px var(--ion-font-family));\n  }\n}\n\na {\n  background-color: transparent;\n  color: ion-color(primary, base);\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  @include margin(16px, null, 10px, null);\n\n  font-weight: $headings-font-weight;\n\n  line-height: $headings-line-height;\n}\n\nh1 {\n  @include margin(20px, null, null, null);\n\n  font-size: $h1-font-size;\n}\n\nh2 {\n  @include margin(18px, null, null, null);\n\n  font-size: $h2-font-size;\n}\n\nh3 {\n  font-size: $h3-font-size;\n}\n\nh4 {\n  font-size: $h4-font-size;\n}\n\nh5 {\n  font-size: $h5-font-size;\n}\n\nh6 {\n  font-size: $h6-font-size;\n}\n\nsmall {\n  font-size: 75%;\n}\n\nsub,\nsup {\n  position: relative;\n\n  font-size: 75%;\n\n  line-height: 0;\n\n  vertical-align: baseline;\n}\n\nsup {\n  top: -.5em;\n}\n\nsub {\n  bottom: -.25em;\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Display\n// --------------------------------------------------\n// Modifies display of a particular element based on the given classes\n\n.ion-hide {\n  display: none !important;\n}\n\n// Adds hidden classes\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `ion-hide-{bp}-up` classes for hiding the element based\n    // on the breakpoint\n    .ion-hide#{$infix}-up {\n      display: none !important;\n    }\n  }\n\n  @include media-breakpoint-down($breakpoint, $screen-breakpoints) {\n    // Provide `ion-hide-{bp}-down` classes for hiding the element based\n    // on the breakpoint\n    .ion-hide#{$infix}-down {\n      display: none !important;\n    }\n  }\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n\n// Element Space\n// --------------------------------------------------\n// Creates padding and margin attributes to be used on\n// any element\n\n$padding: var(--ion-padding, 16px);\n$margin: var(--ion-margin, 16px);\n\n// Padding\n// --------------------------------------------------\n\n.ion-no-padding {\n  --padding-start: 0;\n  --padding-end: 0;\n  --padding-top: 0;\n  --padding-bottom: 0;\n\n  @include padding(0);\n}\n\n.ion-padding {\n  --padding-start: #{$padding};\n  --padding-end: #{$padding};\n  --padding-top: #{$padding};\n  --padding-bottom: #{$padding};\n\n  @include padding($padding);\n}\n\n.ion-padding-top {\n  --padding-top: #{$padding};\n\n  @include padding($padding, null, null, null);\n}\n\n.ion-padding-start {\n  --padding-start: #{$padding};\n\n  @include padding-horizontal($padding, null);\n}\n\n.ion-padding-end {\n  --padding-end: #{$padding};\n\n  @include padding-horizontal(null, $padding);\n}\n\n.ion-padding-bottom {\n  --padding-bottom: #{$padding};\n\n  @include padding(null, null, $padding, null);\n}\n\n.ion-padding-vertical {\n  --padding-top: #{$padding};\n  --padding-bottom: #{$padding};\n\n  @include padding($padding, null, $padding, null);\n}\n\n.ion-padding-horizontal {\n  --padding-start: #{$padding};\n  --padding-end: #{$padding};\n\n  @include padding-horizontal($padding);\n}\n\n\n// Margin\n// --------------------------------------------------\n\n.ion-no-margin {\n  --margin-start: 0;\n  --margin-end: 0;\n  --margin-top: 0;\n  --margin-bottom: 0;\n\n  @include margin(0);\n}\n\n.ion-margin {\n  --margin-start: #{$margin};\n  --margin-end: #{$margin};\n  --margin-top: #{$margin};\n  --margin-bottom: #{$margin};\n\n  @include margin($margin);\n}\n\n.ion-margin-top {\n  --margin-top: #{$margin};\n\n  @include margin($margin, null, null, null);\n}\n\n.ion-margin-start {\n  --margin-start: #{$margin};\n\n  @include margin-horizontal($margin, null);\n}\n\n.ion-margin-end {\n  --margin-end: #{$margin};\n\n  @include margin-horizontal(null, $margin);\n}\n\n.ion-margin-bottom {\n  --margin-bottom: #{$margin};\n\n  @include margin(null, null, $margin, null);\n}\n\n.ion-margin-vertical {\n  --margin-top: #{$margin};\n  --margin-bottom: #{$margin};\n\n  @include margin($margin, null, $margin, null);\n}\n\n.ion-margin-horizontal {\n  --margin-start: #{$margin};\n  --margin-end: #{$margin};\n\n  @include margin-horizontal($margin);\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Float Elements\n// --------------------------------------------------\n// Creates float classes based on screen size\n\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `.ion-float-{bp}-{side}` classes for floating the element based\n    // on the breakpoint and side\n    .ion-float#{$infix}-left {\n      @include float(left, !important);\n    }\n\n    .ion-float#{$infix}-right {\n      @include float(right, !important);\n    }\n\n    .ion-float#{$infix}-start {\n      @include float(start, !important);\n    }\n\n    .ion-float#{$infix}-end {\n      @include float(end, !important);\n    }\n  }\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Text Alignment\n// --------------------------------------------------\n// Creates text alignment attributes based on screen size\n\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `.ion-text-{bp}` classes for aligning the text based\n    // on the breakpoint\n    .ion-text#{$infix}-center {\n      text-align: center !important;\n    }\n\n    .ion-text#{$infix}-justify {\n      text-align: justify !important;\n    }\n\n    .ion-text#{$infix}-start {\n      text-align: start !important;\n    }\n\n    .ion-text#{$infix}-end {\n      text-align: end !important;\n    }\n\n    .ion-text#{$infix}-left {\n      text-align: left !important;\n    }\n\n    .ion-text#{$infix}-right {\n      text-align: right !important;\n    }\n\n    .ion-text#{$infix}-nowrap {\n      white-space: nowrap !important;\n    }\n\n    .ion-text#{$infix}-wrap {\n      white-space: normal !important;\n    }\n  }\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "@import \"../themes/ionic.globals\";\n@import \"../themes/ionic.mixins\";\n\n// Text Transformation\n// --------------------------------------------------\n// Creates text transform attributes based on screen size\n\n@each $breakpoint in map-keys($screen-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $screen-breakpoints);\n\n  @include media-breakpoint-up($breakpoint, $screen-breakpoints) {\n    // Provide `.ion-text-{bp}` classes for transforming the text based\n    // on the breakpoint\n    .ion-text#{$infix}-uppercase {\n      /* stylelint-disable-next-line declaration-no-important */\n      text-transform: uppercase !important;\n    }\n\n    .ion-text#{$infix}-lowercase {\n      /* stylelint-disable-next-line declaration-no-important */\n      text-transform: lowercase !important;\n    }\n\n    .ion-text#{$infix}-capitalize {\n      /* stylelint-disable-next-line declaration-no-important */\n      text-transform: capitalize !important;\n    }\n  }\n}\n", "\n/**\n * A heuristic that applies CSS to tablet\n * viewports.\n *\n * Usage:\n * @include tablet-viewport() {\n *   :host {\n *     background-color: green;\n *   }\n * }\n */\n@mixin tablet-viewport() {\n  @media screen and (min-width: 768px) {\n    @content;\n  }\n}\n\n/**\n * A heuristic that applies CSS to mobile\n * viewports (i.e. phones, not tablets).\n *\n * Usage:\n * @include mobile-viewport() {\n *   :host {\n *     background-color: blue;\n *   }\n * }\n */\n@mixin mobile-viewport() {\n  @media screen and (max-width: 767px) {\n    @content;\n  }\n}\n\n@mixin input-cover() {\n  @include position(0, null, null, 0);\n  @include margin(0);\n\n  position: absolute;\n\n  width: 100%;\n  height: 100%;\n\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n\n  appearance: none;\n  outline: none;\n\n  &::-moz-focus-inner {\n    border: 0;\n  }\n}\n\n@mixin visually-hidden() {\n  position: absolute;\n\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n\n  width: 100%;\n  height: 100%;\n\n  margin: 0;\n  padding: 0;\n\n  border: 0;\n  outline: 0;\n  clip: rect(0 0 0 0);\n\n  opacity: 0;\n  overflow: hidden;\n\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n@mixin text-inherit() {\n  font-family: inherit;\n  font-size: inherit;\n  font-style: inherit;\n  font-weight: inherit;\n  letter-spacing: inherit;\n  text-decoration: inherit;\n  text-indent: inherit;\n  text-overflow: inherit;\n  text-transform: inherit;\n  text-align: inherit;\n  white-space: inherit;\n  color: inherit;\n}\n\n@mixin button-state() {\n  @include position(0, 0, 0, 0);\n\n  position: absolute;\n\n  content: \"\";\n\n  opacity: 0;\n}\n\n// Font smoothing\n// --------------------------------------------------\n\n@mixin font-smoothing() {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n}\n\n// Get the key from a map based on the index\n@function index-to-key($map, $index) {\n  $keys: map-keys($map);\n\n  @return nth($keys, $index);\n}\n\n\n// Breakpoint Mixins\n// ---------------------------------------------------------------------------------\n\n// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$screen-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// ---------------------------------------------------------------------------------\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $screen-breakpoints) {\n  $min: map-get($breakpoints, $name);\n\n  @return if($name != index-to-key($breakpoints, 1), $min, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash infront.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $screen-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $screen-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $screen-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Maximum breakpoint width. Null for the smallest (first) breakpoint.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n//\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\t// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\t// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $screen-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $screen-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n\n// Text Direction - ltr / rtl\n//\n// CSS defaults to use the ltr css, and adds [dir=rtl] selectors\n// to override ltr defaults.\n// ----------------------------------------------------------\n\n@mixin multi-dir() {\n  @content;\n\n  // $root: #{&};\n  // @at-root [dir] {\n  //   #{$root} {\n  //     @content;\n  //   }\n  // }\n}\n\n@mixin rtl() {\n  $root: #{&};\n\n  $rootSplit: str-split($root, \",\");\n  $selectors: #{add-root-selector($root, \"[dir=rtl]\")};\n  $selectorsSplit: str-split($selectors, \",\");\n\n  $hostContextSelectors: ();\n  $restSelectors: ();\n  $dirSelectors: ();\n\n  // Selectors must be split into individual selectors in case the browser\n  // doesn't support a specific selector.\n  // For example, Firefox and Safari doesn't support `:host-context()`.\n  // If an invalid selector is used, then the entire group of selectors\n  // will be ignored.\n  // @link https://www.w3.org/TR/selectors-3/#grouping\n  @each $selector in $selectorsSplit {\n    // Group the selectors back into a single selector to optimize the output.\n    @if str-index($selector, \":host-context\") {\n      $hostContextSelectors: append($hostContextSelectors, $selector, comma);\n    } @else {\n      // Group the selectors back into a single selector to optimize the output.\n      $restSelectors: append($restSelectors, $selector, comma);\n    }\n  }\n\n  // Supported by Chrome.\n  @if length($hostContextSelectors) > 0 {\n    @at-root #{$hostContextSelectors} {\n      @content;\n    }\n  }\n\n  // Supported by all browsers.\n  @if length($restSelectors) > 0 {\n    @at-root #{$restSelectors} {\n      @content;\n    }\n  }\n\n  // If browser can support `:dir()`, then add the `:dir()` selectors.\n  @supports selector(:dir(rtl)) {\n    // Adding :dir() in case the browser doesn't support `:host-context()` and does support `:dir()`.\n    // `:host-context()` is added:\n    // - through the `add-root-selector()` function.\n    // - first so that it takes precedence over `:dir()`.\n    // For example,\n    // - Firefox doesn't support `:host-context()`, but does support `:dir()`.\n    // - Safari doesn't support `:host-context()`, but Safari 16.4+ supports `:dir()`\n    // @link https://webkit.org/blog/13966/webkit-features-in-safari-16-4/\n    // -- However, there is a Webkit bug on v16 that prevents `:dir()` from working when\n    // -- the app direction is changed dynamically. v17+ works fine.\n    // -- @link https://bugs.webkit.org/show_bug.cgi?id=257133\n\n    // Supported by Firefox.\n    @at-root #{add-root-selector($root, \":dir(rtl)\", false)} {\n      @content;\n    }\n  }\n}\n\n@mixin ltr() {\n  @content;\n}\n\n\n// SVG Background Image Mixin\n// @param {string} $svg\n// ----------------------------------------------------------\n@mixin svg-background-image($svg, $flip-rtl: false) {\n  $url: url-encode($svg);\n  $viewBox: str-split(str-extract($svg, \"viewBox='\", \"'\"), \" \");\n\n  @if $flip-rtl != true or $viewBox == null {\n    @include multi-dir() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n  } @else {\n    $transform: \"transform='translate(#{nth($viewBox, 3)}, 0) scale(-1, 1)'\";\n    $flipped-url: $svg;\n    $flipped-url: str-replace($flipped-url, \"<path\", \"<path #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<line\", \"<line #{$transform}\");\n    $flipped-url: str-replace($flipped-url, \"<polygon\", \"<polygon #{$transform}\");\n    $flipped-url: url-encode($flipped-url);\n\n    @include ltr () {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$url}\");\n    }\n    @include rtl() {\n      background-image: url(\"data:image/svg+xml;charset=utf-8,#{$flipped-url}\");\n    }\n  }\n}\n\n// Add property horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin property-horizontal($prop, $start, $end: $start) {\n  @if $start == 0 and $end == 0 {\n    #{$prop}-left: $start;\n    #{$prop}-right: $end;\n\n  } @else {\n    -webkit-#{$prop}-start: $start;\n    #{$prop}-inline-start: $start;\n    -webkit-#{$prop}-end: $end;\n    #{$prop}-inline-end: $end;\n  }\n}\n\n// Add property for all directions\n// @param {string} $prop\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// @param {boolean} $content include content or use default\n// ----------------------------------------------------------\n@mixin property($prop, $top, $end: $top, $bottom: $top, $start: $end) {\n  @include property-horizontal($prop, $start, $end);\n  #{$prop}-top: $top;\n  #{$prop}-bottom: $bottom;\n}\n\n// Add padding horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin padding-horizontal($start, $end: $start) {\n  @include property-horizontal(padding, $start, $end);\n}\n\n// Add padding for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin padding($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(padding, $top, $end, $bottom, $start);\n}\n\n// Add margin horizontal\n// @param {string} $start\n// @param {string} $end\n// ----------------------------------------------------------\n@mixin margin-horizontal($start, $end: $start) {\n  @include property-horizontal(margin, $start, $end);\n}\n\n// Add margin for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin margin($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(margin, $top, $end, $bottom, $start);\n}\n\n// Add position horizontal\n// @param {string} $start - amount to position start\n// @param {string} $end - amount to left: 0; end\n// ----------------------------------------------------------\n@mixin position-horizontal($start: null, $end: null) {\n  @if $start == $end {\n    @include multi-dir() {\n      left: $start;\n      right: $end;\n    }\n  } @else {\n    @at-root {\n      & {\n        inset-inline-start: $start;\n        inset-inline-end: $end;\n      }\n    }\n  }\n}\n\n// Add position for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin position($top: null, $end: null, $bottom: null, $start: null) {\n  @include position-horizontal($start, $end);\n  top: $top;\n  bottom: $bottom;\n}\n\n// Add border for all directions\n// @param {string} $top\n// @param {string} $end\n// @param {string} $bottom\n// @param {string} $start\n// ----------------------------------------------------------\n@mixin border($top, $end: $top, $bottom: $top, $start: $end) {\n  @include property(border, $top, $end, $bottom, $start);\n}\n\n// Add border radius for all directions\n// @param {string} $top-start\n// @param {string} $top-end\n// @param {string} $bottom-end\n// @param {string} $bottom-start\n// ----------------------------------------------------------\n@mixin border-radius($top-start, $top-end: $top-start, $bottom-end: $top-start, $bottom-start: $top-end) {\n  @if $top-start == $top-end and $top-start == $bottom-end and $top-start == $bottom-start {\n    border-radius: $top-start;\n  } @else {\n    border-start-start-radius: $top-start;\n    border-start-end-radius: $top-end;\n    border-end-end-radius: $bottom-end;\n    border-end-start-radius: $bottom-start;\n  }\n}\n\n// Add direction for all directions\n// @param {string} $dir - Direction on LTR\n@mixin direction($dir) {\n  $other-dir: null;\n\n  @if $dir == ltr {\n    $other-dir: rtl;\n  } @else {\n    $other-dir: ltr;\n  }\n\n  @include ltr() {\n    direction: $dir;\n  }\n  @include rtl() {\n    direction: $other-dir;\n  }\n}\n\n// Add float for all directions\n// @param {string} $side\n// @param {string} $decorator - !important\n@mixin float($side, $decorator: null) {\n  @if $side == start {\n    @include ltr() {\n      float: left $decorator;\n    }\n    @include rtl() {\n      float: right $decorator;\n    }\n  } @else if $side == end {\n    @include ltr() {\n      float: right $decorator;\n    }\n    @include rtl() {\n      float: left $decorator;\n    }\n  } @else {\n    @include multi-dir() {\n      float: $side $decorator;\n    }\n  }\n}\n\n@mixin background-position($horizontal, $horizontal-amount: null, $vertical: null, $vertical-amount: null) {\n  @if $horizontal == start or $horizontal == end {\n    $horizontal-ltr: null;\n    $horizontal-rtl: null;\n    @if $horizontal == start {\n      $horizontal-ltr: left;\n      $horizontal-rtl: right;\n    } @else {\n      $horizontal-ltr: right;\n      $horizontal-rtl: left;\n    }\n\n    @include ltr() {\n      background-position: $horizontal-ltr $horizontal-amount $vertical $vertical-amount;\n    }\n    @include rtl() {\n      background-position: $horizontal-rtl $horizontal-amount $vertical $vertical-amount;\n    }\n  } @else {\n    @include multi-dir() {\n      background-position: $horizontal $horizontal-amount $vertical $vertical-amount;\n    }\n  }\n}\n\n@mixin transform-origin($x-axis, $y-axis: null) {\n  @if $x-axis == start {\n    @include ltr() {\n      transform-origin: left $y-axis;\n    }\n    @include rtl() {\n      transform-origin: right $y-axis;\n    }\n  } @else if $x-axis == end {\n    @include ltr() {\n      transform-origin: right $y-axis;\n    }\n    @include rtl() {\n      transform-origin: left $y-axis;\n    }\n  } @else if $x-axis == left or $x-axis == right {\n    @include multi-dir() {\n      transform-origin: $x-axis $y-axis;\n    }\n  } @else {\n    @include ltr() {\n      transform-origin: $x-axis $y-axis;\n    }\n    @include rtl() {\n      transform-origin: calc(100% - #{$x-axis}) $y-axis;\n    }\n  }\n}\n\n// Add transform for all directions\n// @param {string} $transforms - comma separated list of transforms\n@mixin transform($transforms...) {\n  $extra: null;\n\n  $x: null;\n  $ltr-translate: null;\n  $rtl-translate: null;\n\n  @each $transform in $transforms {\n    @if (str-index($transform, translate3d)) {\n      $transform: str-replace($transform, 'translate3d(');\n      $transform: str-replace($transform, ')');\n\n      $coordinates: str-split($transform, ',');\n\n      $x: nth($coordinates, 1);\n      $y: nth($coordinates, 2);\n      $z: nth($coordinates, 3);\n\n      $ltr-translate: translate3d($x, $y, $z);\n      $rtl-translate: translate3d(calc(-1 * #{$x}), $y, $z);\n    } @else {\n      @if $extra == null {\n        $extra: $transform;\n      } @else {\n        $extra: $extra $transform;\n      }\n    }\n  }\n\n  @if $x == '0' or $x == null {\n    @include multi-dir() {\n      transform: $ltr-translate $extra;\n    }\n  } @else {\n    @include ltr() {\n      transform: $ltr-translate $extra;\n    }\n\n    @include rtl() {\n      transform: $rtl-translate $extra;\n    }\n  }\n}\n", "// Flex Utilities\n// --------------------------------------------------\n// Creates flex classes to align flex containers\n// and items\n\n// Align Self\n// --------------------------------------------------\n\n.ion-align-self-start {\n  align-self: flex-start !important;\n}\n\n.ion-align-self-end {\n  align-self: flex-end !important;\n}\n\n.ion-align-self-center {\n  align-self: center !important;\n}\n\n.ion-align-self-stretch {\n  align-self: stretch !important;\n}\n\n.ion-align-self-baseline {\n  align-self: baseline !important;\n}\n\n.ion-align-self-auto {\n  align-self: auto !important;\n}\n\n\n// Flex Wrap\n// --------------------------------------------------\n\n.ion-wrap {\n  flex-wrap: wrap !important;\n}\n\n.ion-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.ion-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n\n// Justify Content\n// --------------------------------------------------\n\n.ion-justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.ion-justify-content-center {\n  justify-content: center !important;\n}\n\n.ion-justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.ion-justify-content-around {\n  justify-content: space-around !important;\n}\n\n.ion-justify-content-between {\n  justify-content: space-between !important;\n}\n\n.ion-justify-content-evenly {\n  justify-content: space-evenly !important;\n}\n\n\n// Align Items\n// --------------------------------------------------\n\n.ion-align-items-start {\n  align-items: flex-start !important;\n}\n\n.ion-align-items-center {\n  align-items: center !important;\n}\n\n.ion-align-items-end {\n  align-items: flex-end !important;\n}\n\n.ion-align-items-stretch {\n  align-items: stretch !important;\n}\n\n.ion-align-items-baseline {\n  align-items: baseline !important;\n}\n", "@import \"./dark\";\n\n@media (prefers-color-scheme: dark) {\n  :root {\n    @include dark-base-palette();\n  }\n\n  :root.ios {\n    @include dark-ios-palette();\n  }\n\n  :root.md {\n    @include dark-md-palette();\n  }\n}\n", "@use \"sass:map\";\n@import \"../../themes/ionic.functions.color\";\n\n$primary: #4d8dff;\n$secondary: #46b1ff;\n$tertiary: #8482fb;\n$success: #2dd55b;\n$warning: #ffce31;\n$danger: #f24c58;\n$light: #222428;\n$medium: #989aa2;\n$dark: #f4f5f8;\n\n$colors:  (\n  primary: (\n    base:             $primary,\n    contrast:         #000,\n    shade:            get-color-shade($primary),\n    tint:             get-color-tint($primary)\n  ),\n  secondary: (\n    base:             $secondary,\n    contrast:         #000,\n    shade:            get-color-shade($secondary),\n    tint:             get-color-tint($secondary)\n  ),\n  tertiary: (\n    base:             $tertiary,\n    contrast:         #000,\n    shade:            get-color-shade($tertiary),\n    tint:             get-color-tint($tertiary)\n  ),\n  success: (\n    base:             $success,\n    contrast:         #000,\n    shade:            get-color-shade($success),\n    tint:             get-color-tint($success)\n  ),\n  warning: (\n    base:             $warning,\n    contrast:         #000,\n    shade:            get-color-shade($warning),\n    tint:             get-color-tint($warning)\n  ),\n  danger: (\n    base:             $danger,\n    contrast:         #000,\n    shade:            get-color-shade($danger),\n    tint:             get-color-tint($danger)\n  ),\n  light: (\n    base:             $light,\n    contrast:         #fff,\n    shade:            get-color-shade($light),\n    tint:             get-color-tint($light)\n  ),\n  medium: (\n    base:             $medium,\n    contrast:         #000,\n    shade:            get-color-shade($medium),\n    tint:             get-color-tint($medium)\n  ),\n  dark: (\n    base:             $dark,\n    contrast:         #000,\n    shade:            get-color-shade($dark),\n    tint:             get-color-tint($dark)\n  )\n);\n\n@mixin dark-base-palette() {\n  & {\n    @each $color-name, $value in $colors {\n      --ion-color-#{$color-name}: #{map.get($value, base)};\n      --ion-color-#{$color-name}-rgb: #{color-to-rgb-list(map.get($value, base))};\n      --ion-color-#{$color-name}-contrast: #{map.get($value, contrast)};\n      --ion-color-#{$color-name}-contrast-rgb: #{color-to-rgb-list(map.get($value, contrast))};\n      --ion-color-#{$color-name}-shade: #{map.get($value, shade)};\n      --ion-color-#{$color-name}-tint: #{map.get($value, tint)};\n    }\n  }\n}\n\n@mixin dark-ios-palette() {\n  & {\n    --ion-background-color: #000000;\n    --ion-background-color-rgb: 0, 0, 0;\n    --ion-text-color: #ffffff;\n    --ion-text-color-rgb: 255, 255, 255;\n    --ion-background-color-step-50: #0d0d0d;\n    --ion-background-color-step-100: #1a1a1a;\n    --ion-background-color-step-150: #262626;\n    --ion-background-color-step-200: #333333;\n    --ion-background-color-step-250: #404040;\n    --ion-background-color-step-300: #4d4d4d;\n    --ion-background-color-step-350: #595959;\n    --ion-background-color-step-400: #666666;\n    --ion-background-color-step-450: #737373;\n    --ion-background-color-step-500: #808080;\n    --ion-background-color-step-550: #8c8c8c;\n    --ion-background-color-step-600: #999999;\n    --ion-background-color-step-650: #a6a6a6;\n    --ion-background-color-step-700: #b3b3b3;\n    --ion-background-color-step-750: #bfbfbf;\n    --ion-background-color-step-800: #cccccc;\n    --ion-background-color-step-850: #d9d9d9;\n    --ion-background-color-step-900: #e6e6e6;\n    --ion-background-color-step-950: #f2f2f2;\n    --ion-text-color-step-50: #f2f2f2;\n    --ion-text-color-step-100: #e6e6e6;\n    --ion-text-color-step-150: #d9d9d9;\n    --ion-text-color-step-200: #cccccc;\n    --ion-text-color-step-250: #bfbfbf;\n    --ion-text-color-step-300: #b3b3b3;\n    --ion-text-color-step-350: #a6a6a6;\n    --ion-text-color-step-400: #999999;\n    --ion-text-color-step-450: #8c8c8c;\n    --ion-text-color-step-500: #808080;\n    --ion-text-color-step-550: #737373;\n    --ion-text-color-step-600: #666666;\n    --ion-text-color-step-650: #595959;\n    --ion-text-color-step-700: #4d4d4d;\n    --ion-text-color-step-750: #404040;\n    --ion-text-color-step-800: #333333;\n    --ion-text-color-step-850: #262626;\n    --ion-text-color-step-900: #1a1a1a;\n    --ion-text-color-step-950: #0d0d0d;\n    --ion-item-background: #000000;\n    --ion-card-background: #1c1c1d;\n  }\n\n  & ion-modal {\n    --ion-background-color: var(--ion-color-step-100, var(--ion-background-color-step-100));\n    --ion-toolbar-background: var(--ion-color-step-150, var(--ion-background-color-step-150));\n    --ion-toolbar-border-color: var(--ion-color-step-250, var(--ion-background-color-step-250));\n  }\n}\n\n@mixin dark-md-palette() {\n  & {\n    --ion-background-color: #121212;\n    --ion-background-color-rgb: 18, 18, 18;\n    --ion-text-color: #ffffff;\n    --ion-text-color-rgb: 255, 255, 255;\n    --ion-background-color-step-50: #1e1e1e;\n    --ion-background-color-step-100: #2a2a2a;\n    --ion-background-color-step-150: #363636;\n    --ion-background-color-step-200: #414141;\n    --ion-background-color-step-250: #4d4d4d;\n    --ion-background-color-step-300: #595959;\n    --ion-background-color-step-350: #656565;\n    --ion-background-color-step-400: #717171;\n    --ion-background-color-step-450: #7d7d7d;\n    --ion-background-color-step-500: #898989;\n    --ion-background-color-step-550: #949494;\n    --ion-background-color-step-600: #a0a0a0;\n    --ion-background-color-step-650: #acacac;\n    --ion-background-color-step-700: #b8b8b8;\n    --ion-background-color-step-750: #c4c4c4;\n    --ion-background-color-step-800: #d0d0d0;\n    --ion-background-color-step-850: #dbdbdb;\n    --ion-background-color-step-900: #e7e7e7;\n    --ion-background-color-step-950: #f3f3f3;\n    --ion-text-color-step-50: #f3f3f3;\n    --ion-text-color-step-100: #e7e7e7;\n    --ion-text-color-step-150: #dbdbdb;\n    --ion-text-color-step-200: #d0d0d0;\n    --ion-text-color-step-250: #c4c4c4;\n    --ion-text-color-step-300: #b8b8b8;\n    --ion-text-color-step-350: #acacac;\n    --ion-text-color-step-400: #a0a0a0;\n    --ion-text-color-step-450: #949494;\n    --ion-text-color-step-500: #898989;\n    --ion-text-color-step-550: #7d7d7d;\n    --ion-text-color-step-600: #717171;\n    --ion-text-color-step-650: #656565;\n    --ion-text-color-step-700: #595959;\n    --ion-text-color-step-750: #4d4d4d;\n    --ion-text-color-step-800: #414141;\n    --ion-text-color-step-850: #363636;\n    --ion-text-color-step-900: #2a2a2a;\n    --ion-text-color-step-950: #1e1e1e;\n    --ion-item-background: #1e1e1e;\n    --ion-toolbar-background: #1f1f1f;\n    --ion-tab-bar-background: #1f1f1f;\n    --ion-card-background: #1e1e1e;\n  }\n}\n", "/* Emergency notification styles - must be first */\r\n@use \"app/styles/emergency-notifications.scss\";\r\n\r\n/*\r\n * App Global CSS\r\n * ----------------------------------------------------------------------------\r\n * Put style rules here that you want to apply globally. These styles are for\r\n * the entire app and not just one component. Additionally, this file can be\r\n * used as an entry point to import other CSS/Sass files to be included in the\r\n * output CSS.\r\n * For more information on global stylesheets, visit the documentation:\r\n * https://ionicframework.com/docs/layout/global-stylesheets\r\n */\r\n\r\n/* Core CSS required for Ionic components to work properly */\r\n@import \"@ionic/angular/css/core.css\";\r\n\r\n/* Basic CSS for apps built with Ionic */\r\n@import \"@ionic/angular/css/normalize.css\";\r\n@import \"@ionic/angular/css/structure.css\";\r\n@import \"@ionic/angular/css/typography.css\";\r\n@import \"@ionic/angular/css/display.css\";\r\n\r\n/* Optional CSS utils that can be commented out */\r\n@import \"@ionic/angular/css/padding.css\";\r\n@import \"@ionic/angular/css/float-elements.css\";\r\n@import \"@ionic/angular/css/text-alignment.css\";\r\n@import \"@ionic/angular/css/text-transformation.css\";\r\n@import \"@ionic/angular/css/flex-utils.css\";\r\n\r\n/**\r\n * Ionic Dark Mode\r\n * -----------------------------------------------------\r\n * For more info, please see:\r\n * https://ionicframework.com/docs/theming/dark-mode\r\n */\r\n\r\n/* @import \"@ionic/angular/css/palettes/dark.always.css\"; */\r\n/* @import \"@ionic/angular/css/palettes/dark.class.css\"; */\r\n@import '@ionic/angular/css/palettes/dark.system.css';\r\n@import \"leaflet/dist/leaflet.css\";\r\n\r\n/* Custom Modal Styles */\r\n.evacuation-center-modal {\r\n  --backdrop-opacity: 0.6;\r\n  --width: 90%;\r\n  --height: auto;\r\n  --border-radius: 12px;\r\n  --box-shadow: 0 28px 48px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.evacuation-center-modal .modal-wrapper {\r\n  position: relative;\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);\r\n  overflow: hidden;\r\n  border-radius: 12px;\r\n}\r\n\r\n.evacuation-center-modal .ion-page {\r\n  position: relative;\r\n  display: block;\r\n  contain: content;\r\n}\r\n\r\n/* Placeholder for evacuation center images */\r\n.center-image img[src$=\"evacuation-placeholder.jpg\"] {\r\n  object-fit: cover;\r\n  background-color: #f0f0f0;\r\n  background-image: linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%, #e0e0e0),\r\n                    linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%, #e0e0e0);\r\n  background-size: 20px 20px;\r\n  background-position: 0 0, 10px 10px;\r\n}\r\n\r\n/* Leaflet popup styling */\r\n.evacuation-popup {\r\n  padding: 5px;\r\n  max-width: 250px;\r\n}\r\n\r\n.evacuation-popup h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #1565c0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 5px;\r\n}\r\n\r\n.evacuation-popup p {\r\n  margin: 5px 0;\r\n  font-size: 14px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.evacuation-popup strong {\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n/* Evacuation Details Modal Styles */\r\n.evacuation-details-modal {\r\n  --backdrop-opacity: 0.6;\r\n  --border-radius: 16px 16px 0 0;\r\n  --box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n/* Marker icon styles */\r\n.leaflet-marker-icon {\r\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));\r\n  transition: transform 0.2s ease-in-out;\r\n}\r\n\r\n.leaflet-marker-icon:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.evacuation-details-modal .modal-wrapper {\r\n  position: absolute;\r\n  bottom: 0;\r\n  border-radius: 16px 16px 0 0;\r\n  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.evacuation-details-modal ion-content {\r\n  --padding-top: 16px;\r\n  --padding-bottom: 16px;\r\n  --padding-start: 16px;\r\n  --padding-end: 16px;\r\n}\r\n\r\n.evacuation-details-modal ion-header {\r\n  position: relative;\r\n}\r\n\r\n.evacuation-details-modal ion-header::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 8px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 40px;\r\n  height: 4px;\r\n  background-color: var(--ion-color-medium);\r\n  border-radius: 2px;\r\n  opacity: 0.5;\r\n}\r\n\r\n/* Emergency Alert Styles */\r\n.emergency-alert {\r\n  --backdrop-opacity: 0.9;\r\n  --background: #f44336;\r\n  --height: auto;\r\n}\r\n\r\n.emergency-alert .alert-head {\r\n  background-color: #d32f2f;\r\n  color: white;\r\n  padding: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.emergency-alert .alert-title {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  color: white;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.emergency-alert .alert-sub-title {\r\n  font-size: 16px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  margin-top: 4px;\r\n}\r\n\r\n.emergency-alert .alert-message {\r\n  color: white;\r\n  font-size: 16px;\r\n  padding: 16px;\r\n  text-align: center;\r\n}\r\n\r\n.emergency-alert .alert-button-group {\r\n  padding: 8px;\r\n}\r\n\r\n.emergency-alert .alert-button {\r\n  color: white;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  border-radius: 4px;\r\n  margin: 4px;\r\n}\r\n\r\n.emergency-alert .alert-button:hover {\r\n  background-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n/* Emergency Notification Styles - Disaster Specific */\r\n.emergency-notification {\r\n  --backdrop-opacity: 0.95;\r\n  --width: 90%;\r\n  --max-width: 400px;\r\n  --border-radius: 16px;\r\n  --box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.emergency-notification .alert-wrapper {\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.emergency-notification .alert-head {\r\n  padding: 24px 20px 16px 20px;\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n\r\n.emergency-notification .alert-head::before {\r\n  content: '!';\r\n  position: absolute;\r\n  top: -10px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background-color: #d32f2f;\r\n  color: white;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 3px solid white;\r\n}\r\n\r\n.emergency-notification .alert-title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: white;\r\n  text-transform: uppercase;\r\n  margin-top: 8px;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.emergency-notification .alert-sub-title {\r\n  font-size: 14px;\r\n  color: rgba(255, 255, 255, 0.95);\r\n  margin-top: 4px;\r\n  font-weight: 600;\r\n}\r\n\r\n.emergency-notification .alert-message {\r\n  color: white;\r\n  font-size: 16px;\r\n  padding: 16px 20px;\r\n  text-align: center;\r\n  line-height: 1.4;\r\n  background-color: rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.emergency-notification .alert-button-group {\r\n  padding: 16px;\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.emergency-notification .alert-button {\r\n  color: white;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  border-radius: 8px;\r\n  margin: 4px;\r\n  font-weight: 600;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.emergency-notification .alert-button:hover {\r\n  background-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n/* Earthquake Alert - Orange */\r\n.earthquake-alert {\r\n  --background: #ffa500;\r\n}\r\n\r\n.earthquake-alert .alert-head {\r\n  background: linear-gradient(135deg, #ffa500 0%, #ff8c00 100%);\r\n}\r\n\r\n.earthquake-alert .alert-head::before {\r\n  background-color: #cc7a00;\r\n}\r\n\r\n/* Flood Alert - Blue */\r\n.flood-alert {\r\n  --background: #0066cc;\r\n}\r\n\r\n.flood-alert .alert-head {\r\n  background: linear-gradient(135deg, #0066cc 0%, #0052a3 100%);\r\n}\r\n\r\n.flood-alert .alert-head::before {\r\n  background-color: #004080;\r\n}\r\n\r\n/* Typhoon Alert - Green */\r\n.typhoon-alert {\r\n  --background: #008000;\r\n}\r\n\r\n.typhoon-alert .alert-head {\r\n  background: linear-gradient(135deg, #008000 0%, #006600 100%);\r\n}\r\n\r\n.typhoon-alert .alert-head::before {\r\n  background-color: #004d00;\r\n}\r\n\r\n/* Fire Alert - Red */\r\n.fire-alert {\r\n  --background: #ff0000;\r\n}\r\n\r\n.fire-alert .alert-head {\r\n  background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);\r\n}\r\n\r\n.fire-alert .alert-head::before {\r\n  background-color: #990000;\r\n}\r\n\r\n/* General Alert - Gray */\r\n.general-alert {\r\n  --background: #666666;\r\n}\r\n\r\n.general-alert .alert-head {\r\n  background: linear-gradient(135deg, #666666 0%, #555555 100%);\r\n}\r\n\r\n.general-alert .alert-head::before {\r\n  background-color: #444444;\r\n}\r\n", "/* Emergency Notification Modal Styles */\r\n\r\n.emergency-notification {\r\n  --backdrop-opacity: 0.9;\r\n  \r\n  .alert-wrapper {\r\n    border-radius: 16px;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n    border: 3px solid;\r\n    animation: emergencyPulse 2s infinite;\r\n  }\r\n\r\n  .alert-head {\r\n    padding: 20px 20px 10px 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .alert-title {\r\n    font-size: 1.4rem;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    letter-spacing: 1px;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .alert-sub-title {\r\n    font-size: 1rem;\r\n    font-weight: 600;\r\n    opacity: 0.9;\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .alert-message {\r\n    font-size: 1.1rem;\r\n    line-height: 1.5;\r\n    padding: 10px 20px 20px 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .alert-button-group {\r\n    padding: 0 20px 20px 20px;\r\n    display: flex;\r\n    gap: 12px;\r\n  }\r\n\r\n  .alert-button {\r\n    flex: 1;\r\n    font-weight: 600;\r\n    border-radius: 8px;\r\n    padding: 12px 16px;\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .alert-button-primary {\r\n    background: rgba(255, 255, 255, 0.2);\r\n    color: white;\r\n    border: 2px solid rgba(255, 255, 255, 0.5);\r\n  }\r\n\r\n  .alert-button-secondary {\r\n    background: rgba(0, 0, 0, 0.1);\r\n    color: white;\r\n    border: 2px solid rgba(255, 255, 255, 0.3);\r\n  }\r\n}\r\n\r\n/* Disaster-specific styles */\r\n.earthquake-alert {\r\n  .alert-wrapper {\r\n    background: linear-gradient(135deg, #FF8C00, #FFA500);\r\n    border-color: #FF6B00;\r\n    color: white;\r\n  }\r\n}\r\n\r\n.flood-alert {\r\n  .alert-wrapper {\r\n    background: linear-gradient(135deg, #0066CC, #4A90E2);\r\n    border-color: #0052A3;\r\n    color: white;\r\n  }\r\n}\r\n\r\n.typhoon-alert {\r\n  .alert-wrapper {\r\n    background: linear-gradient(135deg, #228B22, #32CD32);\r\n    border-color: #1F7A1F;\r\n    color: white;\r\n  }\r\n}\r\n\r\n.fire-alert {\r\n  .alert-wrapper {\r\n    background: linear-gradient(135deg, #DC143C, #FF4500);\r\n    border-color: #B91C3C;\r\n    color: white;\r\n  }\r\n}\r\n\r\n.general-alert {\r\n  .alert-wrapper {\r\n    background: linear-gradient(135deg, #666666, #888888);\r\n    border-color: #555555;\r\n    color: white;\r\n  }\r\n}\r\n\r\n/* Emergency pulse animation */\r\n@keyframes emergencyPulse {\r\n  0% {\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    box-shadow: 0 8px 32px rgba(255, 0, 0, 0.4), 0 0 20px rgba(255, 0, 0, 0.3);\r\n    transform: scale(1.02);\r\n  }\r\n  100% {\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n/* Toast notification styles for fallback */\r\n.emergency-toast {\r\n  --background: linear-gradient(135deg, #FF8C00, #FFA500);\r\n  --color: white;\r\n  --border-radius: 12px;\r\n  --box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\r\n  \r\n  .toast-wrapper {\r\n    border: 2px solid rgba(255, 255, 255, 0.3);\r\n  }\r\n\r\n  .toast-header {\r\n    font-weight: 700;\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .toast-message {\r\n    font-size: 1rem;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  .toast-button {\r\n    --color: white;\r\n    font-weight: 600;\r\n  }\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 480px) {\r\n  .emergency-notification {\r\n    .alert-wrapper {\r\n      margin: 20px;\r\n      max-width: calc(100vw - 40px);\r\n    }\r\n\r\n    .alert-title {\r\n      font-size: 1.2rem;\r\n    }\r\n\r\n    .alert-message {\r\n      font-size: 1rem;\r\n    }\r\n\r\n    .alert-button {\r\n      font-size: 0.9rem;\r\n      padding: 10px 12px;\r\n    }\r\n  }\r\n}\r\n\r\n/* High contrast mode support */\r\n@media (prefers-contrast: high) {\r\n  .emergency-notification .alert-wrapper {\r\n    border-width: 4px;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);\r\n  }\r\n}\r\n\r\n/* Reduced motion support */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .emergency-notification .alert-wrapper {\r\n    animation: none;\r\n  }\r\n  \r\n  @keyframes emergencyPulse {\r\n    0%, 100% {\r\n      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n      transform: scale(1);\r\n    }\r\n  }\r\n}\r\n", "/* required styles */\r\n\r\n.leaflet-pane,\r\n.leaflet-tile,\r\n.leaflet-marker-icon,\r\n.leaflet-marker-shadow,\r\n.leaflet-tile-container,\r\n.leaflet-pane > svg,\r\n.leaflet-pane > canvas,\r\n.leaflet-zoom-box,\r\n.leaflet-image-layer,\r\n.leaflet-layer {\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\ttop: 0;\r\n\t}\r\n.leaflet-container {\r\n\toverflow: hidden;\r\n\t}\r\n.leaflet-tile,\r\n.leaflet-marker-icon,\r\n.leaflet-marker-shadow {\r\n\t-webkit-user-select: none;\r\n\t   -moz-user-select: none;\r\n\t        user-select: none;\r\n\t  -webkit-user-drag: none;\r\n\t}\r\n/* Prevents IE11 from highlighting tiles in blue */\r\n.leaflet-tile::selection {\r\n\tbackground: transparent;\r\n}\r\n/* <PERSON><PERSON> renders non-retina tile on retina better with this, but Chrome is worse */\r\n.leaflet-safari .leaflet-tile {\r\n\timage-rendering: -webkit-optimize-contrast;\r\n\t}\r\n/* hack that prevents hw layers \"stretching\" when loading new tiles */\r\n.leaflet-safari .leaflet-tile-container {\r\n\twidth: 1600px;\r\n\theight: 1600px;\r\n\t-webkit-transform-origin: 0 0;\r\n\t}\r\n.leaflet-marker-icon,\r\n.leaflet-marker-shadow {\r\n\tdisplay: block;\r\n\t}\r\n/* .leaflet-container svg: reset svg max-width decleration shipped in Joomla! (joomla.org) 3.x */\r\n/* .leaflet-container img: map is broken in FF if you have max-width: 100% on tiles */\r\n.leaflet-container .leaflet-overlay-pane svg {\r\n\tmax-width: none !important;\r\n\tmax-height: none !important;\r\n\t}\r\n.leaflet-container .leaflet-marker-pane img,\r\n.leaflet-container .leaflet-shadow-pane img,\r\n.leaflet-container .leaflet-tile-pane img,\r\n.leaflet-container img.leaflet-image-layer,\r\n.leaflet-container .leaflet-tile {\r\n\tmax-width: none !important;\r\n\tmax-height: none !important;\r\n\twidth: auto;\r\n\tpadding: 0;\r\n\t}\r\n\r\n.leaflet-container img.leaflet-tile {\r\n\t/* See: https://bugs.chromium.org/p/chromium/issues/detail?id=600120 */\r\n\tmix-blend-mode: plus-lighter;\r\n}\r\n\r\n.leaflet-container.leaflet-touch-zoom {\r\n\t-ms-touch-action: pan-x pan-y;\r\n\ttouch-action: pan-x pan-y;\r\n\t}\r\n.leaflet-container.leaflet-touch-drag {\r\n\t-ms-touch-action: pinch-zoom;\r\n\t/* Fallback for FF which doesn't support pinch-zoom */\r\n\ttouch-action: none;\r\n\ttouch-action: pinch-zoom;\r\n}\r\n.leaflet-container.leaflet-touch-drag.leaflet-touch-zoom {\r\n\t-ms-touch-action: none;\r\n\ttouch-action: none;\r\n}\r\n.leaflet-container {\r\n\t-webkit-tap-highlight-color: transparent;\r\n}\r\n.leaflet-container a {\r\n\t-webkit-tap-highlight-color: rgba(51, 181, 229, 0.4);\r\n}\r\n.leaflet-tile {\r\n\tfilter: inherit;\r\n\tvisibility: hidden;\r\n\t}\r\n.leaflet-tile-loaded {\r\n\tvisibility: inherit;\r\n\t}\r\n.leaflet-zoom-box {\r\n\twidth: 0;\r\n\theight: 0;\r\n\t-moz-box-sizing: border-box;\r\n\t     box-sizing: border-box;\r\n\tz-index: 800;\r\n\t}\r\n/* workaround for https://bugzilla.mozilla.org/show_bug.cgi?id=888319 */\r\n.leaflet-overlay-pane svg {\r\n\t-moz-user-select: none;\r\n\t}\r\n\r\n.leaflet-pane         { z-index: 400; }\r\n\r\n.leaflet-tile-pane    { z-index: 200; }\r\n.leaflet-overlay-pane { z-index: 400; }\r\n.leaflet-shadow-pane  { z-index: 500; }\r\n.leaflet-marker-pane  { z-index: 600; }\r\n.leaflet-tooltip-pane   { z-index: 650; }\r\n.leaflet-popup-pane   { z-index: 700; }\r\n\r\n.leaflet-map-pane canvas { z-index: 100; }\r\n.leaflet-map-pane svg    { z-index: 200; }\r\n\r\n.leaflet-vml-shape {\r\n\twidth: 1px;\r\n\theight: 1px;\r\n\t}\r\n.lvml {\r\n\tbehavior: url(#default#VML);\r\n\tdisplay: inline-block;\r\n\tposition: absolute;\r\n\t}\r\n\r\n\r\n/* control positioning */\r\n\r\n.leaflet-control {\r\n\tposition: relative;\r\n\tz-index: 800;\r\n\tpointer-events: visiblePainted; /* IE 9-10 doesn't have auto */\r\n\tpointer-events: auto;\r\n\t}\r\n.leaflet-top,\r\n.leaflet-bottom {\r\n\tposition: absolute;\r\n\tz-index: 1000;\r\n\tpointer-events: none;\r\n\t}\r\n.leaflet-top {\r\n\ttop: 0;\r\n\t}\r\n.leaflet-right {\r\n\tright: 0;\r\n\t}\r\n.leaflet-bottom {\r\n\tbottom: 0;\r\n\t}\r\n.leaflet-left {\r\n\tleft: 0;\r\n\t}\r\n.leaflet-control {\r\n\tfloat: left;\r\n\tclear: both;\r\n\t}\r\n.leaflet-right .leaflet-control {\r\n\tfloat: right;\r\n\t}\r\n.leaflet-top .leaflet-control {\r\n\tmargin-top: 10px;\r\n\t}\r\n.leaflet-bottom .leaflet-control {\r\n\tmargin-bottom: 10px;\r\n\t}\r\n.leaflet-left .leaflet-control {\r\n\tmargin-left: 10px;\r\n\t}\r\n.leaflet-right .leaflet-control {\r\n\tmargin-right: 10px;\r\n\t}\r\n\r\n\r\n/* zoom and fade animations */\r\n\r\n.leaflet-fade-anim .leaflet-popup {\r\n\topacity: 0;\r\n\t-webkit-transition: opacity 0.2s linear;\r\n\t   -moz-transition: opacity 0.2s linear;\r\n\t        transition: opacity 0.2s linear;\r\n\t}\r\n.leaflet-fade-anim .leaflet-map-pane .leaflet-popup {\r\n\topacity: 1;\r\n\t}\r\n.leaflet-zoom-animated {\r\n\t-webkit-transform-origin: 0 0;\r\n\t    -ms-transform-origin: 0 0;\r\n\t        transform-origin: 0 0;\r\n\t}\r\nsvg.leaflet-zoom-animated {\r\n\twill-change: transform;\r\n}\r\n\r\n.leaflet-zoom-anim .leaflet-zoom-animated {\r\n\t-webkit-transition: -webkit-transform 0.25s cubic-bezier(0,0,0.25,1);\r\n\t   -moz-transition:    -moz-transform 0.25s cubic-bezier(0,0,0.25,1);\r\n\t        transition:         transform 0.25s cubic-bezier(0,0,0.25,1);\r\n\t}\r\n.leaflet-zoom-anim .leaflet-tile,\r\n.leaflet-pan-anim .leaflet-tile {\r\n\t-webkit-transition: none;\r\n\t   -moz-transition: none;\r\n\t        transition: none;\r\n\t}\r\n\r\n.leaflet-zoom-anim .leaflet-zoom-hide {\r\n\tvisibility: hidden;\r\n\t}\r\n\r\n\r\n/* cursors */\r\n\r\n.leaflet-interactive {\r\n\tcursor: pointer;\r\n\t}\r\n.leaflet-grab {\r\n\tcursor: -webkit-grab;\r\n\tcursor:    -moz-grab;\r\n\tcursor:         grab;\r\n\t}\r\n.leaflet-crosshair,\r\n.leaflet-crosshair .leaflet-interactive {\r\n\tcursor: crosshair;\r\n\t}\r\n.leaflet-popup-pane,\r\n.leaflet-control {\r\n\tcursor: auto;\r\n\t}\r\n.leaflet-dragging .leaflet-grab,\r\n.leaflet-dragging .leaflet-grab .leaflet-interactive,\r\n.leaflet-dragging .leaflet-marker-draggable {\r\n\tcursor: move;\r\n\tcursor: -webkit-grabbing;\r\n\tcursor:    -moz-grabbing;\r\n\tcursor:         grabbing;\r\n\t}\r\n\r\n/* marker & overlays interactivity */\r\n.leaflet-marker-icon,\r\n.leaflet-marker-shadow,\r\n.leaflet-image-layer,\r\n.leaflet-pane > svg path,\r\n.leaflet-tile-container {\r\n\tpointer-events: none;\r\n\t}\r\n\r\n.leaflet-marker-icon.leaflet-interactive,\r\n.leaflet-image-layer.leaflet-interactive,\r\n.leaflet-pane > svg path.leaflet-interactive,\r\nsvg.leaflet-image-layer.leaflet-interactive path {\r\n\tpointer-events: visiblePainted; /* IE 9-10 doesn't have auto */\r\n\tpointer-events: auto;\r\n\t}\r\n\r\n/* visual tweaks */\r\n\r\n.leaflet-container {\r\n\tbackground: #ddd;\r\n\toutline-offset: 1px;\r\n\t}\r\n.leaflet-container a {\r\n\tcolor: #0078A8;\r\n\t}\r\n.leaflet-zoom-box {\r\n\tborder: 2px dotted #38f;\r\n\tbackground: rgba(255,255,255,0.5);\r\n\t}\r\n\r\n\r\n/* general typography */\r\n.leaflet-container {\r\n\tfont-family: \"Helvetica Neue\", Arial, Helvetica, sans-serif;\r\n\tfont-size: 12px;\r\n\tfont-size: 0.75rem;\r\n\tline-height: 1.5;\r\n\t}\r\n\r\n\r\n/* general toolbar styles */\r\n\r\n.leaflet-bar {\r\n\tbox-shadow: 0 1px 5px rgba(0,0,0,0.65);\r\n\tborder-radius: 4px;\r\n\t}\r\n.leaflet-bar a {\r\n\tbackground-color: #fff;\r\n\tborder-bottom: 1px solid #ccc;\r\n\twidth: 26px;\r\n\theight: 26px;\r\n\tline-height: 26px;\r\n\tdisplay: block;\r\n\ttext-align: center;\r\n\ttext-decoration: none;\r\n\tcolor: black;\r\n\t}\r\n.leaflet-bar a,\r\n.leaflet-control-layers-toggle {\r\n\tbackground-position: 50% 50%;\r\n\tbackground-repeat: no-repeat;\r\n\tdisplay: block;\r\n\t}\r\n.leaflet-bar a:hover,\r\n.leaflet-bar a:focus {\r\n\tbackground-color: #f4f4f4;\r\n\t}\r\n.leaflet-bar a:first-child {\r\n\tborder-top-left-radius: 4px;\r\n\tborder-top-right-radius: 4px;\r\n\t}\r\n.leaflet-bar a:last-child {\r\n\tborder-bottom-left-radius: 4px;\r\n\tborder-bottom-right-radius: 4px;\r\n\tborder-bottom: none;\r\n\t}\r\n.leaflet-bar a.leaflet-disabled {\r\n\tcursor: default;\r\n\tbackground-color: #f4f4f4;\r\n\tcolor: #bbb;\r\n\t}\r\n\r\n.leaflet-touch .leaflet-bar a {\r\n\twidth: 30px;\r\n\theight: 30px;\r\n\tline-height: 30px;\r\n\t}\r\n.leaflet-touch .leaflet-bar a:first-child {\r\n\tborder-top-left-radius: 2px;\r\n\tborder-top-right-radius: 2px;\r\n\t}\r\n.leaflet-touch .leaflet-bar a:last-child {\r\n\tborder-bottom-left-radius: 2px;\r\n\tborder-bottom-right-radius: 2px;\r\n\t}\r\n\r\n/* zoom control */\r\n\r\n.leaflet-control-zoom-in,\r\n.leaflet-control-zoom-out {\r\n\tfont: bold 18px 'Lucida Console', Monaco, monospace;\r\n\ttext-indent: 1px;\r\n\t}\r\n\r\n.leaflet-touch .leaflet-control-zoom-in, .leaflet-touch .leaflet-control-zoom-out  {\r\n\tfont-size: 22px;\r\n\t}\r\n\r\n\r\n/* layers control */\r\n\r\n.leaflet-control-layers {\r\n\tbox-shadow: 0 1px 5px rgba(0,0,0,0.4);\r\n\tbackground: #fff;\r\n\tborder-radius: 5px;\r\n\t}\r\n.leaflet-control-layers-toggle {\r\n\tbackground-image: url(images/layers.png);\r\n\twidth: 36px;\r\n\theight: 36px;\r\n\t}\r\n.leaflet-retina .leaflet-control-layers-toggle {\r\n\tbackground-image: url(images/layers-2x.png);\r\n\tbackground-size: 26px 26px;\r\n\t}\r\n.leaflet-touch .leaflet-control-layers-toggle {\r\n\twidth: 44px;\r\n\theight: 44px;\r\n\t}\r\n.leaflet-control-layers .leaflet-control-layers-list,\r\n.leaflet-control-layers-expanded .leaflet-control-layers-toggle {\r\n\tdisplay: none;\r\n\t}\r\n.leaflet-control-layers-expanded .leaflet-control-layers-list {\r\n\tdisplay: block;\r\n\tposition: relative;\r\n\t}\r\n.leaflet-control-layers-expanded {\r\n\tpadding: 6px 10px 6px 6px;\r\n\tcolor: #333;\r\n\tbackground: #fff;\r\n\t}\r\n.leaflet-control-layers-scrollbar {\r\n\toverflow-y: scroll;\r\n\toverflow-x: hidden;\r\n\tpadding-right: 5px;\r\n\t}\r\n.leaflet-control-layers-selector {\r\n\tmargin-top: 2px;\r\n\tposition: relative;\r\n\ttop: 1px;\r\n\t}\r\n.leaflet-control-layers label {\r\n\tdisplay: block;\r\n\tfont-size: 13px;\r\n\tfont-size: 1.08333em;\r\n\t}\r\n.leaflet-control-layers-separator {\r\n\theight: 0;\r\n\tborder-top: 1px solid #ddd;\r\n\tmargin: 5px -10px 5px -6px;\r\n\t}\r\n\r\n/* Default icon URLs */\r\n.leaflet-default-icon-path { /* used only in path-guessing heuristic, see L.Icon.Default */\r\n\tbackground-image: url(images/marker-icon.png);\r\n\t}\r\n\r\n\r\n/* attribution and scale controls */\r\n\r\n.leaflet-container .leaflet-control-attribution {\r\n\tbackground: #fff;\r\n\tbackground: rgba(255, 255, 255, 0.8);\r\n\tmargin: 0;\r\n\t}\r\n.leaflet-control-attribution,\r\n.leaflet-control-scale-line {\r\n\tpadding: 0 5px;\r\n\tcolor: #333;\r\n\tline-height: 1.4;\r\n\t}\r\n.leaflet-control-attribution a {\r\n\ttext-decoration: none;\r\n\t}\r\n.leaflet-control-attribution a:hover,\r\n.leaflet-control-attribution a:focus {\r\n\ttext-decoration: underline;\r\n\t}\r\n.leaflet-attribution-flag {\r\n\tdisplay: inline !important;\r\n\tvertical-align: baseline !important;\r\n\twidth: 1em;\r\n\theight: 0.6669em;\r\n\t}\r\n.leaflet-left .leaflet-control-scale {\r\n\tmargin-left: 5px;\r\n\t}\r\n.leaflet-bottom .leaflet-control-scale {\r\n\tmargin-bottom: 5px;\r\n\t}\r\n.leaflet-control-scale-line {\r\n\tborder: 2px solid #777;\r\n\tborder-top: none;\r\n\tline-height: 1.1;\r\n\tpadding: 2px 5px 1px;\r\n\twhite-space: nowrap;\r\n\t-moz-box-sizing: border-box;\r\n\t     box-sizing: border-box;\r\n\tbackground: rgba(255, 255, 255, 0.8);\r\n\ttext-shadow: 1px 1px #fff;\r\n\t}\r\n.leaflet-control-scale-line:not(:first-child) {\r\n\tborder-top: 2px solid #777;\r\n\tborder-bottom: none;\r\n\tmargin-top: -2px;\r\n\t}\r\n.leaflet-control-scale-line:not(:first-child):not(:last-child) {\r\n\tborder-bottom: 2px solid #777;\r\n\t}\r\n\r\n.leaflet-touch .leaflet-control-attribution,\r\n.leaflet-touch .leaflet-control-layers,\r\n.leaflet-touch .leaflet-bar {\r\n\tbox-shadow: none;\r\n\t}\r\n.leaflet-touch .leaflet-control-layers,\r\n.leaflet-touch .leaflet-bar {\r\n\tborder: 2px solid rgba(0,0,0,0.2);\r\n\tbackground-clip: padding-box;\r\n\t}\r\n\r\n\r\n/* popup */\r\n\r\n.leaflet-popup {\r\n\tposition: absolute;\r\n\ttext-align: center;\r\n\tmargin-bottom: 20px;\r\n\t}\r\n.leaflet-popup-content-wrapper {\r\n\tpadding: 1px;\r\n\ttext-align: left;\r\n\tborder-radius: 12px;\r\n\t}\r\n.leaflet-popup-content {\r\n\tmargin: 13px 24px 13px 20px;\r\n\tline-height: 1.3;\r\n\tfont-size: 13px;\r\n\tfont-size: 1.08333em;\r\n\tmin-height: 1px;\r\n\t}\r\n.leaflet-popup-content p {\r\n\tmargin: 17px 0;\r\n\tmargin: 1.3em 0;\r\n\t}\r\n.leaflet-popup-tip-container {\r\n\twidth: 40px;\r\n\theight: 20px;\r\n\tposition: absolute;\r\n\tleft: 50%;\r\n\tmargin-top: -1px;\r\n\tmargin-left: -20px;\r\n\toverflow: hidden;\r\n\tpointer-events: none;\r\n\t}\r\n.leaflet-popup-tip {\r\n\twidth: 17px;\r\n\theight: 17px;\r\n\tpadding: 1px;\r\n\r\n\tmargin: -10px auto 0;\r\n\tpointer-events: auto;\r\n\r\n\t-webkit-transform: rotate(45deg);\r\n\t   -moz-transform: rotate(45deg);\r\n\t    -ms-transform: rotate(45deg);\r\n\t        transform: rotate(45deg);\r\n\t}\r\n.leaflet-popup-content-wrapper,\r\n.leaflet-popup-tip {\r\n\tbackground: white;\r\n\tcolor: #333;\r\n\tbox-shadow: 0 3px 14px rgba(0,0,0,0.4);\r\n\t}\r\n.leaflet-container a.leaflet-popup-close-button {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tright: 0;\r\n\tborder: none;\r\n\ttext-align: center;\r\n\twidth: 24px;\r\n\theight: 24px;\r\n\tfont: 16px/24px Tahoma, Verdana, sans-serif;\r\n\tcolor: #757575;\r\n\ttext-decoration: none;\r\n\tbackground: transparent;\r\n\t}\r\n.leaflet-container a.leaflet-popup-close-button:hover,\r\n.leaflet-container a.leaflet-popup-close-button:focus {\r\n\tcolor: #585858;\r\n\t}\r\n.leaflet-popup-scrolled {\r\n\toverflow: auto;\r\n\t}\r\n\r\n.leaflet-oldie .leaflet-popup-content-wrapper {\r\n\t-ms-zoom: 1;\r\n\t}\r\n.leaflet-oldie .leaflet-popup-tip {\r\n\twidth: 24px;\r\n\tmargin: 0 auto;\r\n\r\n\t-ms-filter: \"progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678)\";\r\n\tfilter: progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678);\r\n\t}\r\n\r\n.leaflet-oldie .leaflet-control-zoom,\r\n.leaflet-oldie .leaflet-control-layers,\r\n.leaflet-oldie .leaflet-popup-content-wrapper,\r\n.leaflet-oldie .leaflet-popup-tip {\r\n\tborder: 1px solid #999;\r\n\t}\r\n\r\n\r\n/* div icon */\r\n\r\n.leaflet-div-icon {\r\n\tbackground: #fff;\r\n\tborder: 1px solid #666;\r\n\t}\r\n\r\n\r\n/* Tooltip */\r\n/* Base styles for the element that has a tooltip */\r\n.leaflet-tooltip {\r\n\tposition: absolute;\r\n\tpadding: 6px;\r\n\tbackground-color: #fff;\r\n\tborder: 1px solid #fff;\r\n\tborder-radius: 3px;\r\n\tcolor: #222;\r\n\twhite-space: nowrap;\r\n\t-webkit-user-select: none;\r\n\t-moz-user-select: none;\r\n\t-ms-user-select: none;\r\n\tuser-select: none;\r\n\tpointer-events: none;\r\n\tbox-shadow: 0 1px 3px rgba(0,0,0,0.4);\r\n\t}\r\n.leaflet-tooltip.leaflet-interactive {\r\n\tcursor: pointer;\r\n\tpointer-events: auto;\r\n\t}\r\n.leaflet-tooltip-top:before,\r\n.leaflet-tooltip-bottom:before,\r\n.leaflet-tooltip-left:before,\r\n.leaflet-tooltip-right:before {\r\n\tposition: absolute;\r\n\tpointer-events: none;\r\n\tborder: 6px solid transparent;\r\n\tbackground: transparent;\r\n\tcontent: \"\";\r\n\t}\r\n\r\n/* Directions */\r\n\r\n.leaflet-tooltip-bottom {\r\n\tmargin-top: 6px;\r\n}\r\n.leaflet-tooltip-top {\r\n\tmargin-top: -6px;\r\n}\r\n.leaflet-tooltip-bottom:before,\r\n.leaflet-tooltip-top:before {\r\n\tleft: 50%;\r\n\tmargin-left: -6px;\r\n\t}\r\n.leaflet-tooltip-top:before {\r\n\tbottom: 0;\r\n\tmargin-bottom: -12px;\r\n\tborder-top-color: #fff;\r\n\t}\r\n.leaflet-tooltip-bottom:before {\r\n\ttop: 0;\r\n\tmargin-top: -12px;\r\n\tmargin-left: -6px;\r\n\tborder-bottom-color: #fff;\r\n\t}\r\n.leaflet-tooltip-left {\r\n\tmargin-left: -6px;\r\n}\r\n.leaflet-tooltip-right {\r\n\tmargin-left: 6px;\r\n}\r\n.leaflet-tooltip-left:before,\r\n.leaflet-tooltip-right:before {\r\n\ttop: 50%;\r\n\tmargin-top: -6px;\r\n\t}\r\n.leaflet-tooltip-left:before {\r\n\tright: 0;\r\n\tmargin-right: -12px;\r\n\tborder-left-color: #fff;\r\n\t}\r\n.leaflet-tooltip-right:before {\r\n\tleft: 0;\r\n\tmargin-left: -12px;\r\n\tborder-right-color: #fff;\r\n\t}\r\n\r\n/* Printing */\r\n\r\n@media print {\r\n\t/* Prevent printers from removing background-images of controls. */\r\n\t.leaflet-control {\r\n\t\t-webkit-print-color-adjust: exact;\r\n\t\tprint-color-adjust: exact;\r\n\t\t}\r\n\t}\r\n"], "mappings": ";AAMA;AAOI,uBAAA;AACA;IAAA,CAAA;IAAA,EAAA;IAAA;AACA,gCAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,6BAAA;AACA,4BAAA;AALA,yBAAA;AACA;IAAA,CAAA;IAAA,EAAA;IAAA;AACA,kCAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,+BAAA;AACA,8BAAA;AALA,wBAAA;AACA;IAAA,EAAA;IAAA,EAAA;IAAA;AACA,iCAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,8BAAA;AACA,6BAAA;AALA,uBAAA;AACA;IAAA,EAAA;IAAA,GAAA;IAAA;AACA,gCAAA;AACA;IAAA,CAAA;IAAA,CAAA;IAAA;AACA,6BAAA;AACA,4BAAA;AALA,uBAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,gCAAA;AACA;IAAA,CAAA;IAAA,CAAA;IAAA;AACA,6BAAA;AACA,4BAAA;AALA,sBAAA;AACA;IAAA,GAAA;IAAA,CAAA;IAAA;AACA,+BAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,4BAAA;AACA,2BAAA;AALA,qBAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,8BAAA;AACA;IAAA,CAAA;IAAA,CAAA;IAAA;AACA,2BAAA;AACA,0BAAA;AALA,sBAAA;AACA;IAAA,EAAA;IAAA,GAAA;IAAA;AACA,+BAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,4BAAA;AACA,2BAAA;AALA,oBAAA;AACA;IAAA,EAAA;IAAA,EAAA;IAAA;AACA,6BAAA;AACA;IAAA,GAAA;IAAA,GAAA;IAAA;AACA,0BAAA;AACA,yBAAA;AAAA;AAOJ,IAAA,CAAA;AACE;IAAA,aAAA;IAAA,kBAAA;IAAA,gBAAA;IAAA,QAAA;IAAA;AAAA;AAEF,IAAA,CAAA;AACE;IAAA,QAAA;IAAA,gBAAA;IAAA;AAAA;AAGF;AACE,sBAAA;AACA,qBAAA,IAAA;AAAA;AAGF;AACE,cAAA,IAAA;AACA,SAAA,IAAA;AAAA;AAGF,IAAA,CAAA;AACE,YAAA;AAAA;AAYF,IAAA,CA9BA,IA8BA,SAAA,CAAA,WAAA,WAAA,WAAA;AAAA,IAAA,CA9BA,IA8BA,SAAA,CAAA,YAAA,WAAA,WAAA;AAAA,IAAA,CA9BA,IA8BA,UAAA,WAAA,WAAA;AAGE,eC/B+B;AAAA;ADuCjC,IAAA,CAzCA,IAyCA,SAAA,CAXA,WAWA,WAAA,WAAA;AAAA,IAAA,CAzCA,IAyCA,SAAA,CAXA,YAWA,WAAA,WAAA;AAEE,kBCtC+B;AAAA;AD8CjC,IAAA,CAnDA,IAmDA,UAAA;AACE,iBAAA,KAAA,IAAA,uBAAA,EAAA;AACA,gBAAA,KAAA,IAAA,sBAAA,EAAA;AAAA;AAOF,OAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AACE,MAAA,CA7DF,IA6DE,SAAA,CA/BF,UA+BE;AACE,wBAAA;AAAA;AAAA;AA+BJ,SAAA,CAAA,aAAA,CAAA,WAAA,EAAA,SAAA,CAAA;AACE,sBAAA;AACA,gBAAA;AAAA;AAaF,IAAA,CA5GA,IA4GA,SAAA,CA9EA,WA8EA,CAAA;AACE,0BAAA,IAAA;AAAA;AAyBA,CAAA;AATA,oBAAA,IAAA,mBAAA,EAAA;AACA,wBAAA,IAAA,uBAAA,EAAA,CAAA,EAAA,EAAA,EAAA;AACA,wBAAA,IAAA,4BAAA,EAAA;AACA,4BAAA,IAAA,gCAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,qBAAA,IAAA,yBAAA,EAAA;AACA,oBAAA,IAAA,wBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,qBAAA,EAAA;AACA,wBAAA,IAAA,yBAAA,EAAA,CAAA,EAAA,EAAA,EAAA;AACA,wBAAA,IAAA,8BAAA,EAAA;AACA,4BAAA,IAAA,kCAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,qBAAA,IAAA,2BAAA,EAAA;AACA,oBAAA,IAAA,0BAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,oBAAA,EAAA;AACA,wBAAA,IAAA,wBAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AACA,wBAAA,IAAA,6BAAA,EAAA;AACA,4BAAA,IAAA,iCAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,qBAAA,IAAA,0BAAA,EAAA;AACA,oBAAA,IAAA,yBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,mBAAA,EAAA;AACA,wBAAA,IAAA,uBAAA,EAAA,EAAA,EAAA,GAAA,EAAA;AACA,wBAAA,IAAA,4BAAA,EAAA;AACA,4BAAA,IAAA,gCAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,qBAAA,IAAA,yBAAA,EAAA;AACA,oBAAA,IAAA,wBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,mBAAA,EAAA;AACA,wBAAA,IAAA,uBAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,wBAAA,IAAA,4BAAA,EAAA;AACA,4BAAA,IAAA,gCAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,qBAAA,IAAA,yBAAA,EAAA;AACA,oBAAA,IAAA,wBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,kBAAA,EAAA;AACA,wBAAA,IAAA,sBAAA,EAAA,GAAA,EAAA,CAAA,EAAA;AACA,wBAAA,IAAA,2BAAA,EAAA;AACA,4BAAA,IAAA,+BAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,qBAAA,IAAA,wBAAA,EAAA;AACA,oBAAA,IAAA,uBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,iBAAA,EAAA;AACA,wBAAA,IAAA,qBAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,wBAAA,IAAA,0BAAA,EAAA;AACA,4BAAA,IAAA,8BAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,qBAAA,IAAA,uBAAA,EAAA;AACA,oBAAA,IAAA,sBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,kBAAA,EAAA;AACA,wBAAA,IAAA,sBAAA,EAAA,EAAA,EAAA,GAAA,EAAA;AACA,wBAAA,IAAA,2BAAA,EAAA;AACA,4BAAA,IAAA,+BAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,qBAAA,IAAA,wBAAA,EAAA;AACA,oBAAA,IAAA,uBAAA,EAAA;AAAA;AAIA,CAAA;AATA,oBAAA,IAAA,gBAAA,EAAA;AACA,wBAAA,IAAA,oBAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AACA,wBAAA,IAAA,yBAAA,EAAA;AACA,4BAAA,IAAA,6BAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,qBAAA,IAAA,sBAAA,EAAA;AACA,oBAAA,IAAA,qBAAA,EAAA;AAAA;AAaF,CAnCA;AEwQM,QFpOuB;AEqOvB,SFrOiB;AEyPrB,OFzPkB;AE0PlB,UF1PwB;AAExB,WAAA;AACA,YAAA;AAEA,kBAAA;AACA,mBAAA;AAEA,WAAA,OAAA,KAAA;AACA,WGzH+B;AAAA;AHoIjC,UAAA,EAAA,CAxDA;AAyDE,YAAA;AAEA,WAAA,OAAA;AAEA,UAAA;AAAA;AAGF,CAAA,mBAAA,EAAA,CAhEA,QAgEA,CAAA;AACE,YAAA;AAAA;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAAA;AAeE,WAAA;AAAA;AAGF,CAAA;AACE,WAAA;AAAA;AAGF,CAAA,YAAA,EAAA,WAAA;AACE,WAAA;AAAA;AAOF,IAAA,CAAA,OAAA,CAAA;AAAA,IAAA,CAAA,OAAA,CAAA;AACE,2BAAA;AAAA;AAGF,UAAA,CAAA,WAAA,EAAA;AACE;AACE,yBAAA,IAAA;AAAA;AAAA;AAIJ,UAAA,CAAA,WAAA,EAAA,IAAA;AACE;AACE,yBAAA,IAAA;AACA,4BAAA,IAAA;AACA,0BAAA,IAAA;AACA,2BAAA,IAAA;AAAA;AAAA;AAQJ,QAAA,CAAA,UAAA,CAAA;AAAA,eAAA,CAAA,UAAA,CAAA;AAEE,SAAA;AAAA;AAOF,CAAA;AE6TM,aAAA,YAAA,CAAA,EAAA,CAAA,EAAA;AAAA;AFzTN,CAAA;AACE,UAAA;AACA,gBAAA;AAUA,kBAAA;AAMA,cAAA;AAAA;AAUF,CA5BA,kBA4BA;AACE,cAAA;AAAA;AAGF,CAhCA,kBAgCA,CAAA;AACE,YAAA;AAAA;AAGF,CAtRA,IAsRA,CAAA;AACE,cIvS+B,KAAA,EAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAAA;AJ0SjC,CAAA,QAAA,CA1RA,IA0RA,CAJA;AAKE,cIxS+B,IAAA,EAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAAA;AJ+SjC,CA/RA,GA+RA,CAZA;AAaE,cKtT8B,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAAA;ALyThC,CAnSA,GAmSA,CAAA;AACE,cK1T8B,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AAAA;AL8ThC,mBAAA,CAAA,6BAAA,EAAA,aAAA;AACE,0BAAA;AACA,2BAAA;AAAA;AAEF,mBAAA,CAJA,6BAIA,EAAA,aAAA;AACE,6BAAA;AACA,8BAAA;AAAA;AAEF,oBAAA,EAAA,aAAA,cAAA,QAAA,CAAA;AACE,kBAAA;AAAA;AAGF,aAAA,CAAA,mBAAA,EAAA,CAAA,aAAA,CAAA;AACE,cAAA,MAAA,UAAA,aAAA,IAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AAAA;AAGF,OAAA,CAAA,sBAAA,EAAA;AACE,gBAAA,CALF;AAOI,gBAAA;AAAA;AAAA;AASJ,aAAA,CAAA,oBAAA,EAAA,CAAA,aAAA,CAhBA;AAgBA,aAAA,CAAA,mBAAA,EAAA,CAAA,aAAA,CAhBA;AAkBE,aAAA,OAAA;AAAA;AAGF,mBAAA,CAjCA,4BAiCA,CAzUA,GAyUA,EAAA,aAAA,CAAA,mBAAA,QAAA,CAAA;AACE,kBAAA;AACA,wBAAA;AAAA;AAGF,mBAAA,CAtCA,4BAsCA,CA9UA,GA8UA,EAAA,aAAA,CAVA,mBAUA;AAAA,mBAAA,CAtCA,4BAsCA,CA9UA,GA8UA,EAAA,aAAA,CAVA,kBAUA;AAEE,cAAA;AAAA;AAMF,UAAA,KAAA;AACE,cAAA;AAAA;AASF,CAAA;AACE,WAAA;AACA,YAAA;AAAA;AAUF,CAZA,4BAYA,YAAA,CAAA;AACE,SAAA;AACA,cAAA;AAAA;AAUF,CAAA;AAAA,MAAA,CAAA,cAAA;AAAA,CAAA,YAAA,CAAA,cAAA;AAAA,IAAA,CAAA,cAAA;AAAA,CAAA,UAAA,CAAA,cAAA;AAAA,EAAA,CAAA,cAAA;AAAA,CAAA,aAAA,CAAA,eAAA,CAAA,cAAA;AAOE,WAAA;AAAA;AAaF,CAAA,gBAAA,KAAA,EAAA;AACE,YAAA;AAAA;AAUF,UAAA,IAAA,SAAA,CAAA,IAAA,EAAA;AACE,GAZF;AAaI,cAAA;AAAA;AAAA;;;AM9aJ;AAAA;AAAA;AAAA;AAIE,kBAAA;AAAA;AAKF,KAAA,KAAA,CAAA;AACE,WAAA;AAEA,UAAA;AAAA;AAQF;AAAA;AAEE,eAAA;AAAA;AAOF;AACE,aAAA;AAAA;AAMF;AACE,UAAA;AAEA,gBAAA;AAEA,cAAA;AAAA;AAIF;AACE,YAAA;AAAA;AAIF;AAAA;AAAA;AAAA;AAIE,eAAA,SAAA,EAAA;AACA,aAAA;AAAA;AAgBF;AAAA;AAAA;AAAA;AAIE,eAAA;AACA,eAAA;AAAA;AAGF;AACE,YAAA;AAEA,UAAA;AAEA,QAAA;AACA,SAAA;AAAA;AAGF,QAAA;AACE,gBAAA;AAAA;AAGF;AAAA;AAAA;AAAA;AAIE,UAAA;AAEA,QAAA;AACA,SAAA;AAAA;AAQF,KAAA,KAAA,CAAA;AAAA,KAAA,CAAA;AAAA,KAAA,CAAA;AAGE,UAAA;AAEA,sBAAA;AAAA;AAIF;AAAA,EAAA;AAAA,EAAA;AAAA,EAAA;AAAA,EAAA;AAAA;AAAA,OAAA;AAAA,OAAA;AAAA,OAAA;AAAA,OAAA;AAAA,CAAA;AAAA,CAAA;AAAA,CAAA,UAAA;AAAA,CAAA,UAAA;AAAA,CAAA,UAAA;AAAA,CAAA,UAAA;AAAA;AAAA;AAkBE,gBAAA;AAAA;AAGF,EAAA;AAAA,OAAA;AAEE,kBAAA;AAAA;AAGF;AACE,WAAA;AACA,UAAA;AACA,iBAAA;AACA,eAAA;AACA,cAAA;AACA,gBAAA;AACA,eAAA;AACA,kBAAA;AACA,UAAA;AAEA,sBAAA;AAAA;AAGF,CAAA;AACE,UAAA;AAAA;AAIF,CAAA,CAAA;AAAA,MAAA,CAAA;AAAA,KAAA,KAAA,CAAA;AAGE,UAAA;AAAA;AAIF,MAAA;AAAA,KAAA;AAEE,WAAA;AAEA,UAAA;AAAA;AAMF,KAAA,CAAA,YAAA;AAAA,KAAA,CAAA,YAAA;AAEE,UAAA;AAAA;AAMF,KAAA,CAAA,YAAA;AAAA,KAAA,CAAA,YAAA;AAEE,sBAAA;AAAA;AAQF;AACE,mBAAA;AACA,kBAAA;AAAA;AAGF;AAAA;AAEE,WAAA;AAAA;;;AC1MF;AACE,cAAA;AAEA,+BAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,+BAAA;AACA,yBAAA;AAAA;AAGF;AACE,SAAA;AACA,UAAA;AACA,4BAAA;AAEA,oBAAA;AAAA;AAGF,IAAA,KAAA,CAAA,UAAA;AACE,WAAA;AAAA;AAGF,IAAA,CAAA,OAAA;AACE,WAAA;AAAA;AAGF,IAAA,CAAA;AACE,UAAA;AAAA;AAGF;AC0EE,2BAAA;AACA,0BAAA;AA0NE,eDnSc;ACoSd,gBDpSc;ACwThB,cDxTgB;ACyThB,iBDzTgB;ACmSd,gBDlSe;ACmSf,iBDnSe;ACuTjB,eDvTiB;ACwTjB,kBDxTiB;AAEjB,YAAA;AAEA,SAAA;AACA,aAAA;AACA,UAAA;AACA,cAAA;AAsBA,aAAA,WAAA;AAEA,kBAAA;AAEA,YAAA;AAEA,gBAAA;AAEA,qBAAA;AAEA,uBAAA;AAEA,aAAA;AAEA,yBAAA;AACA,4BAAA;AAEA,oBAAA;AAAA;;;AEvDF;AACE,eAAA,IAAA;AAAA;AAOF,UAAA,CAAA,qBAAA,EAAA;AACE;AAIE,UAAA,IAAA,kBAAA,EAAA,KAAA,IAAA;AAAA;AAAA;AAIJ;AACE,oBAAA;AACA,SAAA,IAAA,mBAAA,EAAA;AAAA;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AC0SE,cDpSgB;ACqShB,iBDrS4B;AAE5B,eArD6B;AAuD7B,eApD6B;AAAA;AAuD/B;AC6RE,cD5RgB;AAEhB,aAvD6B;AAAA;AA0D/B;ACuRE,cDtRgB;AAEhB,aA1D6B;AAAA;AA6D/B;AACE,aA3D6B;AAAA;AA8D/B;AACE,aA5D6B;AAAA;AA+D/B;AACE,aA7D6B;AAAA;AAgE/B;AACE,aA9D6B;AAAA;AAiE/B;AACE,aAAA;AAAA;AAGF;AAAA;AAEE,YAAA;AAEA,aAAA;AAEA,eAAA;AAEA,kBAAA;AAAA;AAGF;AACE,OAAA;AAAA;AAGF;AACE,UAAA;AAAA;;;AE1GF,CAAA;AACE,WAAA;AAAA;AAUE,CAAA;AACE,WAAA;AAAA;AAOF,CAAA;AACE,WAAA;AAAA;ACsIF,OAAA,CAAA,SAAA,EAAA;AD/IA,GAAA;AACE,aAAA;AAAA;AAAA;ACuLF,OAAA,CAAA,SAAA,EAAA;ADhLA,GAAA;AACE,aAAA;AAAA;AAAA;ACsIF,OAAA,CAAA,SAAA,EAAA;AD/IA,GAAA;AACE,aAAA;AAAA;AAAA;ACuLF,OAAA,CAAA,SAAA,EAAA;ADhLA,GAAA;AACE,aAAA;AAAA;AAAA;ACsIF,OAAA,CAAA,SAAA,EAAA;AD/IA,GAAA;AACE,aAAA;AAAA;AAAA;ACuLF,OAAA,CAAA,SAAA,EAAA;ADhLA,GAAA;AACE,aAAA;AAAA;AAAA;ACsIF,OAAA,CAAA,SAAA,EAAA;AD/IA,GAAA;AACE,aAAA;AAAA;AAAA;ACuLF,OAAA,CAAA,SAAA,EAAA;ADhLA,GAAA;AACE,aAAA;AAAA;AAAA;;;AEZN,CAAA;AACE,mBAAA;AACA,iBAAA;AACA,iBAAA;AACA,oBAAA;ACsTE,gBDpTe;ACqTf,iBDrTe;ACyUjB,eDzUiB;AC0UjB,kBD1UiB;AAAA;AAGnB,CAAA;AACE,mBAAA,IAAA,aAAA,EAAA;AACA,iBAAA,IAAA,aAAA,EAAA;AACA,iBAAA,IAAA,aAAA,EAAA;AACA,oBAAA,IAAA,aAAA,EAAA;ACiTE,yBDpUM,IAAA,aAAA,EAAA;ACqUN,wBDrUM,IAAA,aAAA,EAAA;ACsUN,uBDtUM,IAAA,aAAA,EAAA;ACuUN,sBDvUM,IAAA,aAAA,EAAA;ACqVR,eDrVQ,IAAA,aAAA,EAAA;ACsVR,kBDtVQ,IAAA,aAAA,EAAA;AAAA;AAwBV,CAAA;AACE,iBAAA,IAAA,aAAA,EAAA;AC4TA,eDrVQ,IAAA,aAAA,EAAA;AAAA;AA8BV,CAAA;AACE,mBAAA,IAAA,aAAA,EAAA;ACqSE,yBDpUM,IAAA,aAAA,EAAA;ACqUN,wBDrUM,IAAA,aAAA,EAAA;AAAA;AAoCV,CAAA;AACE,iBAAA,IAAA,aAAA,EAAA;ACiSE,uBDtUM,IAAA,aAAA,EAAA;ACuUN,sBDvUM,IAAA,aAAA,EAAA;AAAA;AA0CV,CAAA;AACE,oBAAA,IAAA,aAAA,EAAA;AC2SA,kBDtVQ,IAAA,aAAA,EAAA;AAAA;AAgDV,CAAA;AACE,iBAAA,IAAA,aAAA,EAAA;AACA,oBAAA,IAAA,aAAA,EAAA;ACmSA,eDrVQ,IAAA,aAAA,EAAA;ACsVR,kBDtVQ,IAAA,aAAA,EAAA;AAAA;AAuDV,CAAA;AACE,mBAAA,IAAA,aAAA,EAAA;AACA,iBAAA,IAAA,aAAA,EAAA;AC2QE,yBDpUM,IAAA,aAAA,EAAA;ACqUN,wBDrUM,IAAA,aAAA,EAAA;ACsUN,uBDtUM,IAAA,aAAA,EAAA;ACuUN,sBDvUM,IAAA,aAAA,EAAA;AAAA;AAkEV,CAAA;AACE,kBAAA;AACA,gBAAA;AACA,gBAAA;AACA,mBAAA;AC0PE,eDxPc;ACyPd,gBDzPc;AC6QhB,cD7QgB;AC8QhB,iBD9QgB;AAAA;AAGlB,CAAA;AACE,kBAAA,IAAA,YAAA,EAAA;AACA,gBAAA,IAAA,YAAA,EAAA;AACA,gBAAA,IAAA,YAAA,EAAA;AACA,mBAAA,IAAA,YAAA,EAAA;ACqPE,wBDnUK,IAAA,YAAA,EAAA;ACoUL,uBDpUK,IAAA,YAAA,EAAA;ACqUL,sBDrUK,IAAA,YAAA,EAAA;ACsUL,qBDtUK,IAAA,YAAA,EAAA;ACoVP,cDpVO,IAAA,YAAA,EAAA;ACqVP,iBDrVO,IAAA,YAAA,EAAA;AAAA;AAmFT,CAAA;AACE,gBAAA,IAAA,YAAA,EAAA;ACgQA,cDpVO,IAAA,YAAA,EAAA;AAAA;AAyFT,CAAA;AACE,kBAAA,IAAA,YAAA,EAAA;ACyOE,wBDnUK,IAAA,YAAA,EAAA;ACoUL,uBDpUK,IAAA,YAAA,EAAA;AAAA;AA+FT,CAAA;AACE,gBAAA,IAAA,YAAA,EAAA;ACqOE,sBDrUK,IAAA,YAAA,EAAA;ACsUL,qBDtUK,IAAA,YAAA,EAAA;AAAA;AAqGT,CAAA;AACE,mBAAA,IAAA,YAAA,EAAA;AC+OA,iBDrVO,IAAA,YAAA,EAAA;AAAA;AA2GT,CAAA;AACE,gBAAA,IAAA,YAAA,EAAA;AACA,mBAAA,IAAA,YAAA,EAAA;ACuOA,cDpVO,IAAA,YAAA,EAAA;ACqVP,iBDrVO,IAAA,YAAA,EAAA;AAAA;AAkHT,CAAA;AACE,kBAAA,IAAA,YAAA,EAAA;AACA,gBAAA,IAAA,YAAA,EAAA;AC+ME,wBDnUK,IAAA,YAAA,EAAA;ACoUL,uBDpUK,IAAA,YAAA,EAAA;ACqUL,sBDrUK,IAAA,YAAA,EAAA;ACsUL,qBDtUK,IAAA,YAAA,EAAA;AAAA;;;AEGL,CAAA;AC2dE,SAAA;AAAA;ADvdF,CAAA;ACudE,SAAA;AAAA;ADndF,CAAA;ACqcE,SAAA;AAAA;AAzNO,cAAA,CAAA,GAAA,CAAA,MAAA,CD5OT;ACwcE,SAAA;AAAA;AArNO,CAAA,SAAA,CDnPT;ACwcE,SAAA;AAAA;AA/MJ,UAAA,SAAA,CAAA,IAAA;AAcW,GDvQT,eCuQS,KAAA;AAiMP,WAAA;AAAA;AAAA;ADpcF,CAAA;ACwcE,SAAA;AAAA;AAhOO,cAAA,CAAA,GAAA,CAAA,MAAA,CDxOT;AC2cE,SAAA;AAAA;AA5NO,CAAA,SAAA,CD/OT;AC2cE,SAAA;AAAA;AAtNJ,UAAA,SAAA,CAAA,IAAA;AAcW,GDnQT,aCmQS,KAAA;AAwMP,WAAA;AAAA;AAAA;AAnUF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AC2dE,WAAA;AAAA;ADvdF,GAAA;ACudE,WAAA;AAAA;ADndF,GAAA;ACqcE,WAAA;AAAA;AAzNO,gBAAA,CAAA,GAAA,CAAA,MAAA,CD5OT;ACwcE,WAAA;AAAA;AArNO,GAAA,SAAA,CDnPT;ACwcE,WAAA;AAAA;AA/MJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDvQT,kBCuQS,KAAA;AAiMP,aAAA;AAAA;AAAA;ADpcF,GAAA;ACwcE,WAAA;AAAA;AAhOO,gBAAA,CAAA,GAAA,CAAA,MAAA,CDxOT;AC2cE,WAAA;AAAA;AA5NO,GAAA,SAAA,CD/OT;AC2cE,WAAA;AAAA;AAtNJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDnQT,gBCmQS,KAAA;AAwMP,aAAA;AAAA;AAAA;AAAA;AAnUF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AC2dE,WAAA;AAAA;ADvdF,GAAA;ACudE,WAAA;AAAA;ADndF,GAAA;ACqcE,WAAA;AAAA;AAzNO,gBAAA,CAAA,GAAA,CAAA,MAAA,CD5OT;ACwcE,WAAA;AAAA;AArNO,GAAA,SAAA,CDnPT;ACwcE,WAAA;AAAA;AA/MJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDvQT,kBCuQS,KAAA;AAiMP,aAAA;AAAA;AAAA;ADpcF,GAAA;ACwcE,WAAA;AAAA;AAhOO,gBAAA,CAAA,GAAA,CAAA,MAAA,CDxOT;AC2cE,WAAA;AAAA;AA5NO,GAAA,SAAA,CD/OT;AC2cE,WAAA;AAAA;AAtNJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDnQT,gBCmQS,KAAA;AAwMP,aAAA;AAAA;AAAA;AAAA;AAnUF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AC2dE,WAAA;AAAA;ADvdF,GAAA;ACudE,WAAA;AAAA;ADndF,GAAA;ACqcE,WAAA;AAAA;AAzNO,gBAAA,CAAA,GAAA,CAAA,MAAA,CD5OT;ACwcE,WAAA;AAAA;AArNO,GAAA,SAAA,CDnPT;ACwcE,WAAA;AAAA;AA/MJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDvQT,kBCuQS,KAAA;AAiMP,aAAA;AAAA;AAAA;ADpcF,GAAA;ACwcE,WAAA;AAAA;AAhOO,gBAAA,CAAA,GAAA,CAAA,MAAA,CDxOT;AC2cE,WAAA;AAAA;AA5NO,GAAA,SAAA,CD/OT;AC2cE,WAAA;AAAA;AAtNJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDnQT,gBCmQS,KAAA;AAwMP,aAAA;AAAA;AAAA;AAAA;AAnUF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AC2dE,WAAA;AAAA;ADvdF,GAAA;ACudE,WAAA;AAAA;ADndF,GAAA;ACqcE,WAAA;AAAA;AAzNO,gBAAA,CAAA,GAAA,CAAA,MAAA,CD5OT;ACwcE,WAAA;AAAA;AArNO,GAAA,SAAA,CDnPT;ACwcE,WAAA;AAAA;AA/MJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDvQT,kBCuQS,KAAA;AAiMP,aAAA;AAAA;AAAA;ADpcF,GAAA;ACwcE,WAAA;AAAA;AAhOO,gBAAA,CAAA,GAAA,CAAA,MAAA,CDxOT;AC2cE,WAAA;AAAA;AA5NO,GAAA,SAAA,CD/OT;AC2cE,WAAA;AAAA;AAtNJ,YAAA,SAAA,CAAA,IAAA;AAcW,KDnQT,gBCmQS,KAAA;AAwMP,aAAA;AAAA;AAAA;AAAA;;;ACvdF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,eAAA;AAAA;AAGF,CAAA;AACE,eAAA;AAAA;ACuHF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAAA;ACuHF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAAA;ACuHF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAAA;ACuHF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,gBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAGF,GAAA;AACE,iBAAA;AAAA;AAAA;;;AE7BF,CAAA;AAEE,kBAAA;AAAA;AAGF,CAAA;AAEE,kBAAA;AAAA;AAGF,CAAA;AAEE,kBAAA;AAAA;ACwIF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAAA;ACwIF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAAA;ACwIF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAAA;ACwIF,OAAA,CAAA,SAAA,EAAA;ADpJA,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAGF,GAAA;AAEE,oBAAA;AAAA;AAAA;;;AEjBN,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAGF,CAAA;AACE,cAAA;AAAA;AAOF,CAAA;AACE,aAAA;AAAA;AAGF,CAAA;AACE,aAAA;AAAA;AAGF,CAAA;AACE,aAAA;AAAA;AAOF,CAAA;AACE,mBAAA;AAAA;AAGF,CAAA;AACE,mBAAA;AAAA;AAGF,CAAA;AACE,mBAAA;AAAA;AAGF,CAAA;AACE,mBAAA;AAAA;AAGF,CAAA;AACE,mBAAA;AAAA;AAGF,CAAA;AACE,mBAAA;AAAA;AAOF,CAAA;AACE,eAAA;AAAA;AAGF,CAAA;AACE,eAAA;AAAA;AAGF,CAAA;AACE,eAAA;AAAA;AAGF,CAAA;AACE,eAAA;AAAA;AAGF,CAAA;AACE,eAAA;AAAA;;;AC/FF,OAAA,CAAA,oBAAA,EAAA;ACqEE;AAEI,yBAAA;AACA;MAAA,EAAA;MAAA,GAAA;MAAA;AACA,kCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,+BAAA;AACA,8BAAA;AALA,2BAAA;AACA;MAAA,EAAA;MAAA,GAAA;MAAA;AACA,oCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,iCAAA;AACA,gCAAA;AALA,0BAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,mCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,gCAAA;AACA,+BAAA;AALA,yBAAA;AACA;MAAA,EAAA;MAAA,GAAA;MAAA;AACA,kCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,+BAAA;AACA,8BAAA;AALA,yBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,kCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,+BAAA;AACA,8BAAA;AALA,wBAAA;AACA;MAAA,GAAA;MAAA,EAAA;MAAA;AACA,iCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,8BAAA;AACA,6BAAA;AALA,uBAAA;AACA;MAAA,EAAA;MAAA,EAAA;MAAA;AACA,gCAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,6BAAA;AACA,4BAAA;AALA,wBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,iCAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,8BAAA;AACA,6BAAA;AALA,sBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,+BAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,4BAAA;AACA,2BAAA;AAAA;AAMJ,OAAA,CAAA;AACE,4BAAA;AACA;MAAA,CAAA;MAAA,CAAA;MAAA;AACA,sBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,oCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,8BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,2BAAA;AACA,2BAAA;AAAA;AAGF,OAAA,CA/CA,IA+CA;AACE,4BAAA,IAAA,oBAAA,EAAA,IAAA;AACA,8BAAA,IAAA,oBAAA,EAAA,IAAA;AACA,gCAAA,IAAA,oBAAA,EAAA,IAAA;AAAA;AAKF,OAAA,CAAA;AACE,4BAAA;AACA;MAAA,EAAA;MAAA,EAAA;MAAA;AACA,sBAAA;AACA;MAAA,GAAA;MAAA,GAAA;MAAA;AACA,oCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,qCAAA;AACA,8BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,+BAAA;AACA,2BAAA;AACA,8BAAA;AACA,8BAAA;AACA,2BAAA;AAAA;AAAA;;;AEvLJ,CAAA;AACE,sBAAA;;AAEA,CAHF,uBAGE,CAAA;AACE,iBAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,UAAA,IAAA;AACA,aAAA,eAAA,GAAA;;AAGF,CAVF,uBAUE,CAAA;AACE,WAAA,KAAA,KAAA,KAAA;AACA,cAAA;;AAGF,CAfF,uBAeE,CAAA;AACE,aAAA;AACA,eAAA;AACA,kBAAA;AACA,kBAAA;AACA,iBAAA;;AAGF,CAvBF,uBAuBE,CAAA;AACE,aAAA;AACA,eAAA;AACA,WAAA;AACA,iBAAA;;AAGF,CA9BF,uBA8BE,CAAA;AACE,aAAA;AACA,eAAA;AACA,WAAA,KAAA,KAAA,KAAA;AACA,cAAA;;AAGF,CArCF,uBAqCE,CAAA;AACE,WAAA,EAAA,KAAA,KAAA;AACA,WAAA;AACA,OAAA;;AAGF,CA3CF,uBA2CE,CAAA;AACE,QAAA;AACA,eAAA;AACA,iBAAA;AACA,WAAA,KAAA;AACA,aAAA;;AAGF,CAnDF,uBAmDE,CAAA;AACE,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,SAAA;AACA,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGF,CAzDF,uBAyDE,CAAA;AACE,cAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,SAAA;AACA,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAMF,CAAA,iBAAA,CA/DA;AAgEE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,gBAAA;AACA,SAAA;;AAKF,CAAA,YAAA,CAvEA;AAwEE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,gBAAA;AACA,SAAA;;AAKF,CAAA,cAAA,CA/EA;AAgFE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,gBAAA;AACA,SAAA;;AAKF,CAAA,WAAA,CAvFA;AAwFE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,gBAAA;AACA,SAAA;;AAKF,CAAA,cAAA,CA/FA;AAgGE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,gBAAA;AACA,SAAA;;AAKJ,WAnGI;AAoGF;AACE,gBAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,eAAA,MAAA;;AAEF;AACE,gBAAA,EAAA,IAAA,KAAA,KAAA,GAAA,EAAA,CAAA,EAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,KAAA,GAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,eAAA,MAAA;;AAEF;AACE,gBAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,eAAA,MAAA;;;AAKJ,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,WAAA;AACA,mBAAA;AACA,gBAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CANF,gBAME,CAAA;AACE,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGF,CAVF,gBAUE,CAAA;AACE,eAAA;AACA,aAAA;;AAGF,CAfF,gBAeE,CAAA;AACE,aAAA;AACA,eAAA;;AAGF,CApBF,gBAoBE,CAAA;AACE,WAAA;AACA,eAAA;;AAKJ,OAAA,CAAA,SAAA,EAAA;AAEI,GAvJJ,uBAuJI,CApJF;AAqJI,YAAA;AACA,eAAA,KAAA,MAAA,EAAA;;AAGF,GA5JJ,uBA4JI,CA7IF;AA8II,eAAA;;AAGF,GAhKJ,uBAgKI,CAlIF;AAmII,eAAA;;AAGF,GApKJ,uBAoKI,CAzHF;AA0HI,eAAA;AACA,aAAA,KAAA;;;AAMN,OAAA,CAAA,gBAAA,EAAA;AACE,GA7KF,uBA6KE,CA1KA;AA2KE,kBAAA;AACA,gBAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;;AAKJ,OAAA,CAAA,sBAAA,EAAA;AACE,GArLF,uBAqLE,CAlLA;AAmLE,eAAA;;AAGF,aAlLE;AAmLA;AACE,kBAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,iBAAA,MAAA;;;;ADnJN,CAAA;AACE,sBAAA;AACA,WAAA;AACA,YAAA;AACA,mBAAA;AACA,gBAAA,EAAA,KAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CARA,wBAQA,CAAA;AACE,YAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,YAAA;AACA,iBAAA;;AAGF,CAfA,wBAeA,CAAA;AACE,YAAA;AACA,WAAA;AACA,WAAA;;AAIF,CAAA,aAAA,GAAA,CAAA;AACE,cAAA;AACA,oBAAA;AACA;IAAA;MAAA,KAAA;MAAA,QAAA,GAAA;MAAA,YAAA,GAAA;MAAA,YAAA,GAAA;MAAA,QAAA,GAAA;MAAA,QAAA;IAAA;MAAA,KAAA;MAAA,QAAA,GAAA;MAAA,YAAA,GAAA;MAAA,YAAA,GAAA;MAAA,QAAA,GAAA;MAAA;AAEA,mBAAA,KAAA;AACA,uBAAA,EAAA,CAAA,EAAA,KAAA;;AAIF,CAAA;AACE,WAAA;AACA,aAAA;;AAGF,CALA,iBAKA;AACE,UAAA,EAAA,EAAA,IAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA,IAAA,MAAA;AACA,kBAAA;;AAGF,CAdA,iBAcA;AACE,UAAA,IAAA;AACA,aAAA;AACA,eAAA;;AAGF,CApBA,iBAoBA;AACE,eAAA;AACA,SAAA;;AAIF,CAAA;AACE,sBAAA;AACA,mBAAA,KAAA,KAAA,EAAA;AACA,gBAAA,EAAA,KAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAIF,CAAA;AACE,UAAA,YAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA,UAAA,KAAA;;AAGF,CALA,mBAKA;AACE,aAAA,MAAA;;AAGF,CAhBA,yBAgBA,CAlEA;AAmEE,YAAA;AACA,UAAA;AACA,iBAAA,KAAA,KAAA,EAAA;AACA,cAAA,EAAA,KAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAvBA,yBAuBA;AACE,iBAAA;AACA,oBAAA;AACA,mBAAA;AACA,iBAAA;;AAGF,CA9BA,yBA8BA;AACE,YAAA;;AAGF,CAlCA,yBAkCA,UAAA;AACE,WAAA;AACA,YAAA;AACA,OAAA;AACA,QAAA;AACA,aAAA,WAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA,IAAA;AACA,iBAAA;AACA,WAAA;;AAIF,CAAA;AACE,sBAAA;AACA,gBAAA;AACA,YAAA;;AAGF,CANA,gBAMA,CC/IE;ADgJA,oBAAA;AACA,SAAA;AACA,WAAA;AACA,cAAA;;AAGF,CAbA,gBAaA,CCjJE;ADkJA,aAAA;AACA,eAAA;AACA,SAAA;AACA,kBAAA;;AAGF,CApBA,gBAoBA,CChJE;ADiJA,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA;;AAGF,CA1BA,gBA0BA,CC/IE;ADgJA,SAAA;AACA,aAAA;AACA,WAAA;AACA,cAAA;;AAGF,CAjCA,gBAiCA,CC/IE;ADgJA,WAAA;;AAGF,CArCA,gBAqCA,CC7IE;AD8IA,SAAA;AACA,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,UAAA;;AAGF,CA5CA,gBA4CA,CCpJE,YDoJF;AACE,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAIF,CCpMA;ADqME,sBAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,gBAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CC5MA,uBD4MA,CCzME;AD0MA,iBAAA;AACA,YAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CClNA,uBDkNA,CCxME;ADyMA,WAAA,KAAA,KAAA,KAAA;AACA,cAAA;AACA,YAAA;;AAGF,CCxNA,uBDwNA,CC9ME,UD8MF;AACE,WAAA;AACA,YAAA;AACA,OAAA;AACA,QAAA;AACA,aAAA,WAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,oBAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA,IAAA,MAAA;;AAGF,CC3OA,uBD2OA,CC5NE;AD6NA,aAAA;AACA,eAAA;AACA,SAAA;AACA,kBAAA;AACA,cAAA;AACA,eAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CCpPA,uBDoPA,CC7NE;AD8NA,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA;AACA,eAAA;;AAGF,CC3PA,uBD2PA,CC7NE;AD8NA,SAAA;AACA,aAAA;AACA,WAAA,KAAA;AACA,cAAA;AACA,eAAA;AACA,oBAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CCpQA,uBDoQA,CC/NE;ADgOA,WAAA;AACA,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGF,CCzQA,uBDyQA,CC9NE;AD+NA,SAAA;AACA,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,UAAA;AACA,eAAA;AACA,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGF,CClRA,uBDkRA,CCvOE,YDuOF;AACE,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAIF,CCrNE;ADsNA,gBAAA;;AAGF,CCzNE,iBDyNF,CCjRE;ADkRA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;;AAGF,CC7NE,iBD6NF,CCrRE,UDqRF;AACE,oBAAA;;AAIF,CC1NE;AD2NA,gBAAA;;AAGF,CC9NE,YD8NF,CC9RE;AD+RA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;;AAGF,CClOE,YDkOF,CClSE,UDkSF;AACE,oBAAA;;AAIF,CC/NE;ADgOA,gBAAA;;AAGF,CCnOE,cDmOF,CC3SE;AD4SA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;;AAGF,CCvOE,cDuOF,CC/SE,UD+SF;AACE,oBAAA;;AAIF,CCpOE;ADqOA,gBAAA;;AAGF,CCxOE,WDwOF,CCxTE;ADyTA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;;AAGF,CC5OE,WD4OF,CC5TE,UD4TF;AACE,oBAAA;;AAIF,CCzOE;AD0OA,gBAAA;;AAGF,CC7OE,cD6OF,CCrUE;ADsUA;IAAA;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;;AAGF,CCjPE,cDiPF,CCzUE,UDyUF;AACE,oBAAA;;;;;;AEpVF,CAAC;AACD,CAAC;AACD,CAAC;AACD,CAAC;AACD,CAAC;AACD,CALC,aAKa,EAAE;AAChB,CANC,aAMa,EAAE;AAChB,CAAC;AACD,CAAC;AACD,CAAC;AACA,YAAU;AACV,QAAM;AACN,OAAK;AACL;AACD,CAAC;AACA,YAAU;AACV;AACD,CAhBC;AAiBD,CAhBC;AAiBD,CAhBC;AAiBA,uBAAqB;AAClB,oBAAkB;AACb,eAAa;AACnB,qBAAmB;AACrB;AAED,CAzBC,YAyBY;AACZ,cAAY;AACb;AAEA,CAAC,eAAe,CA7Bf;AA8BA,mBAAiB;AACjB;AAED,CAJC,eAIe,CA9Bf;AA+BA,SAAO;AACP,UAAQ;AACR,4BAA0B,EAAE;AAC5B;AACD,CArCC;AAsCD,CArCC;AAsCA,WAAS;AACT;AAGD,CA/BC,kBA+BkB,CAAC,qBAAqB;AACxC,aAAW;AACX,cAAY;AACZ;AACD,CAnCC,kBAmCkB,CAAC,oBAAoB;AACxC,CApCC,kBAoCkB,CAAC,oBAAoB;AACxC,CArCC,kBAqCkB,CAAC,kBAAkB;AACtC,CAtCC,kBAsCkB,GAAG,CA5CrB;AA6CD,CAvCC,kBAuCkB,CApDlB;AAqDA,aAAW;AACX,cAAY;AACZ,SAAO;AACP,WAAS;AACT;AAED,CA9CC,kBA8CkB,GAAG,CA3DrB;AA6DA,kBAAgB;AACjB;AAEA,CAnDC,iBAmDiB,CAAC;AAClB,oBAAkB,MAAM;AACxB,gBAAc,MAAM;AACpB;AACD,CAvDC,iBAuDiB,CAAC;AAClB,oBAAkB;AAElB,gBAAc;AACd,gBAAc;AACf;AACA,CA7DC,iBA6DiB,CANC,kBAMkB,CAVlB;AAWlB,oBAAkB;AAClB,gBAAc;AACf;AACA,CAjEC;AAkEA,+BAA6B;AAC9B;AACA,CApEC,kBAoEkB;AAClB,+BAA6B,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACjD;AACA,CApFC;AAqFA,UAAQ;AACR,cAAY;AACZ;AACD,CAAC;AACA,cAAY;AACZ;AACD,CArFC;AAsFA,SAAO;AACP,UAAQ;AACR,mBAAiB;AACZ,cAAY;AACjB,WAAS;AACT;AAED,CAvDoB,qBAuDE;AACrB,oBAAkB;AAClB;AAED,CAxGC;AAwGuB,WAAS;AAAK;AAEtC,CAvDoB;AAuDI,WAAS;AAAK;AACtC,CA9DoB;AA8DI,WAAS;AAAK;AACtC,CA1DoB;AA0DI,WAAS;AAAK;AACtC,CA5DoB;AA4DI,WAAS;AAAK;AACtC,CAAC;AAAyB,WAAS;AAAK;AACxC,CAAC;AAAuB,WAAS;AAAK;AAEtC,CAAC,iBAAiB;AAAS,WAAS;AAAK;AACzC,CADC,iBACiB;AAAS,WAAS;AAAK;AAEzC,CAAC;AACA,SAAO;AACP,UAAQ;AACR;AACD,CAAC;AACA,YAAU;AACV,WAAS;AACT,YAAU;AACV;AAKD,CAAC;AACA,YAAU;AACV,WAAS;AACT,kBAAgB;AAChB,kBAAgB;AAChB;AACD,CAAC;AACD,CAAC;AACA,YAAU;AACV,WAAS;AACT,kBAAgB;AAChB;AACD,CANC;AAOA,OAAK;AACL;AACD,CAAC;AACA,SAAO;AACP;AACD,CAXC;AAYA,UAAQ;AACR;AACD,CAAC;AACA,QAAM;AACN;AACD,CAxBC;AAyBA,SAAO;AACP,SAAO;AACP;AACD,CAbC,cAac,CA5Bd;AA6BA,SAAO;AACP;AACD,CAzBC,YAyBY,CA/BZ;AAgCA,cAAY;AACZ;AACD,CA3BC,eA2Be,CAlCf;AAmCA,iBAAe;AACf;AACD,CAhBC,aAgBa,CArCb;AAsCA,eAAa;AACb;AACD,CAzBC,cAyBc,CAxCd;AAyCA,gBAAc;AACd;AAKD,CAAC,kBAAkB,CAAC;AACnB,WAAS;AACT,sBAAoB,QAAQ,KAAK;AAC9B,mBAAiB,QAAQ,KAAK;AACzB,cAAY,QAAQ,KAAK;AACjC;AACD,CANC,kBAMkB,CArElB,iBAqEoC,CANjB;AAOnB,WAAS;AACT;AACD,CAAC;AACA,4BAA0B,EAAE;AACxB,wBAAsB,EAAE;AACpB,oBAAkB,EAAE;AAC5B;AACD,GAAG,CALF;AAMA,eAAa;AACd;AAEA,CAAC,kBAAkB,CATlB;AAUA,sBAAoB,kBAAkB,MAAM,aAAa,CAAC,EAAC,CAAC,EAAC,IAAI,EAAC;AAC/D,mBAAoB,eAAe,MAAM,aAAa,CAAC,EAAC,CAAC,EAAC,IAAI,EAAC;AAC1D,cAAoB,UAAU,MAAM,aAAa,CAAC,EAAC,CAAC,EAAC,IAAI,EAAC;AAClE;AACD,CALC,kBAKkB,CAtMlB;AAuMD,CAAC,iBAAiB,CAvMjB;AAwMA,sBAAoB;AACjB,mBAAiB;AACZ,cAAY;AACpB;AAED,CAZC,kBAYkB,CAAC;AACnB,cAAY;AACZ;AAKD,CAAC;AACA,UAAQ;AACR;AACD,CAAC;AACA,UAAQ;AACR,UAAW;AACX,UAAgB;AAChB;AACD,CAAC;AACD,CADC,kBACkB,CATlB;AAUA,UAAQ;AACR;AACD,CAlHC;AAmHD,CAjGC;AAkGA,UAAQ;AACR;AACD,CAAC,iBAAiB,CAbjB;AAcD,CADC,iBACiB,CAdjB,aAc+B,CAjB/B;AAkBD,CAFC,iBAEiB,CAAC;AAClB,UAAQ;AACR,UAAQ;AACR,UAAW;AACX,UAAgB;AAChB;AAGD,CA7OC;AA8OD,CA7OC;AA8OD,CAzOC;AA0OD,CAlPC,aAkPa,EAAE,IAAI;AACpB,CA/OC;AAgPA,kBAAgB;AAChB;AAED,CArPC,mBAqPmB,CAlCnB;AAmCD,CAhPC,mBAgPmB,CAnCnB;AAoCD,CAzPC,aAyPa,EAAE,IAAI,IAAI,CApCvB;AAqCD,GAAG,CAlPF,mBAkPsB,CArCtB,oBAqC2C;AAC3C,kBAAgB;AAChB,kBAAgB;AAChB;AAID,CAnPC;AAoPA,cAAY;AACZ,kBAAgB;AAChB;AACD,CAvPC,kBAuPkB;AAClB,SAAO;AACP;AACD,CAjQC;AAkQA,UAAQ,IAAI,OAAO;AACnB,cAAY,KAAK,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC;AAC7B;AAID,CAjQC;AAkQA;AAAA,IAAa,gBAAgB;AAAA,IAAE,KAAK;AAAA,IAAE,SAAS;AAAA,IAAE;AACjD,aAAW;AACX,aAAW;AACX,eAAa;AACb;AAKD,CAAC;AACA,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AACjC,iBAAe;AACf;AACD,CAJC,YAIY;AACZ,oBAAkB;AAClB,iBAAe,IAAI,MAAM;AACzB,SAAO;AACP,UAAQ;AACR,eAAa;AACb,WAAS;AACT,cAAY;AACZ,mBAAiB;AACjB,SAAO;AACP;AACD,CAfC,YAeY;AACb,CAAC;AACA,uBAAqB,IAAI;AACzB,qBAAmB;AACnB,WAAS;AACT;AACD,CArBC,YAqBY,CAAC;AACd,CAtBC,YAsBY,CAAC;AACb,oBAAkB;AAClB;AACD,CAzBC,YAyBY,CAAC;AACb,0BAAwB;AACxB,2BAAyB;AACzB;AACD,CA7BC,YA6BY,CAAC;AACb,6BAA2B;AAC3B,8BAA4B;AAC5B,iBAAe;AACf;AACD,CAlCC,YAkCY,CAAC,CAAC;AACd,UAAQ;AACR,oBAAkB;AAClB,SAAO;AACP;AAED,CAAC,cAAc,CAxCd,YAwC2B;AAC3B,SAAO;AACP,UAAQ;AACR,eAAa;AACb;AACD,CALC,cAKc,CA7Cd,YA6C2B,CAAC;AAC5B,0BAAwB;AACxB,2BAAyB;AACzB;AACD,CATC,cASc,CAjDd,YAiD2B,CAAC;AAC5B,6BAA2B;AAC3B,8BAA4B;AAC5B;AAID,CAAC;AACD,CAAC;AACA;AAAA,IAAM,KAAK,KAAK,gBAAgB;AAAA,IAAE,MAAM;AAAA,IAAE;AAC1C,eAAa;AACb;AAED,CAtBC,cAsBc,CANd;AAMwC,CAtBxC,cAsBuD,CALvD;AAMA,aAAW;AACX;AAKD,CAAC;AACA,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AACjC,cAAY;AACZ,iBAAe;AACf;AACD,CA1DC;AA2DA,oBAAkB;AAClB,SAAO;AACP,UAAQ;AACR;AACD,CAAC,eAAe,CA/Df;AAgEA,oBAAkB;AAClB,mBAAiB,KAAK;AACtB;AACD,CA3CC,cA2Cc,CAnEd;AAoEA,SAAO;AACP,UAAQ;AACR;AACD,CAlBC,uBAkBuB,CAAC;AACzB,CAAC,gCAAgC,CAxEhC;AAyEA,WAAS;AACT;AACD,CAHC,gCAGgC,CAJR;AAKxB,WAAS;AACT,YAAU;AACV;AACD,CAPC;AAQA,WAAS,IAAI,KAAK,IAAI;AACtB,SAAO;AACP,cAAY;AACZ;AACD,CAAC;AACA,cAAY;AACZ,cAAY;AACZ,iBAAe;AACf;AACD,CAAC;AACA,cAAY;AACZ,YAAU;AACV,OAAK;AACL;AACD,CAzCC,uBAyCuB;AACvB,WAAS;AACT,aAAW;AACX,aAAW;AACX;AACD,CAAC;AACA,UAAQ;AACR,cAAY,IAAI,MAAM;AACtB,UAAQ,IAAI,MAAM,IAAI;AACtB;AAGD,CAAC;AACA,oBAAkB;AAClB;AAKD,CA5YC,kBA4YkB,CAAC;AACnB,cAAY;AACZ,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,UAAQ;AACR;AACD,CALoB;AAMpB,CAAC;AACA,WAAS,EAAE;AACX,SAAO;AACP,eAAa;AACb;AACD,CAXoB,4BAWS;AAC5B,mBAAiB;AACjB;AACD,CAdoB,4BAcS,CAAC;AAC9B,CAfoB,4BAeS,CAAC;AAC7B,mBAAiB;AACjB;AACD,CAAC;AACA,WAAS;AACT,kBAAgB;AAChB,SAAO;AACP,UAAQ;AACR;AACD,CA5RC,aA4Ra,CAAC;AACd,eAAa;AACb;AACD,CA7SC,eA6Se,CAHD;AAId,iBAAe;AACf;AACD,CAxBC;AAyBA,UAAQ,IAAI,MAAM;AAClB,cAAY;AACZ,eAAa;AACb,WAAS,IAAI,IAAI;AACjB,eAAa;AACb,mBAAiB;AACZ,cAAY;AACjB,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,eAAa,IAAI,IAAI;AACrB;AACD,CAnCC,0BAmC0B,KAAK;AAC/B,cAAY,IAAI,MAAM;AACtB,iBAAe;AACf,cAAY;AACZ;AACD,CAxCC,0BAwC0B,KAAK,aAAa,KAAK;AACjD,iBAAe,IAAI,MAAM;AACzB;AAED,CA3IC,cA2Ic,CAlDK;AAmDpB,CA5IC,cA4Ic,CA/Gd;AAgHD,CA7IC,cA6Ic,CArLd;AAsLA,cAAY;AACZ;AACD,CAhJC,cAgJc,CAnHd;AAoHD,CAjJC,cAiJc,CAzLd;AA0LA,UAAQ,IAAI,MAAM,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AAC7B,mBAAiB;AACjB;AAKD,CA1SoB;AA2SnB,YAAU;AACV,cAAY;AACZ,iBAAe;AACf;AACD,CAAC;AACA,WAAS;AACT,cAAY;AACZ,iBAAe;AACf;AACD,CAAC;AACA,UAAQ,KAAK,KAAK,KAAK;AACvB,eAAa;AACb,aAAW;AACX,aAAW;AACX,cAAY;AACZ;AACD,CAPC,sBAOsB;AACtB,UAAQ,KAAK;AACb,UAAQ,MAAM;AACd;AACD,CAAC;AACA,SAAO;AACP,UAAQ;AACR,YAAU;AACV,QAAM;AACN,cAAY;AACZ,eAAa;AACb,YAAU;AACV,kBAAgB;AAChB;AACD,CAAC;AACA,SAAO;AACP,UAAQ;AACR,WAAS;AAET,UAAQ,MAAM,KAAK;AACnB,kBAAgB;AAEhB,qBAAmB,OAAO;AACvB,kBAAgB,OAAO;AACtB,iBAAe,OAAO;AAClB,aAAW,OAAO;AAC1B;AACD,CAvCC;AAwCD,CAdC;AAeA,cAAY;AACZ,SAAO;AACP,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AAClC;AACD,CA9fC,kBA8fkB,CAAC,CAAC;AACpB,YAAU;AACV,OAAK;AACL,SAAO;AACP,UAAQ;AACR,cAAY;AACZ,SAAO;AACP,UAAQ;AACR;AAAA,IAAM,IAAI,CAAC,KAAK,MAAM;AAAA,IAAE,OAAO;AAAA,IAAE;AACjC,SAAO;AACP,mBAAiB;AACjB,cAAY;AACZ;AACD,CA3gBC,kBA2gBkB,CAAC,CAbC,0BAa0B;AAC/C,CA5gBC,kBA4gBkB,CAAC,CAdC,0BAc0B;AAC9C,SAAO;AACP;AACD,CAAC;AACA,YAAU;AACV;AAED,CAAC,cAAc,CAlEd;AAmEA,YAAU;AACV;AACD,CAHC,cAGc,CA3Cd;AA4CA,SAAO;AACP,UAAQ,EAAE;AAEV,cAAY;AACZ,UAAQ,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC;AACtG;AAED,CAXC,cAWc,CAAC;AAChB,CAZC,cAYc,CA/Md;AAgND,CAbC,cAac,CA/Ed;AAgFD,CAdC,cAcc,CAtDd;AAuDA,UAAQ,IAAI,MAAM;AAClB;AAKD,CAAC;AACA,cAAY;AACZ,UAAQ,IAAI,MAAM;AAClB;AAKD,CAAC;AACA,YAAU;AACV,WAAS;AACT,oBAAkB;AAClB,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,SAAO;AACP,eAAa;AACb,uBAAqB;AACrB,oBAAkB;AAClB,mBAAiB;AACjB,eAAa;AACb,kBAAgB;AAChB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AACjC;AACD,CAfC,eAee,CAxXf;AAyXA,UAAQ;AACR,kBAAgB;AAChB;AACD,CAAC,mBAAmB;AACpB,CAAC,sBAAsB;AACvB,CAAC,oBAAoB;AACrB,CAAC,qBAAqB;AACrB,YAAU;AACV,kBAAgB;AAChB,UAAQ,IAAI,MAAM;AAClB,cAAY;AACZ,WAAS;AACT;AAID,CAZC;AAaA,cAAY;AACb;AACA,CAhBC;AAiBA,cAAY;AACb;AACA,CAlBC,sBAkBsB;AACvB,CApBC,mBAoBmB;AACnB,QAAM;AACN,eAAa;AACb;AACD,CAxBC,mBAwBmB;AACnB,UAAQ;AACR,iBAAe;AACf,oBAAkB;AAClB;AACD,CA5BC,sBA4BsB;AACtB,OAAK;AACL,cAAY;AACZ,eAAa;AACb,uBAAqB;AACrB;AACD,CAjCC;AAkCA,eAAa;AACd;AACA,CAnCC;AAoCA,eAAa;AACd;AACA,CAvCC,oBAuCoB;AACrB,CAvCC,qBAuCqB;AACrB,OAAK;AACL,cAAY;AACZ;AACD,CA5CC,oBA4CoB;AACpB,SAAO;AACP,gBAAc;AACd,qBAAmB;AACnB;AACD,CAhDC,qBAgDqB;AACrB,QAAM;AACN,eAAa;AACb,sBAAoB;AACpB;AAID,OAAO;AAEN,GA7gBA;AA8gBC,gCAA4B;AAC5B,wBAAoB;AACpB;AACD;", "names": []}
import {
  environment
} from "./chunk-I7MI46CM.js";
import {
  <PERSON>ert<PERSON><PERSON>roller,
  CommonModule,
  Component,
  FormsModule,
  HttpClient,
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonTitle,
  IonToolbar,
  IonicModule,
  ModalController,
  NgForOf,
  NgIf,
  Router,
  RouterModule,
  ToastController,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-NS3G4TP7.js";
import "./chunk-VI7H4G7Y.js";
import "./chunk-S72IRO7V.js";
import "./chunk-C6K4MQWC.js";
import "./chunk-7YVUC4YJ.js";
import "./chunk-6NM256MY.js";
import "./chunk-JK35ET3X.js";
import "./chunk-5XFA73GC.js";
import "./chunk-VZLU5HUR.js";
import "./chunk-BWFRBVCO.js";
import "./chunk-WOV3UQHA.js";
import "./chunk-NEM5PINF.js";
import "./chunk-JYOJD2RE.js";
import "./chunk-SU5KLDLB.js";
import "./chunk-VPEVKC6V.js";
import "./chunk-UTE7ZTN7.js";
import "./chunk-K36F4VI5.js";
import "./chunk-MLP6EVSE.js";
import "./chunk-XJYVD5XF.js";
import "./chunk-TIORHE7B.js";
import "./chunk-DHMNEELS.js";
import "./chunk-XP6B2FPQ.js";
import "./chunk-5IEENWJY.js";
import "./chunk-3ZS2G4I2.js";
import "./chunk-AMF6HWDG.js";
import "./chunk-DFDJHPIB.js";
import "./chunk-2HS7YJ5A.js";
import "./chunk-F4BDZKIT.js";
import {
  __async
} from "./chunk-UL2P3LPA.js";

// src/app/pages/profile/profile.page.ts
function ProfilePage_p_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.userData.email);
  }
}
var _c0 = "\n\n.modal-title[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  font-weight: bold;\n}\n.modal-section-title[_ngcontent-%COMP%] {\n  font-size: 0.9375rem;\n  margin-bottom: 15px;\n}\n/*# sourceMappingURL=profile.page.css.map */";
function GuideModalComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 7)(1, "div", 8)(2, "span", 9);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 10);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const item_r1 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(item_r1.icon);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(item_r1.label);
  }
}
var ProfilePage = class _ProfilePage {
  constructor(modalCtrl, alertCtrl, toastCtrl, http, router) {
    this.modalCtrl = modalCtrl;
    this.alertCtrl = alertCtrl;
    this.toastCtrl = toastCtrl;
    this.http = http;
    this.router = router;
    this.userData = {};
    this.loadUserData();
  }
  goToSettings() {
    this.router.navigate(["/settings"]);
  }
  loadUserData() {
    const data = localStorage.getItem("userData");
    if (data) {
      this.userData = JSON.parse(data);
    }
  }
  openTermsModal() {
    return __async(this, null, function* () {
      const modal = yield this.modalCtrl.create({
        component: TermsModalComponent,
        cssClass: "terms-modal"
      });
      yield modal.present();
    });
  }
  openPrivacyModal() {
    return __async(this, null, function* () {
      const modal = yield this.modalCtrl.create({
        component: PrivacyModalComponent,
        cssClass: "terms-modal"
      });
      yield modal.present();
    });
  }
  openEmergencyContactsModal() {
    return __async(this, null, function* () {
      const modal = yield this.modalCtrl.create({
        component: EmergencyContactsModalComponent,
        cssClass: "terms-modal"
      });
      yield modal.present();
    });
  }
  openSafetyTipsModal() {
    return __async(this, null, function* () {
      const modal = yield this.modalCtrl.create({
        component: SafetyTipsModalComponent,
        cssClass: "terms-modal"
      });
      yield modal.present();
    });
  }
  openGuideModal() {
    return __async(this, null, function* () {
      const modal = yield this.modalCtrl.create({
        component: GuideModalComponent,
        cssClass: "terms-modal"
      });
      yield modal.present();
    });
  }
  openAccountInfoModal() {
    return __async(this, null, function* () {
      const modal = yield this.modalCtrl.create({
        component: AccountInfoModalComponent,
        cssClass: "account-info-modal"
      });
      yield modal.present();
    });
  }
  testFCM() {
    return __async(this, null, function* () {
      const googlePlayMissing = localStorage.getItem("google_play_services_missing");
      if (googlePlayMissing === "true") {
        const alert = yield this.alertCtrl.create({
          header: "Google Play Services Required",
          message: "Push notifications require Google Play Services. Would you like to install or update Google Play Services?",
          buttons: [
            {
              text: "Install/Update",
              handler: () => {
                window.open("market://details?id=com.google.android.gms", "_system");
              }
            },
            {
              text: "Continue Anyway",
              handler: () => {
                this.checkFCMToken();
              }
            }
          ]
        });
        yield alert.present();
        return;
      }
      yield this.checkFCMToken();
    });
  }
  checkFCMToken() {
    return __async(this, null, function* () {
      const token = localStorage.getItem("fcm_token");
      if (!token) {
        const alert = yield this.alertCtrl.create({
          header: "No FCM Token",
          message: "No FCM token found. Please restart the app to generate a token.",
          buttons: ["OK"]
        });
        yield alert.present();
        return;
      }
      const tokenAlert = yield this.alertCtrl.create({
        header: "FCM Token",
        message: `Current token: ${token.substring(0, 20)}...`,
        buttons: [
          {
            text: "Test Local Notification",
            handler: () => {
              this.showTestNotification();
            }
          },
          {
            text: "Send from Backend",
            handler: () => {
              this.sendTestNotificationFromBackend(token);
            }
          },
          {
            text: "Check Google Play",
            handler: () => {
              this.checkGooglePlayServices();
            }
          },
          {
            text: "Cancel",
            role: "cancel"
          }
        ]
      });
      yield tokenAlert.present();
    });
  }
  checkGooglePlayServices() {
    return __async(this, null, function* () {
      try {
        window.open("market://details?id=com.google.android.gms", "_system");
      } catch (error) {
        console.error("Error opening Google Play Store:", error);
        const alert = yield this.alertCtrl.create({
          header: "Error",
          message: "Could not open Google Play Store. Please check if Google Play Store is installed on your device.",
          buttons: ["OK"]
        });
        yield alert.present();
      }
    });
  }
  showTestNotification() {
    return __async(this, null, function* () {
      const notification = {
        title: "Test Notification",
        body: "This is a local test notification",
        category: "General",
        severity: "medium",
        wasTapped: false,
        time: (/* @__PURE__ */ new Date()).toISOString()
      };
      if ("vibrate" in navigator) {
        navigator.vibrate([500, 100, 500]);
      }
      const alert = yield this.alertCtrl.create({
        header: notification.title,
        subHeader: notification.category ? `${notification.category.toUpperCase()}` : "",
        message: notification.body,
        buttons: ["OK"]
      });
      yield alert.present();
    });
  }
  sendTestNotificationFromBackend(token) {
    return __async(this, null, function* () {
      const loading = yield this.toastCtrl.create({
        message: "Sending test notification from backend...",
        duration: 2e3
      });
      yield loading.present();
      this.http.post(`${environment.apiUrl}/test-notification`, {
        token,
        title: "Test from App",
        message: "This is a test notification sent from the app",
        category: "General",
        severity: "medium"
      }).subscribe({
        next: () => {
          this.toastCtrl.create({
            message: "Test notification sent successfully!",
            duration: 3e3,
            color: "success"
          }).then((toast) => toast.present());
        },
        error: (error) => {
          this.alertCtrl.create({
            header: "Error",
            message: `Failed to send test notification: ${error.message || JSON.stringify(error)}`,
            buttons: ["OK"]
          }).then((alert) => alert.present());
        }
      });
    });
  }
  static {
    this.\u0275fac = function ProfilePage_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ProfilePage)(\u0275\u0275directiveInject(ModalController), \u0275\u0275directiveInject(AlertController), \u0275\u0275directiveInject(ToastController), \u0275\u0275directiveInject(HttpClient), \u0275\u0275directiveInject(Router));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ProfilePage, selectors: [["app-profile"]], decls: 38, vars: 2, consts: [[1, "profile-header"], [1, "profile-background"], [1, "profile-avatar"], ["name", "person", 1, "avatar-icon"], [1, "profile-info"], [4, "ngIf"], ["lines", "full", 1, "menu-list"], ["button", "", 2, "padding-top", "10px", 3, "click"], ["src", "assets/setting (1).png", "slot", "start", 2, "width", "28px", "height", "28px", "display", "block", "margin", "auto"], [2, "padding-left", "15px", "font-size", "17px"], ["src", "assets/info.png", "slot", "start", 2, "width", "28px", "height", "28px", "display", "block", "margin", "auto"], ["src", "assets/medical-call.png", "slot", "start", 2, "width", "28px", "height", "28px", "display", "block", "margin", "auto"], ["src", "assets/first-aid-box.png", "slot", "start", 2, "width", "28px", "height", "28px", "display", "block", "margin", "auto"], ["src", "assets/shield.png", "slot", "start", 2, "width", "28px", "height", "28px", "display", "block", "margin", "auto"], ["src", "assets/terms-and-conditions.png", "slot", "start", 2, "width", "28px", "height", "28px", "display", "block", "margin", "auto"], ["name", "settings-outline", "slot", "start", 2, "width", "28px", "height", "28px", "display", "block", "margin", "auto", "color", "#3880ff"]], template: function ProfilePage_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-content")(1, "div", 0)(2, "div", 1)(3, "div", 2);
        \u0275\u0275element(4, "ion-icon", 3);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(5, "div", 4)(6, "h2");
        \u0275\u0275text(7);
        \u0275\u0275elementEnd();
        \u0275\u0275template(8, ProfilePage_p_8_Template, 2, 1, "p", 5);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(9, "ion-list", 6)(10, "ion-item", 7);
        \u0275\u0275listener("click", function ProfilePage_Template_ion_item_click_10_listener() {
          return ctx.openAccountInfoModal();
        });
        \u0275\u0275element(11, "img", 8);
        \u0275\u0275elementStart(12, "ion-label", 9);
        \u0275\u0275text(13, "Account Information");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(14, "ion-item", 7);
        \u0275\u0275listener("click", function ProfilePage_Template_ion_item_click_14_listener() {
          return ctx.openGuideModal();
        });
        \u0275\u0275element(15, "img", 10);
        \u0275\u0275elementStart(16, "ion-label", 9);
        \u0275\u0275text(17, "Reference Guide for Map Symbols");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(18, "ion-item", 7);
        \u0275\u0275listener("click", function ProfilePage_Template_ion_item_click_18_listener() {
          return ctx.openEmergencyContactsModal();
        });
        \u0275\u0275element(19, "img", 11);
        \u0275\u0275elementStart(20, "ion-label", 9);
        \u0275\u0275text(21, "Emergency Contacts");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(22, "ion-item", 7);
        \u0275\u0275listener("click", function ProfilePage_Template_ion_item_click_22_listener() {
          return ctx.openSafetyTipsModal();
        });
        \u0275\u0275element(23, "img", 12);
        \u0275\u0275elementStart(24, "ion-label", 9);
        \u0275\u0275text(25, "Safety Tips");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(26, "ion-item", 7);
        \u0275\u0275listener("click", function ProfilePage_Template_ion_item_click_26_listener() {
          return ctx.openPrivacyModal();
        });
        \u0275\u0275element(27, "img", 13);
        \u0275\u0275elementStart(28, "ion-label", 9);
        \u0275\u0275text(29, "Privacy Policy");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(30, "ion-item", 7);
        \u0275\u0275listener("click", function ProfilePage_Template_ion_item_click_30_listener() {
          return ctx.openTermsModal();
        });
        \u0275\u0275element(31, "img", 14);
        \u0275\u0275elementStart(32, "ion-label", 9);
        \u0275\u0275text(33, "Terms and Condition");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(34, "ion-item", 7);
        \u0275\u0275listener("click", function ProfilePage_Template_ion_item_click_34_listener() {
          return ctx.goToSettings();
        });
        \u0275\u0275element(35, "ion-icon", 15);
        \u0275\u0275elementStart(36, "ion-label", 9);
        \u0275\u0275text(37, "Notification Settings");
        \u0275\u0275elementEnd()()()();
      }
      if (rf & 2) {
        \u0275\u0275advance(7);
        \u0275\u0275textInterpolate1("Hi, ", ctx.userData.full_name || "User", "");
        \u0275\u0275advance();
        \u0275\u0275property("ngIf", ctx.userData.email);
      }
    }, dependencies: [IonicModule, IonContent, IonIcon, IonItem, IonLabel, IonList, CommonModule, NgIf, FormsModule, RouterModule], styles: [`@charset "UTF-8";



ion-header[_ngcontent-%COMP%] {
  background: var(--ion-color-primary);
}
ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {
  --background: transparent;
}
ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%] {
  color: white;
}
ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-back-button[_ngcontent-%COMP%] {
  --color: white;
}
.profile-header[_ngcontent-%COMP%] {
  position: relative;
  margin-bottom: 20px;
}
.profile-background[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      #03b2dd 0%,
      #0891b2 100%);
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}
.profile-background[_ngcontent-%COMP%]::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.05"><circle cx="30" cy="30" r="2"/></g></svg>');
  pointer-events: none;
}
.profile-avatar[_ngcontent-%COMP%] {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
.profile-avatar[_ngcontent-%COMP%]   .avatar-icon[_ngcontent-%COMP%] {
  font-size: 40px;
  color: white;
}
.profile-info[_ngcontent-%COMP%] {
  text-align: center;
  color: white;
}
.profile-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
  font-weight: 400;
}
.menu-list[_ngcontent-%COMP%] {
  background: transparent;
  margin-top: 20px;
  padding: 0 16px;
}
.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 60px;
  margin-bottom: 8px;
  border-radius: 12px;
  --background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}
.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {
  font-size: 20px;
  margin-right: 16px;
  color: var(--ion-color-primary);
}
.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%] {
  font-size: 16px;
  font-weight: 500;
  color: var(--ion-color-dark);
}
.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {
  filter: brightness(0) saturate(100%) invert(34%) sepia(77%) saturate(2476%) hue-rotate(203deg) brightness(99%) contrast(92%);
}
.terms-modal[_ngcontent-%COMP%] {
  --height: 90%;
  --border-radius: 16px;
}
.terms-modal[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {
  --background: var(--ion-color-light);
}
.terms-modal[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%] {
  font-size: 18px;
  font-weight: 600;
}
.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {
  color: var(--ion-color-dark);
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
}
.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   .effective-date[_ngcontent-%COMP%] {
  color: var(--ion-color-medium);
  font-size: 14px;
  margin-bottom: 24px;
}
.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {
  color: var(--ion-color-dark);
  font-size: 18px;
  font-weight: 600;
  margin: 24px 0 12px;
}
.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {
  color: var(--ion-color-medium);
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 16px;
}
.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {
  list-style: none;
  padding: 0;
  margin: 0;
}
.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {
  color: var(--ion-color-medium);
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 8px;
  padding-left: 24px;
  position: relative;
}
.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before {
  content: "\\2022";
  position: absolute;
  left: 8px;
  color: var(--ion-color-primary);
}
.terms-modal[_ngcontent-%COMP%]   .legend-title[_ngcontent-%COMP%] {
  color: var(--ion-color-dark);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
}
.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%] {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: var(--ion-color-light);
  border-radius: 8px;
}
.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}
.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   .legend-icon[_ngcontent-%COMP%] {
  font-size: 20px;
}
.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]   .legend-label[_ngcontent-%COMP%] {
  color: var(--ion-color-dark);
  font-size: 16px;
}
/*# sourceMappingURL=profile.page.css.map */`] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProfilePage, [{
    type: Component,
    args: [{ selector: "app-profile", standalone: true, imports: [IonicModule, CommonModule, FormsModule, RouterModule], template: `\r
\r
<ion-content>\r
  <div class="profile-header">\r
    <div class="profile-background">\r
      <div class="profile-avatar">\r
        <ion-icon name="person" class="avatar-icon"></ion-icon>\r
      </div>\r
      <div class="profile-info">\r
        <h2>Hi, {{ userData.full_name || 'User' }}</h2>\r
        <p *ngIf="userData.email">{{ userData.email }}</p>\r
      </div>\r
    </div>\r
  </div>\r
\r
  <ion-list lines="full" class="menu-list">\r
    <ion-item button (click)="openAccountInfoModal()" style="padding-top: 10px;">\r
      <img src="assets/setting (1).png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />\r
      <ion-label style="padding-left: 15px; font-size: 17px;">Account Information</ion-label>\r
    </ion-item>\r
\r
    <ion-item button (click)="openGuideModal()" style="padding-top: 10px;">\r
      <img src="assets/info.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />\r
      <ion-label style="padding-left: 15px; font-size: 17px;">Reference Guide for Map Symbols</ion-label>\r
    </ion-item>\r
\r
    <ion-item button (click)="openEmergencyContactsModal()" style="padding-top: 10px;">\r
      <img src="assets/medical-call.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />\r
      <ion-label style="padding-left: 15px; font-size: 17px;">Emergency Contacts</ion-label>\r
    </ion-item>\r
\r
    <ion-item button (click)="openSafetyTipsModal()" style="padding-top: 10px;">\r
      <img src="assets/first-aid-box.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />\r
      <ion-label style="padding-left: 15px; font-size: 17px;">Safety Tips</ion-label>\r
    </ion-item>\r
\r
    <ion-item button (click)="openPrivacyModal()" style="padding-top: 10px;">\r
      <img src="assets/shield.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />\r
      <ion-label style="padding-left: 15px;   font-size: 17px;">Privacy Policy</ion-label>\r
    </ion-item>\r
\r
    <ion-item button (click)="openTermsModal()" style="padding-top: 10px;">\r
      <img src="assets/terms-and-conditions.png" style="width:28px; height:28px; display:block; margin:auto; " slot="start" />\r
      <ion-label style="padding-left: 15px; font-size: 17px;">Terms and Condition</ion-label>\r
    </ion-item>\r
\r
    <ion-item button (click)="goToSettings()" style="padding-top: 10px;">\r
      <ion-icon name="settings-outline" style="width:28px; height:28px; display:block; margin:auto; color: #3880ff;" slot="start"></ion-icon>\r
      <ion-label style="padding-left: 15px; font-size: 17px;">Notification Settings</ion-label>\r
    </ion-item>\r
\r
  </ion-list>\r
</ion-content>`, styles: [`@charset "UTF-8";

/* src/app/pages/profile/profile.page.scss */
ion-header {
  background: var(--ion-color-primary);
}
ion-header ion-toolbar {
  --background: transparent;
}
ion-header ion-toolbar ion-title {
  color: white;
}
ion-header ion-toolbar ion-back-button {
  --color: white;
}
.profile-header {
  position: relative;
  margin-bottom: 20px;
}
.profile-background {
  background:
    linear-gradient(
      135deg,
      #03b2dd 0%,
      #0891b2 100%);
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}
.profile-background::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.05"><circle cx="30" cy="30" r="2"/></g></svg>');
  pointer-events: none;
}
.profile-avatar {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
.profile-avatar .avatar-icon {
  font-size: 40px;
  color: white;
}
.profile-info {
  text-align: center;
  color: white;
}
.profile-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.profile-info p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
  font-weight: 400;
}
.menu-list {
  background: transparent;
  margin-top: 20px;
  padding: 0 16px;
}
.menu-list ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 60px;
  margin-bottom: 8px;
  border-radius: 12px;
  --background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.menu-list ion-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}
.menu-list ion-item ion-icon {
  font-size: 20px;
  margin-right: 16px;
  color: var(--ion-color-primary);
}
.menu-list ion-item ion-label {
  font-size: 16px;
  font-weight: 500;
  color: var(--ion-color-dark);
}
.menu-list ion-item img {
  filter: brightness(0) saturate(100%) invert(34%) sepia(77%) saturate(2476%) hue-rotate(203deg) brightness(99%) contrast(92%);
}
.terms-modal {
  --height: 90%;
  --border-radius: 16px;
}
.terms-modal ion-header ion-toolbar {
  --background: var(--ion-color-light);
}
.terms-modal ion-header ion-toolbar ion-title {
  font-size: 18px;
  font-weight: 600;
}
.terms-modal ion-content h2 {
  color: var(--ion-color-dark);
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
}
.terms-modal ion-content .effective-date {
  color: var(--ion-color-medium);
  font-size: 14px;
  margin-bottom: 24px;
}
.terms-modal ion-content h3 {
  color: var(--ion-color-dark);
  font-size: 18px;
  font-weight: 600;
  margin: 24px 0 12px;
}
.terms-modal ion-content p {
  color: var(--ion-color-medium);
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 16px;
}
.terms-modal ion-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.terms-modal ion-content ul li {
  color: var(--ion-color-medium);
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 8px;
  padding-left: 24px;
  position: relative;
}
.terms-modal ion-content ul li:before {
  content: "\\2022";
  position: absolute;
  left: 8px;
  color: var(--ion-color-primary);
}
.terms-modal .legend-title {
  color: var(--ion-color-dark);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
}
.terms-modal .legend-container {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.terms-modal .legend-container .legend-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: var(--ion-color-light);
  border-radius: 8px;
}
.terms-modal .legend-container .legend-items .icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}
.terms-modal .legend-container .legend-items .icon-wrapper .legend-icon {
  font-size: 20px;
}
.terms-modal .legend-container .legend-items .legend-label {
  color: var(--ion-color-dark);
  font-size: 16px;
}
/*# sourceMappingURL=profile.page.css.map */
`] }]
  }], () => [{ type: ModalController }, { type: AlertController }, { type: ToastController }, { type: HttpClient }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ProfilePage, { className: "ProfilePage", filePath: "src/app/pages/profile/profile.page.ts", lineNumber: 16 });
})();
var TermsModalComponent = class _TermsModalComponent {
  constructor(modalCtrl) {
    this.modalCtrl = modalCtrl;
  }
  dismiss() {
    this.modalCtrl.dismiss();
  }
  static {
    this.\u0275fac = function TermsModalComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TermsModalComponent)(\u0275\u0275directiveInject(ModalController));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _TermsModalComponent, selectors: [["ng-component"]], decls: 61, vars: 0, consts: [[1, "modal-title"], ["slot", "end"], [3, "click"], [1, "ion-padding"], [1, "terms-content"], [1, "modal-section-title"], [1, "effective-date"], [1, "welcome"]], template: function TermsModalComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-title", 0)(3, "strong");
        \u0275\u0275text(4, "Terms and Conditions");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(5, "ion-buttons", 1)(6, "ion-button", 2);
        \u0275\u0275listener("click", function TermsModalComponent_Template_ion_button_click_6_listener() {
          return ctx.dismiss();
        });
        \u0275\u0275text(7, "Close");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(8, "ion-content", 3)(9, "div", 4)(10, "h1", 5)(11, "strong");
        \u0275\u0275text(12, "Terms and Conditions");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(13, "p", 6);
        \u0275\u0275text(14, "Effective Date: April 26, 2025");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(15, "p", 7);
        \u0275\u0275text(16, 'Welcome to Evacuation Mapping System ("we", "our", or "us"). These ');
        \u0275\u0275elementStart(17, "strong");
        \u0275\u0275text(18, "Terms and Conditions");
        \u0275\u0275elementEnd();
        \u0275\u0275text(19, ' ("Terms") govern your access to and use of our online evacuation mapping system (the "Service"). By registering or using the Service, you agree to be bound by these Terms.');
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(20, "section")(21, "h2", 5);
        \u0275\u0275text(22, "1. User Eligibility");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(23, "p");
        \u0275\u0275text(24, "To use this service, you must be at least 13 years old. By registering, you confirm that the information provided is accurate and complete.");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(25, "section")(26, "h2", 5);
        \u0275\u0275text(27, "2. User Account");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(28, "p");
        \u0275\u0275text(29, "To access certain features of the Service, you must create an account. You agree to provide:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(30, "ul")(31, "li");
        \u0275\u0275text(32, "Your full name");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(33, "li");
        \u0275\u0275text(34, "A valid email address");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(35, "li");
        \u0275\u0275text(36, "A password");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(37, "li");
        \u0275\u0275text(38, "Your location data (for accurate evacuation mapping)");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(39, "p");
        \u0275\u0275text(40, "You are responsible for maintaining the confidentiality of your account and for all activities that occur under your account.");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(41, "section")(42, "h2", 5);
        \u0275\u0275text(43, "3. Use of Service");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(44, "p");
        \u0275\u0275text(45, "You agree to use the system solely for lawful purposes and in a way that does not infringe the rights of others. Misuse of the system, including providing false information or tampering with the mapping process, may result in suspension or termination of your account.");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(46, "section")(47, "h2", 5);
        \u0275\u0275text(48, "4. Modifications");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(49, "p");
        \u0275\u0275text(50, "We reserve the right to modify or discontinue the Service at any time without notice. Continued use of the Service following changes means you accept those changes.");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(51, "section")(52, "h2", 5);
        \u0275\u0275text(53, "5. Limitation of Liability");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(54, "p");
        \u0275\u0275text(55, "We strive to provide accurate evacuation data but do not guarantee the completeness, accuracy, or timeliness of the information provided. We are not liable for any loss or damage arising from the use or inability to use the Service.");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(56, "section")(57, "h2", 5);
        \u0275\u0275text(58, "6. Termination");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(59, "p");
        \u0275\u0275text(60, "We may suspend or terminate your access to the Service if you violate these Terms.");
        \u0275\u0275elementEnd()()()();
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonContent, IonHeader, IonTitle, IonToolbar], styles: [_c0] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TermsModalComponent, [{
    type: Component,
    args: [{ template: `
    <ion-header>
      <ion-toolbar>
        <ion-title class="modal-title"><strong>Terms and Conditions</strong></ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="dismiss()">Close</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <div class="terms-content">
        <h1 class="modal-section-title"><strong>Terms and Conditions</strong></h1>
        <p class="effective-date">Effective Date: April 26, 2025</p>
        <p class="welcome">Welcome to Evacuation Mapping System ("we", "our", or "us"). These <strong>Terms and Conditions</strong> ("Terms") govern your access to and use of our online evacuation mapping system (the "Service"). By registering or using the Service, you agree to be bound by these Terms.</p>

        <section>
          <h2 class="modal-section-title">1. User Eligibility</h2>
          <p>To use this service, you must be at least 13 years old. By registering, you confirm that the information provided is accurate and complete.</p>
        </section>

        <section>
          <h2 class="modal-section-title">2. User Account</h2>
          <p>To access certain features of the Service, you must create an account. You agree to provide:</p>
          <ul>
            <li>Your full name</li>
            <li>A valid email address</li>
            <li>A password</li>
            <li>Your location data (for accurate evacuation mapping)</li>
          </ul>
          <p>You are responsible for maintaining the confidentiality of your account and for all activities that occur under your account.</p>
        </section>

        <section>
          <h2 class="modal-section-title">3. Use of Service</h2>
          <p>You agree to use the system solely for lawful purposes and in a way that does not infringe the rights of others. Misuse of the system, including providing false information or tampering with the mapping process, may result in suspension or termination of your account.</p>
        </section>

        <section>
          <h2 class="modal-section-title">4. Modifications</h2>
          <p>We reserve the right to modify or discontinue the Service at any time without notice. Continued use of the Service following changes means you accept those changes.</p>
        </section>

        <section>
          <h2 class="modal-section-title">5. Limitation of Liability</h2>
          <p>We strive to provide accurate evacuation data but do not guarantee the completeness, accuracy, or timeliness of the information provided. We are not liable for any loss or damage arising from the use or inability to use the Service.</p>
        </section>

        <section>
          <h2 class="modal-section-title">6. Termination</h2>
          <p>We may suspend or terminate your access to the Service if you violate these Terms.</p>
        </section>
      </div>
    </ion-content>
  `, standalone: true, imports: [IonicModule], styles: ["/* angular:styles/component:scss;8611f213b0a2c51402f61ae3189897f888a6723cda087c35e8ce04cbcf865896;C:/Users/<USER>/Lastna/LastProject.1/mobile_ionic/src/app/pages/profile/profile.page.ts */\n.modal-title {\n  font-size: 1.2rem;\n  font-weight: bold;\n}\n.modal-section-title {\n  font-size: 0.9375rem;\n  margin-bottom: 15px;\n}\n/*# sourceMappingURL=profile.page.css.map */\n"] }]
  }], () => [{ type: ModalController }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(TermsModalComponent, { className: "TermsModalComponent", filePath: "src/app/pages/profile/profile.page.ts", lineNumber: 307 });
})();
var PrivacyModalComponent = class _PrivacyModalComponent {
  constructor(modalCtrl) {
    this.modalCtrl = modalCtrl;
  }
  dismiss() {
    this.modalCtrl.dismiss();
  }
  static {
    this.\u0275fac = function PrivacyModalComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _PrivacyModalComponent)(\u0275\u0275directiveInject(ModalController));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _PrivacyModalComponent, selectors: [["ng-component"]], decls: 66, vars: 0, consts: [[1, "modal-title"], ["slot", "end"], [3, "click"], [1, "ion-padding"], [1, "modal-section-title"], [1, "effective-date"]], template: function PrivacyModalComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-title", 0)(3, "strong");
        \u0275\u0275text(4, "Privacy Policy");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(5, "ion-buttons", 1)(6, "ion-button", 2);
        \u0275\u0275listener("click", function PrivacyModalComponent_Template_ion_button_click_6_listener() {
          return ctx.dismiss();
        });
        \u0275\u0275text(7, "Close");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(8, "ion-content", 3)(9, "h2", 4)(10, "strong");
        \u0275\u0275text(11, "Privacy Policy");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(12, "p", 5);
        \u0275\u0275text(13, "Effective Date: April 26, 2025");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(14, "p");
        \u0275\u0275text(15, "DisasterGuard is committed to protecting your privacy. This ");
        \u0275\u0275elementStart(16, "strong");
        \u0275\u0275text(17, "Privacy Policy");
        \u0275\u0275elementEnd();
        \u0275\u0275text(18, " outlines how we collect, use, and protect your information when you use our evacuation mapping system.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(19, "h3", 4);
        \u0275\u0275text(20, "1. Information We Collect");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(21, "p");
        \u0275\u0275text(22, "We collect the following personal information upon registration:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(23, "ul")(24, "li");
        \u0275\u0275text(25, "Name");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(26, "li");
        \u0275\u0275text(27, "Email address");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(28, "li");
        \u0275\u0275text(29, "Password (stored securely)");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(30, "li");
        \u0275\u0275text(31, "Location data (for evacuation mapping purposes)");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(32, "h3", 4);
        \u0275\u0275text(33, "2. How We Use Your Information");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(34, "p");
        \u0275\u0275text(35, "Your data is used solely to:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(36, "ul")(37, "li");
        \u0275\u0275text(38, "Provide personalized evacuation routes and mapping");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(39, "li");
        \u0275\u0275text(40, "Contact you regarding urgent updates or emergencies");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(41, "li");
        \u0275\u0275text(42, "Improve system functionality");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(43, "p");
        \u0275\u0275text(44, "We do not sell, rent, or share your personal information with third parties, except as required by law or to ensure user safety during emergencies.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(45, "h3", 4);
        \u0275\u0275text(46, "3. Data Security");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(47, "p");
        \u0275\u0275text(48, "We implement appropriate security measures to protect your data. Your password is encrypted, and location data is only used to provide real-time evacuation support.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(49, "h3", 4);
        \u0275\u0275text(50, "4. Your Rights");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(51, "p");
        \u0275\u0275text(52, "You may:");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(53, "ul")(54, "li");
        \u0275\u0275text(55, "Access or update your personal data");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(56, "li");
        \u0275\u0275text(57, "Request deletion of your account");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(58, "li");
        \u0275\u0275text(59, "Opt-out of communications at any time");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(60, "p");
        \u0275\u0275text(61, "To do so, contact us at: <EMAIL>");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(62, "h3", 4);
        \u0275\u0275text(63, "5. Changes to This Policy");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(64, "p");
        \u0275\u0275text(65, "We may update this Privacy Policy occasionally. You will be notified of any significant changes.");
        \u0275\u0275elementEnd()();
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonContent, IonHeader, IonTitle, IonToolbar], styles: [_c0] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PrivacyModalComponent, [{
    type: Component,
    args: [{ template: `
    <ion-header>
      <ion-toolbar>
        <ion-title class="modal-title"><strong>Privacy Policy</strong></ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="dismiss()">Close</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <h2 class="modal-section-title"><strong>Privacy Policy</strong></h2>
      <p class="effective-date">Effective Date: April 26, 2025</p>

      <p>DisasterGuard is committed to protecting your privacy. This <strong>Privacy Policy</strong> outlines how we collect, use, and protect your information when you use our evacuation mapping system.</p>

      <h3 class="modal-section-title">1. Information We Collect</h3>
      <p>We collect the following personal information upon registration:</p>
      <ul>
        <li>Name</li>
        <li>Email address</li>
        <li>Password (stored securely)</li>
        <li>Location data (for evacuation mapping purposes)</li>
      </ul>

      <h3 class="modal-section-title">2. How We Use Your Information</h3>
      <p>Your data is used solely to:</p>
      <ul>
        <li>Provide personalized evacuation routes and mapping</li>
        <li>Contact you regarding urgent updates or emergencies</li>
        <li>Improve system functionality</li>
      </ul>
      <p>We do not sell, rent, or share your personal information with third parties, except as required by law or to ensure user safety during emergencies.</p>

      <h3 class="modal-section-title">3. Data Security</h3>
      <p>We implement appropriate security measures to protect your data. Your password is encrypted, and location data is only used to provide real-time evacuation support.</p>

      <h3 class="modal-section-title">4. Your Rights</h3>
      <p>You may:</p>
      <ul>
        <li>Access or update your personal data</li>
        <li>Request deletion of your account</li>
        <li>Opt-out of communications at any time</li>
      </ul>
      <p>To do so, contact us at: support&#64;disasterguard.com</p>

      <h3 class="modal-section-title">5. Changes to This Policy</h3>
      <p>We may update this Privacy Policy occasionally. You will be notified of any significant changes.</p>
    </ion-content>
  `, standalone: true, imports: [IonicModule], styles: ["/* angular:styles/component:scss;8611f213b0a2c51402f61ae3189897f888a6723cda087c35e8ce04cbcf865896;C:/Users/<USER>/Lastna/LastProject.1/mobile_ionic/src/app/pages/profile/profile.page.ts */\n.modal-title {\n  font-size: 1.2rem;\n  font-weight: bold;\n}\n.modal-section-title {\n  font-size: 0.9375rem;\n  margin-bottom: 15px;\n}\n/*# sourceMappingURL=profile.page.css.map */\n"] }]
  }], () => [{ type: ModalController }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(PrivacyModalComponent, { className: "PrivacyModalComponent", filePath: "src/app/pages/profile/profile.page.ts", lineNumber: 379 });
})();
var GuideModalComponent = class _GuideModalComponent {
  constructor(modalCtrl) {
    this.modalCtrl = modalCtrl;
    this.legendItems = [
      { icon: "\u{1F7E2}", label: "Your Location" },
      { icon: "\u{1F7E1}", label: "for Earthquake" },
      { icon: "\u26AB", label: "for Typhoon" },
      { icon: "\u{1F535}", label: "for Flash flood" }
    ];
  }
  dismiss() {
    this.modalCtrl.dismiss();
  }
  static {
    this.\u0275fac = function GuideModalComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _GuideModalComponent)(\u0275\u0275directiveInject(ModalController));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _GuideModalComponent, selectors: [["ng-component"]], decls: 13, vars: 1, consts: [[1, "modal-title"], ["slot", "end"], [3, "click"], [1, "ion-padding"], [1, "modal-section-title"], [1, "legend-items"], ["class", "legend-item", 4, "ngFor", "ngForOf"], [1, "legend-item"], [1, "legend-icon-container"], [1, "legend-icon"], [1, "legend-label"]], template: function GuideModalComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-title", 0);
        \u0275\u0275text(3, "Map Symbols Guide");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-buttons", 1)(5, "ion-button", 2);
        \u0275\u0275listener("click", function GuideModalComponent_Template_ion_button_click_5_listener() {
          return ctx.dismiss();
        });
        \u0275\u0275text(6, "Close");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(7, "ion-content", 3)(8, "h3", 4)(9, "strong");
        \u0275\u0275text(10, "Reference Guide for Map Symbols");
        \u0275\u0275elementEnd()();
        \u0275\u0275elementStart(11, "div", 5);
        \u0275\u0275template(12, GuideModalComponent_div_12_Template, 6, 2, "div", 6);
        \u0275\u0275elementEnd()();
      }
      if (rf & 2) {
        \u0275\u0275advance(12);
        \u0275\u0275property("ngForOf", ctx.legendItems);
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonContent, IonHeader, IonTitle, IonToolbar, CommonModule, NgForOf], styles: ["\n\n.modal-title[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  font-weight: bold;\n}\n.modal-section-title[_ngcontent-%COMP%] {\n  font-size: 0.9375rem;\n  margin-bottom: 15px;\n}\n.legend-items[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n.legend-icon-container[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n.legend-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n  width: 30px;\n  text-align: center;\n}\n.legend-label[_ngcontent-%COMP%] {\n  flex-grow: 1;\n}\n/*# sourceMappingURL=profile.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(GuideModalComponent, [{
    type: Component,
    args: [{ template: `
    <ion-header>
      <ion-toolbar>
        <ion-title class="modal-title">Map Symbols Guide</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="dismiss()">Close</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <h3 class="modal-section-title"><strong>Reference Guide for Map Symbols</strong></h3>
      <div class="legend-items">
        <div class="legend-item" *ngFor="let item of legendItems">
          <div class="legend-icon-container">
            <span class="legend-icon">{{ item.icon }}</span>
            <span class="legend-label">{{ item.label }}</span>
          </div>
        </div>
      </div>
    </ion-content>
  `, standalone: true, imports: [IonicModule, CommonModule], styles: ["/* angular:styles/component:scss;6e17a9af916469f7bf8cc0d05a1d4ae6f16dc321c428a65f4fb08b862e5ac2dc;C:/Users/<USER>/Lastna/LastProject.1/mobile_ionic/src/app/pages/profile/profile.page.ts */\n.modal-title {\n  font-size: 1.2rem;\n  font-weight: bold;\n}\n.modal-section-title {\n  font-size: 0.9375rem;\n  margin-bottom: 15px;\n}\n.legend-items {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n.legend-icon-container {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n.legend-icon {\n  font-size: 24px;\n  width: 30px;\n  text-align: center;\n}\n.legend-label {\n  flex-grow: 1;\n}\n/*# sourceMappingURL=profile.page.css.map */\n"] }]
  }], () => [{ type: ModalController }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(GuideModalComponent, { className: "GuideModalComponent", filePath: "src/app/pages/profile/profile.page.ts", lineNumber: 441 });
})();
var AccountInfoModalComponent = class _AccountInfoModalComponent {
  constructor(modalCtrl) {
    this.modalCtrl = modalCtrl;
    this.userData = {};
    this.loadUserData();
  }
  loadUserData() {
    const data = localStorage.getItem("userData");
    if (data) {
      this.userData = JSON.parse(data);
    }
  }
  dismiss() {
    this.modalCtrl.dismiss();
  }
  static {
    this.\u0275fac = function AccountInfoModalComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AccountInfoModalComponent)(\u0275\u0275directiveInject(ModalController));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AccountInfoModalComponent, selectors: [["ng-component"]], decls: 44, vars: 5, consts: [["slot", "end"], [3, "click"], [1, "ion-padding"], ["name", "person-outline", "slot", "start"], ["name", "call-outline", "slot", "start"], ["name", "calendar-outline", "slot", "start"], ["name", "male-female-outline", "slot", "start"], ["name", "location-outline", "slot", "start"]], template: function AccountInfoModalComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-title");
        \u0275\u0275text(3, "Account Information");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-buttons", 0)(5, "ion-button", 1);
        \u0275\u0275listener("click", function AccountInfoModalComponent_Template_ion_button_click_5_listener() {
          return ctx.dismiss();
        });
        \u0275\u0275text(6, "Close");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(7, "ion-content", 2)(8, "ion-list")(9, "ion-item");
        \u0275\u0275element(10, "ion-icon", 3);
        \u0275\u0275elementStart(11, "ion-label")(12, "h2");
        \u0275\u0275text(13, "Full Name");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(14, "p");
        \u0275\u0275text(15);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(16, "ion-item");
        \u0275\u0275element(17, "ion-icon", 4);
        \u0275\u0275elementStart(18, "ion-label")(19, "h2");
        \u0275\u0275text(20, "Contact Number");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(21, "p");
        \u0275\u0275text(22);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(23, "ion-item");
        \u0275\u0275element(24, "ion-icon", 5);
        \u0275\u0275elementStart(25, "ion-label")(26, "h2");
        \u0275\u0275text(27, "Age");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(28, "p");
        \u0275\u0275text(29);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(30, "ion-item");
        \u0275\u0275element(31, "ion-icon", 6);
        \u0275\u0275elementStart(32, "ion-label")(33, "h2");
        \u0275\u0275text(34, "Gender");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(35, "p");
        \u0275\u0275text(36);
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(37, "ion-item");
        \u0275\u0275element(38, "ion-icon", 7);
        \u0275\u0275elementStart(39, "ion-label")(40, "h2");
        \u0275\u0275text(41, "Address");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(42, "p");
        \u0275\u0275text(43);
        \u0275\u0275elementEnd()()()()();
      }
      if (rf & 2) {
        \u0275\u0275advance(15);
        \u0275\u0275textInterpolate(ctx.userData.full_name);
        \u0275\u0275advance(7);
        \u0275\u0275textInterpolate(ctx.userData.mobile_number);
        \u0275\u0275advance(7);
        \u0275\u0275textInterpolate(ctx.userData.age);
        \u0275\u0275advance(7);
        \u0275\u0275textInterpolate(ctx.userData.gender);
        \u0275\u0275advance(7);
        \u0275\u0275textInterpolate(ctx.userData.address);
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonContent, IonHeader, IonIcon, IonItem, IonLabel, IonList, IonTitle, IonToolbar, CommonModule, FormsModule], encapsulation: 2 });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AccountInfoModalComponent, [{
    type: Component,
    args: [{
      template: `
    <ion-header>
      <ion-toolbar>
        <ion-title>Account Information</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="dismiss()">Close</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <ion-list>
        <ion-item>
          <ion-icon name="person-outline" slot="start"></ion-icon>
          <ion-label>
            <h2>Full Name</h2>
            <p>{{ userData.full_name }}</p>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-icon name="call-outline" slot="start"></ion-icon>
          <ion-label>
            <h2>Contact Number</h2>
            <p>{{ userData.mobile_number }}</p>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-icon name="calendar-outline" slot="start"></ion-icon>
          <ion-label>
            <h2>Age</h2>
            <p>{{ userData.age }}</p>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-icon name="male-female-outline" slot="start"></ion-icon>
          <ion-label>
            <h2>Gender</h2>
            <p>{{ userData.gender }}</p>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-icon name="location-outline" slot="start"></ion-icon>
          <ion-label>
            <h2>Address</h2>
            <p>{{ userData.address }}</p>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-content>
  `,
      standalone: true,
      imports: [IonicModule, CommonModule, FormsModule]
    }]
  }], () => [{ type: ModalController }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AccountInfoModalComponent, { className: "AccountInfoModalComponent", filePath: "src/app/pages/profile/profile.page.ts", lineNumber: 510 });
})();
var EmergencyContactsModalComponent = class _EmergencyContactsModalComponent {
  constructor(modalCtrl) {
    this.modalCtrl = modalCtrl;
  }
  dismiss() {
    this.modalCtrl.dismiss();
  }
  static {
    this.\u0275fac = function EmergencyContactsModalComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _EmergencyContactsModalComponent)(\u0275\u0275directiveInject(ModalController));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _EmergencyContactsModalComponent, selectors: [["ng-component"]], decls: 44, vars: 0, consts: [[1, "modal-title"], ["slot", "end"], [3, "click"], [1, "ion-padding"], ["name", "call-outline", "slot", "start"]], template: function EmergencyContactsModalComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-title", 0);
        \u0275\u0275text(3, "Emergency Contacts");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-buttons", 1)(5, "ion-button", 2);
        \u0275\u0275listener("click", function EmergencyContactsModalComponent_Template_ion_button_click_5_listener() {
          return ctx.dismiss();
        });
        \u0275\u0275text(6, "Close");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(7, "ion-content", 3)(8, "ion-list")(9, "ion-item");
        \u0275\u0275element(10, "ion-icon", 4);
        \u0275\u0275elementStart(11, "ion-label")(12, "h2");
        \u0275\u0275text(13, "National Emergency Hotline");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(14, "p");
        \u0275\u0275text(15, "911");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(16, "ion-item");
        \u0275\u0275element(17, "ion-icon", 4);
        \u0275\u0275elementStart(18, "ion-label")(19, "h2");
        \u0275\u0275text(20, "Fire Department");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(21, "p");
        \u0275\u0275text(22, "160");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(23, "ion-item");
        \u0275\u0275element(24, "ion-icon", 4);
        \u0275\u0275elementStart(25, "ion-label")(26, "h2");
        \u0275\u0275text(27, "Police");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(28, "p");
        \u0275\u0275text(29, "117");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(30, "ion-item");
        \u0275\u0275element(31, "ion-icon", 4);
        \u0275\u0275elementStart(32, "ion-label")(33, "h2");
        \u0275\u0275text(34, "Red Cross");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(35, "p");
        \u0275\u0275text(36, "143");
        \u0275\u0275elementEnd()()();
        \u0275\u0275elementStart(37, "ion-item");
        \u0275\u0275element(38, "ion-icon", 4);
        \u0275\u0275elementStart(39, "ion-label")(40, "h2");
        \u0275\u0275text(41, "Local Disaster Office");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(42, "p");
        \u0275\u0275text(43, "Contact your LGU");
        \u0275\u0275elementEnd()()()()();
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonContent, IonHeader, IonIcon, IonItem, IonLabel, IonList, IonTitle, IonToolbar, CommonModule], styles: ["\n\n.modal-title[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  font-weight: bold;\n}\nh2[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  margin-bottom: 4px;\n}\np[_ngcontent-%COMP%] {\n  font-size: 0.95rem;\n  color: var(--ion-color-medium);\n}\n/*# sourceMappingURL=profile.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(EmergencyContactsModalComponent, [{
    type: Component,
    args: [{ template: `
    <ion-header>
      <ion-toolbar>
        <ion-title class="modal-title">Emergency Contacts</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="dismiss()">Close</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <ion-list>
        <ion-item>
          <ion-icon name="call-outline" slot="start"></ion-icon>
          <ion-label>
            <h2>National Emergency Hotline</h2>
            <p>911</p>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-icon name="call-outline" slot="start"></ion-icon>
          <ion-label>
            <h2>Fire Department</h2>
            <p>160</p>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-icon name="call-outline" slot="start"></ion-icon>
          <ion-label>
            <h2>Police</h2>
            <p>117</p>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-icon name="call-outline" slot="start"></ion-icon>
          <ion-label>
            <h2>Red Cross</h2>
            <p>143</p>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-icon name="call-outline" slot="start"></ion-icon>
          <ion-label>
            <h2>Local Disaster Office</h2>
            <p>Contact your LGU</p>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-content>
  `, standalone: true, imports: [IonicModule, CommonModule], styles: ["/* angular:styles/component:scss;d8fa11f00f48ea58e51dc5684bf6e9b0c58199b277ac8d77f2d3d0fe7df956a0;C:/Users/<USER>/Lastna/LastProject.1/mobile_ionic/src/app/pages/profile/profile.page.ts */\n.modal-title {\n  font-size: 1.2rem;\n  font-weight: bold;\n}\nh2 {\n  font-size: 1rem;\n  margin-bottom: 4px;\n}\np {\n  font-size: 0.95rem;\n  color: var(--ion-color-medium);\n}\n/*# sourceMappingURL=profile.page.css.map */\n"] }]
  }], () => [{ type: ModalController }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(EmergencyContactsModalComponent, { className: "EmergencyContactsModalComponent", filePath: "src/app/pages/profile/profile.page.ts", lineNumber: 597 });
})();
var SafetyTipsModalComponent = class _SafetyTipsModalComponent {
  constructor(modalCtrl) {
    this.modalCtrl = modalCtrl;
  }
  dismiss() {
    this.modalCtrl.dismiss();
  }
  static {
    this.\u0275fac = function SafetyTipsModalComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _SafetyTipsModalComponent)(\u0275\u0275directiveInject(ModalController));
    };
  }
  static {
    this.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SafetyTipsModalComponent, selectors: [["ng-component"]], decls: 49, vars: 0, consts: [[1, "modal-title"], ["slot", "end"], [3, "click"], [1, "ion-padding"]], template: function SafetyTipsModalComponent_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275elementStart(0, "ion-header")(1, "ion-toolbar")(2, "ion-title", 0);
        \u0275\u0275text(3, "Safety Tips");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(4, "ion-buttons", 1)(5, "ion-button", 2);
        \u0275\u0275listener("click", function SafetyTipsModalComponent_Template_ion_button_click_5_listener() {
          return ctx.dismiss();
        });
        \u0275\u0275text(6, "Close");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(7, "ion-content", 3)(8, "ion-list")(9, "ion-item")(10, "ion-label")(11, "h2");
        \u0275\u0275text(12, "Earthquake");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(13, "ul")(14, "li");
        \u0275\u0275text(15, "Drop, Cover, and Hold On.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(16, "li");
        \u0275\u0275text(17, "Stay away from windows and heavy objects.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(18, "li");
        \u0275\u0275text(19, "Evacuate only when safe.");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(20, "ion-item")(21, "ion-label")(22, "h2");
        \u0275\u0275text(23, "Flood");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(24, "ul")(25, "li");
        \u0275\u0275text(26, "Move to higher ground immediately.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(27, "li");
        \u0275\u0275text(28, "Avoid walking or driving through floodwaters.");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(29, "ion-item")(30, "ion-label")(31, "h2");
        \u0275\u0275text(32, "Typhoon");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(33, "ul")(34, "li");
        \u0275\u0275text(35, "Stay indoors and away from glass windows.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(36, "li");
        \u0275\u0275text(37, "Prepare an emergency kit.");
        \u0275\u0275elementEnd()()()();
        \u0275\u0275elementStart(38, "ion-item")(39, "ion-label")(40, "h2");
        \u0275\u0275text(41, "General");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(42, "ul")(43, "li");
        \u0275\u0275text(44, "Keep emergency contacts accessible.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(45, "li");
        \u0275\u0275text(46, "Prepare a Go Bag with essentials.");
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(47, "li");
        \u0275\u0275text(48, "Stay informed via official channels.");
        \u0275\u0275elementEnd()()()()()();
      }
    }, dependencies: [IonicModule, IonButton, IonButtons, IonContent, IonHeader, IonItem, IonLabel, IonList, IonTitle, IonToolbar, CommonModule], styles: ["\n\n.modal-title[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  font-weight: bold;\n}\nh2[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  margin-bottom: 4px;\n}\nul[_ngcontent-%COMP%] {\n  margin: 0;\n  padding-left: 18px;\n  font-size: 0.95rem;\n  color: var(--ion-color-medium);\n}\nli[_ngcontent-%COMP%] {\n  margin-bottom: 4px;\n}\n/*# sourceMappingURL=profile.page.css.map */"] });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SafetyTipsModalComponent, [{
    type: Component,
    args: [{ template: `
    <ion-header>
      <ion-toolbar>
        <ion-title class="modal-title">Safety Tips</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="dismiss()">Close</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <ion-list>
        <ion-item>
          <ion-label>
            <h2>Earthquake</h2>
            <ul>
              <li>Drop, Cover, and Hold On.</li>
              <li>Stay away from windows and heavy objects.</li>
              <li>Evacuate only when safe.</li>
            </ul>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-label>
            <h2>Flood</h2>
            <ul>
              <li>Move to higher ground immediately.</li>
              <li>Avoid walking or driving through floodwaters.</li>
            </ul>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-label>
            <h2>Typhoon</h2>
            <ul>
              <li>Stay indoors and away from glass windows.</li>
              <li>Prepare an emergency kit.</li>
            </ul>
          </ion-label>
        </ion-item>
        <ion-item>
          <ion-label>
            <h2>General</h2>
            <ul>
              <li>Keep emergency contacts accessible.</li>
              <li>Prepare a Go Bag with essentials.</li>
              <li>Stay informed via official channels.</li>
            </ul>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-content>
  `, standalone: true, imports: [IonicModule, CommonModule], styles: ["/* angular:styles/component:scss;a85216103d88f7e7ba343138fa5dd95f056ae0a8ab98a73ec49f387f8199e996;C:/Users/<USER>/Lastna/LastProject.1/mobile_ionic/src/app/pages/profile/profile.page.ts */\n.modal-title {\n  font-size: 1.2rem;\n  font-weight: bold;\n}\nh2 {\n  font-size: 1rem;\n  margin-bottom: 4px;\n}\nul {\n  margin: 0;\n  padding-left: 18px;\n  font-size: 0.95rem;\n  color: var(--ion-color-medium);\n}\nli {\n  margin-bottom: 4px;\n}\n/*# sourceMappingURL=profile.page.css.map */\n"] }]
  }], () => [{ type: ModalController }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SafetyTipsModalComponent, { className: "SafetyTipsModalComponent", filePath: "src/app/pages/profile/profile.page.ts", lineNumber: 681 });
})();
export {
  AccountInfoModalComponent,
  EmergencyContactsModalComponent,
  GuideModalComponent,
  PrivacyModalComponent,
  ProfilePage,
  SafetyTipsModalComponent,
  TermsModalComponent
};
//# sourceMappingURL=profile.page-VXQAMXU4.js.map

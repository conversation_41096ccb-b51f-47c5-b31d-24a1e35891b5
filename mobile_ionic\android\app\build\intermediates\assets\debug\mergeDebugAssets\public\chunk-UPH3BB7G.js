import {
  win
} from "./chunk-JYOJD2RE.js";

// node_modules/@ionic/core/dist/esm/capacitor-59395cbd.js
var getCapacitor = () => {
  if (win !== void 0) {
    return win.Capacitor;
  }
  return void 0;
};

export {
  getCapacitor
};
/*! Bundled license information:

@ionic/core/dist/esm/capacitor-59395cbd.js:
  (*!
   * (C) Ionic http://ionicframework.com - MIT License
   *)
*/
//# sourceMappingURL=chunk-UPH3BB7G.js.map

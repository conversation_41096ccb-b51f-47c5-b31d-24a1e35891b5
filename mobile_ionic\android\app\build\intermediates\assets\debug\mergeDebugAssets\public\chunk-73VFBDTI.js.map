{"version": 3, "sources": ["src/app/services/offline-storage.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { environment } from '../../environments/environment';\r\n\r\nexport interface EvacuationCenter {\r\n  id: number;\r\n  name: string;\r\n  address: string;\r\n  latitude: number;\r\n  longitude: number;\r\n  capacity?: number;\r\n  status?: string;\r\n  disaster_type?: string;\r\n  contact?: string;\r\n  image_url?: string;\r\n  last_updated?: string;\r\n}\r\n\r\nexport interface OfflineRoute {\r\n  id?: string;\r\n  start_lat: number;\r\n  start_lng: number;\r\n  end_lat: number;\r\n  end_lng: number;\r\n  disaster_type: string;\r\n  route_data: string; // JSON string of route coordinates\r\n  distance: number;\r\n  duration: number;\r\n  travel_mode: string;\r\n  created_at?: string;\r\n}\r\n\r\nexport interface OfflineMapTile {\r\n  key: string; // z_x_y format\r\n  z: number;\r\n  x: number;\r\n  y: number;\r\n  tile_data: string; // Base64 encoded image\r\n  created_at: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class OfflineStorageService {\r\n  private readonly STORAGE_KEYS = {\r\n    EVACUATION_CENTERS: 'offline_evacuation_centers',\r\n    ROUTES: 'offline_routes',\r\n    MAP_TILES: 'offline_map_tiles',\r\n    LAST_SYNC: 'last_data_sync',\r\n    OFFLINE_MODE: 'offline_mode_enabled',\r\n    USER_LOCATION: 'last_user_location'\r\n  };\r\n\r\n  private readonly MAX_STORAGE_SIZE = 50 * 1024 * 1024; // 50MB limit\r\n  private readonly TILE_CACHE_LIMIT = 1000; // Maximum tiles to cache\r\n\r\n  constructor(private http: HttpClient) {\r\n    this.initializeStorage();\r\n  }\r\n\r\n  private initializeStorage() {\r\n    // Initialize storage with empty arrays if not exists\r\n    if (!localStorage.getItem(this.STORAGE_KEYS.EVACUATION_CENTERS)) {\r\n      localStorage.setItem(this.STORAGE_KEYS.EVACUATION_CENTERS, JSON.stringify([]));\r\n    }\r\n    if (!localStorage.getItem(this.STORAGE_KEYS.ROUTES)) {\r\n      localStorage.setItem(this.STORAGE_KEYS.ROUTES, JSON.stringify([]));\r\n    }\r\n    if (!localStorage.getItem(this.STORAGE_KEYS.MAP_TILES)) {\r\n      localStorage.setItem(this.STORAGE_KEYS.MAP_TILES, JSON.stringify({}));\r\n    }\r\n    console.log('✅ Offline storage initialized');\r\n  }\r\n\r\n  // ===== EVACUATION CENTERS MANAGEMENT =====\r\n\r\n  /**\r\n   * Sync evacuation centers from backend to local storage\r\n   */\r\n  async syncEvacuationCenters(): Promise<boolean> {\r\n    try {\r\n      console.log('🔄 Syncing evacuation centers from backend...');\r\n\r\n      const response = await firstValueFrom(\r\n        this.http.get<{success: boolean, data: EvacuationCenter[], count: number, sync_timestamp: string}>\r\n        (`${environment.apiUrl}/offline/evacuation-centers`)\r\n      );\r\n\r\n      if (response.success && response.data) {\r\n        await this.saveEvacuationCenters(response.data);\r\n        localStorage.setItem(this.STORAGE_KEYS.LAST_SYNC, response.sync_timestamp);\r\n\r\n        console.log(`✅ Synced ${response.count} evacuation centers`);\r\n        return true;\r\n      } else {\r\n        console.error('❌ Invalid response from server');\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Failed to sync evacuation centers:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Save evacuation centers to local storage\r\n   */\r\n  async saveEvacuationCenters(centers: EvacuationCenter[]): Promise<void> {\r\n    try {\r\n      const centersWithTimestamp = centers.map(center => ({\r\n        ...center,\r\n        last_updated: new Date().toISOString()\r\n      }));\r\n\r\n      localStorage.setItem(\r\n        this.STORAGE_KEYS.EVACUATION_CENTERS,\r\n        JSON.stringify(centersWithTimestamp)\r\n      );\r\n\r\n      console.log(`💾 Saved ${centers.length} evacuation centers to local storage`);\r\n    } catch (error) {\r\n      console.error('❌ Error saving evacuation centers:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get evacuation centers from local storage\r\n   */\r\n  async getEvacuationCenters(disasterType?: string): Promise<EvacuationCenter[]> {\r\n    try {\r\n      const stored = localStorage.getItem(this.STORAGE_KEYS.EVACUATION_CENTERS);\r\n      if (!stored) return [];\r\n\r\n      const centers: EvacuationCenter[] = JSON.parse(stored);\r\n\r\n      if (disasterType) {\r\n        return centers.filter(center => center.disaster_type === disasterType);\r\n      }\r\n\r\n      return centers;\r\n    } catch (error) {\r\n      console.error('❌ Error fetching evacuation centers:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get nearest evacuation centers\r\n   */\r\n  async getNearestCenters(\r\n    userLat: number,\r\n    userLng: number,\r\n    disasterType?: string,\r\n    limit: number = 2\r\n  ): Promise<EvacuationCenter[]> {\r\n    const centers = await this.getEvacuationCenters(disasterType);\r\n\r\n    const centersWithDistance = centers.map(center => ({\r\n      ...center,\r\n      distance: this.calculateDistance(userLat, userLng, center.latitude, center.longitude)\r\n    }));\r\n\r\n    return centersWithDistance\r\n      .sort((a, b) => a.distance - b.distance)\r\n      .slice(0, limit);\r\n  }\r\n\r\n  // ===== ROUTE CACHING =====\r\n\r\n  /**\r\n   * Save route to local storage\r\n   */\r\n  async saveRoute(route: OfflineRoute): Promise<void> {\r\n    try {\r\n      const routes = this.getStoredRoutes();\r\n      const routeWithId = {\r\n        ...route,\r\n        id: `${route.start_lat}_${route.start_lng}_${route.end_lat}_${route.end_lng}_${route.travel_mode}`,\r\n        created_at: new Date().toISOString()\r\n      };\r\n\r\n      // Remove existing route with same parameters\r\n      const filteredRoutes = routes.filter(r => r.id !== routeWithId.id);\r\n      filteredRoutes.push(routeWithId);\r\n\r\n      // Keep only recent routes (limit to 100)\r\n      const limitedRoutes = filteredRoutes.slice(-100);\r\n\r\n      localStorage.setItem(this.STORAGE_KEYS.ROUTES, JSON.stringify(limitedRoutes));\r\n      console.log('💾 Route saved to cache');\r\n    } catch (error) {\r\n      console.error('❌ Error saving route:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get cached route\r\n   */\r\n  async getRoute(\r\n    startLat: number,\r\n    startLng: number,\r\n    endLat: number,\r\n    endLng: number,\r\n    travelMode: string\r\n  ): Promise<OfflineRoute | null> {\r\n    try {\r\n      const routes = this.getStoredRoutes();\r\n      const routeId = `${startLat}_${startLng}_${endLat}_${endLng}_${travelMode}`;\r\n\r\n      const route = routes.find(r => r.id === routeId);\r\n\r\n      // Check if route is not too old (24 hours)\r\n      if (route && route.created_at) {\r\n        const routeAge = Date.now() - new Date(route.created_at).getTime();\r\n        const maxAge = 24 * 60 * 60 * 1000; // 24 hours\r\n\r\n        if (routeAge < maxAge) {\r\n          return route;\r\n        }\r\n      }\r\n\r\n      return null;\r\n    } catch (error) {\r\n      console.error('❌ Error fetching route:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  private getStoredRoutes(): OfflineRoute[] {\r\n    try {\r\n      const stored = localStorage.getItem(this.STORAGE_KEYS.ROUTES);\r\n      return stored ? JSON.parse(stored) : [];\r\n    } catch (error) {\r\n      console.error('❌ Error parsing stored routes:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  // ===== MAP TILES CACHING =====\r\n\r\n  /**\r\n   * Save map tile to cache\r\n   */\r\n  async saveMapTile(z: number, x: number, y: number, tileData: string): Promise<void> {\r\n    try {\r\n      const tiles = this.getStoredTiles();\r\n      const tileKey = `${z}_${x}_${y}`;\r\n\r\n      tiles[tileKey] = {\r\n        key: tileKey,\r\n        z, x, y,\r\n        tile_data: tileData,\r\n        created_at: new Date().toISOString()\r\n      };\r\n\r\n      // Limit cache size\r\n      const tileKeys = Object.keys(tiles);\r\n      if (tileKeys.length > this.TILE_CACHE_LIMIT) {\r\n        // Remove oldest tiles\r\n        const sortedTiles = tileKeys\r\n          .map(key => ({ key, created_at: tiles[key].created_at }))\r\n          .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());\r\n\r\n        const tilesToRemove = sortedTiles.slice(0, tileKeys.length - this.TILE_CACHE_LIMIT);\r\n        tilesToRemove.forEach(tile => delete tiles[tile.key]);\r\n      }\r\n\r\n      localStorage.setItem(this.STORAGE_KEYS.MAP_TILES, JSON.stringify(tiles));\r\n    } catch (error) {\r\n      console.error('❌ Error saving map tile:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get cached map tile\r\n   */\r\n  async getMapTile(z: number, x: number, y: number): Promise<OfflineMapTile | null> {\r\n    try {\r\n      const tiles = this.getStoredTiles();\r\n      const tileKey = `${z}_${x}_${y}`;\r\n      return tiles[tileKey] || null;\r\n    } catch (error) {\r\n      console.error('❌ Error getting map tile:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  private getStoredTiles(): { [key: string]: OfflineMapTile } {\r\n    try {\r\n      const stored = localStorage.getItem(this.STORAGE_KEYS.MAP_TILES);\r\n      return stored ? JSON.parse(stored) : {};\r\n    } catch (error) {\r\n      console.error('❌ Error parsing stored tiles:', error);\r\n      return {};\r\n    }\r\n  }\r\n\r\n  // ===== OFFLINE MODE MANAGEMENT =====\r\n\r\n  /**\r\n   * Enable offline mode\r\n   */\r\n  setOfflineMode(enabled: boolean): void {\r\n    localStorage.setItem(this.STORAGE_KEYS.OFFLINE_MODE, enabled.toString());\r\n    console.log(`🔄 Offline mode ${enabled ? 'enabled' : 'disabled'}`);\r\n  }\r\n\r\n  /**\r\n   * Check if offline mode is enabled\r\n   */\r\n  isOfflineMode(): boolean {\r\n    return localStorage.getItem(this.STORAGE_KEYS.OFFLINE_MODE) === 'true';\r\n  }\r\n\r\n  /**\r\n   * Save user location for offline use\r\n   */\r\n  saveUserLocation(lat: number, lng: number): void {\r\n    const location = { lat, lng, timestamp: new Date().toISOString() };\r\n    localStorage.setItem(this.STORAGE_KEYS.USER_LOCATION, JSON.stringify(location));\r\n  }\r\n\r\n  /**\r\n   * Get last known user location\r\n   */\r\n  getLastUserLocation(): { lat: number; lng: number; timestamp: string } | null {\r\n    try {\r\n      const stored = localStorage.getItem(this.STORAGE_KEYS.USER_LOCATION);\r\n      return stored ? JSON.parse(stored) : null;\r\n    } catch (error) {\r\n      console.error('❌ Error getting user location:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // ===== UTILITY METHODS =====\r\n\r\n  /**\r\n   * Check if offline data is available\r\n   */\r\n  async isDataAvailable(): Promise<boolean> {\r\n    const centers = await this.getEvacuationCenters();\r\n    return centers.length > 0;\r\n  }\r\n\r\n  /**\r\n   * Get last sync time\r\n   */\r\n  getLastSyncTime(): string | null {\r\n    return localStorage.getItem(this.STORAGE_KEYS.LAST_SYNC);\r\n  }\r\n\r\n  /**\r\n   * Calculate distance between two points using Haversine formula\r\n   */\r\n  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {\r\n    const R = 6371; // Earth's radius in kilometers\r\n    const dLat = this.toRadians(lat2 - lat1);\r\n    const dLng = this.toRadians(lng2 - lng1);\r\n\r\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +\r\n              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *\r\n              Math.sin(dLng / 2) * Math.sin(dLng / 2);\r\n\r\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\r\n    return R * c;\r\n  }\r\n\r\n  private toRadians(degrees: number): number {\r\n    return degrees * (Math.PI / 180);\r\n  }\r\n\r\n  /**\r\n   * Clear all offline data\r\n   */\r\n  clearOfflineData(): void {\r\n    Object.values(this.STORAGE_KEYS).forEach(key => {\r\n      localStorage.removeItem(key);\r\n    });\r\n    this.initializeStorage();\r\n    console.log('🗑️ All offline data cleared');\r\n  }\r\n\r\n  /**\r\n   * Get storage usage information\r\n   */\r\n  getStorageInfo(): { used: number; available: number; percentage: number } {\r\n    let used = 0;\r\n\r\n    Object.values(this.STORAGE_KEYS).forEach(key => {\r\n      const item = localStorage.getItem(key);\r\n      if (item) {\r\n        used += new Blob([item]).size;\r\n      }\r\n    });\r\n\r\n    const available = this.MAX_STORAGE_SIZE - used;\r\n    const percentage = (used / this.MAX_STORAGE_SIZE) * 100;\r\n\r\n    return { used, available, percentage };\r\n  }\r\n\r\n  /**\r\n   * Check if device is online\r\n   */\r\n  isOnline(): boolean {\r\n    return navigator.onLine;\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;AA6CM,IAAO,wBAAP,MAAO,uBAAqB;EAahC,YAAoB,MAAgB;AAAhB,SAAA,OAAA;AAZH,SAAA,eAAe;MAC9B,oBAAoB;MACpB,QAAQ;MACR,WAAW;MACX,WAAW;MACX,cAAc;MACd,eAAe;;AAGA,SAAA,mBAAmB,KAAK,OAAO;AAC/B,SAAA,mBAAmB;AAGlC,SAAK,kBAAiB;EACxB;EAEQ,oBAAiB;AAEvB,QAAI,CAAC,aAAa,QAAQ,KAAK,aAAa,kBAAkB,GAAG;AAC/D,mBAAa,QAAQ,KAAK,aAAa,oBAAoB,KAAK,UAAU,CAAA,CAAE,CAAC;IAC/E;AACA,QAAI,CAAC,aAAa,QAAQ,KAAK,aAAa,MAAM,GAAG;AACnD,mBAAa,QAAQ,KAAK,aAAa,QAAQ,KAAK,UAAU,CAAA,CAAE,CAAC;IACnE;AACA,QAAI,CAAC,aAAa,QAAQ,KAAK,aAAa,SAAS,GAAG;AACtD,mBAAa,QAAQ,KAAK,aAAa,WAAW,KAAK,UAAU,CAAA,CAAE,CAAC;IACtE;AACA,YAAQ,IAAI,oCAA+B;EAC7C;;;;;EAOM,wBAAqB;;AACzB,UAAI;AACF,gBAAQ,IAAI,sDAA+C;AAE3D,cAAM,WAAW,MAAM,eACrB,KAAK,KAAK,IACT,GAAG,YAAY,MAAM,6BAA6B,CAAC;AAGtD,YAAI,SAAS,WAAW,SAAS,MAAM;AACrC,gBAAM,KAAK,sBAAsB,SAAS,IAAI;AAC9C,uBAAa,QAAQ,KAAK,aAAa,WAAW,SAAS,cAAc;AAEzE,kBAAQ,IAAI,iBAAY,SAAS,KAAK,qBAAqB;AAC3D,iBAAO;QACT,OAAO;AACL,kBAAQ,MAAM,qCAAgC;AAC9C,iBAAO;QACT;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,6CAAwC,KAAK;AAC3D,eAAO;MACT;IACF;;;;;EAKM,sBAAsB,SAA2B;;AACrD,UAAI;AACF,cAAM,uBAAuB,QAAQ,IAAI,YAAW,iCAC/C,SAD+C;UAElD,eAAc,oBAAI,KAAI,GAAG,YAAW;UACpC;AAEF,qBAAa,QACX,KAAK,aAAa,oBAClB,KAAK,UAAU,oBAAoB,CAAC;AAGtC,gBAAQ,IAAI,mBAAY,QAAQ,MAAM,sCAAsC;MAC9E,SAAS,OAAO;AACd,gBAAQ,MAAM,2CAAsC,KAAK;AACzD,cAAM;MACR;IACF;;;;;EAKM,qBAAqB,cAAqB;;AAC9C,UAAI;AACF,cAAM,SAAS,aAAa,QAAQ,KAAK,aAAa,kBAAkB;AACxE,YAAI,CAAC;AAAQ,iBAAO,CAAA;AAEpB,cAAM,UAA8B,KAAK,MAAM,MAAM;AAErD,YAAI,cAAc;AAChB,iBAAO,QAAQ,OAAO,YAAU,OAAO,kBAAkB,YAAY;QACvE;AAEA,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,6CAAwC,KAAK;AAC3D,eAAO,CAAA;MACT;IACF;;;;;EAKM,kBACJ,SACA,SACA,cACA,QAAgB,GAAC;;AAEjB,YAAM,UAAU,MAAM,KAAK,qBAAqB,YAAY;AAE5D,YAAM,sBAAsB,QAAQ,IAAI,YAAW,iCAC9C,SAD8C;QAEjD,UAAU,KAAK,kBAAkB,SAAS,SAAS,OAAO,UAAU,OAAO,SAAS;QACpF;AAEF,aAAO,oBACJ,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE,QAAQ,EACtC,MAAM,GAAG,KAAK;IACnB;;;;;;EAOM,UAAU,OAAmB;;AACjC,UAAI;AACF,cAAM,SAAS,KAAK,gBAAe;AACnC,cAAM,cAAc,iCACf,QADe;UAElB,IAAI,GAAG,MAAM,SAAS,IAAI,MAAM,SAAS,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,WAAW;UAChG,aAAY,oBAAI,KAAI,GAAG,YAAW;;AAIpC,cAAM,iBAAiB,OAAO,OAAO,OAAK,EAAE,OAAO,YAAY,EAAE;AACjE,uBAAe,KAAK,WAAW;AAG/B,cAAM,gBAAgB,eAAe,MAAM,IAAI;AAE/C,qBAAa,QAAQ,KAAK,aAAa,QAAQ,KAAK,UAAU,aAAa,CAAC;AAC5E,gBAAQ,IAAI,gCAAyB;MACvC,SAAS,OAAO;AACd,gBAAQ,MAAM,8BAAyB,KAAK;MAC9C;IACF;;;;;EAKM,SACJ,UACA,UACA,QACA,QACA,YAAkB;;AAElB,UAAI;AACF,cAAM,SAAS,KAAK,gBAAe;AACnC,cAAM,UAAU,GAAG,QAAQ,IAAI,QAAQ,IAAI,MAAM,IAAI,MAAM,IAAI,UAAU;AAEzE,cAAM,QAAQ,OAAO,KAAK,OAAK,EAAE,OAAO,OAAO;AAG/C,YAAI,SAAS,MAAM,YAAY;AAC7B,gBAAM,WAAW,KAAK,IAAG,IAAK,IAAI,KAAK,MAAM,UAAU,EAAE,QAAO;AAChE,gBAAM,SAAS,KAAK,KAAK,KAAK;AAE9B,cAAI,WAAW,QAAQ;AACrB,mBAAO;UACT;QACF;AAEA,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,gCAA2B,KAAK;AAC9C,eAAO;MACT;IACF;;EAEQ,kBAAe;AACrB,QAAI;AACF,YAAM,SAAS,aAAa,QAAQ,KAAK,aAAa,MAAM;AAC5D,aAAO,SAAS,KAAK,MAAM,MAAM,IAAI,CAAA;IACvC,SAAS,OAAO;AACd,cAAQ,MAAM,uCAAkC,KAAK;AACrD,aAAO,CAAA;IACT;EACF;;;;;EAOM,YAAY,GAAW,GAAW,GAAW,UAAgB;;AACjE,UAAI;AACF,cAAM,QAAQ,KAAK,eAAc;AACjC,cAAM,UAAU,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AAE9B,cAAM,OAAO,IAAI;UACf,KAAK;UACL;UAAG;UAAG;UACN,WAAW;UACX,aAAY,oBAAI,KAAI,GAAG,YAAW;;AAIpC,cAAM,WAAW,OAAO,KAAK,KAAK;AAClC,YAAI,SAAS,SAAS,KAAK,kBAAkB;AAE3C,gBAAM,cAAc,SACjB,IAAI,UAAQ,EAAE,KAAK,YAAY,MAAM,GAAG,EAAE,WAAU,EAAG,EACvD,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,UAAU,EAAE,QAAO,IAAK,IAAI,KAAK,EAAE,UAAU,EAAE,QAAO,CAAE;AAErF,gBAAM,gBAAgB,YAAY,MAAM,GAAG,SAAS,SAAS,KAAK,gBAAgB;AAClF,wBAAc,QAAQ,UAAQ,OAAO,MAAM,KAAK,GAAG,CAAC;QACtD;AAEA,qBAAa,QAAQ,KAAK,aAAa,WAAW,KAAK,UAAU,KAAK,CAAC;MACzE,SAAS,OAAO;AACd,gBAAQ,MAAM,iCAA4B,KAAK;MACjD;IACF;;;;;EAKM,WAAW,GAAW,GAAW,GAAS;;AAC9C,UAAI;AACF,cAAM,QAAQ,KAAK,eAAc;AACjC,cAAM,UAAU,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9B,eAAO,MAAM,OAAO,KAAK;MAC3B,SAAS,OAAO;AACd,gBAAQ,MAAM,kCAA6B,KAAK;AAChD,eAAO;MACT;IACF;;EAEQ,iBAAc;AACpB,QAAI;AACF,YAAM,SAAS,aAAa,QAAQ,KAAK,aAAa,SAAS;AAC/D,aAAO,SAAS,KAAK,MAAM,MAAM,IAAI,CAAA;IACvC,SAAS,OAAO;AACd,cAAQ,MAAM,sCAAiC,KAAK;AACpD,aAAO,CAAA;IACT;EACF;;;;;EAOA,eAAe,SAAgB;AAC7B,iBAAa,QAAQ,KAAK,aAAa,cAAc,QAAQ,SAAQ,CAAE;AACvE,YAAQ,IAAI,0BAAmB,UAAU,YAAY,UAAU,EAAE;EACnE;;;;EAKA,gBAAa;AACX,WAAO,aAAa,QAAQ,KAAK,aAAa,YAAY,MAAM;EAClE;;;;EAKA,iBAAiB,KAAa,KAAW;AACvC,UAAM,WAAW,EAAE,KAAK,KAAK,YAAW,oBAAI,KAAI,GAAG,YAAW,EAAE;AAChE,iBAAa,QAAQ,KAAK,aAAa,eAAe,KAAK,UAAU,QAAQ,CAAC;EAChF;;;;EAKA,sBAAmB;AACjB,QAAI;AACF,YAAM,SAAS,aAAa,QAAQ,KAAK,aAAa,aAAa;AACnE,aAAO,SAAS,KAAK,MAAM,MAAM,IAAI;IACvC,SAAS,OAAO;AACd,cAAQ,MAAM,uCAAkC,KAAK;AACrD,aAAO;IACT;EACF;;;;;EAOM,kBAAe;;AACnB,YAAM,UAAU,MAAM,KAAK,qBAAoB;AAC/C,aAAO,QAAQ,SAAS;IAC1B;;;;;EAKA,kBAAe;AACb,WAAO,aAAa,QAAQ,KAAK,aAAa,SAAS;EACzD;;;;EAKQ,kBAAkB,MAAc,MAAc,MAAc,MAAY;AAC9E,UAAM,IAAI;AACV,UAAM,OAAO,KAAK,UAAU,OAAO,IAAI;AACvC,UAAM,OAAO,KAAK,UAAU,OAAO,IAAI;AAEvC,UAAM,IAAI,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,IACtC,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,IAC9D,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC;AAEhD,UAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AACvD,WAAO,IAAI;EACb;EAEQ,UAAU,SAAe;AAC/B,WAAO,WAAW,KAAK,KAAK;EAC9B;;;;EAKA,mBAAgB;AACd,WAAO,OAAO,KAAK,YAAY,EAAE,QAAQ,SAAM;AAC7C,mBAAa,WAAW,GAAG;IAC7B,CAAC;AACD,SAAK,kBAAiB;AACtB,YAAQ,IAAI,0CAA8B;EAC5C;;;;EAKA,iBAAc;AACZ,QAAI,OAAO;AAEX,WAAO,OAAO,KAAK,YAAY,EAAE,QAAQ,SAAM;AAC7C,YAAM,OAAO,aAAa,QAAQ,GAAG;AACrC,UAAI,MAAM;AACR,gBAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;MAC3B;IACF,CAAC;AAED,UAAM,YAAY,KAAK,mBAAmB;AAC1C,UAAM,aAAc,OAAO,KAAK,mBAAoB;AAEpD,WAAO,EAAE,MAAM,WAAW,WAAU;EACtC;;;;EAKA,WAAQ;AACN,WAAO,UAAU;EACnB;;;uCA7WW,wBAAqB,mBAAA,UAAA,CAAA;IAAA;EAAA;;4EAArB,wBAAqB,SAArB,uBAAqB,WAAA,YAFpB,OAAM,CAAA;EAAA;;;sEAEP,uBAAqB,CAAA;UAHjC;WAAW;MACV,YAAY;KACb;;;", "names": []}